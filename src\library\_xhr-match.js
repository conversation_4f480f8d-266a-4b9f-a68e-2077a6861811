import config from "@/config";
import errors from "@/errors";
import Vue from "vue";
import pako from "pako";

export default {
  loading: false,
  get(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "MATCH"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("arguments" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading == true) {
      //   feedback.status = errors.request.pending;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading = true;
        fetch(url, {
          method: "POST",
          body: JSON.stringify(args),
          headers: { "Content-Type": "application/json", "Raw-Data": "on" }
        })
          .then(res => res.json())
          .then(data => {
            // var bin = atob(text);
            // var json = pako.inflate(bin, { to: "string" });
            // var data = JSON.parse(json);

            // console.log(data);

            this.loading = false;
            if (data) {
              // check status code
              if (typeof data.status == "string") {
                feedback.success = data.status == "1";
              } else {
                feedback.success = data.status == 1;
              }

              feedback.status = data.statusdesc;
              feedback.debug = data.debug;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = data.data;
                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.login.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          })
          .catch(err => {
            this.loading = false;

            feedback.status = errors.request.processing;
            feedback.error = err;
            reject(feedback);
          });
      } else {
        this.loading = false;
        reject(feedback);
      }
    });
  }
};
