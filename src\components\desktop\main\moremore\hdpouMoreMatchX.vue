<template lang="pug">
.col-12.hx-more-row(v-if="details['oxt'] != null || details['hdp'] != null || details['ou'] != null || details['oe'] != null")
  //- small.text-primary {{ childId }}
  //- small.text-primary {{ mmoDetails['tn'] > 0 }}
  .card
    .card-header(
      :id="'heading-hdpou-more-' + uid + childId"
      data-toggle="collapse"
      :data-target="'#collapse-hdpou-more-' + uid + childId"
      :aria-controls="'collapse-hdpou-more-' + uid + childId"
      aria-expanded=true
      :class="layoutIndex == 3 ? 'live': 'non-live'"
    )
      i.fad.fa-chevron-circle-down
      span.header-bettype {{ leagueName.trim() }}
    .collapse.show(
      :aria-labelledby="'heading-hdpou-more-' + uid + childId"
      :id="'collapse-hdpou-more-' + uid + childId"
    )
      .card-body.p-0(:id="'accordian-hdpou-more-' + uid + childId")
        //- MMO
        .hx-table.hx-morebet-header(v-if="Object.keys(mmoDetails).length > 0 && mmoDetails['tn'] > 0" :class="marketType == 3 ? 'live' : 'non-live'").bl-1.br-1.bb-1.h-18
          .hx-cell.flex-fill
            .hx-row
              .hx-col
                .hx.text-left {{ $t("ui.full_time") }}
          .hx-cell.w-392.bl-1
            .hx-row
              .hx-col.w-98
                .hx.w-100.text-center {{ $t("ui.home") }}
              .hx-col.w-98.bl-1
                .hx.w-100.text-center {{ $t("ui.away") }}
              .hx-col.w-98.bl-1
                .hx.w-100.text-center {{ $t("ui.over") }}
              .hx-col.w-98.bl-1
                .hx.w-100.text-center {{ $t("ui.under") }}
        .hx-table.hx-match.hx-morebet-body.hx-compact.hx-sl.hx-mmox(v-if="Object.keys(mmoDetails).length > 0 && mmoDetails['tn'] > 0" :class="{ 'live': marketType == 3 }").bl-1.br-1.bb-1
          .hx-cell.w-62
            .hx-row.h-100.hx-rows
              timePanel(:source="source")
          .hx-cell.flex-fill
            .hx-row.h-100.hx-rows.w-392
              .hx-col.d-block.h-100.w-100.text-ellipsis
                .hx.hx-ellipsis(:class="details['team'] == 1 ? 'team-red' : 'team-black'") {{ source.homeTeam }}
                .hx.hx-ellipsis(:class="details['team'] == 0 ? 'team-red' : 'team-black'") {{ source.awayTeam }}
          .hx-cell.w-392
            .hx-row.hx-rows(v-for="(dn, i) in mmoDetails['tn']")
              .hx-col.hx-cols.hx-border.w-98
                .hx.hx-flex-c.h-100(v-if="mmoDetails['hdp'] != null && mmoDetails['hdp'][i] && mmoDetails['hdp'][i][22] != null && mmoDetails['hdp'][i][22] != 0 && mmoDetails['hdp'][i][22] != ''")
                  .hxs.w-72
                    mmoItemX(v-if="mmoDetails['hdp'][i][7] == 1" :odds="mmoDetails['hdp'][i]" idx=10 pos=22 :typ="oddsType" dataType="1" homeaway="H" :giving="mmoDetails['hdp'][i][7]")
                    mmoItemX(v-if="mmoDetails['hdp'][i][7] == 0" :odds="mmoDetails['hdp'][i]" idx=9 pos=22 :typ="oddsType" dataType="1" homeaway="H" :giving="mmoDetails['hdp'][i][7]")
              .hx-col.hx-cols.hx-border.w-98
                .hx.hx-flex-c.h-100(v-if="mmoDetails['hdp'] != null && mmoDetails['hdp'][i] && mmoDetails['hdp'][i][22] != null && mmoDetails['hdp'][i][22] != 0 && mmoDetails['hdp'][i][22] != ''")
                  .hxs.w-72
                    mmoItemX(v-if="mmoDetails['hdp'][i][7] == 1" :odds="mmoDetails['hdp'][i]" idx=9 pos=22 :typ="oddsType" dataType="1" homeaway="A" :giving="mmoDetails['hdp'][i][7]")
                    mmoItemX(v-if="mmoDetails['hdp'][i][7] == 0" :odds="mmoDetails['hdp'][i]" idx=10 pos=22 :typ="oddsType" dataType="1" homeaway="A" :giving="mmoDetails['hdp'][i][7]")
              .hx-col.hx-cols.hx-border.w-98
                .hx.hx-flex-c.h-100(v-if="mmoDetails['ou'] != null && mmoDetails['ou'][i] != null && mmoDetails['ou'][i][23] != null && mmoDetails['ou'][i][23] != 0 && mmoDetails['ou'][i][23] != ''")
                  .hxs.w-72
                    mmoItemX(:odds="mmoDetails['ou'][i]" idx=12 pos=23 :typ="oddsType" dataType="1" homeaway="O" :giving="mmoDetails['ou'][i][7]")
              .hx-col.hx-cols.hx-border.w-98
                .hx.hx-flex-c.h-100(v-if="mmoDetails['ou'] != null && mmoDetails['ou'][i] != null && mmoDetails['ou'][i][23] != null && mmoDetails['ou'][i][23] != 0 && mmoDetails['ou'][i][23] != ''")
                  .hxs.w-72
                    mmoItemX(:odds="mmoDetails['ou'][i]" idx=11 pos=23 :typ="oddsType" dataType="1" homeaway="U" :giving="mmoDetails['ou'][i][7]")
</template>

<script>
import config from "@/config";

export default {
  components: {
    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    mmoItemX: () => import("@/components/desktop/main/mmo/mmoItemX")
  },
  props: {
    childId: {
      type: Number
    },
    uid: {
      type: String
    },
    details: {
      type: Object
    },
    matchId: {
      type: Number
    },
    leagueId: {
      type: Number
    },
    marketType: {
      type: Number
    },
    sportsType: {
      type: Number
    },
    betType: {
      type: String
    },
    layoutIndex: {
      type: Number
    }
  },
  computed: {
    mmoDetails() {
      return this.details["mmo"];
    },
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
    leagueName() {
      var result = this.details["league"].split(" - ");
      if (result.length >= 2) {
        return result[result.length - 1];
      }
      return this.details["league"];
    },
    source(){
      return {
        marketId: this.details.match[4],
        matchTime: this.details.match[8],
        runningScore: this.details.match[11],
        runningTime: this.details.match[12],
        homeTeam: this.details.match[5],
        awayTeam: this.details.match[6],
      }
    }
  }
};
</script>
