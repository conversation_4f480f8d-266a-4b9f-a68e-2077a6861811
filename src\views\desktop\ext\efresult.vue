<template lang="pug">
div.ng-result
  header.short
    .topbar.info-top.d-flex.align-items-center.justify-content-center
      template(v-if="whiteLabel")
        .back-link
          i.fas.fa-chevron-left
          router-link(to="/desktop") {{ $t("ui.back") }}
      .d-flex.info-content.align-items-center.flex-fill
        .logo
          img(:src="'/v1/images/icon-sport-svg/' + getImage(sportsType)")
          span {{ title }}
        .nav-info.flex-fill
        .nav-info
  .info-wrapper
    .info-tablewrap.magicZ
      .table-responsive

        template(v-if="racingList.includes(parseInt(sportsType))")

          .tab-content(v-if="racingList1.includes(parseInt(sportsType))")
            table.table.table-bordered.text-center.table-digits(style="width: 90%; min-width: 900px;")
              tr.bg-color01.text-white
                th(width="auto" style="text-align: left; vertical-align: middle") {{ $t('ui.date') }}
                th(width="160px" style="text-align: left; vertical-align: middle") 1
                  sup st
                th(width="160px" style="text-align: left; vertical-align: middle") 2
                  sup nd
                th(width="160px" style="text-align: left; vertical-align: middle") 3
                  sup rd
                th(width="80px" style="vertical-align: middle") {{ $t('ui.ou') }}
                th(width="80px" style="vertical-align: middle") {{ $t('ui.oe') }}
              template(v-for="(item, key, index) in schedule.EFResult")
                tr.bg-color03
                  td(style="text-align: left; vertical-align: middle") {{ $dayjs(item.match_time).format("MM/DD/YYYY hh:mm A") }}
                  td(style="text-align: left; vertical-align: middle")
                    span(v-if="item.dog1 == 1") {{ item.dog1_name }}
                    span(v-if="item.dog2 == 1") {{ item.dog2_name }}
                    span(v-if="item.dog3 == 1") {{ item.dog3_name }}
                    span(v-if="item.dog4 == 1") {{ item.dog4_name }}
                    span(v-if="item.dog5 == 1") {{ item.dog5_name }}
                    span(v-if="item.dog6 == 1") {{ item.dog6_name }}
                  td(style="text-align: left; vertical-align: middle")
                    span(v-if="item.dog1 == 2") {{ item.dog1_name }}
                    span(v-if="item.dog2 == 2") {{ item.dog2_name }}
                    span(v-if="item.dog3 == 2") {{ item.dog3_name }}
                    span(v-if="item.dog4 == 2") {{ item.dog4_name }}
                    span(v-if="item.dog5 == 2") {{ item.dog5_name }}
                    span(v-if="item.dog6 == 2") {{ item.dog6_name }}
                  td(style="text-align: left; vertical-align: middle")
                    span(v-if="item.dog1 == 3") {{ item.dog1_name }}
                    span(v-if="item.dog2 == 3") {{ item.dog2_name }}
                    span(v-if="item.dog3 == 3") {{ item.dog3_name }}
                    span(v-if="item.dog4 == 3") {{ item.dog4_name }}
                    span(v-if="item.dog5 == 3") {{ item.dog5_name }}
                    span(v-if="item.dog6 == 3") {{ item.dog6_name }}
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-over(v-if="item.dog4 == 1 || item.dog5 == 1 || item.dog6 == 1") {{ $t('m.LOT_OVER') }}
                    .digits.rounded-circle.mx-auto.text-white.ball-under(v-else) {{ $t('m.LOT_UNDER') }}
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-odd(v-if="item.dog1 == 1 || item.dog3 == 1 || item.dog5 == 1") {{ $t('m.LOT_ODD') }}
                    .digits.rounded-circle.mx-auto.text-white.ball-even(v-else) {{ $t('m.LOT_EVEN') }}

          .tab-content(v-if="racingList2.includes(parseInt(sportsType))")
            table.table.table-bordered.text-center.table-digits(style="width: 90%; min-width: 900px;")
              tr.bg-color01.text-white
                th(width="auto" style="text-align: left; vertical-align: middle") {{ $t('ui.date') }}
                th(width="60px" style="vertical-align: middle") 1
                  sup st
                th(width="60px" style="vertical-align: middle") 2
                  sup nd
                th(width="60px" style="vertical-align: middle") 3
                  sup rd
                th(width="60px" style="vertical-align: middle") 4
                  sup th
                th(width="60px" style="vertical-align: middle") 5
                  sup th
                th(width="80px" style="vertical-align: middle") {{ $t('ui.score') }}
                th(width="80px" style="vertical-align: middle") {{ $t('ui.yellow') }} {{ $t('m.GC_OU') }}
                th(width="80px" style="vertical-align: middle") {{ $t('ui.blue') }} {{ $t('m.GC_OU') }}
                th(width="80px" style="vertical-align: middle") {{ $t('ui.yellow') }} {{ $t('m.GC_OE') }}
                th(width="80px" style="vertical-align: middle") {{ $t('ui.blue') }} {{ $t('m.GC_OE') }}
              template(v-for="(item, key, index) in schedule.EFResult")
                tr.bg-color03
                  td(style="text-align: left; vertical-align: middle") {{ $dayjs(item.match_time).format("MM/DD/YYYY hh:mm A") }}
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-blue(v-if="item.ball_1 == 'B'") B
                    .digits.rounded-circle.mx-auto.ball-yellow(v-else) Y
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-blue(v-if="item.ball_2 == 'B'") B
                    .digits.rounded-circle.mx-auto.ball-yellow(v-else) Y
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-blue(v-if="item.ball_3 == 'B'") B
                    .digits.rounded-circle.mx-auto.ball-yellow(v-else) Y
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-blue(v-if="item.ball_4 == 'B'") B
                    .digits.rounded-circle.mx-auto.ball-yellow(v-else) Y
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-blue(v-if="item.ball_5 == 'B'") B
                    .digits.rounded-circle.mx-auto.ball-yellow(v-else) Y
                  td(style="vertical-align: middle")
                    | {{ item.home_score }}-{{ item.away_score }}
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-over(v-if="item.home_score>item.away_score") {{ $t('m.LOT_OVER') }}
                    .digits.rounded-circle.mx-auto.text-white.ball-under(v-else) {{ $t('m.LOT_UNDER') }}
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-over(v-if="item.home_score<item.away_score") {{ $t('m.LOT_OVER') }}
                    .digits.rounded-circle.mx-auto.text-white.ball-under(v-else) {{ $t('m.LOT_UNDER') }}
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-odd(v-if="item.home_score%2!=0") {{ $t('m.LOT_ODD') }}
                    .digits.rounded-circle.mx-auto.text-white.ball-even(v-else) {{ $t('m.LOT_EVEN') }}
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-odd(v-if="item.away_score%2!=0") {{ $t('m.LOT_ODD') }}
                    .digits.rounded-circle.mx-auto.text-white.ball-even(v-else) {{ $t('m.LOT_EVEN') }}

          .tab-content(v-if="racingList3.includes(parseInt(sportsType))")
            table.table.table-bordered.text-center.table-digits(style="width: 90%; min-width: 900px;")
              tr.bg-color01.text-white
                th(width="auto" style="text-align: left; vertical-align: middle") {{ $t('ui.date') }}
                th(width="120px" style="vertical-align: middle") {{ $t('ui.team') }}
                th(width="160px" style="text-align: left; vertical-align: middle") {{ $t('ui.winner') }}
                th(width="100px" style="vertical-align: middle") {{ $t('ui.ou') }}
                th(width="100px" style="vertical-align: middle") {{ $t('ui.oe') }}
              template(v-for="(item, key, index) in schedule.EFResult")
                tr.bg-color03
                  td(style="text-align: left; vertical-align: middle") {{ $dayjs(item.match_time).format("MM/DD/YYYY hh:mm A") }}
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-a(v-if="item.team_1 == 1 || item.team_4 == 1 || item.team_7 == 1 || item.team_10 == 1") A
                    .digits.rounded-circle.mx-auto.text-white.ball-b(v-if="item.team_2 == 1 || item.team_5 == 1 || item.team_8 == 1 || item.team_11 == 1") B
                    .digits.rounded-circle.mx-auto.text-white.ball-c(v-if="item.team_3 == 1 || item.team_6 == 1 || item.team_9 == 1 || item.team_12 == 1") C
                  td(style="text-align: left; vertical-align: middle")
                    span(v-if="item.team_1 == 1") {{ item.team_1_name }}
                    span(v-if="item.team_2 == 1") {{ item.team_2_name }}
                    span(v-if="item.team_3 == 1") {{ item.team_3_name }}
                    span(v-if="item.team_4 == 1") {{ item.team_4_name }}
                    span(v-if="item.team_5 == 1") {{ item.team_5_name }}
                    span(v-if="item.team_6 == 1") {{ item.team_6_name }}
                    span(v-if="item.team_7 == 1") {{ item.team_7_name }}
                    span(v-if="item.team_8 == 1") {{ item.team_8_name }}
                    span(v-if="item.team_9 == 1") {{ item.team_9_name }}
                    span(v-if="item.team_10 == 1") {{ item.team_10_name }}
                    span(v-if="item.team_11 == 1") {{ item.team_11_name }}
                    span(v-if="item.team_12 == 1") {{ item.team_12_name }}
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-over(v-if="item.team_7 == 1 || item.team_8 == 1 || item.team_9 == 1 || item.team_10 == 1 || item.team_11 == 1 || item.team_12 == 1") {{ $t('m.LOT_OVER') }}
                    .digits.rounded-circle.mx-auto.text-white.ball-under(v-else) {{ $t('m.LOT_UNDER') }}
                  td(style="vertical-align: middle")
                    .digits.rounded-circle.mx-auto.text-white.ball-odd(v-if="item.team_1 == 1 || item.team_3 == 1 || item.team_5 == 1 || item.team_7 == 1 || item.team_9 == 1 || item.team_11 == 1") {{ $t('m.LOT_ODD') }}
                    .digits.rounded-circle.mx-auto.text-white.ball-even(v-else) {{ $t('m.LOT_EVEN') }}
        template(v-else)
          .tab-content
            table.table.table-bordered.text-center.table-digits(style="width: 90%; min-width: 900px;")
              tr.bg-color01.text-white
                th(width="150px" style="text-align: left; vertical-align: middle") {{ $t('ui.date') }}
                th(style="text-align: left; vertical-align: middle") {{ $t('ui.match') }}
                th(width="120px" style="vertical-align: middle") {{ $t('ui.score') }}
                th(width="120px" style="vertical-align: middle") {{ $t('ui.winner') }}
                template(v-if="!ef1.includes(sportsType)")
                  th(width="120px" style="vertical-align: middle" v-if="['42'].includes(sportsType)") {{ $t('ui.ko') }}
                  th(width="120px" style="vertical-align: middle" v-else) {{ $t('ui.ou') }}
              tr.bg-color03(v-for="(item, key, index) in schedule.EFResult")
                td(style="text-align: left; vertical-align: middle") {{ $dayjs(item.match_time).format("MM/DD/YYYY h:mm A") }}
                td(style="text-align: left; vertical-align: middle") {{ item.home_name_en }} -vs- {{ item.away_name_en }}
                td(style="vertical-align: middle") {{ item.score }}
                td(style="vertical-align: middle")
                  .digits.rounded-circle.mx-auto.text-white(:class="item.home_win == 1 ? 'ball-red' : 'ball-blue'") {{ $t('m.LOT_'+item.home_win) }}
                td(style="vertical-align: middle" v-if="!ef1.includes(sportsType)")
                  template(v-if="['42'].includes(sportsType)")
                    .digits.rounded-circle.mx-auto.text-white(:class="item.over_under == 'odd' ? 'ball-odd' : 'ball-even'") {{ $t('m.LOT_'+item.over_under.toUpperCase()) }}
                  template(v-else)
                    .digits.rounded-circle.mx-auto.text-white(:class="item.over_under == 'over' ? 'ball-over' : 'ball-under'") {{ $t('m.LOT_'+item.over_under.toUpperCase()) }}

</template>

<script>
import config from "@/config";
import errors from "@/errors";
import SpinButton from "@/components/ui/SpinButton";
import mixinExt from "@/library/mixinExt.js";

export default {
  components: {
    SpinButton
  },
  mixins: [mixinExt],
  data() {
    return {
      sportsType: null,
      title: "",
      url: "",
      img: "",
      domain: "",
      tabs: 0,
      result: []
    };
  },
  computed: {
    ef1() {
      return config.ef1;
    },
    racingList() {
      return config.racingList;
    },
    racingList1() {
      return config.racingList1;
    },
    racingList2() {
      return config.racingList2;
    },
    racingList3() {
      return config.racingList3;
    },
    whiteLabel() {
      return config.whiteLabel;
    }
  },
  created() {},
  destroyed() {},
  mounted() {
    var q = this.$route.query;

    if (q.q) {
      this.sportsType = q.q;
      this.title = q.n.replace("$", "+");
      // this.url = q.u;
      // this.domain = this.extractDomain(q.u);
      setTimeout(() => {
        this.query();
      }, 500);
    }
  },
  methods: {
    getTab(e) {
      return config.sports4dTab[100 + e];
    },
    extractDomain(url) {
      var domain;
      //find & remove protocol (http, ftp, etc.) and get domain
      if (url.indexOf("://") > -1) {
        domain = url.split("/")[2];
      } else {
        domain = url.split("/")[0];
      }

      //find & remove www
      if (domain.indexOf("www.") > -1) {
        domain = domain.split("www.")[1];
      }

      domain = domain.split(":")[0]; //find & remove port number
      domain = domain.split("?")[0]; //find & remove url params

      return domain;
    },
    query() {
      // this function is in mixinExt.js
      this.getEFResult(this.sportsType).then(() => {
        // console.log(this.schedule);
      });
    },
    getImage(e) {
      return config.getSportsImage(e);
    }
  }
};
</script>
