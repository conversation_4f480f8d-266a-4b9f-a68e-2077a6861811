<template lang="pug">
  .group.parlay
    ul.subgroup
      li(@click="toggleAll()")
        a(href="javascript:void(0);")
          .d-flex.justify-content-center.align-items-center(:title="$t('ui.all_sports')")
            .mr-0
              input(type="checkbox" :checked="selectedAll")
            .sport-icon
              img(src="/v1/images/icon-sport-svg/0.svg")
            .flex-fill
              span.pl-1.text-capitalize.menu-item.mopts(:class="cls") {{ $t("ui.all_sports") }}
            .text-live.active-none(v-if="allLive > 0") {{ lang == 'vi' ? "live" : $t("ui.live") }}
            .game-number {{ allCount }}
      template(v-for="n in order")
        li(:key="n" v-if="items[n]" @click="toggleOptions(n)")
          a(href="javascript:void(0);")
            .d-flex.justify-content-start.align-items-center(:title="sports[n]")
              .mr-0
                input(type="checkbox" :checked="options[n]")
              .sport-icon
                img(:src="'/v1/images/icon-sport-svg/' + getSportsImage(n)")
              .flex-fill(:class="cls")
                span.pl-1.menu-item.mopts(:class="![42].includes(n) ? 'text-capitalize' : 'text-uppercase'") {{ sports[n].toLowerCase() }}
              .text-live.active-none(v-if="getLive(n) > 0") {{ lang == 'vi' ? "live" : $t("ui.live") }}
              .game-number(:class="cls") {{ items[n].tn }}
</template>

<script>
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";
import { setTimeout } from "timers";

export default {
  props: {
    cls: {
      type: String
    },
    name: {
      type: String
    },
    order: {
      type: Array,
      default: () => []
    },
    items: {
      type: Object,
      default: () => { }
    },
    menu: {
      type: Object,
      default: () => { }
    },
    storage: {
      type: Object,
      default: () => { }
    },
    sports: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      options: {},
      selectedAll: false
    };
  },
  computed: {
    lang() {
      return this.$store.getters.language;
    },
    allLive() {
      if (!this.menu) {
        return 0;
      }

      if (!this.menu) {
        return 0;
      }

      if (!this.menu["3"]) {
        return 0;
      }

      var result = 0;

      for (var n in this.order) {
        if (this.menu["3"][n]) {
          if (this.menu["3"][n]["tn"]) {
            result += this.menu["3"][n]["tn"];
          }
        }
      }

      return result;
    },
    allCount() {
      if (!this.items) {
        return 0;
      }

      if (!this.items) {
        return 0;
      }

      var result = 0;

      for (var n in this.order) {
        if (this.items[n]) {
          if (this.items[n]["tn"]) {
            result += this.items[n]["tn"];
          }
        }
      }

      return result;
    }
  },
  destroyed() {

  },
  mounted() {
    this.initial();

  },
  methods: {
    getSportsImage(e) {
      return config.getSportsImage(e);
    },
    initial() {
      var invalidate = false;
      for (var n in this.order) {
        var m = this.order[n];
        if (this.storage[m] != null && this.storage[m] != undefined) {
          this.$set(this.options, m, this.storage[m]);
        } else {
          this.$set(this.options, m, true);
          invalidate = true;
        }
      }
      this.detectAll();
      if (invalidate) {
        this.saveOptions();
      }
    },
    getLive(e) {
      if (!this.menu) {
        return 0;
      }

      if (!this.menu) {
        return 0;
      }

      if (!this.menu["3"]) {
        return 0;
      }

      if (!this.menu["3"][e]) {
        return 0;
      }

      return this.menu["3"][e]["tn"];
    },
    detectAll() {
      setTimeout(() => {
        var availItems = Object.keys(this.items);
        var allTrue = true;
        // for (var n in this.options) {
        //   if (this.options[n] == false) {
        //     allTrue = false;
        //     break;
        //   }
        // }
        for (var n in availItems) {
          if (this.options[n] == false) {
            allTrue = false;
            break;
          }
        }
        this.selectedAll = allTrue;
      }, 500);
    },
    toggleOptions(e) {
      this.options[e] = !this.options[e];
      if (!this.options[e]) {
        this.selectedAll = false;
      } else {
        this.detectAll();
      }
      this.saveOptions();
    },
    toggleAll() {
      this.selectedAll = !this.selectedAll;
      for (var n in this.options) {
        this.options[n] = this.selectedAll;
      }
      this.saveOptions();
    },
    saveOptions() {
      // console.log(this.options);
      this.$emit("save", Object.assign({}, this.options));
      EventBus.$emit("INVALIDATE");
    }
  }
};
</script>
