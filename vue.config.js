const Critters = require("critters-webpack-plugin");

module.exports = {
  devServer: {
    proxy: "http://localhost:8080",
    public: "localhost:8080",
  },
  chainWebpack: (config) => {
    // Existing i18n loader config
    config.module
      .rule("i18n")
      .resourceQuery(/blockType=i18n/)
      .type("javascript/auto")
      .use("i18n")
      .loader("@intlify/vue-i18n-loader")
      .options({
        preCompile: true,
      })
      .end();

    // Optimize images
    config.module
      .rule("images")
      .use("url-loader")
      .loader("url-loader")
      .tap((options) => Object.assign(options, { limit: 10240 })) // 10kb limit
      .end()
      .use("image-webpack-loader")
      .loader("image-webpack-loader")
      .options({
        mozjpeg: {
          progressive: true,
          quality: 75,
        },
        optipng: {
          enabled: true,
        },
        pngquant: {
          quality: [0.75, 0.9],
          speed: 4,
        },
        gifsicle: {
          interlaced: true,
        },
        webp: {
          quality: 75,
        },
      });
  },
  lintOnSave: true,
  productionSourceMap: false,
  // Asset optimization settings
  configureWebpack: {
    plugins: [...([new Critters({ preload: "swap" })])],
    optimization: {
      splitChunks: {
        chunks: "all",
        cacheGroups: {
          vendor: {
            name: "chunk-vendors",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: "initial",
          },
          common: {
            name: "chunk-common",
            minChunks: 2,
            priority: 5,
            chunks: "initial",
            reuseExistingChunk: true,
          },
        },
      },
      // Enable module concatenation for better performance
      concatenateModules: true,
      // Minimize in production
      minimize: true,
    },
    // Performance hints
    performance: {
      hints: process.env.NODE_ENV === "production" ? "warning" : false,
      maxEntrypointSize: 512000,
      maxAssetSize: 512000,
    },
  },
  // CSS optimization
  css: {
    extract: process.env.NODE_ENV === "production",
    sourceMap: false,
    loaderOptions: {},
  },
  pluginOptions: {
    quasar: {
      importStrategy: "manual",
      rtlSupport: false,
    },
  },
};
