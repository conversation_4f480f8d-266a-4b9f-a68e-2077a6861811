<template lang="pug">
.esports-gamelist(v-if="source.items != null && source.items[0] != null && source.items.length == 4")
  .esports-row.game-bottom-line(:class="source.items[0].marketId == 3 ? 'game-top-line' : ''")
    .esports-cell.esports-cell-sm.flex-grow-1.justify-content-start.w-100(v-if="source.items[0].marketId == 3")
      .live-now(style="margin-left: 16px;")
        img(src="/v1/images/esports/live-icon.svg")
        span.text-uppercase live
        i.fas.fa-circle
      .d-flex.flex-fill.justify-content-end.align-items-center(v-if="racingList.includes(source.items[0].sportsId) && source.items[0].matchTime")
        span(style="color: #fff; margin-right: 16px;") No. {{ $dayjs(this.source.items[0].matchTime).format("MMDDhhmm") }}
    .esports-cell.esports-cell-sm(v-else)
      efTime(:source="source.items[0]" :horiz="true")
  .game-group
    .game-table
      //- Red
      .game-table-left
        .bg-clash.bg-clash-yellow
          div
            img(src="/v1/images/esports/marble-clash/ball-yellow.svg")
          .px-2 {{ $t("ui.yellow") }}
        .game-table.pb-0.px-0
          .game-table-top
            .game-row
              .game-cell.w-40
              .game-cell.flex-fill
              .game-cell.game-state.w-152 {{ $t("m.GC_ML0") }}
            .game-row.mb-2
              .game-cell.game-cell-sm.w-40.justify-content-start
                img(src="/v1/images/esports/crown/no1.svg")
              .game-cell.game-cell-sm.flex-fill.justify-content-start
                .game-group
                  .game-group-top.game-group-top-1(v-html="$t('ui.no1')")
              efItemChild4(:source="source.items[2]" :isHome="true")
            .game-line
        .clash-wrapper
          div
            .game-group-top.mb-0 {{ $t("ui.best_5_balls") }}
          div
            div
              .game-row
                .game-cell.game-state.w-90 {{ $t("m.GC_OVER") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_UNDER") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_ODD") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_EVEN") }}
              efItemChild4a(:source="source.items[0]")
            .game-line.mt-2
            div
              .game-row
                .game-cell.game-state.w-90 {{ $t("m.GC_5_0") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_4_1") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_3_2") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_1X20") }}
              efItemChild4b(:source="source.items[3]" :isHome="true")
      //- Blue
      .game-table-right
        .bg-clash.bg-clash-blue
          div
            img(src="/v1/images/esports/marble-clash/ball-blue.svg")
          .px-2.text-white {{ $t("ui.blue") }}
        .game-table.pb-0.px-0
          .game-table-top
            .game-row
              .game-cell.w-40
              .game-cell.flex-fill
              .game-cell.game-state.w-152 {{ $t("m.GC_ML0") }}
            .game-row.mb-2
              .game-cell.game-cell-sm.w-40.justify-content-start
                img(src="/v1/images/esports/crown/no1.svg")
              .game-cell.game-cell-sm.flex-fill.justify-content-start
                .game-group
                  .game-group-top.game-group-top-1(v-html="$t('ui.no1')")
              efItemChild4(:source="source.items[2]" :isHome="false")
            .game-line
        .clash-wrapper
          div
            .game-group-top.mb-0 {{ $t("ui.best_5_balls") }}
          div
            div
              .game-row
                .game-cell.game-state.w-90 {{ $t("m.GC_OVER") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_UNDER") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_ODD") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_EVEN") }}
              efItemChild4a(:source="source.items[1]")
            .game-line.mt-2
            div
              .game-row
                .game-cell.game-state.w-90 {{ $t("m.GC_0_5") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_1_4") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_2_3") }}
                .game-cell.game-state.w-90 {{ $t("m.GC_1X20") }}
              efItemChild4b(:source="source.items[3]" :isHome="false")
</template>

<script>
import config from "@/config";
export default {
  components: {
    efTime: () => import("@/components/desktop/main/efTime"),
    efItemChild4: () => import("@/components/desktop/main/efItemChild4"),
    efItemChild4a: () => import("@/components/desktop/main/efItemChild4a"),
    efItemChild4b: () => import("@/components/desktop/main/efItemChild4b"),
  },
  props: {
    source: {
      type: Object,
    },
    index: {
      type: Number,
    },
  },
  computed: {
    racingList() {
      return config.racingList;
    },
  },
  methods: {},
};
</script>
