export default {
  whlmSkr6Ou: "UzdEMCkAsvRw5jYSwxetRn1Hg7VQIPOb",
  isTodayOnly: false,
  appTitle: process.env.VUE_APP_TITLE,
  appVersion: process.env.VUE_APP_VERSION,
  whiteLabel: process.env.VUE_APP_WHITE_LABEL == "1",
  baseUrl: process.env.VUE_APP_BASE_URL,
  cacheUrl: process.env.VUE_APP_CACHE_URL,
  rulesUrl: process.env.VUE_APP_RULES_URL,
  brand: process.env.VUE_APP_BRAND,
  decription: process.env.VUE_APP_DESCRIPTION,
  efsrc: process.env.VUE_APP_EFSRC,

  // Third-party games API
  ext2Url: process.env.VUE_APP_EXT2_URL,
  gpUrl: process.env.VUE_APP_EXT_URL,
  saUrl: process.env.VUE_APP_SA_URL,
  ctUrl: process.env.VUE_APP_CT_URL,
  pgUrl: process.env.VUE_APP_PG_URL,
  evoUrl: process.env.VUE_APP_EVO_URL,
  magUrl: process.env.VUE_APP_MAG_URL,
  ybUrl: process.env.VUE_APP_YB_URL,
  pgsUrl: process.env.VUE_APP_PGS_URL,
  pragmaticUrl: process.env.VUE_APP_PPG_URL,
  pragmaticCasinoUrl: process.env.VUE_APP_LCPPG_URL,
  e2Url: process.env.VUE_APP_E2_URL,
  jokerUrl: process.env.VUE_APP_JKR_URL,
  sppUrl: process.env.VUE_APP_SPP_URL,
  jiliUrl: process.env.VUE_APP_JILI_URL,
  uuslUrl: process.env.VUE_APP_UUSL_URL,
  nextUrl: process.env.VUE_APP_NEXT_URL,
  dbhashUrl: process.env.VUE_APP_DBHASH_URL,
  dbpokerUrl: process.env.VUE_APP_DBPOKER_URL,

  wowUrl: process.env.VUE_APP_WOW_URL,
  aiUrl: process.env.VUE_APP_AI_URL,
  live22Url: process.env.VUE_APP_LIVE22_URL,
  wfUrl: process.env.VUE_APP_WF_URL,
  epwUrl: process.env.VUE_APP_EPW_URL,
  awcUrl: process.env.VUE_APP_AWC_URL,
  aioUrl: process.env.VUE_APP_AIO_URL,

  pgsPath: process.env.VUE_APP_PGS_DETAIL_URL,
  pragmaticPath: process.env.VUE_APP_PPG_DETAIL_URL,
  pragmaticCasinoPath: process.env.VUE_APP_LCPPG_DETAIL_URL,
  pragmaticThumbnailPath: process.env.VUE_APP_PPG_THUMBNAIL_URL,
  jokerPath: process.env.VUE_APP_JKR_DETAIL_URL,
  sppPath: process.env.VUE_APP_SPP_DETAIL_URL,
  jiliPath: process.env.VUE_APP_JILI_DETAIL_URL,
  uuslPath: process.env.VUE_APP_UUSL_DETAIL_URL,
  nextPath: process.env.VUE_APP_NEXT_DETAIL_URL,
  dbhashPath: process.env.VUE_APP_DBHASH_DETAIL_URL,

  wowPath: process.env.VUE_APP_WOW_DETAIL_URL,
  aiPath: process.env.VUE_APP_AI_DETAIL_URL,
  live22Path: process.env.VUE_APP_LIVE22_DETAIL_URL,
  wfPath: process.env.VUE_APP_WF_DETAIL_URL,
  epwPath: process.env.VUE_APP_EPW_DETAIL_URL,
  awcPath: process.env.VUE_APP_AWC_DETAIL_URL,
  aioPath: process.env.VUE_APP_AIO_DETAIL_URL,

  arcadePath: process.env.VUE_APP_AR_DETAIL_URL,
  arcadeCert: process.env.VUE_APP_AR_DETAIL_CERT,

  // Third-party result API
  r1Url: process.env.VUE_APP_R1_URL,
  r2Url: process.env.VUE_APP_R2_URL,
  r3Url: process.env.VUE_APP_R3_URL,

  // Live stream API
  liveUrl: process.env.VUE_APP_LIVE_URL,

  // 4D games API
  w4dUrl: process.env.VUE_APP_W4D_URL,
  ipify1: process.env.VUE_APP_IPIFY1,
  ipify2: process.env.VUE_APP_IPIFY2,

  w4dSupport: process.env.VUE_APP_W4D_SUPPORT == "1",
  mmoSupport: process.env.VUE_APP_MMO_SUPPORT == "1",
  sportsradar: process.env.VUE_APP_SPORTSRADAR == "1",
  newFeatures: process.env.VUE_APP_NEW_FEATURES == "1",
  logoPath: process.env.VUE_APP_LOGO,
  defaultLang: process.env.VUE_APP_LANG,
  defaultOddsType: process.env.VUE_APP_ODDS_TYPE,
  defaultTheme: process.env.VUE_APP_THEME,
  contact: process.env.VUE_APP_CONTACT == "1",
  popup: process.env.VUE_APP_POPUP_SUPPORT == "1",
  landingPath: process.env.VUE_APP_LANDING,

  flagPath: "https://logo.myw0011001.com/",
  tournamentBackgroundPath: process.env.VUE_APP_RESOURCE + "/v1/img/tournament/",
  basePath: "",
  cachePath: "",
  extPath: "/osg",
  betPath: "/bet",
  tournamentPath: "/tournament",
  debugMode: process.env.VUE_APP_DEBUG == "true",
  tournamentInterval: 60,
  defaultInterval: 1000,
  initialInterval: 1000,
  showInterval: 50,
  parlayMaxTicket: 12, // 12
  parlayMinTicket: 2, // 2
  parlayLiveMinTicket: 3, // 3
  mmoParlayMinTicket: 2,
  mmoParlayMaxTicket: 10, // 10
  timeZone: { timeZone: "Asia/Kuala_Lumpur" },

  defaultSportOrder: [
    1, 40, 2, 7, 3, 5, 12, 4, 11, 8, 30, 9, 13, 6, 19, 35, 14, 15, 24, 34, 25, 23, 17, 31, 16, 27, 10, 18, 26, 36, 37, 32, 21, 28, 38, 29, 20, 22, 39, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54,
  ],
  defaultSportOrder1: [
    12, 1, 40, 2, 7, 3, 5, 4, 11, 8, 30, 9, 13, 6, 19, 35, 14, 15, 24, 34, 25, 23, 17, 31, 16, 27, 10, 18, 26, 36, 37, 32, 21, 28, 38, 29, 20, 22, 39, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54,
  ],
  defaultSportExclude: [22, 33],

  gameList: process.env.VUE_APP_NEW_FEATURES == "1" ? ["50", "52", "49", "51", "43", "41", "44", "42", "45", "46", "47", "53", "54"] : ["50", "52", "49", "51", "43", "41", "44", "42", "45", "46", "47"],
  ef1: ['41','47','52','53'],
  day1: [1, 20, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54],
  special: [40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54],
  vg1: [41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54],
  vg2: ["41", "42", "43", "44", "45", "46", "47", "49", "50", "51", "52", "53", "54"],
  efightList: [41, 42, 43, 44, 47, 52, 53, 54], // Fighter games
  racingList: [45, 46, 49, 50, 51], //- Racing games
  racingList1: [45, 46, 49], //- Greyhound game like result
  racingList2: [50], //- Marble Survival game like result
  racingList3: [51], //- Marble Clash game like result
  racingList4: [54], //- Marble Rafting game like result
  params: {
    54: {
      delay: 2,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/marble-rafting-thumb.jpg",
    },
    53: {
      delay: 1,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/naruto-thumb.jpg",
    },
    52: {
      delay: 0,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/dragonball-thumb.jpg",
    },
    51: {
      delay: 4,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/marble-survival-thumb.jpg",
    },
    50: {
      delay: 1,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/marble-clash-thumb.jpg",
    },
    49: {
      delay: 0,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/marble-thumb.jpg",
    },
    47: {
      delay: 2,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/tk8-thumb.jpg",
    },
    46: {
      delay: 3,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/speedway-thumb.jpg",
    },
    45: {
      delay: 2,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/greyhound-thumb.jpg",
    },
    44: {
      delay: 4,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/mk-thumb.jpg",
    },
    43: {
      delay: 0,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/kof-thumb.jpg",
    },
    42: {
      delay: 1,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/ufc-thumb.jpg",
    },
    41: {
      delay: 3,
      duration: 5,
      live: 1,
      thumbnail: "images/esports/game-thumb/sf6-thumb.jpg",
    },
  },
  /*
  41: SF4
  42: UFC3
  43: KOF
  44: MK
  45: GREYHOUND
  46: MOTOR
  47: TEKKEN 8
  49: MARBLE RUN
  50: MARBLE CLASH
  51: MARBLE SURVIVAL
  52: DRAGON BALL
  53: NARUTO
  54: MARBLE RAFTING
  55: NOT IN USED
  */
  gameStream: {
    41: "vDYS8-n3Zn1", // SF4
    42: "vDYS8-t1rfZ", // UFC3
    43: "vDYS8-N7FsO", // KOF
    44: "vDYS8-vbyfY", // MK
    45: "vDYS8-UgMqO", // GREYHOUND
    46: "vDYS8-AXaJR", // MOTOR
    47: "vDYS8-wZpoI", // TEKKEN 8
    49: "vDYS8-lTgN2", // MARBLE RUN
    50: "vDYS8-ixFkb", // MARBLE CLASH
    51: "vDYS8-eemJ4", // MARBLE SURVIVAL
    52: "vDYS8-0tGgf", // DRAGON BALL
    53: "vDYS8-v2kwn", // NARUTO
    54: "vDYS8-3Mnd3", // MARBLE RAFTING
    55: "vDYS8-MeVYp", // NOT IN USED

    141: "vDYS8-n3Zn1", // SF4
    142: "vDYS8-t1rfZ", // UFC3
    143: "vDYS8-N7FsO", // KOF
    144: "vDYS8-vbyfY", // MK
    145: "vDYS8-UgMqO", // GREYHOUND
    146: "vDYS8-AXaJR", // MOTOR
    147: "vDYS8-wZpoI", // TEKKEN 8
    149: "vDYS8-lTgN2", // MARBLE RUN
    150: "vDYS8-ixFkb", // MARBLE CLASH
    151: "vDYS8-eemJ4", // MARBLE SURVIVAL
    152: "vDYS8-0tGgf", // DRAGON BALL
    153: "vDYS8-v2kwn", // NARUTO
    154: "vDYS8-3Mnd3", // MARBLE RAFTING
    155: "vDYS8-MeVYp", // NOT IN USED
  },
  moreBetTypes: "tw|cs|dc|fglg|htft|tg|orz|anb|bs|cl|dnb|hnb|twtn|ml|oehm|oeaw|oxtou|dcou|ouoe|etg|ehtg|eatg|wm|htftoe|cshtft|etghtft",
  currencyList: ["CNY", "IDR", "THB", "MYR", "USD", "SGD", "MMK"],
  oddsTypeLocale: {
    1: "ui.my_odds",
    2: "ui.hk_odds",
    3: "ui.id_odds",
    4: "ui.dec_odds",
    5: "ui.mm_odds",
  },
  oddsTypeName: {
    1: "MY",
    2: "HK",
    3: "ID",
    4: "DEC",
  },
  oddsTypeId: {
    MY: 1,
    HK: 2,
    ID: 3,
    DEC: 4,
  },
  matchStatusName: {
    0: "ui.match_pending",
    1: "ui.match_completed",
    2: "ui.match_cancelled",
    5: "ui.match_suspended",
    500: "-",
  },
  matchStatusId: {
    Live: 0,
    Completed: 1,
    Cancelled: 2,
    Suspended: 5,
  },
  marking: ["HDP", "HDPH", "OU", "OUH", "OE", "OEH", "OEAW", "OEAWH", "OEHM", "OEHMH"],
  markets: ["close", "early", "today", "live", "parlay"],
  languageAvailable: [
    { lang: "English", id: "en" },
    { lang: "简体中文", id: "cn" },
    { lang: "កម្ពុជា", id: "tw" },
    { lang: "ဗမာ", id: "my" },
    { lang: "Bahasa Indonesia", id: "id" },
    { lang: "日本語", id: "jp" },
    { lang: "한국인", id: "kr" },
    { lang: "ไทย", id: "th" },
    { lang: "Tiếng Việt", id: "vi" },
  ],
  esportsTab: {
    101: "ui.map1",
    102: "ui.map2",
    103: "ui.map3",
    104: "ui.map4",
    105: "ui.map5",
    106: "ui.map6",
    107: "ui.map7",
    108: "ui.map8",
    109: "ui.map9",
    110: "ui.map10",
  },
  sports4dTab: {
    101: "ui.prize1",
    102: "ui.prize2",
    103: "ui.prize3",
  },
  sportsSFVTab: {
    101: "ui.round1",
    102: "ui.round2",
    103: "ui.round3",
    104: "ui.round4",
    105: "ui.round5",
  },

  // CSHTFT: {
  //   0: "0-0",
  //   1: "0-1",
  //   2: "0-2",
  //   3: "0-3",
  //   4: "0-4",
  //   5: "1-0",
  //   6: "1-1",
  //   7: "1-2",
  //   8: "1-3",
  //   9: "1-4",
  //   10: "2-0",
  //   11: "2-1",
  //   12: "2-2",
  //   13: "2-3",
  //   14: "2-4",
  //   15: "3-0",
  //   16: "3-1",
  //   17: "3-2",
  //   18: "3-3",
  //   19: "3-4",
  //   20: "4-0",
  //   21: "4-1",
  //   22: "4-2",
  //   23: "4-3",
  //   24: "4-4",
  //   25: "AOS",
  // },
  CSHTFTX: ["0-0", "0-1", "0-2", "0-3", "0-4", "1-0", "1-1", "1-2", "1-3", "1-4", "2-0", "2-1", "2-2", "2-3", "2-4", "3-0", "3-1", "3-2", "3-3", "3-4", "4-0", "4-1", "4-2", "4-3", "4-4", "AOS"],
  // ETGHTFT: {
  //   0: "0",
  //   1: "1",
  //   2: "2",
  //   3: "3",
  //   4: "4",
  //   5: "5",
  //   6: "6",
  //   7: "7",
  //   8: "7+",
  //   9: "8",
  //   10: "8+",
  //   11: "9",
  //   12: "9+",
  //   13: "10+",
  // },
  ETGHTFTX: ["0", "1", "2", "3", "4", "5", "6", "7", "7+", "8", "8+", "9", "9+", "10+"],
  matchPerPage: 30,
  OT_MAX_ITEMS: 4,

  useConfigUrl: process.env.VUE_APP_STATIC_URL == "true",
  resourceUrl: process.env.VUE_APP_RESOURCE,
  getLandingUrl(fn) {
    return process.env.VUE_APP_RESOURCE + "/v1/images/" + this.landingPath + "/" + fn + "?v=4";
  },
  getDomain(url, subdomain) {
    subdomain = subdomain || false;

    url = url.replace(/(https?:\/\/)?(www.)?/i, "");

    if (!subdomain) {
      url = url.split(".");

      url = url.slice(url.length - 2).join(".");
    }

    if (url.indexOf("/") !== -1) {
      return url.split("/")[0];
    }

    return url;
  },
  getBaseUrl() {
    if (this.useConfigUrl) {
      return this.baseUrl;
    }
    var domain = this.getDomain(window.location.hostname, false);
    if (domain != "localhost") {
      return "https://app." + domain;
    } else {
      return this.baseUrl;
    }
  },
  getCacheUrl() {
    if (this.useConfigUrl) {
      return this.cacheUrl;
    }
    var domain = this.getDomain(window.location.hostname, false);
    if (domain != "localhost") {
      return "https://odds." + domain;
    } else {
      return this.cacheUrl;
    }
  },
  getMobileUrl() {
    var domain = this.getDomain(window.location.hostname, false);
    if (domain != "localhost") {
      return "https://m." + domain;
    } else {
      return window.location.origin;
    }
  },
  getSportsImage(e) {
    var result = e + ".svg";
    return result;
  },
  radarUrl() {
    // Sportsradar
    return this.liveUrl + "/sportsradar?m=";
  },
  scoreboardUrl() {
    // Sportsradar
    return this.liveUrl + "/scoreboard?m=";
  },
  pitchUrl() {
    // Sportsradar
    return this.liveUrl + "/pitch?m=";
  },
  statscenterUrl() {
    // Sportsradar
    if (this.sportsradar) {
      return this.liveUrl + "/statscenter?m=";
    } else {
      return this.liveUrl + "/football?m=";
    }
  },
  bintuUrl() {
    return this.liveUrl + "/bintu?v=";
  },
  player10Url() {
    return this.liveUrl + "/player10?v=";
  },
  r3ResultUrl() {
    // Live22
    return this.r3Url + "/result?bet_id=";
  },
  r2ResultUrl() {
    // GamingSoft
    return this.r2Url + "/result?bet_id=";
  },
  r1ResultUrl() {
    // All casino
    return this.r1Url + "/result?";
  },
  efResultUrl() {
    return this.getBaseUrl() + this.basePath + "/efresult";
  },
  ngResultUrl() {
    return this.getBaseUrl() + this.basePath + "/ngresult";
  },
  prgUrl() {
    return this.getBaseUrl() + this.basePath + "/prg";
  },
  apiTvUrl() {
    return this.getBaseUrl() + this.basePath + "/apitv";
  },
  liveTvUrl() {
    return this.getBaseUrl() + this.basePath + "/livetv2";
  },
  liveTvListUrl() {
    return this.getBaseUrl() + this.basePath + "/livetvlist";
  },
  liveRadarListUrl() {
    return this.getBaseUrl() + this.basePath + "/radarlist";
  },
  liveMatchListUrl() {
    return this.getBaseUrl() + this.basePath + "/livematchlist";
  },
  highlightLeagueUrl() {
    return this.getBaseUrl() + this.basePath + "/highlightleague";
  },
  highlightListUrl() {
    return this.getBaseUrl() + this.basePath + "/highlightlist";
  },
  getLogoUrl() {
    return this.getBaseUrl() + this.basePath + "/getlogo";
  },
  eventListUrl() {
    return this.getBaseUrl() + this.basePath + "/eventlist";
  },
  standingListUrl() {
    return this.getBaseUrl() + this.basePath + "/standinglist";
  },
  standingResultUrl() {
    return this.getBaseUrl() + this.basePath + "/standingresult";
  },
  bracketListUrl() {
    return this.getBaseUrl() + this.basePath + "/bracketlist";
  },
  versionUrl() {
    return this.getBaseUrl() + this.basePath + "/version";
  },
  authLoginUrl() {
    return this.getBaseUrl() + this.basePath + "/authlogin";
  },
  switchLoginUrl() {
    return this.getBaseUrl() + this.basePath + "/switchlogin";
  },
  loginUrl() {
    return this.getBaseUrl() + this.basePath + "/userlogin";
  },
  secureLoginUrl() {
    return this.getBaseUrl() + this.basePath + "/securelogin";
  },
  getBalanceUrl() {
    return this.getBaseUrl() + this.basePath + "/userbalance";
  },
  getVBalanceUrl() {
    return this.getBaseUrl() + this.basePath + "/vuserbalance";
  },
  getProfileUrl() {
    return this.getBaseUrl() + this.basePath + "/userprofile";
  },
  getMenuUrl() {
    return this.getCacheUrl() + this.cachePath + "/uxmenu";
  },
  getMarketUrl() {
    return this.getCacheUrl() + this.cachePath + "/uxmarket";
  },
  getMatchUrl() {
    return this.getCacheUrl() + this.cachePath + "/uxmatch";
  },
  getMatch2Url() {
    return this.getCacheUrl() + this.cachePath + "/uxmatch2";
  },
  changePasswordUrl() {
    return this.getBaseUrl() + this.basePath + "/changepassword";
  },
  addSecondaryUrl() {
    return this.getBaseUrl() + this.basePath + "/addsecondary";
  },
  setSettingsUrl() {
    return this.getBaseUrl() + this.basePath + "/usersettings";
  },
  dailyStatementUrl() {
    return this.getBaseUrl() + this.basePath + "/dailystatement";
  },
  platformWinloseUrl() {
    return this.getBaseUrl() + this.basePath + "/platformwinlose";
  },
  settlementUrl() {
    return this.getBaseUrl() + this.basePath + "/settlementhistory";
  },
  gameResultTournamentUrl() {
    return this.getBaseUrl() + this.basePath + "/gameresulttournament";
  },
  gameResultTournamentRefundUrl() {
    return this.getBaseUrl() + this.basePath + "/gameresulttournamentrefund";
  },
  memberValidateTournamentUrl() {
    return this.getBaseUrl() + this.basePath + "/membervalidatetournament";
  },
  gameResultSportsbookUrl() {
    return this.getBaseUrl() + this.basePath + "/gameresultsportsbook";
  },
  gameResultAdjustmentUrl() {
    return this.getBaseUrl() + this.basePath + "/gameresultadjustment";
  },
  gameResultEsports2Url() {
    return this.getBaseUrl() + this.basePath + "/gameresultesports2";
  },
  gameResultEsports2DetailUrl() {
    return this.getBaseUrl() + this.basePath + "/gameresultesports2detail";
  },
  gameResultSlotsUrl() {
    return this.getBaseUrl() + this.basePath + "/gameresultslots";
  },
  gameResultLiveCasinoUrl() {
    return this.getBaseUrl() + this.basePath + "/gameresultlivecasino";
  },
  gameResultLotteryUrl() {
    return this.getBaseUrl() + this.basePath + "/gameresultlottery";
  },
  cancelledBetListUrl() {
    return this.getBaseUrl() + this.basePath + "/cancelledbetlist";
  },
  cancelledBetListLotteryUrl() {
    return this.getBaseUrl() + this.basePath + "/cancelledbetlistlottery";
  },
  mmoSingleOddCheckUrl() {
    return this.getBaseUrl() + this.betPath + "/betmmoclick";
  },
  mmoSingleUrl() {
    return this.getBaseUrl() + this.betPath + "/betsinglemmo";
  },
  mmoParlayUrl() {
    return this.getBaseUrl() + this.betPath + "/betparlaymmo";
  },
  vMMOSingleUrl() {
    return this.getBaseUrl() + this.betPath + "/vbetsinglemmo";
  },
  vMMOParlayUrl() {
    return this.getBaseUrl() + this.betPath + "/vbetparlaymmo";
  },
  betSingleOddCheckUrl() {
    return this.getBaseUrl() + this.betPath + "/betcheck";
  },
  betSingleOddCheckSpecialUrl() {
    return this.getBaseUrl() + this.betPath + "/betcheckspecial";
  },
  betMultiOddCheckUrl() {
    return this.getBaseUrl() + this.betPath + "/betcheckmultispecial";
  },
  betSingleUrl() {
    return this.getBaseUrl() + this.betPath + "/betsingle";
  },
  betSingleSpecialUrl() {
    return this.getBaseUrl() + this.betPath + "/betsinglespecial";
  },
  betParlayUrl() {
    return this.getBaseUrl() + this.betPath + "/betparlay";
  },
  betSystemParlayUrl() {
    return this.getBaseUrl() + this.betPath + "/betsysparlay";
  },
  vBetSystemParlayUrl() {
    return this.getBaseUrl() + this.betPath + "/vbetsysparlay";
  },
  vBetSingleUrl() {
    return this.getBaseUrl() + this.betPath + "/vbetsingle";
  },
  vBetSingleSpecialUrl() {
    return this.getBaseUrl() + this.betPath + "/vbetsinglespecial";
  },
  vBetParlayUrl() {
    return this.getBaseUrl() + this.betPath + "/vbetparlay";
  },
  betAcceptListUrl() {
    return this.getBaseUrl() + this.betPath + "/betacceptlist";
  },
  betRejectListUrl() {
    return this.getBaseUrl() + this.betPath + "/betrejectlist";
  },
  betPendingListUrl() {
    return this.getBaseUrl() + this.betPath + "/pendingbetlist";
  },
  betAcceptListAllUrl() {
    return this.getBaseUrl() + this.betPath + "/betacceptfulllist";
  },
  betRejectListAllUrl() {
    return this.getBaseUrl() + this.betPath + "/betrejectfulllist";
  },

  dBetAcceptListUrl() {
    return this.getBaseUrl() + this.betPath + "/dbetlist";
  },
  dBetRejectListUrl() {
    return this.getBaseUrl() + this.betPath + "/dbetlistcancel";
  },

  parlayAcceptDetailsUrl() {
    return this.getBaseUrl() + this.betPath + "/parlayacceptbetdetails";
  },
  parlayPendingDetailsUrl() {
    return this.getBaseUrl() + this.betPath + "/parlaypendingbetdetails";
  },
  parlayRejectDetailsUrl() {
    return this.getBaseUrl() + this.betPath + "/parlayrejectbetdetails";
  },
  announceListUrl() {
    return this.getBaseUrl() + this.basePath + "/announcelist";
  },
  marqueeListUrl() {
    return this.getBaseUrl() + this.basePath + "/marqueelist";
  },
  personalListUrl() {
    return this.getBaseUrl() + this.basePath + "/personallist";
  },
  personalUnreadUrl() {
    return this.getBaseUrl() + this.basePath + "/personalunread";
  },
  matchResultUrl() {
    return this.getBaseUrl() + this.basePath + "/matchresult";
  },
  singleMatchResultUrl() {
    return this.getBaseUrl() + this.basePath + "/singlematchresult";
  },
  parlayMatchResultUrl() {
    return this.getBaseUrl() + this.basePath + "/parlaymatchresult";
  },
  statementParlayDetailsUrl() {
    return this.getBaseUrl() + this.basePath + "/parlaybetdetails";
  },
  gameResultSummaryUrl() {
    return this.getBaseUrl() + this.basePath + "/gameresultsummarysportsbook";
  },
  reportBetParlayUrl() {
    return this.getBaseUrl() + this.basePath + "/betsysparlay";
  },
  reportBetParlayMatchUrl() {
    return this.getBaseUrl() + this.basePath + "/betsysparlaymatch";
  },
  reportBetParlayBetUrl() {
    return this.getBaseUrl() + this.basePath + "/betsysparlaybet";
  },
  reportBetSystemParlayUrl() {
    return this.getBaseUrl() + this.basePath + "/betsysparlay";
  },
  reportBetSystemParlayMatchUrl() {
    return this.getBaseUrl() + this.basePath + "/betsysparlaymatch";
  },
  reportBetSystemParlayBetUrl() {
    return this.getBaseUrl() + this.basePath + "/betsysparlaybet";
  },
  tournamentUrl() {
    return {
      roomlist: this.getBaseUrl() + this.tournamentPath + "/roomlist",
      roomleaguelist: this.getBaseUrl() + this.tournamentPath + "/roomleaguelist",
      roommatchlist: this.getBaseUrl() + this.tournamentPath + "/roommatchlist",
      matchlookup: this.getBaseUrl() + this.tournamentPath + "/matchlookup",
      roomcreate: this.getBaseUrl() + this.tournamentPath + "/roomcreate",
      roomjoin: this.getBaseUrl() + this.tournamentPath + "/roomjoin",
      vroomcreate: this.getBaseUrl() + this.tournamentPath + "/vroomcreate",
      vroomjoin: this.getBaseUrl() + this.tournamentPath + "/vroomjoin",
      memberbetlist: this.getBaseUrl() + this.tournamentPath + "/memberbetlist",
      betresultlist: this.getBaseUrl() + this.tournamentPath + "/betresultlist",
      betresultfinal: this.getBaseUrl() + this.tournamentPath + "/betresultfinal",
      bet: this.getBaseUrl() + this.tournamentPath + "/bet",
      betplus: this.getBaseUrl() + this.tournamentPath + "/betplus",
      roomrank: this.getBaseUrl() + this.tournamentPath + "/roomrank",
      roominfo: this.getBaseUrl() + this.tournamentPath + "/roominfo",
    };
  },
  launchGSUrl() {
    return this.ext2Url + this.extPath + "/launchslotgame";
  },
  launchL22Url() {
    return this.ext2Url + this.extPath + "/launchslotgame";
  },
  launchGPUrl() {
    return this.gpUrl + this.extPath + "/launchgp";
  },
  launchSAUrl() {
    // return this.saUrl + "/LoginRequest";
    return this.saUrl + "/LaunchGame";
  },
  launchCTUrl() {
    return this.ctUrl + "/LaunchGame";
  },
  launchPGUrl() {
    return this.pgUrl + "/LaunchGame";
  },
  launchEvoUrl() {
    return this.evoUrl + "/LaunchGame";
  },
  launchMagUrl() {
    return this.magUrl + "/LaunchGame";
  },
  launchYbUrl() {
    return this.ybUrl + "/LaunchGame";
  },
  launchPgsUrl() {
    return this.pgsUrl + "/LaunchGame";
  },
  launchPragmaticUrl() {
    return this.pragmaticUrl + "/LaunchGame";
  },
  launchPragmaticCasinoUrl() {
    return this.pragmaticCasinoUrl + "/LaunchGame";
  },
  getPragmaticGameListUrl() {
    return this.pragmaticUrl + "/GameList";
  },
  launchJokerUrl() {
    return this.jokerUrl + "/LaunchGame";
  },
  getJokerGameListUrl() {
    return this.jokerUrl + "/GameList";
  },
  launchEsports2Url() {
    return this.e2Url + "/LaunchGame";
  },
  launchW4DUrl() {
    return this.w4dUrl + "/LaunchLottery";
  },
  launchSimplePlayUrl() {
    return this.sppUrl + "/LaunchGame";
  },
  launchJiliUrl() {
    return this.jiliUrl + "/LaunchGame";
  },
  getJiliGameListUrl() {
    return this.jiliUrl + "/GameList";
  },
  launchUuslUrl() {
    return this.uuslUrl + "/LaunchGame";
  },
  getUuslGameListUrl() {
    return this.uuslUrl + "/GameList";
  },
  launchNextSpinUrl() {
    return this.nextUrl + "/LaunchGame";
  },
  launchDbHashUrl() {
    return this.dbhashUrl + "/LaunchGame";
  },
  launchDbPokerUrl() {
    return this.dbpokerUrl + "/LaunchGame";
  },
  launchWowGamingUrl() {
    return this.wowUrl + "/LaunchGame";
  },
  launchAICasinoUrl() {
    return this.aiUrl + "/LaunchGame";
  },
  launchLive22Url() {
    return this.live22Url + "/LaunchGame";
  },
  getLive22GameListUrl() {
    return this.live22Url + "/GameList";
  },
  launchWfUrl() {
    return this.wfUrl + "/LaunchGame";
  },
  getWfGameListUrl() {
    return this.wfUrl + "/GameList";
  },
  launchEpwUrl() {
    return this.epwUrl + "/LaunchGame";
  },
  getEpwGameListUrl() {
    return this.epwUrl + "/GameList";
  },
  launchAwcUrl() {
    return this.awcUrl + "/LaunchGame";
  },
  launchAioUrl() {
    return this.aioUrl + "/LaunchGame";
  },
  getAwcGameListUrl() {
    return this.awcUrl + "/GameList";
  },

  // Utility function to check if a game provider URL exists
  isGameProviderAvailable(urlName) {
    const url = this[urlName];
    return url && url.trim() !== '';
  },
};
