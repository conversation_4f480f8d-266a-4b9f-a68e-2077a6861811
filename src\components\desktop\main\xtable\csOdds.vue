<template lang="pug">
  .hx-main.csOdds
    .hx-table.hx-match.hx-compact(:class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }")
      .hx-cell.w-62
        .hx-row.h-100.hx-rows
          timePanel(:source="source")
      .hx-cell.flex-fill
        .hx-row.h-100.hx-rows
          xTeam(:source="source" isDraw=false cls="w-133")
          xFavorite(:source="source")
      template(v-if="pageType != '4'")
        .hx-cell.w-390
          .hx-row
            .hx-col.hx-cols.w-39(v-for="item in cs1")
              csItem(:details="details" :oddsType="oddsType" betType="cso" :item="item")
          .hx-row
            .hx-col.hx-cols.w-39(v-for="item in cs2")
              csItem(:details="details" :oddsType="oddsType" betType="cso" :item="item")
        .hx-cell.w-234
          .hx-row.h-100
            .hx-col.hx-cols.w-39.h-100(v-for="item in cs3")
              csItem(:details="details" :oddsType="oddsType" betType="cso" :item="item")
      template(v-if="pageType == '4'")
        .hx-cell.w-234
          .hx-row
            .hx-col.hx-cols.w-39(v-for="item in csh1")
              csItem(:details="details" :oddsType="oddsType" betType="csho" :item="item")
          .hx-row
            .hx-col.hx-cols.w-39(v-for="item in csh2")
              csItem(:details="details" :oddsType="oddsType" betType="csho" :item="item")
        .hx-cell.w-195
          .hx-row.h-100
            .hx-col.hx-cols.w-39.h-100(v-for="item in csh3")
              csItem(:details="details" :oddsType="oddsType" betType="csho" :item="item")
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";
// import timePanel from "@/components/desktop/main/xtable/timePanel";
// import xTeam from "@/components/desktop/main/xtable/xitem/xTeam";
// import xFavorite from "@/components/desktop/main/xtable/xitem/xFavorite";
// import csItem from "@/components/desktop/main/xtable/xitem/csItem";

export default {
  components: {
    // timePanel,
    // xTeam,
    // xFavorite,
    // csItem


    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    xTeam: () => import("@/components/desktop/main/xtable/xitem/xTeam"),
    xFavorite: () => import("@/components/desktop/main/xtable/xitem/xFavorite"),
    csItem: () => import("@/components/desktop/main/xtable/xitem/csItem")
  },
  mixins: [mixinHDPOUOdds],
  data() {
    return {
      cs1: ['1-0', '2-0', '2-1', '3-0', '3-1', '3-2', '4-0', '4-1', '4-2', '4-3'],
      cs2: ['0-1', '0-2', '1-2', '0-3', '1-3', '2-3', '0-4', '1-4', '2-4', '3-4'],
      cs3: ['0-0', '1-1', '2-2', '3-3', '4-4', 'AOS'],
      csh1: ['1-0', '2-0', '2-1', '3-0', '3-1', '3-2'],
      csh2: ['0-1', '0-2', '1-2', '0-3', '1-3', '2-3'],
      csh3: ['0-0', '1-1', '2-2', '3-3', 'AOS'],
    };
  },
  computed: {
    pageType() {
      return this.$store.getters.pageDisplay.pageType;
    }
  },
  // updated: function() {
  //   this.$nextTick(function() {
  //     console.log("odds", new Date(), this.source.homeTeam);
  //   });
  // },
  mounted() {
  },
};
</script>
