<template lang="pug">
.tournament-room-join
  .tournament-pool-single
    .tournament-pool#tournament-top-anchor
      .tournament-pool-top
        .tournament-background(v-if="roomData.league_logo && roomData.room_type != 2")
          img(:src="getBackground(roomData.league_logo)")
        .tournament-pool-amount
          .tournament-text.d-flex.align-items-center.justify-content-start
            span {{ $t('ui.pool') }}:&nbsp;
            .tournament-color-green {{ $numeral(roomData.pool).format("0,0") }}
            .tournament-jackpot.ml-2(v-if="roomData.jackpot > 0")
              i.fad.fa-chevron-double-up
              span {{ $numeral(roomData.jackpot * 100).format("0,0") }}%
          .tournament-pool-number {{ $t('ui.room') }}:&nbsp;
            span.tournament-color-white(v-if="roomData.room_id") {{ roomData.room_id }}

        a.tournament-pool-fee(v-if="canJoin && (roomData.room_count < roomData.room_limit)")
          .tournament-pool-people
            i.fad.fa-users
            span &nbsp;{{ $t('ui.players') }}:&nbsp;
            span &nbsp;{{ roomData.room_count }} / {{ roomData.room_limit }}&nbsp;
          .tournament-pool-entry(v-if="roomData.room_rate > 0")
            span {{ $t('ui.entry_fee') }}:&nbsp;
            span.tournament-color-green {{ $numeral(roomData.room_rate).format("0,0") }}&nbsp;&nbsp;
            small(style="color: #08ff00cc;") (&nbsp;{{ currency_code }}&nbsp;{{ $numeral(roomData.room_rate / roomData.rate).format("0,0.00")  }}&nbsp;)
          .tournament-pool-entry(v-else)
            span.tournament-color-green {{ $t('ui.free_room') }}
          .tournament-trophy(v-if="(roomData.position != null) && ([1,2,3].includes(roomData.position))")
            img(:src="'img/tn/trophy' + roomData.position.toString() + '.svg'")

        a.tournament-pool-fee(v-else)
          .tournament-pool-people(v-if="roomData.room_count < roomData.room_limit")
            i.fad.fa-users
            span &nbsp;{{ $t('ui.players') }}:&nbsp;
            span &nbsp;{{ roomData.room_count }} / {{ roomData.room_limit }}&nbsp;
          .tournament-pool-people(v-else) {{ $t('message.room_full') }}
          .tournament-pool-entry(v-if="canBet && roomData.room_join === 1")
            template(v-if="roomData.end_time")
              span {{ $t('ui.last_betting_time') }}:&nbsp;
              span.tournament-color-green(:data-tick="ticker") {{ getCountDown($dayjs(roomData.end_time).subtract(1, 'minute').format("YYYY-MM-DDTHH:mm:ss")) }}
          .tournament-pool-entry(v-else)
            span.tournament-color-green(v-if="roomData.room_status") {{ $t('m.ROOM_' + roomData.room_status) }}

        .tournament-pool-details
          button.tournament-pool-status.room_3.tournament-button-hover.mr-1(@click="leaveRoom" style="width: 150px;")
            i.fad.fa-sign-out.mr-2
            span {{ $t("ui.leave_room") }}
          template(v-if="canJoin")
            button.tournament-pool-status.room_0.tournament-button-hover.mr-2(@click="debouncePush" :disabled="loading.getMatch")
              template(v-if="loading.getMatch")
                i.fad.fa-spinner-third.fa-spin
              template(v-else)
                i.fad.fa-redo.mr-2
                span {{ counter }}s
          template(v-else)
            button.tournament-pool-status.room_0.tournament-button-hover.mr-2(@click="debouncePush" :disabled="loading.getBetResultList")
              template(v-if="loading.getBetResultList")
                i.fad.fa-spinner-third.fa-spin
              template(v-else)
                i.fad.fa-redo.mr-2
                span {{ counter }}s

      .tournament-point
        .tournament-point-top {{ $t("ui.total_points") }}
        template(v-if="roomData.total_points != null")
          .tournament-point-bottom {{ $numeral(roomData.total_points).format("0,0.00") }}
        template(v-else)
          .tournament-point-bottom -
        .tournament-menu
          ul
            li#v-step-4(:class="listMode == 0 ? 'active' : ''")
              a.pointer(@click="listMode = 0") {{ $t("ui.details") }}
            li#v-step-5(:class="listMode == 1 ? 'active' : ''")
              a.pointer(@click="listMode = 1")
                span(v-if="canBet") {{ $t("ui.matches") }}
                span(v-else) {{ $t("ui.bet_list") }}
            li#v-step-6(:class="listMode == 2 ? 'active' : ''")
              a.pointer(@click="listMode = 2")
                span(v-if="isRanking") {{ $t("ui.ranking") }}
                span(v-else)  {{ $t("ui.players") }}

      //- details
      template(v-if="listMode == 0")
        roomGameDetails(:roomData="roomData")

      //- matches / my bet
      template(v-if="listMode == 1")
        //- matches
        .tournament-pool-body#accordion-tournament(v-if="canBet")
          .tournament-collapse(v-if="(roomData.total_match != null) && (roomData.total_match > 0)")
            .collapse.show(:id="'room-' + roomData.room_id")
              template(v-if="leagueList != null && leagueList.hasOwnProperty(roomData.room_id) && canBet")
                .tournament-league-single(v-for="ln in leagueList[roomData.room_id]")
                  .tournament-league-list.collapsed(
                    :id="'heading-' + roomData.room_id + '-league-' + ln.league_id"
                    data-toggle="collapse"
                    :data-target="'#room-' + roomData.room_id + '-league-' + ln.league_id"
                    @click="selectedLeague = '#heading-' + roomData.room_id + '-league-' + ln.league_id"
                    )
                    .tournament-league-header
                      .tournament-league-club
                        img(v-if="ln.logo" :src="getLogo('league', ln.logo)")
                      .tournament-league-type.flex-grow-1 {{ getName('name_', ln) }}
                      .tournament-arrow
                        i.fas.fa-chevron-up.ml-2
                  .collapse(
                    :id="'room-' + roomData.room_id + '-league-' + ln.league_id"
                    :aria-labelledby="'heading-' + roomData.room_id + '-league-' + ln.league_id"
                    data-parent="#accordion-tournament"
                    )
                    template(v-if="matchList.hasOwnProperty(roomData.room_id)")
                      template(v-if="matchList[roomData.room_id].hasOwnProperty(ln.league_id)")
                        .room-wrapper
                          .room-row.room-h33.room-bb
                            .room-col.room-w-teams.room-h33.room-br &nbsp;
                            .room-col.room-w-odds.room-h33.room-text.room-br.d-flex {{ $t('m.BT_HDP') }}
                            .room-col.room-w-odds.room-h33.room-text.d-flex {{ $t('m.BT_OU') }}
                          template(v-for="match in matchList[roomData.room_id][ln.league_id]")
                            .room-row.room-h95.room-bb
                              .room-col.room-w-teams.room-h95.room-br
                                .room-date.room-w80.room-h70.room-br(v-if="match.match_time != null")
                                  .room-date-date {{ getMatchDate(match.match_time) }}
                                  .room-date-time {{ getMatchTime(match.match_time) }}
                                .room-teams
                                  template(v-if="odds[match.match_id] && odds[match.match_id]['hdp'] && odds[match.match_id]['hdp'][9] != 0 && odds[match.match_id]['hdp'][10] != 0 && odds[match.match_id]['hdp'][9] != '' && odds[match.match_id]['hdp'][10] != ''")
                                    .tournament-league-team
                                      .tournament-league-team-name(:class="odds[match.match_id]['hdp'][7] == 1 ? 'tournament-home' : ''")
                                        span {{ getName('home_name_', match) }}
                                    .tournament-league-team
                                      .tournament-league-team-name(:class="odds[match.match_id]['hdp'][7] == 0 ? 'tournament-home' : ''")
                                        span {{ getName('away_name_', match) }}
                                  template(v-else)
                                    .tournament-league-team
                                      .tournament-league-team-name
                                        span {{ getName('home_name_', match) }}
                                    .tournament-league-team
                                      .tournament-league-team-name
                                        span {{ getName('away_name_', match) }}
                              template(v-if="canBet")
                                .room-col.room-w-odds.room-h95.d-flex.room-br
                                  .room-odds-col.room-w100(v-if="odds[match.match_id] && odds[match.match_id]['hdp'] && odds[match.match_id]['hdp'][9] != 0 && odds[match.match_id]['hdp'][10] != 0 && odds[match.match_id]['hdp'][9] != '' && odds[match.match_id]['hdp'][10] != ''")
                                    .room-odds
                                      .room-ball-value.room-w40
                                        span(v-if="odds[match.match_id]['hdp'][7] == 1") {{ odds[match.match_id]['hdp'][8] }}
                                      .room-bet-value.room-w40r
                                        roomOdds(
                                          :odds="odds[match.match_id]['hdp']"
                                          :idx="odds[match.match_id]['hdp'][7] == 1 ? '10' : '9'"
                                          :typ="oddsType"
                                          dataType="1"
                                          :roomData="roomData"
                                          :match="match"
                                          )
                                    .room-odds
                                      .room-ball-value.room-w40
                                        span(v-if="odds[match.match_id]['hdp'][7] == 0") {{ odds[match.match_id]['hdp'][8] }}
                                      .room-bet-value.room-w40r
                                        roomOdds(
                                          :odds="odds[match.match_id]['hdp']"
                                          :idx="odds[match.match_id]['hdp'][7] == 1 ? '9' : '10'"
                                          :typ="oddsType"
                                          dataType="1"
                                          :roomData="roomData"
                                          :match="match"
                                          )
                                  .room-odds-col.room-w100(v-else)
                                    .room-odds
                                      .room-ball-value.room-w40 -
                                      .room-bet-value.room-w40r.room-value-lock
                                        i.fas.fa-lock
                                    .room-odds
                                      .room-ball-value.room-w40 -
                                      .room-bet-value.room-w40r.room-value-lock
                                        i.fas.fa-lock
                                .room-col.room-w-odds.room-h95.d-flex
                                  .room-odds-col.room-w100(v-if="odds[match.match_id] && odds[match.match_id]['ou'] && odds[match.match_id]['ou'][11] != 0 && odds[match.match_id]['ou'][12] != 0 && odds[match.match_id]['ou'][11] != '' && odds[match.match_id]['ou'][12] != ''")
                                    .room-odds
                                      .room-ball-value.room-w40 {{ odds[match.match_id]['ou'][8] }}
                                      .room-bet-value.room-w40r
                                        roomOdds(
                                          :odds="odds[match.match_id]['ou']"
                                          :idx="odds[match.match_id]['ou'][7] == 1 ? '12' : '11'"
                                          :typ="oddsType"
                                          dataType="1"
                                          :roomData="roomData"
                                          :match="match"
                                          )
                                    .room-odds
                                      .room-ball-value.room-w40 u
                                      .room-bet-value.room-w40r
                                        roomOdds(
                                          :odds="odds[match.match_id]['ou']"
                                          :idx="odds[match.match_id]['ou'][7] == 1 ? '11' : '12'"
                                          :typ="oddsType"
                                          dataType="1"
                                          :roomData="roomData"
                                          :match="match"
                                          )
                                  .room-odds-col.room-w100(v-else)
                                    .room-odds
                                      .room-ball-value.room-w40 -
                                      .room-bet-value.room-w40r.room-value-lock
                                        i.fas.fa-lock
                                    .room-odds
                                      .room-ball-value.room-w40 -
                                      .room-bet-value.room-w40r.room-value-lock
                                        i.fas.fa-lock
                              template(v-else)
                                .room-col.room-w-odds.room-h95.d-flex.room-br
                                  .room-odds-col.room-w100
                                    .room-odds
                                      .room-ball-value.room-w40 -
                                      .room-bet-value.room-w40r.room-value-lock
                                        i.fas.fa-lock
                                    .room-odds
                                      .room-ball-value.room-w40 -
                                      .room-bet-value.room-w40r.room-value-lock
                                        i.fas.fa-lock
                                .room-col.room-w-odds.room-h95.d-flex
                                  .room-odds-col.room-w100
                                    .room-odds
                                      .room-ball-value.room-w40 -
                                      .room-bet-value.room-w40r.room-value-lock
                                        i.fas.fa-lock
                                    .room-odds
                                      .room-ball-value.room-w40 -
                                      .room-bet-value.room-w40r.room-value-lock
                                        i.fas.fa-lock
              template(v-else)
                .d-flex.align-items-center.justify-content-center.p-3
                  .fad.fa-spinner.fa-spin(style="line-height: 1; font-size: 14px; color: #888;")
        //- my bet
        .tournament-pool-body(v-else)
          template(v-if="betResultList == 'undefined' || betResultList.length <= 0")
            .tournament-pool-body-wrapper
              .text-center
                span {{ $t('message.no_information_available') }}
          template(v-else)
            table.table-info(width='100%')
              tbody
                tr
                  th.text-center(scope='col', width='4%') {{ $t("ui.no/") }}
                  th.text-left(scope='col', width='18%') {{ $t("ui.trans_time") }}
                  th.text-left(scope='col', width='34%') {{ $t("ui.event") }}
                  th.text-right(scope='col', width='6%') {{ $t("ui.odds") }}
                  th.text-right(scope='col', width='10%') {{ $t("ui.stake") }}
                  th.text-right(scope='col', width='10%') {{ $t("ui.win") }}/{{ $t("ui.lose") }}
                  th.text-right(scope='col', width='10%') {{ $t("ui.payout") }}
                  th(scope='col', width='8%') {{ $t("ui.status") }}
                tr(v-for="(item, index) in betResultList" :class="index % 2 === 0 ? 'grey' : ''")
                  td.text-center(valign='top')
                    span {{ index + 1 }}
                  td.text-left(valign='top')
                    div {{ $t("ui.ref_no") }}: {{ item.bet_id }}
                    div {{ $dayjs(item.bet_time).format("MM/DD/YYYY hh:mm:ss A") }}
                  td.text-left(valign='top')
                    .bet-info
                      .bet-type.blue.mb-1(style="font-size: 13px") {{ sports[item.sports_type] }} - {{ getBetTypeName(item.bet_type) }}
                      .bet-detail.blue.pl-2
                        .name {{ getBetDetail(item) }}
                        .oddsdetail
                          .d-flex.justify-content-start
                            .selector-name(v-if="['HDP', 'HDPH', 'OU', 'OUH', '1X2HDP', '1X2HDPH'].includes(item.bet_type)") {{ ballDisplay(item) }}
                            span(v-if="item.market_type == 3 && item.home_running_score != null") [{{ item.home_running_score }}-{{ item.away_running_score }}]
                      .match-info.d-flex.flex-column.pl-2.mb-1(style="border-left: 4px solid #cc9966cc; border-radius: 4px; overflow: hidden;")
                        .name-home {{ item.home_team_name }}
                        .name-away {{ item.away_team_name }}
                        .name-league.font-weight-bold {{ item.league_name }}
                  td.text-right(valign='top')
                    div {{ item.odds_display }}
                  td.text-right(valign='top')
                    span {{ $numeral(item.bet_member).format("0,0") }}
                  td.text-right(valign='top')
                    span(:class="parseFloat(item.winlose) < 0 ? 'red': ''") {{ $numeral(item.winlose).format("0,0") }}
                  td.text-right(valign='top')
                    span(:class="parseFloat(item.payout) < 0 ? 'red' : ''") {{ $numeral(item.payout).format("0,0") }}
                  td.text-left(valign='top')
                    div {{ parseFloat(item.winlose) != 0 ? (parseFloat(item.winlose) < 0 ? $t("ui.lost") : $t("ui.won")) : $t("ui.draw") }}
                    div {{ item.score }}
      //- ranking
      template(v-if="listMode == 2")
        template(v-if="isRanking")
          roomGameRanking(:roomRank="roomRank" :participants="participants" :roomId="roomData.room_id" :roomStatus="roomData.room_status" :roomFormula="roomData.formula")
        template(v-else)
          roomGameParticipants(:participants="participants" :roomId="roomData.room_id")

    //- matches drop down button
    .tournament-btn#tournament-bottom-anchor(
      v-if="(roomData.total_match != null) && (roomData.total_match > 0) && canBet && (listMode == 1)"
      data-toggle="collapse"
      :data-target="'#room-' + roomData.room_id"
      )
      .tournament-text
        span {{ match_count }} / {{ roomData.total_match }} {{ $t('ui.matches') }}
      .tournament-arrow
        i.fas.fa-chevron-up.ml-2

  .tournament-float(v-if="listMode == 1 && roomData.total_match > 5")
    .tournament-float-btn(@click="goTop()")
      i.fad.fa-arrow-up
    .tournament-float-btn(@click="goBottom()")
      i.fad.fa-arrow-down
  v-tour(name="tour2" :steps="tour.steps" :callbacks="tour.callbacks" :options="tour.options")
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import service from "@/tournament2/library/_xhr.js";
import calc from "@/library/_calculation.js";
import sync2 from "@/library/_sync-match";
import xhrMatch from "@/library/_xhr-match";
import naming from "@/library/_name";

const _INTERVAL = 300;

export default {
  components: {
    roomOdds: () => import("@/tournament2/components/roomOdds.vue"),
    roomGameDetails: () => import("@/tournament2/components/roomGameDetails.vue"),
    roomGameRanking: () => import("@/tournament2/components/roomGameRanking.vue"),
    roomGameParticipants: () => import("@/tournament2/components/roomGameParticipants.vue"),
  },
  props: {
    roomId: {
      type: Number,
    },
  },
  data() {
    return {
      selectedLeague: null,
      tour: {
        options: {
          labels: {
            buttonSkip: this.$t("message.button_skip"),
            buttonPrevious: this.$t("message.button_previous"),
            buttonNext: this.$t("message.button_next"),
            buttonStop: this.$t("message.button_stop"),
          },
        },
        callbacks: {
          onStart: this.onStart,
          onPreviousStep: this.onPreviousStep,
          onNextStep: this.onNextStep,
          onSkip: this.onSkip,
          onFinish: this.onFinish,
          onStop: this.onStop,
        },
        steps: [
          {
            target: "#v-step-4",
            content: this.$t("message.tour_content_6"),
          },
          {
            target: ".tournament-pool-amount",
            content: this.$t("message.tour_content_7"),
          },
          {
            target: "#v-step-6",
            content: this.$t("message.tour_content_10"),
          },
        ],
      },
      match_count: 0,
      ticker: 0,
      roomData: {},
      listMode: 0,
      debouncePop: null,
      handler: null,
      counter: _INTERVAL,
      last_betting_time: null,
      leagueList: {},
      matchList: {},
      matches: [],
      odds: {},
      loading: {
        data: false,
        getLeagueList: false,
        getMatchList: false,
        getMatch: false,
        getBetResultList: false,
        getRoomRank: false,
        getMemberBetList: false,
      },
      betList: [],
      betResultList: [],
      participants: [],
      roomRank: [],
      betMode: false,
    };
  },
  computed: {
    sports() {
      return this.$store.state.layout.sports;
    },
    tour1() {
      return this.$store.state.layout.tour1;
    },
    isRanking() {
      return this.roomRank.length > 0;
    },
    canJoin() {
      return [0].includes(this.roomData.room_status) && this.roomData.room_join === 0;
    },
    canBet() {
      return [0, 1].includes(this.roomData.room_status);
    },
    oddsTypeLocale() {
      return config.oddsTypeLocale;
    },
    commType() {
      return "A";
    },
    oddsType() {
      return "DEC";
    },
    language() {
      return this.$store.getters.language;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
  },
  destroyed() {
    if (this.handler != null) {
      clearTimeout(this.handler);
    }
    EventBus.$off("roomGameRefresh2", this.getData);
  },
  mounted() {
    this.handleTimer();
    this.debouncePop = this.debounce(this.getData, 250);
    // this.debouncePop();
    this.getData(true);

    EventBus.$on("roomGameRefresh2", this.getData);

    if (this.tour1 == false) {
      this.$tours["tour1"].stop();
      this.$tours["tour2"].start();
    }
  },
  methods: {
    getMatchDate(e) {
      var mdate = this.$dayjs(e).format("MM/DD");
      switch (this.currency_code) {
      case "MMK":
      case "MMO":
        mdate = this.$dayjs(e).subtract(90, "minute").format("MM/DD");
        break;
      case "NGN":
        mdate = this.$dayjs(e).subtract(420, "minute").format("MM/DD");
        break;
      }
      return mdate;
    },
    getMatchTime(e) {
      var mdate = this.$dayjs(e).format("hh:mm A");
      switch (this.currency_code) {
      case "MMK":
      case "MMO":
        mdate = this.$dayjs(e).subtract(90, "minute").format("hh:mm A");
        break;
      case "NGN":
        mdate = this.$dayjs(e).subtract(420, "minute").format("hh:mm A");
        break;
      }
      return mdate;
    },
    ballDisplay(e) {
      return naming.ballDisplay1(e, this);
    },

    getBetDetail(bet) {
      return naming.betDisplay(bet, this);
    },
    getBetTypeName(e) {
      return this.$t("m.BT_" + e);
    },
    onStart() {},
    onPreviousStep(currentStep) {},
    onNextStep(currentStep) {},
    onSkip() {
      this.$store.dispatch("layout/setTour1", true);
    },
    onFinish() {
      this.$store.dispatch("layout/setTour1", true);
    },
    onStop() {
      this.$store.dispatch("layout/setTour1", true);
    },
    goTop() {
      document.getElementById("tournament-top-anchor").scrollIntoView();
    },
    goBottom() {
      document.getElementById("tournament-bottom-anchor").scrollIntoView();
    },
    getCountDown(e) {
      var difference = this.$dayjs.utc(e + "+0800").diff(this.$dayjs.utc());
      var days = Math.floor(difference / (1000 * 60 * 60 * 24));
      var hours = Math.floor((difference / (1000 * 60 * 60)) % 24);
      var minutes = Math.floor((difference / 1000 / 60) % 60);
      var seconds = Math.floor((difference / 1000) % 60);
      if (days < 0 || hours < 0 || minutes < 0 || seconds < 0) {
        return this.$t("ui.match_started");
      } else {
        if (days === 0) {
          if (hours === 0) {
            if (minutes === 0) {
              return seconds + "s";
            } else {
              return minutes + "m";
            }
          } else {
            return hours + "h " + minutes + "m";
          }
        } else {
          return days + "d " + hours + "h " + minutes + "m";
        }
      }
    },
    leaveRoom() {
      this.$emit("room-leave2");
    },
    formatOddsDisplay(odds, bet_type) {
      if (bet_type == "cs") return calc.fmcs(odds);
      else return calc.fmt(odds);
    },
    debouncePush() {
      if (this.debouncePop) {
        this.debouncePop();
      }
    },
    debounce(func, wait, immediate) {
      var timeout;

      return function executedFunction() {
        var context = this;
        var args = arguments;

        var later = function () {
          timeout = null;
          if (!immediate) func.apply(context, args);
        };

        var callNow = immediate && !timeout;

        clearTimeout(timeout);

        timeout = setTimeout(later, wait);

        if (callNow) func.apply(context, args);
      };
    },
    getData(e) {
      this.getRoomInfo(e).then(() => {
        this.getBetResultList();
        this.getMemberBetList();
        this.getRoomRank();
        this.getLeagueList().then(
          () => {
            this.populateMatches().then(
              () => {
                this.counter = _INTERVAL;
                if (this.selectedLeague) {
                  if ($(this.selectedLeague).hasClass("collapsed")) {
                    $(this.selectedLeague).click();
                    $(this.selectedLeague).get(0).scrollIntoView();
                  }
                }
              },
              (err) => {}
            );
          },
          (err) => {}
        );
      });
    },
    handleTimer() {
      this.ticker += 1;

      // this.counter = e;
      this.handler = setTimeout(() => {
        // minus counter and reset to 60 if counter is 0
        if (this.counter > 0) {
          this.counter--;
        } else {
          this.counter = _INTERVAL;
          this.getData();
        }
        this.handleTimer();
      }, 1000);
    },
    cancel() {
      this.$emit("room-leave2");
    },
    getMatchList(e, f) {
      return new Promise((resolve, reject) => {
        var json = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          room_id: e,
          league_id: f,
          page_number: 1,
          page_size: 100,
        };

        var feedback = {
          success: false,
          status: errors.session.invalidSession,
        };

        this.loading.getMatchList = true;
        service.getMatchList(config.tournamentUrl().roommatchlist, json).then(
          (result) => {
            this.loading.getMatchList = false;
            if (result) {
              feedback.success = result.success;
              feedback.status = result.status;
              if (result.success == true) {
                if (!this.matchList.hasOwnProperty(e)) {
                  this.$set(this.matchList, e, {});
                }
                this.$set(this.matchList[e], f, result.data.value);

                for (var i = 0; i < result.data.value.length; i++) {
                  this.matches.push(result.data.value[i].match_id);
                }
                resolve({ league_id: f, count: result.data.value.length });
              } else {
                this.$helpers.handleFeedback(feedback.status);
                resolve({ league_id: f, count: 0 });
              }
            } else {
              resolve({ league_id: f, count: 0 });
            }
          },
          (err) => {
            this.loading.getMatchList = false;
            feedback.success = false;
            feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);
            resolve({ league_id: f, count: 0 });
          }
        );
      });
    },
    getLeagueList() {
      return new Promise((resolve, reject) => {
        if (![0, 1].includes(this.roomData.room_status)) {
          resolve();
        }

        var e = this.roomData.room_id;

        var json = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          room_id: e,
          page_number: 1,
          page_size: 100,
        };

        var feedback = {
          success: false,
          status: errors.session.invalidSession,
        };

        this.loading.getLeagueList = true;
        service.getLeagueList(config.tournamentUrl().roomleaguelist, json).then(
          (result) => {
            this.loading.getLeagueList = false;
            if (result) {
              feedback.success = result.success;
              feedback.status = result.status;
              if (result.success == true) {
                var ln = result.data.value;
                var j = 0;
                var k = ln.length;
                var items = [];
                for (var i = 0; i < k; i++) {
                  var lid = ln[i].league_id;
                  var ret = false;
                  this.getMatchList(e, lid).then((res) => {
                    items.push(res);
                    j = j + 1;
                    if (j == k) {
                      setTimeout(() => {
                        this.populateMatches().then(() => {
                          var deleted = [];
                          var inserted_array = [];
                          // var inserted_object = {};
                          // var inserted_ids = [];

                          var mc = 0;
                          for (var i = 0; i < items.length; i++) {
                            mc += items[i].count;
                            if (items[i].count <= 0) {
                              deleted.push(items[i].league_id);
                            }
                          }
                          this.match_count = mc;
                          for (var i = 0; i < k; i++) {
                            if (!deleted.includes(ln[i].league_id)) {
                              // inserted_ids.push(ln[i].league_id);
                              inserted_array.push(ln[i]);
                              // inserted_object[ln[i].league_id] = ln[i];
                            }
                          }
                          this.$set(this.leagueList, e, inserted_array);
                        });
                      }, 100);
                    }
                  });
                }
              } else {
                this.$helpers.handleFeedback(feedback.status);
              }
            }

            resolve();
          },
          (err) => {
            this.loading.getLeagueList = false;
            feedback.success = false;
            feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);

            resolve();
          }
        );
      });
    },
    getName(p, e) {
      var name = e[p + this.language];
      if (name == null || name == "" || !name) {
        name = e[p + "en"];
      }
      return name;
    },
    getLogo(p, e) {
      return config.flagPath + p + "/" + e;
    },
    getMatch(url, id, mid, mkt, bt, fn) {
      return new Promise((resolve, reject) => {
        if (mid != null && mid.length > 0) {
          var args = {
            account_id: this.$store.getters.accountId,
            session_token: this.$store.getters.sessionToken,
            arguments: [this.language, bt, mkt],
            ids: mid.join("|"),
            mmo: "ODDS",
          };

          xhrMatch.get(url, args).then(
            (res) => {
              if (res.success) {
                if (res.data) {
                  var parlay = false;
                  var m = sync2.decode2(res.data, this.menu3, this.commType, this.oddsType, parlay);

                  for (var i = 0; i < mid.length; i++) {
                    var match_id = mid[i];
                    if (!this.odds.hasOwnProperty(match_id)) {
                      this.$set(this.odds, match_id, {});
                    }
                  }

                  var k = {
                    hdp: {},
                    ou: {},
                  };

                  for (var key in m.odds) {
                    var mt = m.odds[key];
                    for (var bt in k) {
                      var m1 = mt[bt];
                      if (m1 == null) {
                      } else {
                        for (var i = 0; i < mid.length; i++) {
                          var m3 = m1[mid[i]];
                          if (m3 == null) {
                          } else {
                            k[bt][mid[i]] = m3;
                          }
                        }
                      }
                    }
                  }

                  for (var bt in k) {
                    var m1 = k[bt];
                    if (m1 == null) {
                      // clear all matches if this bet type not exists
                      for (var i = 0; i < mid.length; i++) {
                        if (this.odds[mid[i]].hasOwnProperty(bt)) {
                          this.$delete(this.odds[mid[i]], bt);
                        }
                      }
                    } else {
                      for (var i = 0; i < mid.length; i++) {
                        var m3 = m1[mid[i]];
                        if (m3 == null) {
                          // clear this match if this bet type not exists
                          if (this.odds[mid[i]].hasOwnProperty(bt)) {
                            this.$delete(this.odds[mid[i]], bt);
                          }
                        } else {
                          for (var m4 in m3) {
                            if (m3[m4][4].toUpperCase() == bt.toUpperCase()) {
                              this.$set(this.odds[mid[i]], bt, m3[m4]);
                              break;
                            }
                          }
                        }
                      }
                    }
                  }
                }
              } else {
                this.$helpers.handleFeedback(res.status);
              }
              resolve();
            },
            (err) => {
              this.$helpers.handleFeedback(err.status);
              resolve();
            }
          );
        } else {
          resolve();
        }
      });
    },
    populateMatches() {
      return new Promise((resolve, reject) => {
        if (![0, 1].includes(this.roomData.room_status)) {
          resolve();
        }

        if (this.matches.length > 0) {
          this.loading.getMatch = true;
          this.$forceUpdate();
          setTimeout(() => {
            this.getMatch(config.getMatchUrl() + "?match=tournament", "tournament", this.matches, "1|2", "hdp|ou", null)
              .then(() => {
                this.loading.getMatch = false;
                resolve();
              })
              .catch((err) => {
                this.loading.getMatch = false;
                resolve();
              });
          }, 1000);
        }
      });
    },
    //- get self bet result list
    getBetResultList() {
      if ([0, 1].includes(this.roomData.room_status)) {
        return;
      }

      var e = this.roomData.room_id;

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        room_id: e,
        page_number: 1,
        page_size: 100,
        target: null,
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      this.loading.getBetResultList = true;
      service.betResult(config.tournamentUrl().betresultlist, json).then(
        (result) => {
          this.loading.getBetResultList = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              this.betResultList = result.data.value;
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          this.loading.getBetResultList = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getMemberBetList() {
      var e = this.roomData.room_id;

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        room_id: e,
        page_number: 1,
        page_size: 1000,
        target: "all",
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      this.loading.getMemberBetList = true;
      service.memberBetList(config.tournamentUrl().memberbetlist, json).then(
        (result) => {
          this.loading.getMemberBetList = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              var mbl = result.data.value;
              var mb = {};
              for (var i = 0; i < mbl.length; i++) {
                var ref = mbl[i].ref;
                if (!mb.hasOwnProperty(ref)) {
                  mb[ref] = {
                    member_id: mbl[i].member_id,
                    bet_list: [mbl[i]],
                  };
                } else {
                  mb[ref].bet_list.push(mbl[i]);
                }
              }
              this.participants = mb;
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          this.loading.getMemberBetList = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getRoomRank() {
      var e = this.roomData.room_id;

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        room_id: e,
        page_number: 1,
        page_size: 100,
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      this.loading.getRoomRank = true;
      service.roomRank(config.tournamentUrl().roomrank, json).then(
        (result) => {
          this.loading.getRoomRank = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              this.roomRank = result.data.value;
              // this.roomRank.sort((a, b) => {
              //   return a.top_position - b.top_position;
              // });
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          this.loading.getRoomRank = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getRoomInfo(e) {
      return new Promise((resolve, reject) => {
        var room_id = this.roomId;
        var json = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          room_id: room_id,
        };

        var feedback = {
          success: false,
          status: errors.session.invalidSession,
        };

        // this.roomData = {};
        service.getRoomInfo(config.tournamentUrl().roominfo, json).then(
          (result) => {
            if (result) {
              feedback.success = result.success;
              feedback.status = result.status;
              if (result.success == true) {
                var r = result.data.value[0];
                this.$set(this, "roomData", r);
                EventBus.$emit("ROOM_DATA", r);
                if (e) {
                  if (r.room_join == 1) {
                    if (r.total_points > 0) {
                      this.listMode = 1;
                    }
                  }
                }
              } else {
                this.$helpers.handleFeedback(feedback.status);
              }
              resolve();
            }
          },
          (err) => {
            this.loading.getRoomInfo = false;
            feedback.success = false;
            feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);
            resolve();
          }
        );
      });
    },
    getBackground(e) {
      return config.tournamentBackgroundPath + e + "?v=20230605";
    },
  },
};
</script>
