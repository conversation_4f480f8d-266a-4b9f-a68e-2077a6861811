.content .left #sidebar-left {
  position: absolute;
  top: 0;
  padding: 2px 5px;
  z-index: -1;
  cursor: pointer;
  right: -12px;
}

.content .left #sidebar-left:hover {
  z-index: 9999;
}

.content .left .content-bet {
  background: #fff;
  padding: 2px;
  border-left: 1px solid #5078af;
  border-right: 1px solid #5078af;
}

.content .left .content-bet .tab-content {
  border: 1px solid #f9d040;
}

.content .left .content-bet .nav li.nav-item {
  width: 50%;
  text-align: center;
  position: relative;
}

.content .left .content-bet .nav li.nav-item a.nav-link {
  font-size: 11px;
  line-height: 23px;
  height: 23px;
  padding: 0 6px;
  color: #ffffff;
  background: #145693;
  border-radius: 0 0 0 0;
  border-top: 1px solid #d9d9dd;
  border-left: 1px solid #d9d9dd;
  border-right: 1px solid #d9d9dd;
  text-transform: uppercase;
}

.content .left .content-bet .nav li.nav-item a.nav-link .number-parlay {
  color: #fff;
  font-size: 10px;
  padding: 3px 5px;
  margin: 0;
  background: #bb4038;
  border-radius: 3px;
  position: absolute;
  top: 3px;
  right: 3px;
  line-height: 1;
  font-weight: 300;
}

.content .left .content-bet .nav li.nav-item a.nav-link.active {
  color: #333;
  background: #f9d040;
  font-weight: 600;
  border-top: 1px solid #f9d040;
  border-left: 1px solid #f9d040;
  border-right: 1px solid #f9d040;
}

.content .left .group .sport-type.active {
  background: #145693;
  color: #F6C344;
}

.content .left .group .sport-type:hover a,
.content .left .group .sport-type.active:hover a {
  color: #fff;
}

.content .left .group .sport-type.active a,
.content .left .group .sport-type.active a i {
  color: #f9d040;
}

.content .left .content-bet .nav li.nav-item a#pills-single-tab.nav-link {
  margin-right: 2px;
}

.content .left .content-bet .nav li:nth-child(2).nav-item.nav-mybet a.nav-link {
  margin: 0 2px;
}

.content .left .content-bet .tab-content .tab-pane {
  background: #fff;
}

.content .left .content-bet .tab-content .tab-pane .bet-info {
  background: #ffddd2;
  margin-bottom: 0;
  position: relative;
  background: #ffddd2;
  padding: 4px 0 0 0;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .close-bet {
  position: absolute;
  top: 7px;
  right: 10px;
  cursor: pointer;
  color: #7c7c7c;
  position: absolute;
}

.content .left .content-bet .tab-content .tab-pane .bet-info.grey {
  background: #F4F7FC;
}

.content .left .content-bet .tab-content .tab-pane .bet-info.grey .bet-type {
  color: #5574a7;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-type {
  color: #b53f39;
  text-transform: capitalize;
  font-size: 11px;
  font-weight: 700;
  line-height: 1;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-type.blue {
  color: #5574a7;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail {
  margin: 0;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .name {
  font-size: 13px;
  font-weight: 400;
  text-transform: capitalize;
  color: #000;
  position: relative;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .name .dollar {
  background: #0d3a64;
  color: #fff;
  text-align: center;
  border-radius: 3px 3px 3px 3px;
  cursor: pointer;
  position: absolute;
  right: 0;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .name.red {
  color: #b53f39;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .oddsdetail {
  font-size: 12px;
  font-weight: 300;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .oddsdetail .selector-name {
  margin-right: 0.25em;
  display: flex;
  align-items: center;
  font-size: 1em;

  /* inline-size: min-content;
  overflow: hidden;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap; */
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .oddsdetail .selector-score {
  color: #7c7c7c;
  margin-right: 0.25em;
  display: flex;
  align-items: center;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .oddsdetail .selector-other {
  margin-right: 0.25em;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .oddsdetail .selector-odds {
  color: #01122b;
  font-weight: 700;
  margin-right: 0.25em;
  font-size: 13px;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .oddsdetail .selector-odds.accent {
  color: #b53f39;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .oddsdetail .selector-odds.small {
  font-size: 13px;
  color: inherit;
  color: black;
  display: flex;
  align-items: center;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .oddsdetail .selector-odds.small.text-red {
  color: #b53f39 !important;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .oddsdetail .stacks {
  font-weight: 700;
  /* overflow: hidden; */
  /* text-decoration: none; */
  /* text-overflow: ellipsis; */
  white-space: nowrap;
  padding-left: 8px;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .selector-odds.red {
  color: #b53f39;
}

.content .left .content-bet .tab-content .tab-pane .bet-info .bet-detail .selector-odds.changed {
  text-decoration: line-through;
}

.content .left .content-bet .tab-content .tab-pane .bet-info.changed {
  background: #ffddd2;
  -webkit-animation: fade-blink 1s infinite;
  animation: fade-blink 1s infinite;
}

.content .left .content-bet .tab-content .tab-pane .bet-info.white {
  background: #fff;
}

.content .left .content-bet .tab-content .tab-pane .bet-info.live {
  background: #ffddd2;
}

.content .left .content-bet .tab-content .tab-pane .bet-infosub label {
  margin-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.content .left .content-bet .tab-content .tab-pane .bet-infosub label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin: 1px 5px 0 0;
  cursor: pointer;
}

.content .left .content-bet .tab-content .tab-pane .bet-infosub .text {
  font-size: 11px;
  line-height: 1;
}

.content .left .content-bet .tab-content .tab-pane .bet-infosub .countdown {
  font-size: 12px;
}

.content .left .content-bet .tab-content .tab-pane .match-info {
  color: #7c7c7c;
  padding: 0 7px;
}

.content .left .content-bet .tab-content .tab-pane .match-info.red {
  background: #ffddd2;
}

.content .left .content-bet .tab-content .tab-pane .match-info.grey {
  background: #ececec;
}

.content .left .content-bet .tab-content .tab-pane .match-info .name-home {
  font-size: 10px;
}

.content .left .content-bet .tab-content .tab-pane .match-info .name-home.black {
  color: #01122b;
}

.content .left .content-bet .tab-content .tab-pane .match-info .name-home.red {
  color: #b53f39;
}

.content .left .content-bet .tab-content .tab-pane .match-info .name-away {
  font-size: 10px;
}

.content .left .content-bet .tab-content .tab-pane .match-info .name-away.black {
  color: #01122b;
}

.content .left .content-bet .tab-content .tab-pane .match-info .name-away.red {
  color: #b53f39;
}

.content .left .content-bet .tab-content .tab-pane .match-info .name-league {
  font-weight: 700;
  font-size: 11px;
}

.content .left .content-bet .tab-content .tab-pane .match-info .vs {
  font-size: 13px;
}

.content .left .content-bet .tab-content .tab-pane .match-info.white {
  background: #fff;
}

.content .left .content-bet .tab-content .tab-pane .match-info.live {
  background: #ffddd2;
}

.content .left .content-bet .tab-content .tab-pane .remove-parlay {
  background: #ececec;
  padding: 5px 7px;
}

.content .left .content-bet .tab-content .tab-pane .stake-info {
  background: #dfe7f3;
  color: #5574a7;
  padding: 5px 7px;
  font-size: 14px;
}

.content .left .content-bet .tab-content .tab-pane .stake-info .entry .content-stake .input-group {
  height: 26px;
}

.content .left .content-bet .tab-content .tab-pane .stake-info .entry .content-stake .input-group .form-control {
  height: inherit;
  font-size: 13px;
  border-right: none;
  padding: 0 2px 0 10px;
}

.content .left .content-bet .tab-content .tab-pane .stake-info .entry .content-stake .input-group .input-group-append .input-group-text {
  height: 26px;
  background: #fff;
  padding: 0 3px;
  border-left: none;
}

.content .left .content-bet .tab-content .tab-pane .entry-box {
  font-size: 12px;
  color: #7c7c7c;
  background: #ececec;
  padding: 5px 7px;
  margin-bottom: 6px;
}

.content .left .content-bet .tab-content .tab-pane .entry-box .title {
  text-transform: capitalize;
}

.content .left .content-bet .tab-content .tab-pane .entry-box .entry-amount {
  color: #5574a7;
}

.content .left .content-bet .tab-content .tab-pane .entry-box .dollar {
  background: #0d3a64;
  color: #fff;
  text-align: center;
  border-radius: 3px 3px 3px 3px;
  cursor: pointer;
}

.content .left .content-bet .tab-content .tab-pane .btn-area .btn-process {
  padding: 0 10px;
  height: inherit;
  line-height: 26px;
  background: #0d3a64;
  font-size: 13px;
  border: none;
  text-transform: capitalize;
}

.content .left .content-bet .tab-content .tab-pane .btn-area .btn-process:hover {
  background: #345684;
}

.content .left .content-bet .tab-content .tab-pane .btn-area .btn-cancel {
  padding: 0 10px;
  height: inherit;
  line-height: 26px;
  background: #a4a4a4;
  font-size: 13px;
  border: none;
  color: #000;
  text-transform: capitalize;
}

.content .left .content-bet .tab-content .tab-pane .empty {
  background: #ececec;
  padding: 5px 7px;
  font-size: 12px;
}

.content .left .content-bet .tab-content .tab-pane .text-box {
  padding: 5px 7px;
  font-size: 12px;
}

.content .left .content-bet .tab-content .tab-pane .error-message {
  padding: 2px 8px 8px 8px;
  font-size: 11px;
  color: #dc3545;
  text-align: center;
}

.content .left .content-bet .tab-content .tab-pane .ticket-info {
  color: #7c7c7c;
  padding: 0 7px 5px;
  background: #ececec;
  font-size: 13px;
  margin-top: -6px;
}

.content .left .content-bet .tab-content .tab-pane .ticket-info {
  color: #7c7c7c;
  padding: 0 4px 4px 4px;
  background: #ececec;
  font-size: 13px;
  margin: 0;
}

.content .left .content-bet .tab-content .tab-pane .ticket-info .ticket-status {
  text-transform: capitalize;
  border-radius: 10px 10px 10px 10px;
  background: #0d3a64;
  padding: 0 10px;
  color: #fff;
  line-height: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.content .left .content-bet .tab-content .tab-pane .ticket-info .ticket-status.running {
  background: #0d3a64;
}

.content .left .content-bet .tab-content .tab-pane .ticket-info .ticket-status.accept {
  background: #28a745;
}

.content .left .content-bet .tab-content .tab-pane .ticket-info .ticket-status.reject {
  background: #b53f39;
}

.content .left .content-bet .tab-content .tab-pane .ticket-info .ticket-status.pending {
  background: #7c7c7c;
}

.content .left .content-bet .tab-content .tab-pane .ticket-info .ticket-status.waiting {
  background: #e63048;
}

@-webkit-keyframes WaitWait {
  50% {
    background: #e63048;
  }

  75% {
    background: #971e2e;
  }
}

@keyframes WaitWait {
  50% {
    background: #e63048;
  }

  75% {
    background: #971e2e;
  }
}

.content .left .content-bet .tab-content .tab-pane .ticket-info.live {
  background: #ffddd2;
}

.content .left .content-bet .tab-content .tab-pane .warning {
  background: #f7e8b6;
  color: #0f4f8c;
  border: 1px solid #d1c18d;
  padding: 5px 7px;
  font-size: 11px;
  border-radius: 0.25rem;
}

.content .left.active .nav-header .collapsed,
.content .left.active .nav-header [aria-expanded="true"],
.content .left.active .nav-header [aria-expanded="false"] {
  background: transparent;
}

.content .left .nav-header .collapsed {}

.content .left .nav-header {
  color: #FFFFFF;
  text-transform: capitalize;
  width: 50%;
  text-align: center;
  line-height: 39px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  border-bottom: 0;
  font-weight: 500;
  font-size: 13px;
  border-radius: 3px;
}

.content .left .nav-header.nav-left {
  border-radius: 3px 0 0 3px;
}

.content .left .nav-header.nav-right {
  border-radius: 0 3px 3px 0;
}

.content .left .nav-header [aria-expanded="true"] {
  color: #F6C344;
  background: #145693;
}

.content .left .nav-header [aria-expanded="false"] {
  color: #FFFFFF;
  background: #276FA8;
}

.content .left .nav-header.nav-left .collapsed {}

.content .left .nav-header span {
  padding-left: 3px;
}

.content .left .group {
  line-height: 39px;
  padding: 0 15px;
  color: #FFFFFF;
  text-transform: capitalize;
  font-size: 14px;
  cursor: pointer;
  letter-spacing: 0.5px;
  padding: 0 4px;
  border-top: 1px solid #5991C188;
}

.content .left .group.selected {
  background: url(/images/tab-bg.png) no-repeat;
  background-size: auto 100%;
  color: #fff;
}

.content .left .group.changed.selected {
  background: #145693;
}

.content .left .bg-mmo .group.selected {
  background: #072c3a;
  color: #fff;
}

.content .left .group .fa-star {
  color: #ffe105;
}

.content .left .group span {
  font-size: 13px;
  text-transform: capitalize;
  padding: 0 8px 0 8px;
}

.content .left .group .menu-item {
  display: block;
  width: 120px;
  min-width: 110px;
  max-width: 120px;
  line-height: 1;
  font-size: 12px;
}

.content .left .group .menu-item.mopts {
  width: 110px;
}

.content .left .group .menu-item:hover {
  color: #fff !important;
}

.content .left .group .hot-item {
  margin: 0;
  padding-right: 4px;
  border: 0;
}

.content .left .group .number {
  width: 30px;
  height: 20px;
  line-height: 1;
  text-align: center;
  background: #C11A00;
  border-radius: 3px;
  color: #fff;
  font-size: 11px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding-top: 1px;
}

.content .left .group .sport-type {
  background: #276FA8;
  text-align: center;
  width: 33.3333%;
  padding: 4px 0;
}

.content .left .group .sport-type a {
  color: #FFFFFF;
}

.content .left .group .market-menu .sport-type .nav-link {
  padding: 0;
}

.content .left .group .sport-type a:hover {
  text-decoration: none;
}

.content .left .group .sport-type.second {
  border-left: 1px solid #c9d1dc;
  border-right: 1px solid #c9d1dc;
}

.content .left .group .sport-type i {
  padding: 0;
  padding-right: 2px;
  color: #a5cae5;
  font-size: 12px;
}

.content .left .group .sport-type span {
  padding: 0;
  width: 60px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  font-size: 12px;
  letter-spacing: 0;
}

.content .left .group .sport-icon img {
  width: 18px;
  position: relative;
  top: -1px;
}

.content .left .group .text-live {
  font-style: normal;
  text-transform: uppercase;
  font-size: 9px;
  color: #fff;
  background: #C11A00;
  line-height: 15px;
  text-align: center;
  padding: 0 2px 0 2px;
  margin: 0;
  white-space: nowrap !important;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 33px;
  height: 15px;
  border-radius: 30px;
}

.content .left .group .text-hot {
  font-style: normal;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 9px;
  color: #fff;
  background: transparent;
  line-height: 13px;
  text-align: center;
  padding: 0 3px 0 4px;
  margin: 0;
  border: 0;
  white-space: nowrap !important;
  border-radius: 2px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 28px;
  height: 15px;
  position: relative;
  display: block;
}

.content .left .group .text-hot img {
  width: 28px;
  height: 24px;
  padding: 0;
  border: 0;
  margin: -2px 0 0 0;
}

.content .left .group .text-new {
  text-transform: uppercase;
  font-size: 9px;
  color: #be3a36;
  font-weight: bold;
  background: #f8df00;
  line-height: 13px;
  text-align: center;
  border-radius: 0;
  padding: 0 2px 0 3px;
  margin: 0;
  border: 1px solid #be3a36;
  white-space: nowrap !important;
  border-radius: 3px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 28px;
  -webkit-transform: skew(-10deg);
  -ms-transform: skew(-10deg);
  transform: skew(-10deg);
}

.content .left .group .text-new2 {
  text-transform: uppercase;
  font-size: 9px;
  color: #FFFFFF;
  font-weight: bold;
  background: #e61839cc;
  line-height: 13px;
  text-align: center;
  border-radius: 0;
  padding: 0 2px 0 3px;
  margin: 0;
  border: 1px solid #e61839cc;
  white-space: nowrap !important;
  border-radius: 3px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 28px;
}

.content .left .group .game-number {
  width: 24px;
  text-align: right;
  font-size: 11px;
  margin-top: 0px;
  padding: 0;
  margin-right: 6px;
  font-weight: 300;
  line-height: 1;
}

.content .left ul.subgroup {
  padding: 0;
  margin: 0;
  line-height: 35px;
}

.content .left ul.subgroup input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin: 5px 5px 0 0;
  position: relative;
  top: 3px;
}

.content .left ul.subgroup .sport-icon-small img {
  width: 16px;
  position: relative;
  top: -2px;
}

.content .left ul.subgroup li {
  list-style: none;
}

.content .left ul.subgroup li.small {
  list-style: none;
  /* border-bottom: 1px solid #19446c; */
  border-bottom: 1px solid #5991C188;
  height: 30px;
  line-height: 30px;
}

.content .left ul.subgroup li.small:last-child {
  border-bottom: 0;
}

.content .left ul.subgroup li a {
  color: #fff;
  text-transform: capitalize;
  font-size: 15px;
  height: 100%;
}

.content .left ul.subgroup li a>div {
  height: 100%;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.content .left ul.subgroup li .active {
  color: #f9d040;
}

.content .left ul.subgroup li a:hover {
  text-decoration: none;
}

.content .left ul.subgroup li a .icon {
  padding-left: 0;
  overflow: hidden;
  width: 18px;
  height: 18px;
}

.content .left ul.subgroup li a .title {
  padding-left: 10px;
  font-size: 11px;
}

.content .left ul.subgroup li.hot {
  list-style: none;
  border-bottom: 1px solid #5991C188;
  height: 36px;
  line-height: 36px;
}

.content .left ul.subgroup li a span {
  padding-left: 26px;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

.content .left ul.subgroup li a .flex-fill.subtitle {
  max-width: 176px;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

.content .left ul.subgroup li span {
  font-size: 11px;
}

.content .left .tb {
  border-top: 1px solid #395f83;
  border-radius: 3px 3px 0 0;
}

.content .left .xb {
  border-left: 1px solid #5991C1;
  border-right: 1px solid #5991C1;
  border-radius: 5px;
}

.content .left #market-group {
  line-height: 30px;
  padding-left: 0;
  padding-right: 0;
}

.content .left #market-group .market-menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.content .left #market-group .market-icon {
  display: none;
}

.content .left #market-group .market-icon.parlay {
  -webkit-filter: brightness(5000);
  filter: brightness(5000);
}

.content .right #sidebar-right {
  position: absolute;
  top: 0;
  padding: 2px 5px;
  z-index: -1;
  cursor: pointer;
  left: -12px;
}

.content .right #sidebar-right:hover {
  z-index: 9999;
}

.content .right .z-side .card {
  border-radius: 0 !important;
  background-color: transparent;
  border: 0;
  margin-bottom: 7px;
}

.content .right .z-side .card:first-child,
.content .right .z-side .card .card-header:first-child {
  border-radius: 3px 3px 0 0;
}

.content .right .z-side .card .card-header {
  background: url(/images/tab-bg.png) no-repeat;
  background-size: cover;
  color: #fff;
  padding: 0;
  font-size: 14px;
  font-family: "Roboto", "Tahoma", sans-serif;
  font-weight: 700;
  border: 1px solid #5991C1;
}

.content .right .z-side .card .card-footer {
  background: #0d3a64;
  color: #fff;
  padding: 1px;
  font-size: 12px;
  font-family: "Roboto", "Tahoma", sans-serif;
  font-weight: 500;
  border-left: 1px solid #4f7eab;
  border-right: 1px solid #2f669b;
  border-bottom: 1px solid #2f669b;
  line-height: 1;
}

.content .right .z-side .card .card-footer span {
  color: #89bdee;
  padding: 6px 12px;
  font-size: 12px;
  font-family: "Roboto", "Tahoma", sans-serif;
  font-weight: 500;
  line-height: 1;
  cursor: pointer;
}

.content .right .z-side .card .card-footer span:hover {
  color: #fff;
}

.content .right .z-side .card .frame-header .icon,
.content .right .z-side .card .card-header .icon {
  line-height: 1;
  padding: 8px 0 8px 0;
  width: 32px;
  min-width: 32px;
  max-width: 32px;
  font-size: 14px;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 0;
  border-right: 1px solid #ffffff22;
}

.content .right .z-side .card .frame-header .icon.bl-1,
.content .right .z-side .card .card-header .icon.bl-1 {
  border-right: 0;
  border-left: 1px solid #ffffff22;
}

.content .right .z-side .card .frame-header .icon.b-0,
.content .right .z-side .card .card-header .icon.b-0 {
  border-right: 0;
  border-left: 0;
}

.content .right .z-side .card .frame-header .icon:last-child,
.content .right .z-side .card .card-header .icon:last-child {
  border-right: 0;
  border-left: 1px solid #ffffff22;
}

.content .right .z-side .card .frame-header .icon i,
.content .right .z-side .card .card-header .icon i {
  height: 16px;
  line-height: 16px;
}

.content .right .z-side .card .frame-header .icon a,
.content .right .z-side .card .card-header .icon a {
  height: 16px;
  line-height: 16px;
  text-decoration: none;
  color: #fff;
}

.content .right .z-side .card .frame-header .icon a i .content .right .z-side .card .card-header .icon a i {
  height: 16px;
  line-height: 16px;
}

.content .right .hx-court {
  width: 20px;
  height: 20px;
  fill: #fff;
  margin-top: -2px;
}

.content .right .z-side .card .card-header .text {
  font-size: 13px;
  padding: 6px 8px;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  line-height: 20px;
}

.content .right .z-side .card .card-body {
  padding: 0;
}

.content .right .z-side .card .card-body .column {
  padding: 6px 15px;
  background: #fff;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  padding: 6px 0;
  text-align: center;
}

.content .right .z-side .card .card-body .column.column01 {
  background: #f9d040;
}

.content .right .z-side .card .card-body .column .icon {
  margin-right: 12px;
  margin-right: 4px;
}

.content .right .z-side .card .card-body .column .text {
  color: #0d3a64;
  font-size: 14px;
  text-transform: capitalize;
  line-height: 27px;
}

.content .right.active .card {
  overflow: inherit;
}

.content .right.active .card .text {
  display: none;
}

.content .right.active .card .collapse.show .text {
  display: inline-block;
}

.content .right.active .card .card-header {
  position: relative;
}

.content .right.active .card #collapseOne {
  position: absolute;
  right: 50px;
  top: 0;
}

.content .right.active .card #collapseOne.collapsing {
  display: none;
}

.content .right.active .card #collapseTwo {
  position: absolute;
  right: 50px;
  top: 0;
}

.content .right.active .card #collapseTwo.collapsing {
  display: none;
}

.content .right.active .card .new-text {
  display: block !important;
}

.content .right.active .z-side .card .card-header {
  padding: 6px 11px;
}

.content .right.active2 .card #collapseOne {
  position: inherit;
  right: inherit;
  top: inherit;
}

.content .left-nomatch {
  border: 6px solid #fff;
  background: #ececec;
  padding: 8px 7px;
  font-size: 12px;
}

.no-match {
  text-align: center;
  padding: 2rem 1rem 1rem;
  background: #c4c4c4;
}

.content .left #sidebar-left,
.content .right #sidebar-right {
  display: none;
}

.sport-list .group span.text-capitalize {
  color: #FFFFFF;
}

.sport-list .group.selected span.text-capitalize {
  color: #fff;
}

.banner .text {
  width: 100%;
}

.banner .text small {
  color: #d4b500;
  font-size: 10px;
}

.content .left .group .sport-type {}

.content .left .group .sport-type i,
.content .left .group .sport-type span {
  line-height: 20px;
}

.tab-p1 {
  padding: 1px;
}

.new-betslip {
  font-size: 13px;
  margin-bottom: 0;
  background: #FFFFFF;
}

.new-betslip:last-child {
  margin-bottom: 0;
}

.new-betslip .head {
  padding: 0;
  background: #DCE6EF;
  color: #0d3a64;
  height: 19px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-size: 11px;
}

.new-betslip.live {
  background: #FFF0EA;
}

.new-betslip.live .head {
  background: #F4D1C5;
  color: #5e4037;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.new-betslip .live .head .head-btn {
  background: #C35A3A;
}

.new-betslip .head .head-btn {
  cursor: pointer;
  height: 19px;
  width: 19px;
  padding: 0;
  color: #ffffff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 11px;
  background: #4A80AE;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-right: 1px solid #b7c8e2;
  text-shadow: 0 1px 1px #00000088;
}

.new-betslip .head .head-btn:hover {
  background: #395f83;
}

.new-betslip .head .head-text {
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  font-size: 11px;
  font-weight: 600;
  margin-top: 1px;
}

.new-betslip.live .head .head-btn,
.new-betslip .head .head-btn.head-btn-danger {
  color: #ffffff;
  background: #C35A3A;
}

.new-betslip.live .head .head-btn:hover,
.new-betslip .head .head-btn.head-btn-danger:hover {
  color: #ffffff;
  background: #bb4038;
}

.new-betslip .sport {
  text-align: center;
  background: grey;
  color: #efdd00;
}

.new-betslip .betslip-content {}

.new-betslip .betslip-content .sport-type {
  text-align: center;
}

.new-betslip .betslip-content .new-betdetail {
  border-radius: 0;
  border: 1px solid #6496db;
  text-align: center;
  padding: 3px 6px;
  background: #f6f6f6;
  -webkit-box-shadow: 0 0 1px rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.25);
}

.new-betslip.live .betslip-content .new-betdetail {
  border: 1px solid #C35A3A;
  background: #FBE9E1;
}

.new-betslip .betslip-content .new-betdetail .team {
  color: #01122b;
  font-size: 14px;
  font-weight: 700;
}

.new-betslip .betslip-content .new-betdetail .odds {
  font-size: 15px;
}

.new-betslip .betslip-content .new-betdetail .odds.red {
  color: #b53f39;
}

.new-betslip .betslip-content .new-betdetail .new-oddsdetail {}

.new-betslip .betslip-content .league {
  padding: 4px 0;
  font-size: 11px;
}

.new-betslip .betslip-content .team01 {
  color: #01122b;
  font-weight: 700;
}

.new-betslip .betslip-content .team02 {
  color: #01122b;
  font-weight: 700;
}

.new-betslip .betslip-content .red {
  color: #b53f39 !important;
  font-weight: 700;
}

.new-betslip .betslip-content .text-vs {
  color: #7c7c7c;
  margin: -2px auto;
  font-size: 10px;
}

.new-betslip .betslip-content .score {}

.new-betslip .stake {
  padding: 6px 8px;
}

.new-betslip .stake-field {
  padding: 6px 8px;
}

.new-betslip .stake-field label {
  line-height: 20px;
}

.new-betslip .stake-field .form-control {
  height: 24px;
  font-size: 12px;
}

.new-betslip .stake .btn {
  line-height: 1;
  font-size: 13px;
  padding: 6px 4px;
}

.new-betslip .stake .form-control-sm {
  line-height: 1;
  font-size: 13px;
  padding: 6px 4px;
}

.new-betslip .stake-field .form-control option {
  padding: 2px 4px;
}

.new-betslip .stake .btn-cancel {
  -webkit-box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset;
  box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset;
  background: #a4a4a4;
  border: 1px solid #848484;
  color: #ffffff;
  width: 80px;
}

.new-betslip .stake .btn-cancel:hover {
  background: #c4c4c4;
  -webkit-box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset, 0 0 3px #00000044;
  box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset, 0 0 3px #00000044;
}

.new-betslip .stake .btn-process {
  -webkit-box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset;
  box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset;
  background: #ffc107;
  border: 1px solid #b28705;
  font-weight: bold;
}

.new-betslip .stake .btn-process:hover {
  background: #ffd452;
  -webkit-box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset, 0 0 3px #00000044;
  box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset, 0 0 3px #00000044;
}

.new-livematch {
  background: #0d3a64;
  border: 1px solid rgba(255, 255, 255, 1);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.new-livematch .dropdown {
  font-size: 13px;
  line-height: 29px;
}

.live-tv,
.livematch {
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-league {
  cursor: pointer;
}

.btn-league.dropdown-toggle .text {
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 240px;
  display: inline-block;
  vertical-align: middle;
}

@media (max-width: 1340px) {
  .btn-league.dropdown-toggle .text {
    max-width: 132px;
  }
}

.new-livematch .d-league {
  border-radius: 0 0 0 0;
  width: 100%;
  padding: 0;
  max-height: 200px;
  overflow-y: auto;
  -webkit-transform: none !important;
  -ms-transform: none !important;
  transform: none !important;
  top: 27px !important;
}

.new-livematch .d-league .dropdown-item {
  padding: 4px 7px;
  font-size: 12px;
  word-wrap: break-word;
  width: 100%;
  white-space: normal;
  line-height: 16px;
  border-bottom: 1px #0f4f8c solid;
  background: #d5e0f0;
}

.new-livematch .d-league .dropdown-item .icon-league {
  margin-top: 2px;
  margin-right: 6px;
}

.new-livematch .d-league .dropdown-item:focus,
.new-livematch .d-league .dropdown-item:hover {
  color: #fff;
  background: #0f4f8c;
}

.blink_bg {
  animation: blinker 1s infinite;
  -webkit-animation: blinker 1s infinite;
  -moz-animation: blinker 1s infinite;
  -o-animation: blinker 1s infinite;
}

@keyframes blinker {
  0%, 49% {
    background: #f6f6f6;
  }
  50%, 100% {
    background: #fff;
  }
}

.big-inplay {
  font-size: 18px;
}

.big-soccer {
  color: #ffffffaa;
  font-size: 14px;
}

.content .right .z-side .card.some-border,
.some-border {
  border-top: 1px solid #4f7eab !important;
  border-left: 1px solid #38648d !important;
  border-right: 1px solid #2f669b !important;
  border-bottom: 1px solid #1b4c7c !important;
  -webkit-box-sizing: border-box !important;
  box-sizing: border-box !important;
  border-collapse: collapse !important;
  border-radius: 3px !important;
}

.hover-dark img {
  -webkit-filter: brightness(95%);
  filter: brightness(95%);
}

.hover-dark:hover img {
  -webkit-filter: brightness(100%);
  filter: brightness(100%);
}

/*
.bet-mmo .new-betslip {
	background: #aee4b3;
}
.bet-mmo .new-betslip .head {
	background: #b7c8e2;
}
.bet-mmo .new-betslip.live {
	background: #cbe4ae;
}
.bet-mmo .new-betslip.live .head {
	background: #ffaf96;
} */

.bet-mmo .new-betslip.live .betslip-content .new-betdetail {
  border: 1px solid #bad49c;
  background: #e3f3d0;
}

.bet-mmo .new-betslip .betslip-content .new-betdetail {
  border: 1px solid #b2cf90;
  background: #d1e7b7;
}