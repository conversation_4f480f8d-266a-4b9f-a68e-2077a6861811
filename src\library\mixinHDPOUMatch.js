export default {
  props: {
    // countKey: {
    //   type: Number
    // },
    length: {
      type: Number
    },
    items: {
      type: Array
    },
    leagueId: {
      type: Number
    },
    marketType: {
      type: String
    },
    sportsType: {
      type: Number
    },
    betType: {
      type: String
    },
    layoutIndex: {
      type: String
    }
  },
  data() {
    return {
      result: {}
    };
  },
  computed: {
    debug() {
      return config.debugMode;
    },
    uid() {
      return this.marketType + "-" + this.sportsType + "-" + this.betType + "-" + this.layoutIndex + "-" + this.leagueId;
    },
    selectedMatch() {
      return this.$store.getters.selectedMatch;
    }
  },
  methods: {
    more() {
      if (this.$store.getters.data.hasOwnProperty("more")) {
        if (this.$store.getters.data.more[this.layoutIndex] != null) {
          return this.$store.getters.data.more[this.layoutIndex];
        } else {
          return null;
        }
      }
      return null;
    },
  }
};
