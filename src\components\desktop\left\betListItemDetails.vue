<template lang="pug">
  div
    template(v-for="item in items")
      .bet-info.white.mb-0
        .bet-type.blue {{ sportsType[item.sports_type] }} - {{ betTypeDisplay(item)  }}
        .bet-detail.blue
          .name(v-if="['HDP', 'HDPH','OU', 'OUH'].includes(item.bet_type)") {{ betDisplay(item) }}
          .oddsdetail
            .d-flex.justify-content-start
              template(v-if="item.criteria2")
                //- MMO details
                .selector-name(v-if="item.ball_display && ['HDP', 'HDPH','OU', 'OUH'].includes(item.bet_type)")
                  | {{ ballDisplayMMO(item) }}({{ $numeral(item.criteria2).format("0") }})
                template(v-else)
                  .selector-name(v-if="item.ball_display") {{ ballDisplay(item) }}
                  .selector-name {{ betDisplay(item) }}
                  .selector-other @
                  .selector-odds.accent.small.flex-fill {{ item.odds_display }}
                .selector-score(v-if="item.market_type == 3") [{{ item.home_running_score }}-{{ item.away_running_score }}]
              template(v-else)
                .selector-name(v-if="item.ball_display && ['HDP', 'HDPH','OU', 'OUH'].includes(item.bet_type)") {{ ballDisplay(item) }}
                .selector-name(v-if="!['HDP', 'HDPH','OU', 'OUH'].includes(item.bet_type)") {{ betDisplay(item) }}
                .selector-score(v-if="item.market_type == 3") [{{ item.home_running_score }}-{{ item.away_running_score }}]
                .selector-other @
                .selector-odds.accent.small.flex-fill {{ item.odds_display }}
      .match-info.white
        span.name-home {{ getHomeTeam(item) }} &nbsp;
        span.name-away(v-if="item.bet_type != 'OR'") vs {{ getAwayTeam(item) }}
</template>

<script>
import naming from "@/library/_name";

export default {
  props: {
    selectedId: {
      type: Number,
    },
    items: {
      type: Array,
    },
  },
  computed: {
    sportsType() {
      return this.$store.state.layout.sports;
    },
    language() {
      return this.$store.getters.language;
    },
  },
  methods: {
    ballDisplayMMO(e) {
      return naming.ballDisplayMMO2(e, this);
    },
    getHomeTeam(e) {
      var r = e["home_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.home_team_name;
    },
    getAwayTeam(e) {
      var r = e["away_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.away_team_name;
    },
    getLeague(e) {
      var r = e["name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.league_name;
    },
    betType(e) {
      return this.$t("m.BT_" + e.bet_type);
    },
    betTypeDisplay(e) {
      return this.$t("m.BS_" + e.bet_type);
    },
    betDisplay(e) {
      return naming.betDisplay(e, this, this.language);
    },
    ballDisplay(e) {
      return naming.ballDisplay(e, this);
    },
    isBallDisplay(e) {
      var result = naming.ballDisplay(e, this);
      if (["HDP", "HDPH"].includes(e.bet_type)) {
        return result != null && result != "0";
      } else {
        return false;
      }
    },
  },
};
</script>
