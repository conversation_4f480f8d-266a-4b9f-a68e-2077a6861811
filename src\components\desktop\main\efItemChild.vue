<template lang="pug">
.game-row
  //- small {{ details }}
  .game-cell.game-cell-sm.w-40.justify-content-start
    img(:src="getImage()")
  .game-cell.game-cell-sm.flex-fill.justify-content-start
    .game-group
      .game-group-top {{ team[1] }}
  .game-cell.game-cell-sm.w-60.bg-01.ef-bet.ef-gh
    template(v-if="details['oxt'] != null && details['oxt'][0] != null")
      oddsItem(:odds="details['oxt'][0]" idx=5 :typ="oddsType" dataType="2")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-60.bg-01.ef-bet.ef-gh
    //- HOME+2
    template(v-if="details['tw'] != null && details['tw'][1] != null && details['tw'][1][5] != '' && details['tw'][1][5] != 0 && details['tw'][1][17] == 2")
      oddsItem(:odds="details['tw'][1]" idx=5 :typ="oddsType" dataType="2")
    template(v-else)
      template(v-if="details['tw'] != null && details['tw'][0] != null && details['tw'][0][5] != '' && details['tw'][0][5] != 0 && details['tw'][0][17] == 2")
        oddsItem(:odds="details['tw'][0]" idx=5 :typ="oddsType" dataType="2")
      template(v-else)
        .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-60.bg-01.ef-bet.ef-gh
    //- HOME+4
    template(v-if="details['tw'] != null && details['tw'][0] != null && details['tw'][0][5] != '' && details['tw'][0][5] != 0 && details['tw'][0][17] == 4")
      oddsItem(:odds="details['tw'][0]" idx=5 :typ="oddsType" dataType="2")
    template(v-else)
      template(v-if="details['tw'] != null && details['tw'][1] != null && details['tw'][1][5] != '' && details['tw'][1][5] != 0 && details['tw'][1][17] == 4")
        oddsItem(:odds="details['tw'][1]" idx=5 :typ="oddsType" dataType="2")
      template(v-else)
        .fad.fa-lock-alt.text-muted
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

export default {
  components: {
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem")
  },
  mixins: [mixinHDPOUOdds],
  props: {
    source: {
      type: Object
    },
    player: {
      type: Number
    }
  },
  computed: {
    team() {
      var n = this.source.homeTeam.split(" - ");
      return n;
    }
  },
  methods: {
    getImage() {
      return "images/esports/dog-number/dog" + String(this.team[0]) +".svg";
    }
  }
};
</script>
