# WBET Sportsbook Desktop Site

## Project Overview

This is a Vue.js-based Single Page Application (SPA) for a betting/gaming platform with the following features:

- **Sports Betting**: Supports various betting types including handicap, over/under, and moneyline.
- **Casino Games Integration**: Seamlessly integrates with various casino games, providing a unified gaming experience.
- **Tournament Functionality**: Allows users to participate in tournaments with real-time updates and rankings.
- **Multi-Language Support**: Offers internationalization to cater to users from different regions.
- **User Account Management**: Provides features for user registration, login, and account management.

## Tech Stack

- **Framework**: Vue.js 2.6.10 - A progressive JavaScript framework for building user interfaces.
- **Template Engine**: Pug - A high-performance template engine heavily influenced by Haml.
- **Styling**: SCSS - A preprocessor scripting language that is interpreted or compiled into Cascading Style Sheets (CSS).
- **State Management**: Vuex 3.1.1 - A state management pattern + library for Vue.js applications.
- **Internationalization**: Vue i18n 8.12.0 - Custom implementation to manage translations and locale-specific content.
- **HTTP Client**: Vue Resource + Custom XHR implementation for making API calls.
- **Date Handling**: Day.js with timezone support for managing and formatting dates.
- **Number Formatting**: Numeral.js for formatting numbers, especially useful for displaying odds and currency.
- **Form Validation**: Vuelidate for validating forms in a reactive way.
- **Build Tool**: Vue CLI 3.9.3 with Webpack 4
- **Package Manager**: Yarn for dependency management

## Project Structure

- `/src`: Main source code
  - `/components`: Reusable Vue components
    - `/desktop`: Desktop-specific components designed for larger screens.
      - `/info`: Information-related components (e.g., statements, bet lists).
      - `/left`: Left sidebar components (e.g., bet slip, bet info).
      - `/ui`: UI components like buttons, modals, and other reusable elements.
  - `/views`: Page components that represent different routes in the application.
    - `/desktop`: Desktop views tailored for larger displays.
    - `/game`: Game-related views for casino and other gaming experiences.
  - `/tournament2`: Tournament-related components and logic.
    - `/components`: Components specific to tournament functionality.
    - `/views`: Views related to tournament displays.
    - `/library`: Utilities and helper functions for tournament logic.
  - `/library`: Utility functions and services.
    - `/_xhr-*.js`: API service modules for different features, handling all HTTP requests.
    - `/_calculation.js`: Betting calculation utilities for odds and payouts.
    - `/_event-bus.js`: Event bus for cross-component communication, facilitating event handling.
    - `/_name.js`: Naming utilities for consistent naming conventions across the application.
  - `/locales`: Internationalization files containing translations for different languages.
    - Language files (e.g., en, id, jp, kr) organized by feature/module.
  - `/config.js`: Application configuration settings, including API endpoints and feature flags.
  - `/errors.js`: Error handling definitions, including standardized error codes and messages.
  - `/store`: Vuex store modules for managing application state.

## Coding Conventions

1. **Component Structure**:

   - Vue components use the Single File Component pattern, encapsulating template, script, and style in one file.
   - Template section uses Pug templating for cleaner HTML structure.
   - Script section follows standard Vue component structure, including lifecycle hooks.
   - Style section uses SCSS for modular and maintainable styles.

2. **Naming Conventions**:

   - Component files: `camelCase.vue` (e.g., `UserProfile.vue`).
   - Utility files: `_kebab-case.js` (with leading underscore) (e.g., `_api.js`).
   - Constants: `UPPER_CASE` (e.g., `API_URL`).
   - Methods: `camelCase` (e.g., `fetchData`).
   - Component props: `camelCase` with validation objects (e.g., `userId: { type: String, required: true }`).

3. **State Management**:

   - Use Vuex store for global state management, ensuring a single source of truth.
   - Component local state for component-specific data to avoid unnecessary reactivity.
   - EventBus for cross-component communication, allowing decoupled components to interact.

4. **Internationalization**:
   - Text strings are referenced via the `$t('key')` function for translation.
   - Language files organized by feature/module for easier management.
   - UI strings prefixed with 'ui.' (e.g., `ui.submitButton`) and message strings prefixed with 'message.' (e.g., `message.welcome`).

## Common Patterns

1. **API Calls**:

   - Custom XHR services in the `/library` folder organized by feature, ensuring modularity.
   - Services return promises with standardized feedback objects, including success and error states.
   - Loading states tracked in service objects to provide user feedback during API calls.
   - Error handling with standardized error codes from `errors.js`, allowing for consistent error management.

2. **Event Handling**:

   - Use EventBus for cross-component communication, allowing for a publish-subscribe model.
   - Standard Vue events for parent-child communication, ensuring clear data flow.
   - Event naming in UPPERCASE with underscores (e.g., `USER_LOGGED_IN`).

3. **Form Validation**:

   - Uses Vuelidate for form validation, providing a reactive approach to validation rules.
   - Feedback messages stored in component data for user-friendly error reporting.
   - Input validation with patterns and constraints to ensure data integrity.

4. **Modal Dialogs**:

   - Bootstrap-based modals triggered via jQuery (`$('#modal-id').modal('show')`).
   - Modal components with specific functionality, such as confirmation dialogs or information displays.

5. **Betting Logic**:

   - Complex calculations in dedicated utility files to separate business logic from UI components.
   - Odds formatting and stake input validation to ensure accurate betting information.
   - Bet type handling with standardized codes for consistency across the application.
   - Specialized components for different bet types (e.g., single, parlay) to enhance user experience.

6. **Game Integration**:

   - iFrame-based game embedding for seamless integration of external games.
   - Device detection for mobile/desktop experiences to optimize user interface.
   - Game provider-specific API integrations to support various gaming platforms.

7. **Tournament System**:
   - Room-based tournament structure to facilitate competitive play.
   - Player rankings and bet tracking to enhance user engagement.
   - Tournament-specific betting components to provide tailored experiences.

## Best Practices

1. **Internationalization**: Always use the `$t()` function for user-facing text to ensure translations are applied.
2. **API Calls**: Follow established patterns for API calls and error handling to maintain consistency.
3. **Component Structure**: Maintain separation between UI components and business logic for better maintainability.
4. **State Management**: Use Vuex for global state and local state for component-specific data to avoid conflicts.
5. **Performance**: Optimize component rendering and minimize unnecessary API calls to enhance user experience.

## Common Issues

1. **Browser Compatibility**: Ensure code works across supported browsers, testing on major browsers (Chrome, Firefox, Safari).
2. **Mobile Responsiveness**: Check both desktop and mobile layouts using responsive design techniques.
3. **API Error Handling**: Always handle API errors gracefully, providing user-friendly messages.
4. **State Management**: Be careful with state mutations to avoid unintended side effects.
5. **Performance**: Watch for unnecessary re-renders or heavy computations that could slow down the application.

## Build Process

- Use `yarn install` for dependencies to set up the project.
- Use `yarn run serve` for development, enabling hot-reloading for a better development experience.
- Use `yarn run build` for production builds, optimizing assets for deployment.
- Use `yarn run lint` for code linting to maintain code quality.

### Build Optimizations

The production build includes several optimizations:

- **Code Splitting**: Automatic vendor and common chunk splitting for better caching
- **CSS Extraction**: CSS is extracted into separate files for better performance
- **Source Maps**: Disabled in production for smaller bundle sizes
- **Tree Shaking**: Unused code is automatically removed from the final bundle
- **Bundle Optimization**: Vendor chunks are optimized for better caching and loading performance
- **Performance Monitoring**: Built-in performance monitoring for development builds

## Testing

- Write unit tests for critical components to ensure functionality.
- Write integration tests for complex interactions to verify component interactions.
- Use existing test frameworks like Jest or Cypress for testing.
- Test edge cases in betting calculations to ensure robustness.

## Deployment

- Use existing deployment processes for Vue.js applications, ensuring a smooth transition to production.
- Ensure all dependencies are properly versioned to avoid compatibility issues.
- Verify environment-specific configurations (e.g., API endpoints, feature flags) before deployment.

## Security

- Follow best practices for secure coding, including input validation and output encoding.
- Use existing security libraries to handle common vulnerabilities (e.g., XSS, CSRF).
- Implement proper session handling and validate all user inputs to protect against unauthorized access.

## Documentation

- Use JSDoc for documenting functions and components, ensuring clarity for future developers.
- Keep the README up-to-date with project details and instructions to assist new contributors.

## Code Review

- All code changes should be reviewed by at least one other team member to ensure quality.
- Follow existing code style and conventions to maintain consistency across the codebase.

## Version Control

- Use Git for version control, following best practices for commit messages and branching strategies.
- Follow existing branching and merging strategies to maintain a clean history.

## CI/CD

- Use existing CI/CD pipelines for automated testing and deployment, ensuring code quality before merging.
- Ensure tests pass before merging code to maintain stability in the main branch.

## Accessibility

- Ensure the application is accessible to all users, following WCAG guidelines.
- Use semantic HTML elements and provide appropriate ARIA attributes for assistive technologies.

## User Experience

- Ensure the application is intuitive and easy to use, following user experience best practices.
- Provide clear feedback for user actions, such as loading indicators and success messages.

## Update Browserlist

```
rmdir /s /q node_modules
del *.lock
yarn cache clean
yarn install
npx update-browserslist-db@latest
```

## Recent Codebase Improvements

### ✅ Completed Cleanup (Latest)
- **Dependency Cleanup**: Removed 9 unused npm packages including `braces`, `browserslist`, `css-vars-ponyfill`, `serialize-javascript`, `stylus`, `stylus-loader`, `url-loader`, `vue-virtual-scroll-list`, and `vue2-perfect-scrollbar`
- **Code Quality**: Cleaned up 50+ commented import statements and console.log statements
- **Import Standardization**: Converted `require()` statements to ES6 imports for better tree-shaking
- **Bundle Optimization**: Estimated 15-20% reduction in bundle size and 10-15% faster build times
- **Build Verification**: All changes tested and verified to maintain functionality

### 🔧 Performance Enhancements
- **Modern JavaScript**: Implemented ES6 import patterns for better tree-shaking
- **Code Splitting**: Optimized vendor and common chunk splitting
- **Lazy Loading**: Components are properly lazy-loaded with dynamic imports
- **Performance Monitoring**: Added development-time performance monitoring

## Roadmap

- **Vue 3 Migration**: The project is planned to migrate from Vue 2.6.10 to Vue 3, adopting the Composition API, Vite for build tooling, Pinia for state management, and TypeScript for type safety.
- **AI Integration**: Planned integration with OpenAI API and custom ML models for smart betting recommendations, user behavior analysis, and odds prediction.
- **UI/UX Enhancements**: Ongoing improvements for accessibility, responsiveness, and user experience.
- **Performance Optimizations**: Further code splitting, virtual scrolling for large lists, and runtime optimizations.
- **Testing Improvements**: Expanded use of Jest and Cypress for unit and E2E testing.
- **Component Refactoring**: Break down large components (like mainPanel.vue) into smaller, more maintainable pieces.

## Getting Started (Onboarding)

1. **Clone the repository:**
   ```sh
   git clone <repo-url>
   cd member-spa
   ```
2. **Install dependencies:**
   ```sh
   yarn install
   ```
3. **Set up environment variables:**
   - Copy `.env.example` to `.env.local` (if available) and fill in the required values, or create `.env.local` manually as described above.
4. **Run the development server:**
   ```sh
   yarn run serve
   ```
5. **Build for production:**
   ```sh
   yarn run build
   ```
6. **Run tests:**
   ```sh
   yarn run test
   # or for E2E
   yarn run cypress:open
   ```

## Contributing

- Fork the repository and create a feature branch.
- Follow the coding conventions and commit message guidelines.
- Ensure all tests pass before submitting a pull request.
- All code changes should be reviewed by at least one other team member.
- For major changes, please open an issue first to discuss what you would like to change.

## Troubleshooting

- **Install issues:**
   - Delete `node_modules` and run `yarn install` again.
   - Clear yarn cache: `yarn cache clean`.
   - Use the browserlist update commands if needed: `npx update-browserslist-db@latest`
- **API errors:**
   - Check your `.env.local` configuration and API endpoints.
   - Ensure your network connection is stable.
- **Browser issues:**
   - Clear browser cache and cookies.
   - Try a hard refresh (Ctrl+F5).
- **Build errors:**
   - Ensure Node.js and Yarn are up to date.
   - Check for missing or incompatible dependencies.
   - Verify that all dependencies are properly installed after recent cleanup
- **Performance issues:**
   - Check the browser console for performance warnings
   - Monitor bundle sizes in the build output
   - Use the built-in performance monitoring in development mode

## FAQ

- **Q: Why can't I log in?**
   - A: Check your credentials, ensure your account is active, and verify API endpoints in `.env.local`.
- **Q: How do I update browser compatibility data?**
   - A: Run the commands in the 'Update Browserlist' section above.
- **Q: How do I add a new language?**
   - A: Add translation files in `src/locales/<lang>/` and update the i18n configuration.
- **Q: What dependencies were removed in the recent cleanup?**
   - A: 9 unused packages were removed including `braces`, `browserslist`, `css-vars-ponyfill`, `serialize-javascript`, `stylus`, `stylus-loader`, `url-loader`, `vue-virtual-scroll-list`, and `vue2-perfect-scrollbar`.
- **Q: How do I check if the cleanup affected my build?**
   - A: Run `yarn build` and verify that the build completes successfully. The bundle size should be smaller than before.
- **Q: What if I need one of the removed dependencies?**
   - A: You can reinstall any dependency using `yarn add <package-name>` if needed for future development.

## Changelog

### Latest Changes (Recent Cleanup)
- **Dependency Cleanup**: Removed 9 unused npm packages to reduce bundle size
- **Code Quality**: Cleaned up commented imports and console.log statements
- **Import Standardization**: Modernized import patterns for better tree-shaking
- **Performance**: Added development-time performance monitoring
- **Build Optimization**: Improved vendor chunk splitting and caching

### Previous Changes
- See `future/FUTURE.md` for upcoming migration and AI integration plans.
- For detailed changes, refer to the project's commit history or pull requests.
