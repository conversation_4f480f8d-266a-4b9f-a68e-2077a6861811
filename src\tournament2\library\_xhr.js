import config from "@/config";
import errors from "@/errors";
import Vue from "vue";

export default {
  loading: {
    getRoomInfo: false,
    getRoomList: false,
    getLeagueList: false,
    getMatchList: false,
    getMatchLookup: false,
    roomCreate: false,
    roomJoin: false,
    memberBetList: false,
    bet: false,
    betResult: false,
    roomRank: false,
    validateTournament: false,
  },
  validateTournament(args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "validateTournament",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (canRequest == true) {
        this.loading.validateTournament = true;
        Vue.http.post(config.memberValidateTournamentUrl(), args).then(
          (res) => {
            this.loading.validateTournament = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.validateTournament = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getRoomInfo(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getRoomInfo",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_id" in args) || args.room_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.getRoomInfo = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.getRoomInfo = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.getRoomInfo = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getRoomList(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getRoomList",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_type" in args) || args.room_type == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (canRequest == true) {
        this.loading.getRoomList = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.getRoomList = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.getRoomList = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getLeagueList(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getLeagueList",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_id" in args) || args.room_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (canRequest == true) {
        this.loading.getLeagueList = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.getLeagueList = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.getLeagueList = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getMatchList(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getMatchList",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_id" in args) || args.room_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("league_id" in args) || args.league_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (canRequest == true) {
        this.loading.getMatchList = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.getMatchList = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.getMatchList = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getMatchLookup(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getMatchLookup",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("sports_type" in args) || args.sports_type == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("match_date" in args) || args.match_date == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (canRequest == true) {
        this.loading.getMatchLookup = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.getMatchLookup = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.getMatchLookup = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  roomCreate(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "roomCreate",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_type" in args) || args.room_type == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_rate" in args) || args.room_rate == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_limit" in args) || args.room_limit == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("match" in args) || args.match == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_pwd" in args || args.room_pwd == null)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (canRequest == true) {
        this.loading.roomCreate = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.roomCreate = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.roomCreate = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  roomJoin(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "roomJoin",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_id" in args) || args.room_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_type" in args) || args.room_type == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_pwd" in args || args.room_pwd == null)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (canRequest == true) {
        this.loading.roomJoin = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.roomJoin = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.roomJoin = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  memberBetList(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "memberBetList",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_id" in args) || args.room_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (canRequest == true) {
        this.loading.memberBetList = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.memberBetList = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.memberBetList = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  bet(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "bet",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_id" in args) || args.room_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("bet_member" in args) || args.bet_member == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("accept_better_odds" in args) || args.accept_better_odds == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (canRequest == true) {
        this.loading.bet = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.bet = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.bet = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  betResult(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "betResult",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_id" in args) || args.room_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (canRequest == true) {
        this.loading.betResult = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.betResult = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.betResult = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  roomRank(url, args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "roomRank",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || args.account_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || args.session_token == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("room_id" in args) || args.room_id == null) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (canRequest == true) {
        this.loading.roomRank = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.roomRank = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data;
                  resolve(feedback);
                } catch (error) {
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.roomRank = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
};
