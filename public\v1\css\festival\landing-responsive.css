
@media screen and (min-width: 970px) {
    .landing2 .container{
        max-width: 1200px;
    }  
  .landing2 .switch-device-mobile {
    display: block;
  }

  .landing2 .switch-device-desktop {
    display: none;
  }

  .landing2 .swiper-slide h1 {
    font-size: 2rem;
    padding: 0px;
  }

  .landing2 .banner-desktop {
    display: block;
  }

  .landing2 .banner-mobile {
    display: none;
  }

  .landing2 .swiper-slide-active .banner-text {
    animation-name: fadeInUp;
    -webkit-animation-name: fadeInUp;
    animation-duration: 1s;
    animation-fill-mode: both;
    -webkit-animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    opacity: 0;
  }

  @keyframes fadeInUp {
    from {
      top: 60%;
      opacity: 0;
      transform: translateY(-50%);
    }

    to {
      top: 50%;
      opacity: 1;
      transform: translateY(-50%);
    }
  }

  @-webkit-keyframes fadeInUp {
    from {
      top: 60%;
      opacity: 0;
      transform: translateY(-50%);
    }

    to {
      top: 50%;
      opacity: 1;
      transform: translateY(-50%);
    }
  }

  .landing2 .swiper-slide .banner-text {
    position: absolute;
    width: 30%;
    top: 50%;
    transform: translateY(-50%);
    left: auto;
    right: 0;
    text-align: left;
    opacity: 0;
    padding-right: 50px;
  }

  .landing2 .swiper-horizontal>.swiper-pagination-bullets,
  .landing2 .swiper-pagination-bullets.swiper-pagination-horizontal,
  .landing2 .swiper-pagination-custom,
  .landing2 .swiper-pagination-fraction {
    bottom: 50px;
    left: auto;
    width: 30%;
    text-align: left;
    right: 0;
  }

  .landing2 .swiper-slide p {
    font-size: 0.8rem;
    padding: 0;
  }

  .landing2 .break {
    display: block;
  }

  .landing2 .mobile-login {
    display: none;
  }

  .landing2 .login-group {
    display: flex;
  }

  .landing2 .login-group .invalid-feedback {
    margin-bottom: 0px;
    position: absolute;
    width: 100%;
    text-align: right;
    top: -20px;
    color: #ffee00;
    font-size: 11px;
  }

  .landing2 .event-slider-wrap {
    display: block;
  }

  .landing2 .widget-wrap {
    padding: 30px 0px 30px 0px;
  }

  .landing2 .footer-wrapper {
    display: block;
  }
}

@media screen and (min-width: 1028px) {
  .landing2 .logo {
    width: 165px;
    margin-left: 0;
  }

  .landing2 .header.small-top .logo {
    width: 120px;
    transition: all 0.3s ease-in-out;
  }

  .landing2 .right-wrapper .line {
    height: 30px;
  }

  .landing2 .btn-select,
  .landing2 .btn-switch-device {
    width: 40px;
    height: 40px;
    line-height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.5);
  }

  .landing2 .header.small-top .btn-select,
  .landing2 .header.small-top .btn-switch-device {
    width: 32px;
    height: 32px;
    line-height: 32px;
    transition: all 0.3s ease-in-out;
  }

  .landing2 .header.small-top .switch-device-mobile {
    font-size: 16px;
    transition: all 0.3s ease-in-out;
  }

  .landing2 .header.small-top .btn-select img {
    width: 20px;
    transition: all 0.3s ease-in-out;
  }

  .landing2 .header.small-top .login-btn {
    height: 32px;
    line-height: 32px;
    padding: 0 1rem;
    transition: all 0.3s ease-in-out;
  }

  .landing2 .header.small-top .form-control {
    padding: 0.2rem 0.75rem;
    line-height: 1.3;
    height: 32px;
    transition: all 0.3s ease-in-out;
  }

  .landing2 .swiper-slide h1 {
    font-size: 2.5rem;
    padding: 0px;
  }

  .landing2 .swiper-slide .banner-text {
    width: 28%;
  }

  .landing2 .swiper-horizontal>.swiper-pagination-bullets,
  .landing2 .swiper-pagination-bullets.swiper-pagination-horizontal,
  .landing2 .swiper-pagination-custom,
  .landing2 .swiper-pagination-fraction {
    bottom: 40px;
    left: auto;
    width: 28%;
    text-align: left;
    right: 0;
  }

  .landing2 .swiper-slide p {
    font-size: 1rem;
    padding: 0;
  }

  .landing2 .widget-wrap .widget-content-wrapper .left {
    width: 30%;
  }

  .landing2 .widget-wrap .widget-content-wrapper .right {
    width: 70%;
  }
}

@media screen and (min-width: 1400px) {

  .landing2 .swiper-horizontal>.swiper-pagination-bullets,
  .landing2 .swiper-pagination-bullets.swiper-pagination-horizontal,
  .landing2 .swiper-pagination-custom,
  .landing2 .swiper-pagination-fraction {
    bottom: 50px;
  }
}
@media screen and (min-width: 1800px) {
  .landing2 .container{
    max-width: 1400px;
  }
  .landing2 .event-slider-wrap .container {
    max-width: 1400px;
}  
}