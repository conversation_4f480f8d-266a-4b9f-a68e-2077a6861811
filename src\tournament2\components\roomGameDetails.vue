<template lang="pug">
.tournament-pool-body
  .tournament-details
    .tournament-details-row.winning-row
      .tournament-details-content
        .tournament-details-single
          .tournament-details-icon
            img(src="img/tn/icon-dollar.svg")
          .tournament-details-text
            .tournament-details-top {{ $t("ui.total_winning") }}
            .tournament-details-bottom {{ roomData.pool }}
        .tournament-details-single
          .tournament-details-icon
            i.fas.fa-users
          .tournament-details-text
            .tournament-details-top  {{ $t("ui.players") }}
            .tournament-details-bottom &nbsp;{{ roomData.room_count }} / {{ roomData.room_limit }}&nbsp;
        .tournament-details-single
          .tournament-details-icon
            i.fas.fa-calendar-alt
          .tournament-details-text
            .tournament-details-top  {{ $t("ui.matches") }}
            .tournament-details-bottom {{ roomData.total_match }}
        .tournament-details-single
          .tournament-details-icon
            img(src="img/tn/icon-point.svg")
          .tournament-details-text
            .tournament-details-top {{ $t("ui.entry_fee") }}
            .tournament-details-bottom(v-if="roomData.room_rate > 0") {{ roomData.room_rate }}
            .tournament-details-bottom(v-else) {{ $t("ui.free_room") }}
    .tournament-details-row
      .tournament-details-content
        .tournament-details-left
          .icon-trophy
            img(src="img/tn/trophy1.svg")
          .tournament-position {{ $t("ui.first_prize") }}
        .tournament-details-right {{ roomData.prize1 }}
    template(v-if="roomData.formula == 1")
      .tournament-details-row
        .tournament-details-content
          .tournament-details-left
            .icon-trophy
              img(src="img/tn/trophy2.svg")
            .tournament-position {{ $t("ui.second_prize") }}
          .tournament-details-right {{ roomData.prize2 }}
      .tournament-details-row
        .tournament-details-content
          .tournament-details-left
            .icon-trophy
              img(src="img/tn/trophy3.svg")
            .tournament-position {{ $t("ui.third_prize") }}
          .tournament-details-right {{ roomData.prize3 }}
</template>

<script>
import config from "@/config";
import errors from "@/errors";
// import { EventBus } from "@/library/_event-bus.js";
// import service from "@/tournament2/library/_xhr.js";
// import calc from "@/library/_calculation.js";
// import sync2 from "@/library/_sync-match";
// import xhrMatch from "@/library/_xhr-match";

export default {
  props: {
    roomData: {
      type: Object,
    },
  },
};
</script>
