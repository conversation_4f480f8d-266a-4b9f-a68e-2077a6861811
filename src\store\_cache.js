// import Vue from "vue";
import sync1 from "@/library/_sync-market";
import store from "@/store";
import dayjs from "dayjs-ext";

function difference(a1, a2) {
  var result = [];
  for (var i = 0; i < a1.length; i++) {
    if (a2.indexOf(a1[i]) === -1) {
      result.push(a1[i]);
    }
  }
  return result;
}

const Vue = {
  set(e1, e2, e3) {
    e1[e2] = e3;
  },
  delete(e1, e2) {
    delete e1[e2];
  },
};

export default {
  namespaced: true,
  state: {
    league: {},
    match: {},
    child: {},
    head: {},
    live: {},
    today: {},
    early: {},
    parent: {},
    group: {},
    odds: {},
    mmo: {},
    mmoIndex: [],
    more: {},
    moremmo: {},
    ln: [],
    options: {},
    isFilterTime: true,
    filterMode: 0,
    isFilterLive: false,
    parlayMode: 2,
  },
  // 0 - All
  // 9 - Next 2 Hours
  // 1 - Next 4 Hours
  // 2 - Next 8 Hours
  // 3 - Now ~ 00:00
  // 4 - 00:00 ~ 04:00 (from 12pm every 4 hours)
  // 5 - 04:00 ~ 12:00 (from 4am to 12pm due to less matches)
  mutations: {
    updateParlayMode(state, payload) {
      state.parlayMode = payload;
    },
    updateFilterMode(state, payload) {
      // console.log(new Date().getDay());
      state.filterMode = payload;
    },
    updateFilterLive(state, payload) {
      state.isFilterLive = payload;
    },
    updateGroup(state, payload) {
      // console.time("updateGroup");
      try {
        var bymarket = state.options.bymarket;
        var bytime = state.options.bytime;
        var t = state.options.t;
        var bypass = state.options.bypass;

        // var menu = state.options.menu;
        var menu = {
          menu0: store.state.layout.menu0,
          menu1: store.state.layout.menu1,
          menu2: store.state.layout.menu2,
          menu3: store.state.layout.menu3,
        };

        // console.log(menu);

        var mcn1 = ["match_id", "sports_type", "market_type", "league_id", "total", "parlay", "live"];
        var mcn2 = ["hdp", "ou", "oe", "oxt", "ml", "tw", "cs", "dc", "fglg", "htft", "tg", "orz"];
        var mcn3 = ["oe", "tw", "cs", "dc", "fglg", "htft", "tg", "orz"];

        var kk = [];

        state.isFilterTime = true;
        if (menu.menu0 != "all") {
          state.isFilterTime = false;
        }
        if (![1].includes(menu.menu2)) {
          state.isFilterTime = false;
        }
        if (menu.menu1 != "today") {
          state.isFilterTime = false;
        }
        if (menu.menu3 == "orz") {
          state.isFilterTime = false;
        }

        for (var n in state.head) {
          for (var k in n) {
            if (state.head[n][k]) {
              var llnn = state.league[state.head[n][k][3]];
              // filter market types
              var proc = true;
              if (llnn != null) {
                switch (bymarket) {
                  case "1":
                    // All Markets
                    // this setting turn on only main and corners showing
                    if (llnn[8] != null) {
                      // console.log(llnn, llnn[5], llnn[8]);
                      proc = llnn[5] == 1 || llnn[8] == 25;
                    }
                    break;
                  case "2":
                    // Main Markets
                    proc = llnn[5] == 1;
                    break;
                  case "3":
                    // Others Markets
                    proc = llnn[5] == 0;
                    break;
                }
              } else {
                proc = false;
              }

              if (proc == true || !["hdpou", "parlay", "live"].includes(t)) {
                var mmm = {
                  match_id: state.head[n][k][0],
                  sports_type: state.head[n][k][1],
                  market_type: state.head[n][k][2],
                  league_id: state.head[n][k][3],
                  total: state.head[n][k][4],
                  parlay: state.head[n][k][5],
                  live: state.head[n][k][6],
                  more: state.head[n][k][7],
                  moremore: state.head[n][k][8],
                  hdp: state.head[n][k][9],
                  ou: state.head[n][k][10],
                  oe: state.head[n][k][11],
                  oxt: state.head[n][k][12],
                  ml: state.head[n][k][13],
                  tw: state.head[n][k][14],
                  cs: state.head[n][k][15],
                  dc: state.head[n][k][16],
                  fglg: state.head[n][k][17],
                  htft: state.head[n][k][18],
                  tg: state.head[n][k][19],
                  orz: state.head[n][k][20],
                };

                var isFit = false;
                var layoutType = t;

                if (bypass) {
                  if (mmm["hdp"] > 0 || mmm["ou"] > 0 || mmm["oxt"] > 0 || mmm["ml"] > 0 || mmm["oe"] > 0) {
                    isFit = true;
                    layoutType = "hdpou";
                  } else {
                    for (var bt1 in mcn3) {
                      // console.log(bt1)
                      if (mmm[mcn3[bt1]] > 0) {
                        isFit = true;
                        layoutType = mcn3[bt1];
                        break;
                      }
                    }
                  }
                } else {
                  switch (layoutType) {
                    case "hdpou":
                      if (mmm["hdp"] > 0 || mmm["ou"] > 0 || mmm["oxt"] > 0 || mmm["ml"] > 0 || mmm["oe"] > 0) {
                        isFit = true;
                        layoutType = "hdpou";
                      }
                      break;
                    case "parlay":
                    case "live":
                      if (mmm["hdp"] > 0 || mmm["ou"] > 0 || mmm["oxt"] > 0 || mmm["ml"] > 0 || mmm["oe"] > 0) {
                        isFit = true;
                        layoutType = "hdpou";
                      } else {
                        for (var bt in mcn3) {
                          // console.log(bt)
                          if (mmm[mcn3[bt]] > 0) {
                            isFit = true;
                            layoutType = mcn3[bt];
                            break;
                          }
                        }
                      }
                      break;
                    default:
                      if (mmm[t] > 0) {
                        isFit = true;
                        layoutType = t;
                      }
                      break;
                  }
                }

                if (bypass) {
                  if (layoutType == "hdpou") {
                    for (var bt2 in mcn3) {
                      if (mmm[mcn3[bt2]] > 0) {
                        break;
                      }
                    }
                  } else {
                    var mcn4 = [];
                    for (var i in mcn2) {
                      // console.log(i)
                      if (mcn2[i] != layoutType) {
                        mcn4.push(mcn2[i]);
                      }
                    }
                    for (var bt3 in mcn4) {
                      if (mmm[mcn4[bt3]] > 0) {
                        break;
                      }
                    }
                  }
                } else {
                  var mcn41 = [];
                  for (var i1 in mcn2) {
                    // console.log(mcn2[i1])
                    if (mcn2[i1] != layoutType) {
                      mcn41.push(mcn2[i1]);
                    }
                  }
                  for (var bt4 in mcn41) {
                    if (mmm[mcn41[bt4]] > 0) {
                      break;
                    }
                  }
                  // console.log(mmm)
                }

                if (isFit) {
                  var kkl = parseInt(mmm["league_id"]);
                  var kkm = parseInt(mmm["match_id"]);
                  if (state.league && state.league[kkl]) {
                    var kkms = 0;
                    if (state.match && state.match[kkm]) {
                      var ch = state.match[kkm][13];

                      if (mmm["market_type"] == 3) {
                        kkms = parseInt(state.match[kkm][10]);
                      } else {
                        kkms = parseInt(state.match[kkm][9]);
                      }
                      var kkls = parseInt(state.league[kkl][3]);
                      if (state.match[kkm][26] != null) {
                        kkls = parseInt(state.match[kkm][26]);
                      }
                      var kkmwd = state.match[kkm][7];
                      var kkmmt = state.match[kkm][8];

                      var isFilterLivePass = true;
                      if (state.isFilterLive == true) {
                        if (ch == null || ch == "") {
                          isFilterLivePass = false;
                        }
                      }

                      if (isFilterLivePass) {
                        if (state.isFilterTime == false) {
                          kk.push([kkl, kkls, kkm, kkms, mmm["sports_type"], mmm["market_type"], t, layoutType, kkmwd, kkmmt]);
                        } else {
                          var isFilterTimePass = false;
                          var t1 = new Date(kkmmt + "+08:00");
                          var t2 = new Date();
                          var t3 = (t1 - t2) / 60000;
                          // console.log(state.match[kkm][1], kkmmt, t1, t2, t3);
                          var t4 = new Date(t2.getTime() + 86400000);
                          var td = dayjs(t4).format("YYYY-MM-DD");
                          var t5 = new Date(td + "T00:00:00+08:00");
                          var t6 = new Date(td + "T04:00:00+08:00");
                          var t7 = new Date(td + "T12:00:00+08:00");

                          if (mmm["market_type"] == 3) {
                            isFilterTimePass = true;
                          } else {
                            switch (state.filterMode) {
                              case 0:
                                isFilterTimePass = true;
                                break;
                              case 9:
                                if (t3 < 120) {
                                  isFilterTimePass = true;
                                }
                                break;
                              case 1:
                                if (t3 < 240) {
                                  isFilterTimePass = true;
                                }
                                break;
                              case 2:
                                if (t3 < 480) {
                                  isFilterTimePass = true;
                                }
                                break;
                              case 3:
                                if (t1 <= t5) {
                                  isFilterTimePass = true;
                                }
                                break;
                              case 4:
                                if (t1 < t2 || (t1 >= t5 && t1 <= t6)) {
                                  isFilterTimePass = true;
                                }
                                break;
                              case 5:
                                if (t1 < t2 || (t1 >= t6 && t1 <= t7)) {
                                  isFilterTimePass = true;
                                }
                                break;
                            }
                          }

                          if (isFilterTimePass == true) {
                            kk.push([kkl, kkls, kkm, kkms, mmm["sports_type"], mmm["market_type"], t, layoutType, kkmwd, kkmmt]);
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }

        // group by market
        var jj = {};
        var layout = {};
        for (var jji in kk) {
          if (jj[kk[jji][5]] == null) {
            jj[kk[jji][5]] = [];
          }
          jj[kk[jji][5]].push(kk[jji]);

          if (layout[kk[jji][4]] == null) {
            layout[kk[jji][4]] = [];
          }
          layout[kk[jji][4]].push(kk[jji]);
        }

        for (var nn in layout) {
          var jjjpp = {};
          for (var ji1 in layout[nn]) {
            if (jjjpp[layout[nn][ji1][5]] == null) {
              jjjpp[layout[nn][ji1][5]] = [];
            }
            jjjpp[layout[nn][ji1][5]].push(layout[nn][ji1]);
          }

          for (var nnn in jjjpp) {
            var jjjjpp = {};
            for (var ji12 in jjjpp[nnn]) {
              if (jjjjpp[jjjpp[nnn][ji12][7]] == null) {
                jjjjpp[jjjpp[nnn][ji12][7]] = [];
              }
              jjjjpp[jjjpp[nnn][ji12][7]].push(jjjpp[nnn][ji12]);
            }
            jjjpp[nnn] = jjjjpp;
          }
          layout[nn] = jjjpp;
        }

        var group = sync1.grouping(layout, state.child, bytime);

        var x1 = Object.keys(state.group);
        if (x1.length > 0) {
          var x2 = Object.keys(group);
          var x3 = difference(x1, x2);
          for (var n0 in x3) {
            Vue.delete(state.group, x3[n0]);
          }
          var x4 = Object.keys(state.group);
          for (var x41 in x4) {
            var x41n = x4[x41];
            var x5 = Object.keys(state.group[x41n]);
            if (x5.length > 0) {
              var x6 = Object.keys(group[x41n]);
              var x7 = difference(x5, x6);
              for (var n1 in x7) {
                Vue.delete(state.group[x41n], x7[n1]);
              }
            }
          }
        }

        for (var nn2 in group) {
          if (!state.group.hasOwnProperty(nn2)) {
            Vue.set(state.group, nn2, {});
          }
          for (var nnn0 in group[nn2]) {
            Vue.set(state.group[nn2], nnn0, group[nn2][nnn0]);
          }
        }
      } catch (ex) {
        console.warn(ex);
      }

      // console.timeEnd("updateGroup");
    },
    removeData(state, payload) {
      state.league = {};
      state.match = {};
      state.child = {};
      state.head = {};
      state.live = {};
      state.today = {};
      state.early = {};
      state.parent = {};
      state.group = {};
      state.odds = {};
      state.mmo = {};
      state.mmoIndex = [];
      state.more = {};
      state.moremmo = {};
      state.ln = [];
    },
    removePartialData(state, payload) {
      // console.warn("removePartialData", payload);
      try {
        var mid = null;
        var tokens = payload[2].split("|");
        for (var t in tokens) {
          switch (tokens[t]) {
            case "1":
              mid = "early";
              break;
            case "2":
              mid = "today";
              break;
            case "3":
              mid = "live";
              break;
            default:
              mid = null;
              break;
          }

          if (mid != null) {
            if (state.hasOwnProperty(mid)) {
              var x1 = Object.keys(state[mid]);
              if (x1.length > 0) {
                for (var n in x1) {
                  var nid = x1[n];
                  Vue.delete(state[mid], nid);
                  for (var x4 in state.child[nid]) {
                    Vue.delete(state.parent, state.child[nid][x4][0]);
                  }
                  Vue.delete(state.child, nid);
                  Vue.delete(state.parent, nid);
                  Vue.delete(state.head, nid);
                  Vue.delete(state.match, nid);
                  for (var iii in state.odds) {
                    for (var jjj in state.odds[iii]) {
                      Vue.delete(state.odds[iii][jjj], nid);
                    }
                  }

                  if (store.getters.mmoMode) {
                    for (var iii1 in state.mmo) {
                      for (var jjj1 in state.mmo[iii1]) {
                        Vue.delete(state.mmo[iii1][jjj1], nid);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      } catch (ex) {
        console.warn(ex);
      }
    },
    updateData(state, payload) {
      // console.warn("updateData", payload);
      var mkts = ["live", "today", "early"];
      try {
        for (var mkt in mkts) {
          var mid = mkts[mkt];
          if (payload.hasOwnProperty(mid)) {
            var x1 = Object.keys(state[mid]);
            if (x1.length > 0) {
              var x2 = Object.keys(payload[mid]);
              var x3 = difference(x1, x2);
              for (var n in x3) {
                var nid = x3[n];
                Vue.delete(state[mid], nid);
                for (var x4 in state.child[nid]) {
                  Vue.delete(state.parent, state.child[nid][x4][0]);
                }
                Vue.delete(state.child, nid);
                Vue.delete(state.parent, nid);
                Vue.delete(state.head, nid);
                Vue.delete(state.match, nid);
                for (var iii in state.odds) {
                  for (var jjj in state.odds[iii]) {
                    Vue.delete(state.odds[iii][jjj], nid);
                  }
                }

                if (store.getters.mmoMode) {
                  for (var iii1 in state.mmo) {
                    for (var jjj1 in state.mmo[iii1]) {
                      Vue.delete(state.mmo[iii1][jjj1], nid);
                    }
                  }
                }
              }
            }
          } else {
            if (state.hasOwnProperty(mid)) {
              var x12 = Object.keys(state[mid]);
              if (x12.length > 0) {
                for (var n9 in x12) {
                  var nid1 = x12[n9];
                  Vue.delete(state[mid], nid1);
                  for (var x44 in state.child[nid1]) {
                    Vue.delete(state.parent, state.child[nid1][x44][0]);
                  }
                  Vue.delete(state.child, nid1);
                  Vue.delete(state.parent, nid1);
                  Vue.delete(state.head, nid1);
                  Vue.delete(state.match, nid1);
                  for (var iii12 in state.odds) {
                    for (var jjj12 in state.odds[iii12]) {
                      Vue.delete(state.odds[iii12][jjj12], nid1);
                    }
                  }

                  if (store.getters.mmoMode) {
                    for (var iii11 in state.mmo) {
                      for (var jjj11 in state.mmo[iii11]) {
                        Vue.delete(state.mmo[iii11][jjj11], nid1);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      } catch (ex) {
        console.warn(ex);
      }

      for (var n8 in payload) {
        switch (n8) {
          case "ln":
            try {
              Vue.set(state, n8, payload[n8]);
            } catch (ex) {
              console.warn(ex);
            }
            break;
          case "early":
          case "live":
          case "today":
          case "league":
          case "parent":
          case "head":
          case "child":
          case "match":
          case "odds":
            try {
              for (var nn in payload[n8]) {
                Vue.set(state[n8], nn, payload[n8][nn]);
              }
            } catch (ex) {
              console.warn(ex);
            }
            break;
          case "mmo":
            // console.log("mmo", payload);
            if (store.getters.mmoMode) {
              try {
                for (var nn1 in payload[n8]) {
                  Vue.set(state[n8], nn1, payload[n8][nn1]);
                }
              } catch (ex) {
                console.warn(ex);
              }
            }
            break;
          case "options":
            try {
              Vue.set(state, n8, payload[n8]);
            } catch (ex) {
              console.warn(ex);
            }
            break;
        }
      }
    },
    updatePartialData(state, payload) {
      // console.warn("updatePartialData", payload);
      var mkts = ["live", "today", "early"];
      try {
        for (var mkt in mkts) {
          var mid = mkts[mkt];
          if (payload.hasOwnProperty(mid)) {
            var x1 = Object.keys(state[mid]);
            if (x1.length > 0) {
              var x2 = Object.keys(payload[mid]);
              var x3 = difference(x1, x2);
              for (var n in x3) {
                var nid = x3[n];
                Vue.delete(state[mid], nid);
                for (var x4 in state.child[nid]) {
                  Vue.delete(state.parent, state.child[nid][x4][0]);
                }
                Vue.delete(state.child, nid);
                Vue.delete(state.parent, nid);
                Vue.delete(state.head, nid);
                Vue.delete(state.match, nid);
                for (var iii in state.odds) {
                  for (var jjj in state.odds[iii]) {
                    Vue.delete(state.odds[iii][jjj], nid);
                  }
                }

                if (store.getters.mmoMode) {
                  for (var iii1 in state.mmo) {
                    for (var jjj1 in state.mmo[iii1]) {
                      Vue.delete(state.mmo[iii1][jjj1], nid);
                    }
                  }
                }
              }
            }
          }
        }
      } catch (ex) {
        console.warn(ex);
      }

      for (var n7 in payload) {
        switch (n7) {
          case "ln":
            try {
              Vue.set(state, n7, payload[n7]);
            } catch (ex) {
              console.warn(ex);
            }
            break;
          case "early":
          case "live":
          case "today":
          case "league":
          case "parent":
          case "head":
          case "child":
          case "match":
          case "odds":
            try {
              for (var nn in payload[n7]) {
                Vue.set(state[n7], nn, payload[n7][nn]);
              }
            } catch (ex) {
              console.warn(ex);
            }
            break;
          case "mmo":
            // console.log("mmoPartial", payload);
            if (store.getters.mmoMode) {
              try {
                for (var nn1 in payload[n7]) {
                  Vue.set(state[n7], nn1, payload[n7][nn1]);
                }
              } catch (ex) {
                console.warn(ex);
              }
            }
            break;
          case "options":
            try {
              Vue.set(state, n7, payload[n7]);
            } catch (ex) {
              console.warn(ex);
            }
            break;
        }
      }
    },
    updateOdds(state, payload) {
      var o1 = 0;
      var o2 = 0;

      if (payload && payload.odds) {
        o1 = Object.keys(payload.odds).length;
      }
      if (payload && payload.mmo) {
        o2 = Object.keys(payload.mmo).length;
      }

      var o3 = {
        live: "3",
        today: "2",
        early: "1",
        live2: "3",
        today2: "2",
        early2: "1",
      };

      // if response is empty then delete the market

      var o4 = o3[payload.id];

      if (o1 > 0) {
        for (var n in payload.odds) {
          Vue.set(state.odds, n, payload.odds[n]);
        }
      } else {
        Vue.delete(state.odds, o4);
      }

      if (store.getters.mmoMode) {
        // console.log("mmoOdds", payload);
        if (o2 > 0) {
          for (var n1 in payload.mmo) {
            Vue.set(state.mmo, n1, payload.mmo[n1]);
          }
        } else {
          Vue.delete(state.mmo, o4);
        }

        var qq = [];

        for (var ii in state.mmo) {
          for (var jj in state.mmo[ii]) {
            var id = Object.keys(state.mmo[ii][jj]);
            qq = qq.concat(id);
          }
        }

        state.mmoIndex = qq;
      }
    },
    updateMore(state, payload) {
      var o1 = Object.keys(payload.odds).length;
      var o2 = Object.keys(payload.mmo).length;

      var o3 = {
        live: "3",
        today: "2",
        early: "1",
        live2: "3",
        today2: "2",
        early2: "1",
      };

      // if response is empty then delete the market

      var o4 = o3[payload.id];
      if (o1 > 0) {
        for (var n in payload.odds) {
          Vue.set(state.more, n, payload.odds[n]);
        }
      } else {
        Vue.delete(state.more, o4);
      }

      if (store.getters.mmoMode) {
        if (o2 > 0) {
          for (var n1 in payload.odds) {
            Vue.set(state.moremmo, n1, payload.mmo[n1]);
          }
        } else {
          Vue.delete(state.moremmo, o4);
        }
      }
    },
  },
  actions: {
    setParlayMode(context, payload) {
      context.commit("updateParlayMode", payload);
    },
    setFilterLive(context, payload) {
      return new Promise((resolve) => {
        context.commit("updateFilterLive", payload);
        resolve();
      });
    },
    setFilterMode(context, payload) {
      return new Promise((resolve) => {
        context.commit("updateFilterMode", payload);
        resolve();
      });
    },
    resetData(context, payload) {
      return new Promise((resolve) => {
        context.commit("removeData", payload);
        resolve();
      });
    },
    resetPartialData(context, payload) {
      return new Promise((resolve) => {
        context.commit("removePartialData", payload);
        context.commit("updateGroup");
        resolve();
      });
    },
    setData(context, payload) {
      return new Promise((resolve) => {
        context.commit("updateData", payload);
        context.commit("updateGroup");
        resolve();
      });
    },
    setPartialData(context, payload) {
      return new Promise((resolve) => {
        context.commit("updatePartialData", payload);
        context.commit("updateGroup");
        resolve();
      });
    },
    setOdds(context, payload) {
      return new Promise((resolve) => {
        context.commit("updateOdds", payload);
        resolve();
      });
    },
    setMore(context, payload) {
      return new Promise((resolve) => {
        context.commit("updateMore", payload);
        resolve();
      });
    },
  },
};
