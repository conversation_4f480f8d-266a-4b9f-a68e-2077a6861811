<template lang="pug">
.hl-small-single.d-flex.flex-row.fade-me-in.pointable(v-if="show", @click="handleClick()", :class="{ active: isme }")
  .hl-video-small
    //- viewer(:src="item.video_link")
    .live-video
      //- .play-btn
      .inner-logo(v-if="item.home_logo_path && item.away_logo_path")
        .inner-logo-team
          img(v-if="item.home_logo_path", :src="item.home_logo_path")
        span vs
        .inner-logo-team
          img(v-if="item.away_logo_path", :src="item.away_logo_path")
      .inner-match
        .hl-team-name {{ getName('home_name_', item) }}
        .inner-event
          .hl-label {{ item.final_home }} : {{ item.final_away }}
          .hl-label.small(v-if="item.half_home != null")
            .d-block HT
            .d-block {{ item.half_home }} : {{ item.half_away }}
        .hl-team-name.text-left {{ getName('away_name_', item) }}

  .hl-filter-small.d-flex.flex-column
    .hl-league-titlebar
      .hl-league-logo
        img(v-if="item.league_logo_path", :src="item.league_logo_path")
      .hl-league-name {{ getName('name_', item) }}
    .hl-match-team.flex-fill.d-flex.flex-row.align-items-stretch
      .hl-team.hl-team01
        .hl-team-logo
          img(v-if="item.home_logo_path", :src="item.home_logo_path")
        .hl-team-name {{ getName('home_name_', item) }}
      .hl-event-info.d-flex.flex-column.align-items-center.justify-content-center
        .hl-match-time.d-flex.align-items-center.justify-content-center.flex-column
          .hl-date {{ $dayjs(item.match_time).format('MM/DD') }}
          .hl-time {{ $dayjs(item.match_time).format('h:mmA') }}
        .hl-match-score.d-flex.align-items-center.justify-content-center.flex-column
          .hl-label {{ item.final_home }} : {{ item.final_away }}
          .hl-score
            .label HT
            .score {{ item.half_home }} : {{ item.half_away }}
      .hl-team.hl-team02
        .hl-team-logo
          img(v-if="item.away_logo_path", :src="item.away_logo_path")
        .hl-team-name {{ getName('away_name_', item) }}
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import service from "@/library/_xhr-ext";

import { EventBus } from "@/library/_event-bus.js";
export default {
  components: {
    viewer: () => import("@/views/highlight/viewer"),
  },
  props: {
    item: {
      type: Object,
    },
    idx: {
      type: Number,
    },
    isme: {
      type: Boolean,
    },
  },
  data() {
    return {
      show: false,
    };
  },
  computed: {
    language() {
      return this.$store.getters.language;
    },
  },
  mounted() {
    setTimeout(() => {
      this.show = true;
    }, 100 * this.idx);
  },
  methods: {
    handleClick() {
      this.$emit("selected", this.item);
    },
    getName(p, e) {
      var name = e[p + this.language];
      if (name == null || name == "" || !name) {
        name = e[p + "en"];
      }
      return name;
    },
    getRemark(p, e) {
      var name = e[p + this.language];
      if (name == null || name == "" || !name) {
        name = e[p + "en"];
      }
      var html = "";
      var p = name.split(",");
      for (var i = 0; i < p.length; i++) {
        var b = p[i].split("|");
        if (b.length > 1) {
          html += "<div class='remark-item'>";
          for (var j = 0; j < b.length; j++) {
            if (j == 0) {
              html += "<div class='remark-detail " + b[j].toLowerCase() + "'>" + b[j] + "</div>";
            } else {
              html += "<div class='remark-detail'>" + b[j] + "</div>";
            }
          }
          html += "</div>";
        } else {
          html += "<div class='remark-item'>" + b[0] + "</div>";
        }
      }
      return html;
    },
  },
};
</script>