<template lang="pug">
#bet-parlay-panel.magicY.bet-parlay-scroll(v-if="isDataExists")
  .new-betslip
    .betslip-content
      template(v-for="(item, index) in betparlay")
        betInfoParlay(:betslip="item", :key="'bi-' + item.matchId", @handleCloseBet="handleCloseBet", :no="index")
      .bet-infosub
        .m-1(v-if="isPlaceBet && !betConfirm")
          label(@click="handleAcceptAnyOdds")
            input(name="odds", type="checkbox", :checked="betting.acceptAnyOdds == 'true'")
            span.text(@click="handleAcceptAnyOdds") {{ $t("ui.accept_any_odds") }}
        .warning.m-1(v-if="oddsIsUpdating")
          i.fal.fa-exclamation-circle.text-danger.mr-1
          span {{ $t("error.oddsIsUpdating") }}
          .clearfix
        .warning.m-1(v-if="errorMessage")
          i.fal.fa-times-circle.text-danger.mr-1
          span {{ errorMessage }}
          .clearfix
      template(v-if="isPlaceBet && !betConfirm")
        .stake-field.py-1
          .d-flex(style="margin-bottom: 2px")
            .flex-fill.w-50.pl-1(style="padding-top: 3px; font-weight: bold") {{ $t("ui.combo") }}
            .w-128
              select.form-control.form-control-sm(v-model="fold")
                template(v-for="mc in comboKeys")
                  option(:value="mc", :key="mc") {{ mc }}{{ $t("ui.afold") }} ({{ comboIndex[mc] }}{{ $t("ui.abets") }})
          .d-flex
            .flex-fill.w-50.pl-1(style="padding-top: 3px; font-weight: bold") {{ currency_code }}
            .w-128
              StakeInput(v-model="stake", @handleStake="handleStake", ref="stake", :loadbet="loading.check")
    template(v-if="isPlaceBet")
      template(v-if="!betConfirm")
        .p-1
          table.table-entry(width="100%")
            tbody
              tr
                td(width="50%")
                  span {{ $t("ui.total_payout") }}
                td.text-right(width="50%") {{ $numeral(payout).format("0,0.00") }}
              tr
                td(width="50%") {{ $t("ui.mix_parlay") }}
                td.text-right(width="50%") {{ $numeral(multi).format("0,0.00[0]") }}
              tr
                td {{ $t("ui.min") }}
                td.text-right {{ $numeral(minBet).format("0,0") }}
              tr
                td {{ $t("ui.max") }}
                td.text-right {{ $numeral(maxBet).format("0,0") }}
        .stake.pt-0.mt-0
          .d-flex.justify-content-around
            div
              SpinButton(css="btn btn-block btn-cancel btn-sm btn-secondary", @click="cancelBetClick", :text="$t('ui.cancel')", :loading="loading.cancel")
            .ml-1.flex-fill
              SpinButton(css="btn btn-sm btn-block btn-process btn-warning text-ellipsis", @click="processConfirmBet", :text="$t('ui.process_bet')", :loading="loading.process")
      template(v-else)
        .p-1
          table.table-entry(width="100%")
            tbody
              tr
                td(width="50%") {{ $t("ui.combo") }}
                td.text-right(width="50%") {{ fold }}{{ $t("ui.afold") }} ({{ total_bet }}{{ $t("ui.abets") }})
              tr
                td(width="50%") {{ $t("ui.total_stake") }}
                td.text-right(width="50%") {{ $numeral(total_stake).format("0,0.00") }}
              tr
                td(colspan="2")
                  i.fad.fa-check-circle.mr-1.text-success
                  span.text-dark {{ $t("message.confirm_bet") }}
        .stake.pt-0.mt-0
          .d-flex.justify-content-around
            div
              SpinButton(css="btn btn-block btn-cancel btn-sm btn-secondary", @click="betConfirm = false", :text="$t('ui.no')", :loading="loading.cancel")
            .ml-1.flex-fill
              SpinButton(css="btn btn-sm btn-block btn-process btn-warning text-ellipsis", @click="processBet", :text="$t('ui.yes')", :loading="loading.process")
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";
import xhrBet from "@/library/_xhr-bet.js";
import calc from "@/library/_calculation.js";
import config from "@/config";
import betInfoParlay from "@/components/desktop/left/betInfoParlay";
import matchInfoParlay from "@/components/desktop/left/matchInfoParlay";
import StakeInput from "@/components/desktop/left/stakeInput";
import SpinButton from "@/components/ui/SpinButton";
import mixinBetType from "@/library/mixinBetType";
import naming from "@/library/_name";

export default {
  components: {
    betInfoParlay,
    matchInfoParlay,
    SpinButton,
    StakeInput,
  },
  mixins: [mixinBetType],
  data() {
    return {
      errorMessage: "",
      loading: {
        check: false,
        cancel: false,
        process: false,
      },
      league: {},
      match: {},
      child: {},
      defaultCounter: 10,
      counter: 10,
      placeSlip: {},
      stake: null,
      total_bet: 1,
      total_stake: 0,
      fold: 0,
      payout: null,
      invalidOdds: false,
      oddsIsUpdating: false,
      autoCloseOddsIsUpdating: null,
      parlayMatch: {},
      betConfirm: false,
      isMMO: false,
    };
  },
  computed: {
    combo() {
      return this.$store.state.betparlay.combo;
    },
    comboKeys() {
      return Object.keys(this.$store.state.betparlay.combo);
    },
    comboIndex() {
      var results = {};
      for (var n in this.comboKeys) {
        results[this.comboKeys[n]] = this.combo[this.comboKeys[n]].length;
      }

      return results;
    },
    comboOdds() {
      var results = {};
      for (var n in this.comboKeys) {
        for (var m in this.combo[this.comboKeys[n]]) {
          if (results[this.comboKeys[n]] == null) {
            results[this.comboKeys[n]] = [];
          }
          var multi = 1.0;
          var po = -1;
          for (var j in this.combo[this.comboKeys[n]][m]) {
            var test = this.$store.state.betparlay.data[this.combo[this.comboKeys[n]][m][j]];
            if (test != null) {
              multi = multi * parseFloat(test.val);
              var mpo = test.maxParlayPayout;
              if (po < 0 || po > mpo) po = mpo;
            }
          }
          results[this.comboKeys[n]].push([multi, po]);
        }
      }
      return results;
    },
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
    commType() {
      return this.$store.getters.commType;
    },
    multi() {
      var result = 0.0;
      if (this.comboOdds.hasOwnProperty(this.fold)) {
        for (var n in this.comboOdds[this.fold]) {
          result = result + this.comboOdds[this.fold][n][0];
        }
      }
      var f = parseFloat(calc.fm(result, false, 4)).toFixed(3);
      return f;
    },
    MPP() {
      var result = 0;
      var mm = null;
      if (this.comboOdds.hasOwnProperty(this.fold)) {
        for (var n in this.comboOdds[this.fold]) {
          var j = this.comboOdds[this.fold][n][1] / this.comboOdds[this.fold][n][0];
          if (mm == null) {
            mm = j;
            result = j;
          } else {
            if (j < mm) {
              mm = j;
              result = j;
            }
          }
        }
      }
      return result;
    },
    maxBet() {
      var max = 0;
      var max = calc.truncateInteger(this.MPP);
      for (var n in this.betparlay) {
        if (max > parseFloat(this.betparlay[n].maxBet)) {
          max = parseFloat(this.betparlay[n].maxBet);
        }
      }
      return max;
    },
    minBet() {
      var result = 0;
      for (var n in this.betparlay) {
        if (result == 0) {
          result = this.betparlay[n].minBet;
        } else {
          if (result < this.betparlay[n].minBet) {
            result = this.betparlay[n].minBet;
          }
        }
      }
      return result;
    },

    debug() {
      return config.debugMode;
    },
    betparlay() {
      var tempOrder = [];
      tempOrder = Object.values(this.$store.state.betparlay.data).sort(function (a, b) {
        return a.bettime < b.bettime ? -1 : a.bettime > b.bettime ? 1 : 0;
      });
      return tempOrder;
    },
    pageDisplay() {
      return this.$store.getters.pageDisplay;
    },
    betting() {
      return this.$store.getters.betting;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
    isDataExists() {
      if (this.matchCount > 0) {
        return true;
      } else {
        return false;
      }
    },
    isPlaceBet() {
      var is_live = false;
      for (var n in this.betparlay) {
        if (this.betparlay[n].marketType == 3) {
          is_live = true;
          break;
        }
      }

      if (is_live) {
        if (Object.keys(this.betparlay).length >= config.parlayLiveMinTicket) {
          return true;
        } else {
          return false;
        }
      } else {
        if (Object.keys(this.betparlay).length >= config.parlayMinTicket) {
          return true;
        } else {
          return false;
        }
      }
    },
    matchCount() {
      if (this.betparlay) {
        return Object.keys(this.betparlay).length;
      } else {
        return 0;
      }
    },
    menu2() {
      return this.$store.getters.menu2;
    },
  },
  watch: {
    fold(newVal) {
      this.changeTotalBet();
    },
    stake(newVal) {
      this.handlePayout();
    },
    multi(newVal) {
      this.handlePayout();
    },
    matchCount(newVal) {
      this.fold = newVal;
    },
  },
  destroyed() {
    clearInterval(this.autoRefresh);

    EventBus.$off("BETPARLAY", this.runbet);
  },
  mounted() {
    setInterval(this.autoRefresh, this.defaultCounter * 1000);

    EventBus.betParlay = this.triggerRunBet;
    EventBus.closeParlay = this.handleCloseBet;
    EventBus.cancelParlay = this.cancelBet;
    EventBus.addPick = this.addPick;

    EventBus.$on("BETPARLAY", this.runbet);

    this.fold = this.matchCount;
  },
  methods: {
    changeFold(e) {
      this.fold = parseInt(e.target.value);
    },
    changeTotalBet() {
      if (this.comboIndex.hasOwnProperty(this.fold)) {
        this.total_bet = this.comboIndex[this.fold];
      } else {
        this.total_bet = 0;
      }
      this.handleStake();
    },
    getBallDisplay(b, g, ha, bt) {
      return naming.ballDisplay2(b, g, ha, bt, this);
    },
    triggerRunBet(odds, typ, idx, val, bt, amt, isMMO, e, pos, giving) {
      if (EventBus.cancelParlayMMO) EventBus.cancelParlayMMO();
      EventBus.$emit("BETPARLAY", odds, typ, idx, val, bt, amt, isMMO, e, pos, giving);
    },

    cache() {
      return this.$store.getters.data;
    },
    processConfirmBet() {
      this.errorMessage = "";
      this.betConfirm = true;
      document.onkeyup = (event) => {
        if (event.which == 13 || event.keyCode == 13) {
          this.scrollToBottom();
          this.processBet();
        } else {
          if (event.which == 27 || event.keyCode == 27) {
            this.betConfirm = false;
          }
        }
      };
    },
    scrollToBottom() {
      var e = document.getElementById("bet-parlay-panel");
      e.scrollTop = e.scrollHeight - e.clientHeight;
    },
    handleCloseBet(e) {
      this.$store.dispatch("betparlay/removeData", e).then(() => {
        this.$store.dispatch("betparlay/setCombo").then(() => {
          this.changeTotalBet();
        });
      });
      this.parlayMatch[e] = 0;
    },
    handlePayout() {
      var a = this.stake * this.multi;
      var b = this.maxBet * this.multi;
      if (a > b) {
        a = b;
      }

      // this.payout = parseFloat(calc.fm(a, false, 4)).toFixed(3);
      this.payout = a.toFixed(3);
    },
    calculateStake() {
      if (this.stake > this.maxBet) {
        this.stake = this.maxBet;
      } else {
        if (this.stake < this.minBet) {
          this.stake = this.minBet;
        }
      }
    },
    handleStake() {
      if (this.stake % 1 != 0) {
        this.stake = Math.round(this.stake);
      }

      this.total_stake = this.stake * this.total_bet;
    },
    autoRefresh() {
      this.errorMessage = "";
      var p = [];
      if (this.isDataExists) {
        if ((this.betting.autoRefreshOdds == true || this.betting.autoRefreshOdds == "true") && this.isDataExists) {
          this.requestMultiBet(this.betparlay, true).then(() => {
            this.$store.dispatch("betparlay/setCombo").then(() => { });
          });
        }
      }
    },
    handleAutoRefresh() {
      this.$store.dispatch("layout/setSingleBetting", {
        property: "autoRefreshOdds",
        value: this.betting.autoRefreshOdds == "true" ? "false" : "true",
      });
      this.counter = this.defaultCounter;
    },
    handleAcceptAnyOdds() {
      this.$store.dispatch("layout/setSingleBetting", {
        property: "acceptAnyOdds",
        value: this.betting.acceptAnyOdds == "true" ? "false" : "true",
      });
    },
    refreshOddsYes() {
      this.invalidOdds = false;
      this.counter = this.defaultCounter;
      this.autoRefresh();
    },
    refreshOddsNo() {
      this.invalidOdds = false;
    },
    getBetLimit() {
      return this.$store.getters.playerBetLimit["PARLAY"];
    },
    showMe() {
      $("#collapse-betslip").collapse("show");
      $("#pills-parlay-tab").tab("show");
    },
    cancelBetClick() {
      this.cancelBet();
      setTimeout(() => {
        if (config.vg1.includes(this.menu2)) {
          $("#collapse-vgames").collapse("show");
        } else {
          $("#collapse-allsports").collapse("show");
        }
      }, 100);
    },
    cancelBet() {
      clearTimeout(this.autoCloseOddsIsUpdating);
      this.invalidOdds = false;
      this.oddsIsUpdating = false;
      this.loading.cancel = true;
      this.loading.check = false;
      this.league = {};
      this.match = {};
      this.counter = this.defaultCounter;
      this.$store.dispatch("betparlay/clearData").then(() => {
        this.loading.cancel = false;
        this.loading.process = false;
      });
    },

    existsInSlip(match_id) {
      return Object.keys(this.$store.state.betparlay.data).includes(match_id);
    },

    runbet(odds, typ, idx, val, bt, amt, isMMO, e, pos, giving) {
      if (isMMO) {
        this.$helpers.showDialog(this.$t("ui.action"), this.$t("error.invalidCurrency"), "error");
        return;
      }

      if (EventBus.cancelParlayMMO) EventBus.cancelParlayMMO();
      this.isMMO = isMMO;

      this.showMe();

      var isExists = true;
      if (!this.existsInSlip(odds[1].toString())) {
        isExists = false;
        if (Object.keys(this.$store.state.betparlay.data).length + 1 > config.parlayMaxTicket) {
          this.$helpers.showDialog(this.$t("ui.action"), this.$t("error.maxParlayTicket"), "info");
          return;
        }
      }

      this.stake = amt;
      this.calculateStake();
      this.handleStake();
      this.checkParlayBet(odds, typ, idx, val, bt, isExists);
      this.xFocus();
      clearTimeout(this.autoCloseOddsIsUpdating);
      this.invalidOdds = false;
      this.oddsIsUpdating = false;
    },
    addPick(e, t, amt) {
      if (EventBus.cancelParlayMMO) EventBus.cancelParlayMMO();
      this.showMe();

      var cnt = 1;

      this.stake = amt;

      var nonExistList = [];
      for (var i = 0; i < e.length; i++) {
        var x = e[i];
        // replace the existing betslip
        if (this.existsInSlip(x.match_id.toString())) {
          this.checkPick(x, t, amt, true, cnt++).then(() => { });
        } else {
          nonExistList.push(x);
        }
      }

      // check if got free space to add
      var total = Object.keys(this.$store.state.betparlay.data).length;
      var validTotal = config.parlayMaxTicket - total;
      if (nonExistList.length < validTotal) {
        validTotal = nonExistList.length;
      }
      if (total >= config.parlayMaxTicket) {
        this.$helpers.showDialog(this.$t("ui.action"), this.$t("error.maxParlayTicket"), "info");
        return;
      } else {
        if (total + validTotal > config.parlayMaxTicket) {
          this.$helpers.showDialog(this.$t("ui.action"), this.$t("error.maxParlayTicket"), "info");
          return;
        }
      }
      // add those valid betslip
      for (var i = 0; i < validTotal; i++) {
        this.checkPick(nonExistList[i], t, amt, false, cnt++).then(() => { });
      }

      setTimeout(() => {
        this.calculateStake();
        this.handleStake();
      }, cnt * 100);

      this.xFocus();
      clearTimeout(this.autoCloseOddsIsUpdating);
      this.invalidOdds = false;
      this.oddsIsUpdating = false;
    },
    xFocus() {
      EventBus.$emit("STAKE_FOCUS");
    },
    checkPick(odds, typ, amt, blink, cnt) {
      return new Promise((resolve) =>
        setTimeout(() => {
          this.counter = this.defaultCounter;

          var sd = {};

          sd.matchId = odds.match_id;

          sd.typ = typ;
          sd.pick = odds.target;
          sd.idx = odds.pick_idx;
          sd.val = odds.pick_odds;
          // sd.origin = calc.fmt(odds.pick_odds);

          sd.leagueId = odds.league_id;
          sd.subMatchId = odds.submatch_id;
          sd.oddsId = odds.odds_id;

          sd.target = "parlay";
          sd.criteria1 = sd.special ? odds[36] : odds[6];
          sd.criteria2 = sd.special ? odds[37] : odds[7];

          sd.sportsType = odds.sports_type;
          sd.leagueName = odds.league_name_en;

          var limit = this.getBetLimit();
          sd.minBet = limit.min_bet;
          sd.maxBet = limit.max_bet;
          sd.maxPayout = limit.max_payout;
          sd.maxParlayPayout = 0;
          sd.marketType = odds.market_type;
          sd.workingDate = odds.working_date;
          sd.matchTime = odds.match_time;
          sd.score = odds.home_running_score == null ? "" : odds.home_running_score + " - " + odds.away_running_score;
          sd.typId = config.oddsTypeId[sd.typ];

          sd.betType = odds.betType;
          sd.homeId = odds.homeId;
          sd.awayId = odds.awayId;
          sd.homeName = odds.homeName;
          sd.awayName = odds.awayName;

          sd.giving = odds.giving;
          sd.parlay = odds.parlay;
          sd.ballDisplay = odds.ballDisplay;

          sd.betTeamId = odds.betTeamId;
          sd.betTeamName = odds.betTeamName;
          sd.betDisplay = odds.betDisplay;

          sd.homeAway = odds.homeAway;

          this.requestSingleBet(sd, false).then(() => {
            if (blink) {
              var elem = document.getElementById("betslip-parlay-" + sd.matchId);
              if (elem) {
                if (elem.classList.contains("blink3")) {
                  elem.classList.remove("blink3");
                }
                elem.classList.add("blink3");
              }
            }
            this.$store.dispatch("betparlay/setCombo").then(() => {
              this.changeTotalBet();
            });
          });
        }, cnt * 50)
      );
    },

    checkParlayBet(odds, typ, idx, val, bt, blink) {
      this.counter = this.defaultCounter;
      this.loading.check = true;
      var cache = this.cache();
      if (cache.hasOwnProperty("league") && cache.hasOwnProperty("match")) {
        var sd = {};

        // add support for special bet
        sd.betType = odds[4];
        sd.special = false;
        switch (sd.betType.toUpperCase()) {
        case "CSHTFT":
        case "ETGHTFT":
          sd.special = true;
          break;
        }

        sd.matchId = odds[1];
        sd.child = cache.child[sd.matchId];
        sd.parent = cache.parent[sd.matchId];

        if (sd.child != undefined) {
          for (var n in sd.child) {
            this.$store.dispatch("betparlay/removeData", sd.child[n][0]).then(() => { });
          }
        }

        if (sd.parent != undefined) {
          this.$store.dispatch("betparlay/removeData", sd.parent).then(() => { });
        }

        sd.typ = typ;
        sd.idx = idx;
        sd.val = val;
        // sd.origin = calc.fmt(val);

        sd.leagueId = odds[0];
        sd.subMatchId = odds[2];
        sd.oddsId = odds[3];

        sd.target = bt;
        sd.criteria1 = sd.special ? odds[36] : odds[6];
        sd.criteria2 = sd.special ? odds[37] : odds[7];

        this.league = cache.league[sd.leagueId];
        this.match = cache.match[sd.matchId];

        sd.sportsType = this.league[1];
        sd.leagueName = this.league[4];

        var limit = this.getBetLimit();
        sd.minBet = limit.min_bet;
        sd.maxBet = limit.max_bet;
        sd.maxPayout = limit.max_payout;
        sd.maxParlayPayout = 0;
        sd.marketType = this.match[4];
        sd.homeName = this.match[5];
        sd.awayName = this.match[6];
        sd.workingDate = this.match[7];
        sd.matchTime = this.match[8];
        sd.homeId = this.match[22];
        sd.awayId = this.match[23];
        sd.score = this.match[11] ? this.match[11].toString().trim() : "";

        sd.typId = config.oddsTypeId[sd.typ];
        sd.ballDisplay = "";
        sd.betDisplay = "";


        this.setBetType(sd, odds);
        this.requestSingleBet(sd, false).then(() => {
          if (blink) {
            var elem = document.getElementById("betslip-parlay-" + sd.matchId);
            if (elem) {
              if (elem.classList.contains("blink3")) {
                elem.classList.remove("blink3");
              }
              setTimeout(() => {
                elem.classList.add("blink3");
              }, 500);
            }
          }
          this.$store.dispatch("betparlay/setCombo").then(() => {
            this.changeTotalBet();
          });
        });

        this.league = null;
        this.match = null;
      }
    },
    processBet() {
      if (!this.betConfirm || Object.keys(this.betparlay).length == 0) return;

      this.errorMessage = "";
      this.betConfirm = false;
      this.loading.process = true;
      var old = this.placeSlip;
      var validBet = true;
      this.placeSlip = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        bet_member: this.stake,
        accept_any_odds: this.betting.acceptAnyOdds,
        bet_detail: [],
        operator_type: this.$store.getters.operatorType,
        parent_id: this.$store.getters.parentId,
        fold: this.fold,
        bet_total: this.total_stake,
        mode: this.fold == this.matchCount && this.total_bet == 1 && this.total_stake == this.stake,
      };

      if (this.stake > this.maxBet) {
        this.$helpers.showDialog(this.$t("ui.action"), this.$t("error.betOverMaxBet"), "info");
        this.loading.process = false;
        this.betConfirm = true;
        return;
      }

      var is_live = false;

      for (var n in this.betparlay) {
        var item = {};
        item["a"] = this.betparlay[n].oddsId;
        item["b"] = this.betparlay[n].subMatchId;
        item["c"] = this.betparlay[n].matchId;
        item["d"] = this.betparlay[n].betType;
        item["e"] = this.betparlay[n].betTeamId;
        item["f"] = this.betparlay[n].homeAway;
        item["g"] = parseFloat(this.betparlay[n].val);
        item["h"] = parseFloat(this.betparlay[n].origin);
        item["j"] = this.betparlay[n].ballDisplay;
        switch (this.betparlay[n].betType.toUpperCase()) {
        case "CSHTFT":
        case "ETGHTFT":
          item["k"] = this.betparlay[n].idx - 5;
          break;
        default:
          // item["k"] = parseInt(this.betparlay[n].idx);
          break;
        }

        // check if live bet, if yes, set the bet detail to bet detail live bet
        if (this.betparlay[n].marketType == 3) {
          is_live = true;
        }

        this.placeSlip.bet_detail.push(item);

        // check if bet status is false, set the validBet to false
        if (this.betparlay[n].betStatus == "false") {
          validBet = false;
        }
      }

      // check the parlay match count before send
      if (is_live) {
        if (this.placeSlip.bet_detail.length < config.parlayLiveMinTicket) {
          this.$helpers.showDialog(this.$t("ui.action"), this.$t("error.atLeastThreeMatchToBetParlay"), "info");
          this.loading.process = false;
          this.betConfirm = true;
          return;
        }
      } else {
        if (this.placeSlip.bet_detail.length < config.parlayMinTicket) {
          this.$helpers.showDialog(this.$t("ui.action"), this.$t("error.atLeastTwoMatchToBetParlay"), "info");
          this.loading.process = false;
          this.betConfirm = true;
          return;
        }
      }

      if (!validBet) {
        this.$helpers.showDialog(this.$t("ui.action"), this.$t("error.matchHide"), "info");
        this.loading.process = false;
        this.betConfirm = true;
        return;
      }

      old = null;
      xhrBet.betParlay(this.placeSlip).then(
        (res) => {
          if (res.success) {
            if (this.$store.state.layout.betting.defaultStake == "1") {
              this.$store.dispatch("layout/setSingleBetting", {
                property: "defaultStakeAmount",
                value: this.stake,
              });
            }
            if (res.data.pending == false || res.data.pending == undefined) {
              if (EventBus.betListAccept) {
                EventBus.betListAccept();
              }
            } else {
              if (EventBus.betListPending) {
                EventBus.betListPending();
              }
            }
            this.cancelBet();
            if (EventBus.getBalance) {
              EventBus.getBalance();
            }
          } else {
            this.$helpers.handleFeedback(res.status);
          }
        },
        (err) => {
          this.loading.process = false;
          switch (err.status) {
          case "insufficient_balance":
            this.errorMessage = this.$t("error.insufficient_balance");
            break;
          case "invalidOdds":
            this.invalidOdds = true;
            break;
          case "oddsIsUpdating":
            this.oddsIsUpdating = true;
            clearTimeout(this.autoCloseOddsIsUpdating);
            this.autoCloseOddsIsUpdating = setTimeout(() => {
              this.oddsIsUpdating = false;
            }, this.defaultCounter * 1000);
            break;
          default:
            this.$helpers.handleFeedback(err.status);
            break;
          }
        }
      );
    },
    processMultiBet(multi, results, refresh) {
      let m = JSON.parse(JSON.stringify(multi));

      for (var n in m) {
        var sd = m[n];
        var res = results[n];
        var d = res.odds_check_details[0];
        var success = false;
        if (typeof res.status == "string") {
          success = res.status == "1";
        } else {
          success = res.status == 1;
        }
        if (success) {
          if (sd.oddsId == d.odds_id) {
            sd.oddsChanged = d.odds_change;
            sd.ballChanged = d.ball_change;
            if (d.home_giving == true) {
              sd.giving = 1;
            } else {
              sd.giving = 0;
            }
            if (d.ball_display_new != null) {
              sd.ballChangedText = this.getBallDisplay(sd.ballDisplay.toString(), sd.giving, sd.homeAway, sd.betType);
              sd.ballDisplay = d.ball_display_new;
            }
            if (d.odds_display_new != null) {
              sd.oddsChangedText = sd.val;
              sd.val = calc.fmtType(d.odds_display_new, this.commType, sd.betType);
            }
            if (d.odds_new != null) {
              sd.origin = calc.fmt(d.odds_new);
            }
            if (sd.minBet < d.min_bet) {
              sd.minBet = d.min_bet;
            }
            if (sd.maxBet > d.max_bet) {
              sd.maxBet = d.max_bet;
            }
            sd.maxParlayPayout = d.max_parlay_payout;
            var flag = 1;
            if (!refresh) {
              Object.keys(this.parlayMatch).forEach(
                function (i) {
                  if (i == sd.matchId) {
                    flag = 0;
                    sd.bettime = this.parlayMatch[sd.matchId];
                  }

                  if (i == sd.matchId && this.parlayMatch[sd.matchId] == 0) {
                    flag = 1;
                  }
                }.bind(this)
              );

              if (flag) {
                sd.bettime = new Date().getTime();
                this.parlayMatch[sd.matchId] = sd.bettime;
              }
            }
            sd.betStatus = true;
            if (sd.typ != "DEC") {
              sd.multi = parseFloat(sd.val).toFixed(4);
            }
            this.$store.dispatch("betparlay/setData", sd).then(() => { });
          }
        }
      }
    },
    requestMultiBet(multi, refresh) {
      return new Promise((resolve, reject) => {
        var parlay = [];
        for (var n in multi) {
          var data = multi[n];
          if (data == null) {
            this.cancelBet("requestMultiBetNoData");
            return;
          }
          if (!data.betType || !data.oddsId || !data.sportsType) {
            this.cancelBet("requestMultiBetInvalid");
            return;
          }
          var item = {
            sports_type: data.sportsType,
            parlay: data.target == "parlay",
            odds_id: data.oddsId,
            submatch_id: data.subMatchId,
            bet_type: data.betType,
            bet_team_id: data.betTeamId,
            home_away: data.homeAway,
            odds_display: parseFloat(data.val),
            odds_mo: parseFloat(data.origin),
            odds_type: data.typId,
            ball_display: data.ballDisplay,
            odds_col: data.idx - 5,
          };
          parlay.push(item);
        }

        var slip = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          parlay: parlay,
        };

        var cache = this.cache();
        xhrBet.betMultiOddsCheck(slip).then(
          (res) => {
            if (res.success) {
              if (multi != null && res.data[0].status == 1) {
                this.processMultiBet(multi, res.data, refresh);
                this.loading.check = false;
                this.invalidOdds = false;
              } else {
                // this.$helpers.handleFeedback(res.data[0].statusdesc);
              }
            } else {
              this.$helpers.handleFeedback(res.status);
            }

            resolve();
          },
          (err) => {
            this.$helpers.handleFeedback(err.status);
            resolve();
          }
        );
      });
    },
    requestSingleBet(data, refresh) {
      return new Promise((resolve, reject) => {
        if (data == null) {
          this.cancelBet("requestSingleBetNoData");
          return;
        }
        if (!data.betType || !data.oddsId || !data.sportsType) {
          this.cancelBet("requestSingleBetInvalid");
          return;
        }

        var slip = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          sports_type: data.sportsType,
          parlay: data.target == "parlay",
          odds_id: data.oddsId,
          submatch_id: data.subMatchId,
          bet_type: data.betType,
          bet_team_id: data.betTeamId,
          home_away: data.homeAway,
          odds_display: parseFloat(data.val),
          odds_mo: parseFloat(data.origin),
          odds_type: data.typId,
          ball_display: data.ballDisplay,
          odds_col: data.idx - 5,
        };
        xhrBet.betSingleOddsCheck(slip, data.special).then(
          (res) => {
            if (res.success) {
              if (data != null) {
                let sd = JSON.parse(JSON.stringify(data));
                sd.oddsChanged = res.data[0].odds_change;
                sd.ballChanged = res.data[0].ball_change;
                if (res.data[0].home_giving == true) {
                  sd.giving = 1;
                } else {
                  sd.giving = 0;
                }
                if (res.data[0].ball_display_new != null) {
                  sd.ballChangedText = this.getBallDisplay(sd.ballDisplay.toString(), sd.giving, sd.homeAway, sd.betType);
                  sd.ballDisplay = res.data[0].ball_display_new;
                }
                if (res.data[0].odds_display_new != null) {
                  sd.oddsChangedText = sd.val;
                  sd.val = calc.fmtType(res.data[0].odds_display_new, this.commType, sd.betType);
                }
                if (res.data[0].odds_new != null) {
                  sd.origin = calc.fmt(res.data[0].odds_new);
                }

                if (sd.minBet < res.data[0].min_bet) {
                  sd.minBet = res.data[0].min_bet;
                }
                if (sd.maxBet > res.data[0].max_bet) {
                  sd.maxBet = res.data[0].max_bet;
                }
                sd.maxParlayPayout = res.data[0].max_parlay_payout;
                var flag = 1;
                if (!refresh) {
                  Object.keys(this.parlayMatch).forEach(
                    function (i) {
                      if (i == sd.matchId) {
                        flag = 0;
                        sd.bettime = this.parlayMatch[sd.matchId];
                      }

                      if (i == sd.matchId && this.parlayMatch[sd.matchId] == 0) {
                        flag = 1;
                      }
                    }.bind(this)
                  );

                  if (flag) {
                    sd.bettime = new Date().getTime();
                    this.parlayMatch[sd.matchId] = sd.bettime;
                  }
                }
                sd.betStatus = true;
                if (sd.typ != "DEC") {
                  sd.multi = parseFloat(sd.val).toFixed(4);
                }
                this.$store.dispatch("betparlay/setData", sd).then(() => { });
                this.loading.check = false;
                this.invalidOdds = false;
              }
            } else {
              this.$helpers.handleFeedback(res.status);
            }

            resolve();
          },
          (err) => {
            this.$helpers.handleFeedback(err.status);
            resolve();
          }
        );
      });
    },
  },
};
</script>
