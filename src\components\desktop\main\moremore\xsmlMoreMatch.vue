<template lang="pug">
.col-4.hx-more-row(v-if="details['ml'] != null")
  .card
    .card-header(
      :id="'heading-ml-more-' + uid + childId"
      data-toggle="collapse"
      :data-target="'#collapse-ml-more-' + uid + childId"
      :aria-controls="'collapse-ml-more-' + uid + childId"
      aria-expanded=true
      :class="layoutIndex == 3 ? 'live': 'non-live'"
    )
      i.fad.fa-chevron-circle-down
      span.header-bettype {{ leagueName.trim() }}
    .collapse.show(
      :aria-labelledby="'heading-ml-more-' + uid + childId"
      :id="'collapse-ml-more-' + uid + childId"
    )
      .card-body.p-0(:id="'accordian-ml-more-' + uid + childId")
        .hx-table.hx-match.hx-more-bet(:class="{ 'live': marketType == 3 }")
          .d-flex.flex-column.w-100
            .hx-cell.w-100
              .hx-row.hx-more-col.hx-more-col3.header.bl-1.br-1.bb-1
                .hx-col
                  .hx {{ homeTeam }}
                .hx-col
                  .hx {{ awayTeam }}
            .d-flex.flex-row
              .hx-cell.w-100
                .hx-row.hx-more-col.hx-more-col3.body.bl-1.br-1.bb-1
                  .hx-col
                    .hx
                      .hxs(v-if="details != null && details['ml'][0] != null && details['ml'][0][5] != null").hx-flex-c
                        oddsItem(:odds="details['ml'][0]" idx=5 :typ="oddsType" dataType="2" cls="more-value hx-w80")
                  .hx-col
                    .hx
                      .hxs(v-if="details != null && details['ml'][0] != null && details['ml'][0][7] != null").hx-flex-c
                        oddsItem(:odds="details['ml'][0]" idx=7 :typ="oddsType" dataType="2" cls="more-value hx-w80")
        template(v-if="details['mlh'] != null")

</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";

export default {
  components: {
    oddsItem
  },
  props: {
    customTeam: {
      type: Boolean
    },
    childId: {
      type: Number
    },
    uid: {
      type: String
    },
    details: {
      type: Object
    },
    matchId: {
      type: Number
    },
    leagueId: {
      type: Number
    },
    marketType: {
      type: Number
    },
    sportsType: {
      type: Number
    },
    betType: {
      type: String
    },
    layoutIndex: {
      type: Number
    }
  },
  computed: {
    homeTeam() {
      if (this.customTeam == true) {
        var result = this.source.homeTeam.split(" - ");
        if (result.length >= 2) {
          if (result.length - 2 != 0) {
            if (result.length <= 5) {
              return result[result.length - 2] + " - " + result[result.length - 1];
            }
            if (result.length <= 6) {
              return result[result.length - 3] + " - " + result[result.length - 2] + " - " + result[result.length - 1];
            }
          }
          return result[result.length - 1];
        }
        return this.source.homeTeam;
      } else {
        return this.source.homeTeam;
      }
    },
    awayTeam() {
      if (this.customTeam == true) {
        var result = this.source.awayTeam.split(" - ");
        if (result.length >= 2) {
          if (result.length - 2 != 0) {
            if (result.length <= 5) {
              return result[result.length - 2] + " - " + result[result.length - 1];
            }
            if (result.length <= 6) {
              return result[result.length - 3] + " - " + result[result.length - 2] + " - " + result[result.length - 1];
            }
          }
          return result[result.length - 1];
        }
        return this.source.awayTeam;
      } else {
        return this.source.awayTeam;
      }
    },
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
    leagueName() {
      var result = this.details["league"].split(" - ");
      // console.log(result);
      if (result.length >= 2) {
        return result[result.length - 1];
      }
      return this.details["league"];
    },
    source() {
      return {
        marketId: this.details.match[4],
        matchTime: this.details.match[8],
        runningScore: this.details.match[11],
        runningTime: this.details.match[12],
        homeTeam: this.details.match[5],
        awayTeam: this.details.match[6]
      };
    }
  }
};
</script>