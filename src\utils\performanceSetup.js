/**
 * Global Performance Monitoring Setup
 * Provides easy-to-use performance monitoring for the entire application
 */
import performanceMonitor from './performanceMonitor';

class PerformanceSetup {
  constructor() {
    this.isInitialized = false;
    this.reportInterval = null;
  }

  /**
   * Initialize performance monitoring with configuration
   * @param {Object} config - Configuration options
   */
  init(config = {}) {
    if (this.isInitialized) return;

    const defaultConfig = {
      // Enable in development only by default
      enabled: process.env.NODE_ENV === 'development',
      // Auto-report every 30 seconds
      autoReport: true,
      reportInterval: 30000,
      // Performance thresholds
      thresholds: {
        computed: 16,  // 16ms for computed properties
        watcher: 5,    // 5ms for watchers
        method: 10,    // 10ms for methods
      },
      // Components to monitor specifically
      monitorComponents: [
        'MainPanel',
        'MainPanelX', 
        'BetParlay',
        'BetSingle',
        'RightBar',
        'LeftBar'
      ]
    };

    const finalConfig = { ...defaultConfig, ...config };

    // Update performance monitor settings
    if (performanceMonitor.thresholds) {
      Object.assign(performanceMonitor.thresholds, finalConfig.thresholds);
    }

    performanceMonitor.enabled = finalConfig.enabled;

    // Setup auto-reporting
    if (finalConfig.autoReport && finalConfig.enabled) {
      this.startAutoReporting(finalConfig.reportInterval);
    }

    // Add global performance monitoring helpers to window (development only)
    if (finalConfig.enabled && typeof window !== 'undefined') {
      window.perf = {
        report: () => performanceMonitor.printReport(),
        clear: () => performanceMonitor.clear(),
        getMetrics: () => performanceMonitor.getReport(),
        startTiming: (name, type) => performanceMonitor.startTiming(name, type),
      };

      console.log('🚀 Performance monitoring enabled. Use window.perf for manual controls.');
    }

    this.isInitialized = true;
  }

  /**
   * Start automatic performance reporting
   * @param {number} interval - Report interval in milliseconds
   */
  startAutoReporting(interval = 30000) {
    if (this.reportInterval) {
      clearInterval(this.reportInterval);
    }

    this.reportInterval = setInterval(() => {
      const metrics = performanceMonitor.getReport();
      if (metrics.length > 0) {
        console.group('📊 Automatic Performance Report');
        performanceMonitor.printReport();
        console.groupEnd();
      }
    }, interval);
  }

  /**
   * Stop automatic performance reporting
   */
  stopAutoReporting() {
    if (this.reportInterval) {
      clearInterval(this.reportInterval);
      this.reportInterval = null;
    }
  }

  /**
   * Monitor a specific function with performance tracking
   * @param {string} name - Function name for reporting
   * @param {Function} fn - Function to monitor
   * @param {string} type - Type of monitoring (method, computed, watcher)
   * @returns {Function} Wrapped function
   */
  monitor(name, fn, type = 'method') {
    return performanceMonitor.wrapMethod(name, fn);
  }

  /**
   * Create a performance-tracked Vue computed property
   * @param {string} name - Computed property name
   * @param {Function} computedFn - Computed function
   * @returns {Object} Vue computed property object
   */
  createMonitoredComputed(name, computedFn) {
    return {
      get() {
        const endTiming = performanceMonitor.startTiming(name, 'computed');
        try {
          return computedFn.call(this);
        } finally {
          endTiming();
        }
      }
    };
  }

  /**
   * Get current performance statistics
   * @returns {Object} Performance statistics
   */
  getStats() {
    const metrics = performanceMonitor.getReport();
    
    return {
      totalComputations: metrics.reduce((sum, m) => sum + m.count, 0),
      slowestComputation: metrics[0] || null,
      averageTime: metrics.reduce((sum, m) => sum + m.avgTime, 0) / metrics.length || 0,
      totalTime: metrics.reduce((sum, m) => sum + m.totalTime, 0),
      componentsMonitored: [...new Set(metrics.map(m => m.name.split('.')[0]))],
    };
  }

  /**
   * Export performance data for analysis
   * @returns {Object} Exportable performance data
   */
  exportData() {
    return {
      timestamp: new Date().toISOString(),
      metrics: performanceMonitor.getReport(),
      stats: this.getStats(),
      config: {
        enabled: performanceMonitor.enabled,
        thresholds: performanceMonitor.thresholds,
      }
    };
  }
}

// Create global instance
const performanceSetup = new PerformanceSetup();

export default performanceSetup; 