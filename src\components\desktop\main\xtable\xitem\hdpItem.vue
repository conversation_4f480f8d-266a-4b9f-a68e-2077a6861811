<template lang="pug">
  .hx-col.hx-cols(:class="cls ? cls : 'w-98'")
    template(v-if="details[betType] != null && details[betType][i] && details[betType][i][9] != 0 && details[betType][i][10] != 0 && details[betType][i][9] != '' && details[betType][i][10] != ''")
      .hx
        .hxs.w-43
          .ball-value(v-if="details[betType][i][7] == 1") {{ details[betType][i][8] }}
        .hxs.w-43r
          oddsItem(:odds="details[betType][i]" :idx="details[betType][i][7] == 1 ? '10' : '9'" :typ="oddsType" dataType="1")
      .hx
        .hxs.w-43
          .ball-value(v-if="details[betType][i][7] == 0") {{ details[betType][i][8] }}
        .hxs.w-43r
          oddsItem(:odds="details[betType][i]" :idx="details[betType][i][7] == 1 ? '9' : '10'" :typ="oddsType" dataType="1")
</template>

<script>
// import oddsItem from "@/components/desktop/main/xtable/oddsItem";

export default {
  components: {
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem")
  },
  props: {
    cls: {
      type: String
    },
    details: {
      type: Object
    },
    oddsType: {
      type: String
    },
    item: {
      type: String
    },
    i: {
      type: [String, Number]
    },
    betType: {
      type: String
    }
  },
  // updated: function() {
  //   this.$nextTick(function() {
  //     console.log("cs", new Date(), this.oddsType, this.item);
  //   });
  // }
};
</script>
