<template lang="pug">
.info-tablewrap.magicZ
  ul.nav.nav-tabs(role='tablist')
    li.nav-item
      a.nav-link(href="javascript:void(0);" :class="{ 'active' : mode == 0 }" @click="setMode(0)") {{ $t("ui.accepted") }}
    li.nav-item
      a.nav-link(href="javascript:void(0);" :class="{ 'active' : mode == 1 }" @click="setMode(1)") {{ $t("ui.waiting") }}
    li.nav-item
      a.nav-link(href="javascript:void(0);" :class="{ 'active' : mode == 2 }" @click="setMode(2)") {{ $t("ui.void") }}
  .tab-content
    betListSportsbookAccepted(v-if="mode == 0" @loading="handleLoading")
    betListSportsbookWaiting(v-if="mode == 1" @loading="handleLoading")
    betListSportsbookVoid(v-if="mode == 2" @loading="handleLoading")
</template>


<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import betListSportsbookAccepted from "@/components/desktop/info/betList/betListSportsbookAccepted";
import betListSportsbookWaiting from "@/components/desktop/info/betList/betListSportsbookWaiting";
import betListSportsbookVoid from "@/components/desktop/info/betList/betListSportsbookVoid";

export default {
  components: {
    betListSportsbookAccepted,
    betListSportsbookWaiting,
    betListSportsbookVoid
  },
  data() {
    return {
      mode: 0,
      feedback: {
        loading: false,
        success: false,
        status: errors.session.invalidSession,
        source: ""
      }
    };
  },
  methods: {
    handleLoading(e) {
      this.$emit("loading", e);
    },
    setMode(e) {
      this.mode = e;
    },
    refreshList() {
      EventBus.$emit("BETLIST_REFRESH");
    }
  }
};
</script>