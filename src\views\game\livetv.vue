<template lang="pug">
.wrapper(style="border: 1px solid black; width: 100%; height: 100vh;").bg-soccer
  header.short
    .topbar.info-top(style="height: 25px;")
      .hl-container.d-flex.align-items-center.justify-content-start(style="width: 100%; max-width: 100%;")
        router-link(to="/desktop")
          i.fas.fa-chevron-left
          span {{ $t("ui.back")  }}
  #widget.embedded
  template(v-if="url && iframe")
    .preloader.d-flex.align-items-center.justify-content-center(v-if="!iframeLoaded && !iframeEmbeded")
      .game-warning
        i.fad.fa-spin.fa-spinner.mr-1
        span Loading...
  template(v-else)
    .preloader.d-flex.align-items-center.justify-content-center(v-if="error")
      .game-warning
        i.fad.fa-exclamation-triangle.mr-1
        span {{ error }}
    .lds-overlay(v-else)
      .lds-roller
        div
        div
        div
        div
        div
        div
        div
        div
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import xhr from "@/library/_xhr-game.js";
import xhrUtils from "@/library/_xhr-utils.js";

export default {
  data() {
    return {
      iframe: false,
      iframeLoaded: false,
      iframeEmbeded: false,
      url: "",
      error: "",
      expanded: false,
      loading: false,
    };
  },
  destroyed() {
    $("html").removeClass("minimal");
    $("body").removeClass("minimal");
  },
  mounted() {
    var q = this.$route.query;
    if (q.channel == null || q.match == null || q.sports == null) {
      this.error = "Please select a channel to watch...";
    } else if (q.channel == "1") {
      this.expanded = true;
      this.$nextTick(() => {
        setTimeout(() => {
          this.launchLiveTV(q.channel, q.match, q.sports, "*******");
        }, 1000);
      });
    } else {
      this.expanded = true;
      this.getVersion(q).then(
        (res) => {
          this.$nextTick(() => {
            setTimeout(() => {
              this.launchLiveTV(q.channel, q.match, q.sports, res);
            }, 1000);
          });
        },
        (err) => {
          this.error = err;
        }
      );
    }
  },
  created() {
    $("html").addClass("minimal");
    $("body").addClass("minimal");
  },
  methods: {
    handleIFrame() {
      this.iframeLoaded = true;
    },
    getVersion(q) {
      return new Promise((resolve, reject) => {
        this.$http.get(config.ipify1).then(
          (res) => {
            resolve(res.bodyText);
          },
          (err) => {
            reject(err);
          }
        );
      });
    },
    clearWidget(e) {
      $("#widget").html(e);
    },
    setWidget(e) {
      $("#widget").html(e);
    },
    getDomain(url, subdomain) {
      subdomain = subdomain || false;

      url = url.replace(/(https?:\/\/)?(www.)?/i, "");

      if (!subdomain) {
        url = url.split(".");

        url = url.slice(url.length - 2).join(".");
      }

      if (url.indexOf("/") !== -1) {
        return url.split("/")[0];
      }

      return url;
    },
    tvLink(e) {
      if (config.vg1.includes(e)) {
        return config.player10Url();
      } else {
        return config.bintuUrl();
      }
    },
    launchLiveTV(ch, mc, sp, ip) {
      var args = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        channel: ch,
        match: mc,
        sports: sp,
        ip: ip,
        brand: config.brand,
      };

      xhrUtils.launchLiveTV(args).then(
        (res) => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              this.iframe = true;

              this.iframeEmbeded = this.url.includes("<iframe");
              if (!this.iframeEmbeded) {
                this.iframeEmbeded = this.url.includes("<script");
              }
              if (!this.iframeEmbeded) {
                // not a iframe link, can be glive or self-hosted live streaming
                //
                if (ch != 1) {
                  var link = this.url + "&compact=1";
                  var html =
                    '<iframe src="' +
                    link +
                    '" frameborder="0" allowfullscreen="true" scrolling="no" width="100%" height="100%" loading="lazy" />';
                  this.setWidget(html);
                  this.iframeLoaded = true;
                } else {
                  var link =
                    this.tvLink(sp) +
                    this.url +
                    "&u=" +
                    args.account_id +
                    "&t=" +
                    args.session_token +
                    "&product=" +
                    sp;
                  var html =
                    '<iframe src="' +
                    link +
                    '" frameborder="0" allowfullscreen="true" scrolling="no" width="100%" height="100%" loading="lazy" />';
                  this.setWidget(html);
                  this.iframeLoaded = true;
                }
              } else {
                // is a iframe link, can be twitch, youtube or any embeded video
                //
                // var domain = this.getDomain(window.location.hostname, false);
                var kns = this.url.replace("www.example.com", window.location.hostname);
                this.setWidget(kns);
              }
            } else {
              this.error = this.$t("error." + res.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.error = this.$t("error." + err.status);
        }
      );
    },
  },
};
</script>
