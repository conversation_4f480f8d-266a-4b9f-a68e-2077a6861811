<template lang="pug">
  .hx-main.hdpouslMMO
    //- MMO
    .hx-table.hx-match.hx-compact.hx-sl.hx-mmo(v-if="Object.keys(mmoDetails).length > 0 && mmoDetails['tn'] > 0" :class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }")
      .hx-cell.w-62
        .hx-row.h-100.hx-rows
          timePanel(:source="source")
      .hx-cell.flex-fill
        .hx-row.h-100.hx-rows
          xTeam(:source="source" isDraw=false cls="w-117")
      template(v-if="pageType == 1 || pageType == 3")
        .hx-cell.w-300
          .hx-row.hx-rows(v-for="(dn, i) in mmoDetails['kns']")
            .hx-col.hx-cols.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['hdp'] != null && mmoDetails['hdp'][i] && mmoDetails['hdp'][i][22] != null && mmoDetails['hdp'][i][22] != 0 && mmoDetails['hdp'][i][22] != ''")
                .hxs.justify-content-start
                  .ball-value.ball-mmo(v-if="mmoDetails['hdp'][i][7] == 1").d-flex
                    .mmo(:class="{ red : mmoDetails['hdp'][i][25] < 0 }") {{ mmoDetails['hdp'][i][25] }}
                    .percent(:class="{ red : mmoDetails['hdp'][i][24] < 0 }") ({{ mmoDetails['hdp'][i][24] }})
                    .giving H
                  .ball-value.ball-mmo(v-if="mmoDetails['hdp'][i][7] == 0").d-flex
                    .mmo(:class="{ red : mmoDetails['hdp'][i][25] < 0 }") {{ mmoDetails['hdp'][i][25] }}
                    .percent(:class="{ red : mmoDetails['hdp'][i][24] < 0 }") ({{ mmoDetails['hdp'][i][24] }})
                    .giving A
            .hx-col.hx-cols.hx-border.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['hdp'] != null && mmoDetails['hdp'][i] && mmoDetails['hdp'][i][22] != null && mmoDetails['hdp'][i][22] != 0 && mmoDetails['hdp'][i][22] != ''")
                .hxs
                  mmoItem(v-if="mmoDetails['hdp'][i][7] == 1" :odds="mmoDetails['hdp'][i]" idx=10 pos=22 :typ="oddsType" dataType="1")
                  mmoItem(v-if="mmoDetails['hdp'][i][7] == 0" :odds="mmoDetails['hdp'][i]" idx=9 pos=22 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['hdp'] != null && mmoDetails['hdp'][i] && mmoDetails['hdp'][i][22] != null && mmoDetails['hdp'][i][22] != 0 && mmoDetails['hdp'][i][22] != ''")
                .hxs
                  mmoItem(v-if="mmoDetails['hdp'][i][7] == 1" :odds="mmoDetails['hdp'][i]" idx=9 pos=22 :typ="oddsType" dataType="1")
                  mmoItem(v-if="mmoDetails['hdp'][i][7] == 0" :odds="mmoDetails['hdp'][i]" idx=10 pos=22 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['ou'] != null && mmoDetails['ou'][i] != null && mmoDetails['ou'][i][23] != null && mmoDetails['ou'][i][23] != 0 && mmoDetails['ou'][i][23] != ''")
                .hxs.justify-content-start
                  .ball-value.ball-mmo.d-flex
                    .mmo(:class="{ red : mmoDetails['ou'][i][25] < 0 }") {{ mmoDetails['ou'][i][25] }}
                    .percent(:class="{ red : mmoDetails['ou'][i][24] < 0 }") ({{ mmoDetails['ou'][i][24] }})
            .hx-col.hx-cols.hx-border.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['ou'] != null && mmoDetails['ou'][i] != null && mmoDetails['ou'][i][23] != null && mmoDetails['ou'][i][23] != 0 && mmoDetails['ou'][i][23] != ''")
                .hxs
                  mmoItem(:odds="mmoDetails['ou'][i]" idx=12 pos=23 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['ou'] != null && mmoDetails['ou'][i] != null && mmoDetails['ou'][i][23] != null && mmoDetails['ou'][i][23] != 0 && mmoDetails['ou'][i][23] != ''")
                .hxs
                  mmoItem(:odds="mmoDetails['ou'][i]" idx=11 pos=23 :typ="oddsType" dataType="1")
      template(v-if="pageType == 1 || pageType == 4")
        .hx-cell.w-300
          .hx-row.hx-rows(v-for="(dn, i) in mmoDetails['kns']")
            .hx-col.hx-cols.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['hdph'] != null && mmoDetails['hdph'][i] && mmoDetails['hdph'][i][22] != null && mmoDetails['hdph'][i][22] != 0 && mmoDetails['hdph'][i][22] != ''")
                .hxs.justify-content-start
                  .ball-value.ball-mmo(v-if="mmoDetails['hdph'][i][7] == 1").d-flex
                    .ball(:class="{ red : mmoDetails['hdph'][i][25] < 0 }") {{ mmoDetails['hdph'][i][25] }}
                    .percent(:class="{ red : mmoDetails['hdph'][i][24] < 0 }") ({{ mmoDetails['hdph'][i][24] }})
                    .giving H
                  .ball-value.ball-mmo(v-if="mmoDetails['hdph'][i][7] == 0").d-flex
                    .ball(:class="{ red : mmoDetails['hdph'][i][25] < 0 }") {{ mmoDetails['hdph'][i][25] }}
                    .percent(:class="{ red : mmoDetails['hdph'][i][24] < 0 }") ({{ mmoDetails['hdph'][i][24] }})
                    .giving A
            .hx-col.hx-cols.hx-border.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['hdph'] != null && mmoDetails['hdph'][i] && mmoDetails['hdph'][i][22] != null && mmoDetails['hdph'][i][22] != 0 && mmoDetails['hdph'][i][22] != ''")
                .hxs
                  mmoItem(v-if="mmoDetails['hdph'][i][7] == 1" :odds="mmoDetails['hdph'][i]" idx=10 pos=22 :typ="oddsType" dataType="1")
                  mmoItem(v-if="mmoDetails['hdph'][i][7] == 0" :odds="mmoDetails['hdph'][i]" idx=9 pos=22 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['hdph'] != null && mmoDetails['hdph'][i] && mmoDetails['hdph'][i][22] != null && mmoDetails['hdph'][i][22] != 0 && mmoDetails['hdph'][i][22] != ''")
                .hxs
                  mmoItem(v-if="mmoDetails['hdph'][i][7] == 1" :odds="mmoDetails['hdph'][i]" idx=9 pos=22 :typ="oddsType" dataType="1")
                  mmoItem(v-if="mmoDetails['hdph'][i][7] == 0" :odds="mmoDetails['hdph'][i]" idx=10 pos=22 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['ouh'] != null && mmoDetails['ouh'][i] != null && mmoDetails['ouh'][i][23] != null && mmoDetails['ouh'][i][23] != 0 && mmoDetails['ouh'][i][23] != ''")
                .hxs.justify-content-start
                  .ball-value.ball-mmo.d-flex
                    .ball(:class="{ red : mmoDetails['ouh'][i][25] < 0 }") {{ mmoDetails['ouh'][i][25] }}
                    .percent(:class="{ red : mmoDetails['ouh'][i][24] < 0 }") ({{ mmoDetails['ouh'][i][24] }})
            .hx-col.hx-cols.hx-border.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['ouh'] != null && mmoDetails['ouh'][i] != null && mmoDetails['ouh'][i][23] != null && mmoDetails['ouh'][i][23] != 0 && mmoDetails['ouh'][i][23] != ''")
                .hxs
                  mmoItem(:odds="mmoDetails['ouh'][i]" idx=12 pos=23 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-50px
              .hx.hx-flex-c.h-100(v-if="mmoDetails['ouh'] != null && mmoDetails['ouh'][i] != null && mmoDetails['ouh'][i][23] != null && mmoDetails['ouh'][i][23] != 0 && mmoDetails['ouh'][i][23] != ''")
                .hxs
                  mmoItem(:odds="mmoDetails['ouh'][i]" idx=11 pos=23 :typ="oddsType" dataType="1")
      .hx-cell.w-40
        .hx-row.h-100.hx-rows(v-if="menuX")
          .hx-col.hx-cols.w-100.d-flex.align-items-center.justify-content-center
            template(v-if="more > 0")
              .hx-more.collapsed(
                :id="'morehead_' + id"
                data-toggle='collapse'
                :aria-expanded="false"
                :data-target="'#morebet_' + id"
                :aria-controls="'morebet_' + id"
                @click="handleMore(matchId, $event.target)"
                )
                i.far.fa-chevron-up
                span &nbsp;{{ more }}

    //- NORMAL
    .hx-table.hx-match.hx-compact.hx-sl.hx-non-mmo(v-if="!menuX" :class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }")
      .hx-cell.w-62
        .hx-row.h-100.hx-rows
          timePanel(:source="source")
      .hx-cell.flex-fill
        .hx-row.h-100.hx-rows
          xTeam(:source="source" isDraw=false cls="w-117")
          xFavorite(:source="source")
      template(v-if="pageType == 1 || pageType == 3")
        .hx-cell.w-300
          .hx-row.hx-rows(v-for="(dn, i) in dlp")
            .hx-col.hx-cols.w-44
              .hx.hx-flex-c.h-100(v-if="details['hdp'] != null && details['hdp'][i] && details['hdp'][i][9] != 0 && details['hdp'][i][10] != 0 && details['hdp'][i][9] != '' && details['hdp'][i][10] != ''")
                .hxs.justify-content-start
                  .ball-value(v-if="details['hdp'][i][7] == 1") {{ details['hdp'][i][8] }}
                  .ball-value(v-if="details['hdp'][i][7] == 0") {{ details['hdp'][i][8] }}
            .hx-col.hx-cols.hx-border.w-53
              .hx.hx-flex-c.h-100(v-if="details['hdp'] != null && details['hdp'][i] && details['hdp'][i][9] != 0 && details['hdp'][i][10] != 0 && details['hdp'][i][9] != '' && details['hdp'][i][10] != ''")
                .hxs
                  oddsItem(v-if="details['hdp'][i][7] == 1" :odds="details['hdp'][i]" idx=10 :typ="oddsType" dataType="1")
                  oddsItem(v-if="details['hdp'][i][7] == 0" :odds="details['hdp'][i]" idx=9 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-53
              .hx.hx-flex-c.h-100(v-if="details['hdp'] != null && details['hdp'][i] && details['hdp'][i][9] != 0 && details['hdp'][i][10] != 0 && details['hdp'][i][9] != '' && details['hdp'][i][10] != ''")
                .hxs
                  oddsItem(v-if="details['hdp'][i][7] == 1" :odds="details['hdp'][i]" idx=9 :typ="oddsType" dataType="1")
                  oddsItem(v-if="details['hdp'][i][7] == 0" :odds="details['hdp'][i]" idx=10 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-44
              .hx.hx-flex-c.h-100(v-if="details['ou'] != null && details['ou'][i] != null && details['ou'][i][11] != 0 && details['ou'][i][12] != 0 && details['ou'][i][11] != '' && details['ou'][i][12] != ''")
                .hxs.justify-content-start
                  .ball-value {{ details['ou'][i][8] }}
            .hx-col.hx-cols.hx-border.w-53
              .hx.hx-flex-c.h-100(v-if="details['ou'] != null && details['ou'][i] != null && details['ou'][i][11] != 0 && details['ou'][i][12] != 0 && details['ou'][i][11] != '' && details['ou'][i][12] != ''")
                .hxs
                  oddsItem(:odds="details['ou'][i]" idx=12 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-53
              .hx.hx-flex-c.h-100(v-if="details['ou'] != null && details['ou'][i] != null && details['ou'][i][11] != 0 && details['ou'][i][12] != 0 && details['ou'][i][11] != '' && details['ou'][i][12] != ''")
                .hxs
                  oddsItem(:odds="details['ou'][i]" idx=11 :typ="oddsType" dataType="1")
      template(v-if="pageType == 1 || pageType == 4")
        .hx-cell.w-300
          .hx-row.hx-rows(v-for="(dn, i) in dlp")
            .hx-col.hx-cols.w-44
              .hx.hx-flex-c.h-100(v-if="details['hdph'] != null && details['hdph'][i] && details['hdph'][i][9] != 0 && details['hdph'][i][10] != 0 && details['hdph'][i][9] != '' && details['hdph'][i][10] != ''")
                .hxs.justify-content-start
                  .ball-value(v-if="details['hdph'][i][7] == 1") {{ details['hdph'][i][8] }}
                  .ball-value(v-if="details['hdph'][i][7] == 0") {{ details['hdph'][i][8] }}
            .hx-col.hx-cols.hx-border.w-53
              .hx.hx-flex-c.h-100(v-if="details['hdph'] != null && details['hdph'][i] && details['hdph'][i][9] != 0 && details['hdph'][i][10] != 0 && details['hdph'][i][9] != '' && details['hdph'][i][10] != ''")
                .hxs
                  oddsItem(v-if="details['hdph'][i][7] == 1" :odds="details['hdph'][i]" idx=10 :typ="oddsType" dataType="1")
                  oddsItem(v-if="details['hdph'][i][7] == 0" :odds="details['hdph'][i]" idx=9 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-53
              .hx.hx-flex-c.h-100(v-if="details['hdph'] != null && details['hdph'][i] && details['hdph'][i][9] != 0 && details['hdph'][i][10] != 0 && details['hdph'][i][9] != '' && details['hdph'][i][10] != ''")
                .hxs
                  oddsItem(v-if="details['hdph'][i][7] == 1" :odds="details['hdph'][i]" idx=9 :typ="oddsType" dataType="1")
                  oddsItem(v-if="details['hdph'][i][7] == 0" :odds="details['hdph'][i]" idx=10 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-44
              .hx.hx-flex-c.h-100(v-if="details['ouh'] != null && details['ouh'][i] != null && details['ouh'][i][11] != 0 && details['ouh'][i][12] != 0 && details['ouh'][i][11] != '' && details['ouh'][i][12] != ''")
                .hxs.justify-content-start
                  .ball-value {{ details['ouh'][i][8] }}
            .hx-col.hx-cols.hx-border.w-53
              .hx.hx-flex-c.h-100(v-if="details['ouh'] != null && details['ouh'][i] != null && details['ouh'][i][11] != 0 && details['ouh'][i][12] != 0 && details['ouh'][i][11] != '' && details['ouh'][i][12] != ''")
                .hxs
                  oddsItem(:odds="details['ouh'][i]" idx=12 :typ="oddsType" dataType="1")
            .hx-col.hx-cols.hx-border.w-53
              .hx.hx-flex-c.h-100(v-if="details['ouh'] != null && details['ouh'][i] != null && details['ouh'][i][11] != 0 && details['ouh'][i][12] != 0 && details['ouh'][i][11] != '' && details['ouh'][i][12] != ''")
                .hxs
                  oddsItem(:odds="details['ouh'][i]" idx=11 :typ="oddsType" dataType="1")
      .hx-cell.w-40
        .hx-row.h-100.hx-rows
          .hx-col.hx-cols.w-100.d-flex.align-items-center.justify-content-center
            template(v-if="more > 0")
              .hx-more.collapsed(
                :id="'morehead_' + id"
                data-toggle='collapse'
                :aria-expanded="false"
                :data-target="'#morebet_' + id"
                :aria-controls="'morebet_' + id"
                @click="handleMore(matchId, $event.target)"
                )
                i.far.fa-chevron-up
                span &nbsp;{{ more }}
    template(v-if="kns > MAX_ITEMS") 
      .hx-table.hx-ot-more(
        :class="{ live: source.marketId == 3, alternate: source.matchIndex % 2 == 0 }"        
      )
        .btn(@click="expand($event.target)")
          span(v-if="!expanded") +
          span(v-else) -
          span {{ kns - MAX_ITEMS }}
    template(v-if="more > 0")
      .hx-table.hx-match.hx-morebet.collapse(
        :id="'morebet_' + id"
        :aria-labelledby="'morehead_' + id"
        data-parent="#hdpou"
        :class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }"
        )
        morePanel(
          v-if="source.matchId == selectedMatch"
          ref="morePanel"
          :uid="id"
          :details="moreItems"
          :child1Ids="details['child1']"
          :child2Ids="details['child2']"
          :matchId="matchId"
          :leagueId="leagueId"
          :marketType="marketType"
          :sportsType="sportsType"
          :betType="betType"
          :layoutIndex="layoutIndex"
        )
</template>

<script>
import config from "@/config";
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

export default {
  components: {
    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    morePanel: () => import("@/components/desktop/main/xheader/morePanel"),
    xTeam: () => import("@/components/desktop/main/xtable/xitem/xTeam"),
    xFavorite: () => import("@/components/desktop/main/xtable/xitem/xFavorite"),
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem"),
    mmoItem: () => import("@/components/desktop/main/mmo/mmoItem")
  },
  mixins: [mixinHDPOUOdds],
  data() {
    return {
      expanded: false,
    };
  },
  computed: {
    MAX_ITEMS() {
      return config.OT_MAX_ITEMS;
    },
    pageType() {
      return this.$store.getters.pageDisplay.pageType
    },
    kns() {
      return this.details["kns"];
    },
    dlp() {
      if (this.expanded) {
        return this.kns;
      } else {
        if (this.kns > this.MAX_ITEMS) {
          return this.MAX_ITEMS;
        } else {
          return this.kns;
        }
      }
    },
    more() {
      return this.details["more"];
    },
    menuX() {
      return this.$store.getters.menuX;
    },
    menu0() {
      return this.$store.getters.menu0;
    }
  },
  methods: {
    expand(e) {
      this.expanded = !this.expanded;
    },
  },
};
</script>
