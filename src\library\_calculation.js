import store from "@/store";
import config from "@/config";

export default {
  countDecimals(v) {
    if (v == null) return null;
    if (Math.floor(v.valueOf()) === v.valueOf()) return 0;
    return v.toString().split(".")[1].length || 0;
  },

  truncateInteger(v) {
    if (v == null) return null;
    var r = v.toFixed(4).toString();
    if (r == "0") {
      return "";
    }
    var i = r.indexOf(".");
    if (i <= 0) {
      return r;
    }
    var rs = r.substr(0, i);
    return parseInt(rs);
  },

  truncate(v) {
    if (v == null) return null;
    var r = v.toFixed(4).toString();
    if (r == "0") {
      return "";
    }
    var i = r.indexOf(".");
    if (i <= 0) {
      return r;
    }
    var rs = r.substr(0, i + 3);
    return parseFloat(rs);
  },

  fmtType(v, c, bt) {
    if (v == null) return null;
    var r = v.toFixed(4).toString();
    var show_digit = 3;
    switch (c) {
      case "B":
        show_digit = 4;
        break;
      case "C":
        break;
      case "D":
        show_digit = 4;
        break;
      case "E":
        break;
      case "F":
        show_digit = 4;
    }
    switch (bt.toLowerCase()) {
      case "hdp":
      case "hdph":
      case "ou":
      case "ouh":
      case "oe":
      case "oeh":
      case "oxt":
      case "oxth":
      case "tw":
      case "twh":
        r = this.fm(r, false, show_digit);
        break;
      case "cs":
      case "csh":
        r = this.fmcs(r);
        break;
      default:
        r = this.fm(r, true, 3);
        break;
    }
    return r;
  },

  fmt(v, t = false) {
    if (v == null) return null;
    var r = v.toString();

    if (r == "0") {
      return "";
    }

    var i = r.indexOf(".");
    if (i <= 0) {
      return r;
    }

    var l = 4;
    var da = r.substr(i + 1, 3);
    var d1 = r.substr(i + 1, 1);
    var d2 = r.substr(i + 2, 1);
    var d3 = r.substr(i + 3, 1);

    if (d3 == "5") {
      l = 4;
    } else {
      if (t) {
        if (v >= 10) {
          if (da == "000") {
            l = 0;
          } else {
            if (d2 != "0") {
              l = 3;
            } else {
              if (d1 != "0") {
                l = 2;
              } else {
                l = 0;
              }
            }
          }
        }
      }
    }
    var rs = r.substr(0, i + l);
    return rs;
  },

  format(v) {
    if (v == null) return null;
    var r = v.toString();

    if (r == "0") {
      return "";
    }

    var i = r.indexOf(".");
    if (i <= 0) {
      return r;
    }

    var l = 4;
    var da = r.substr(i + 1, 3);
    var d1 = r.substr(i + 1, 1);
    var d2 = r.substr(i + 2, 1);
    var d3 = r.substr(i + 3, 1);

    if (d3 == "5") {
      l = 4;
    } else {
      if (da == "000") {
        l = 0;
      } else {
        if (d2 != "0") {
          l = 3;
        } else {
          if (d1 != "0") {
            l = 2;
          } else {
            l = 0;
          }
        }
      }
    }
    var rs = r.substr(0, i + l);
    return rs;
  },

  fm(v, t = false, digit = 3) {
    if (v == null) return null;
    var r = v.toString();

    if (r == "0") {
      return "";
    }

    var i = r.indexOf(".");
    if (i <= 0) {
      return r;
    }

    var l = digit;
    var da = r.substr(i + 1, 3);
    var d1 = r.substr(i + 1, 1);
    var d2 = r.substr(i + 2, 1);

    if (t) {
      if (v >= 10) {
        if (da == "000") {
          l = 0;
        } else {
          if (d2 != "0") {
            l = 3;
          } else {
            if (d1 != "0") {
              l = 2;
            } else {
              l = 0;
            }
          }
        }
      }
    }
    var rs = r.substr(0, i + l);
    return rs;
  },

  fmcs(v) {
    if (v == null) return null;
    var r = v.toString();

    if (r == "0") {
      return "";
    }

    var i = r.indexOf(".");
    if (i <= 0) {
      return r;
    }

    var l = 2;
    var da = r.substr(i + 1, 3);
    var db = r.substr(i + 1, 2);
    var d1 = r.substr(i + 1, 1);
    var d2 = r.substr(i + 2, 1);

    if (da == "000") {
      l = 0;
    } else {
      if (d2 != "0") {
        l = 3;
      } else {
        if (d1 != "0") {
          l = 2;
        } else {
          l = 0;
        }
      }
    }

    var rs = r.substr(0, i + l);
    return rs;
  },

  struncate(v) {
    var r = v.toFixed(4).toString();
    if (r == "0") {
      return "";
    }
    var i = r.indexOf(".");
    if (i <= 0) {
      return r;
    }
    var rs = r.substr(0, i + 3);
    return parseFloat(rs);
  },

  smark(odds) {
    var v = odds * 10;
    var r = v;
    if (v > 0) {
      r = this.truncate(v / 10 + 1);
    } else {
      if (v == 0 || v == null || v == undefined || v == "") {
        r = 0;
      } else {
        r = this.truncate((10 / v) * -1 + 1);
      }
    }
    return r;
  },

  collate(c, ot, od, parlay) {
    var minus_a = 0;
    var minus_b = 0;
    var show_digit = 3;
    // console.log(c, ot, od);

    switch (c) {
      case "B":
        minus_a = 0.005;
        minus_b = 0.05;
        show_digit = 4;
        break;
      case "C":
        minus_a = 0.01;
        minus_b = 0.1;
        break;
      case "D":
        minus_a = 0.015;
        minus_b = 0.15;
        show_digit = 4;
        break;
      case "E":
        minus_a = 0.02;
        minus_b = 0.2;
        break;
      case "F":
        minus_a = 0.025;
        minus_b = 0.25;
        show_digit = 4;
        break;
      case "J":
        minus_a = -0.01;
        minus_b = -0.1;
        break;
      case "I":
        minus_a = -0.005;
        minus_b = -0.05;
        show_digit = 4;
        break;
    }

    for (var n in od) {
      for (var k in od[n]) {
        // console.log(k);
        switch (k) {
          //------------------------
          case "hdp":
            for (var j in od[n][k]) {
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][13] == 0) {
                  delete od[n][k][j][i];
                } else {
                  var o9 = parseFloat(od[n][k][j][i][9]) - minus_b;
                  var o10 = parseFloat(od[n][k][j][i][10]) - minus_b;
                  if (o9 < 0 && o10 < 0) {
                    delete od[n][k][j][i];
                  } else {
                    od[n][k][j][i].push(od[n][k][j][i][9]);
                    od[n][k][j][i].push(od[n][k][j][i][10]);

                    if (od[n][k][j][i][9] !== 0) {
                      if (parlay) {
                        od[n][k][j][i][9] = (this.mark(ot, od[n][k][j][i][9], minus_b).toFixed(3) - od[n][k][j][i][16]).toFixed(3);
                      } else {
                        od[n][k][j][i][9] = this.mark(ot, od[n][k][j][i][9], minus_b).toFixed(3);
                      }
                    }
                    if (od[n][k][j][i][10] !== 0) {
                      if (parlay) {
                        od[n][k][j][i][10] = (this.mark(ot, od[n][k][j][i][10], minus_b).toFixed(3) - od[n][k][j][i][16]).toFixed(3);
                      } else {
                        od[n][k][j][i][10] = this.mark(ot, od[n][k][j][i][10], minus_b).toFixed(3);
                      }
                    }

                    od[n][k][j][i][9] = this.fm(od[n][k][j][i][9], false, show_digit);
                    od[n][k][j][i][10] = this.fm(od[n][k][j][i][10], false, show_digit);

                    // change from 1.01 to 1.05
                    if (parlay && (od[n][k][j][i][9] <= 1.05 || od[n][k][j][i][10] <= 1.05)) {
                      delete od[n][k][j][i];
                    } else {
                      od[n][k][j][i].push(this.smark(od[n][k][j][i][9]));
                      od[n][k][j][i].push(this.smark(od[n][k][j][i][10]));
                      od[n][k][j][i].push(od[n][k][j][i][11]);
                      od[n][k][j][i].push(od[n][k][j][i][12]);
                    }
                  }
                }
              }
            }
            break;
          case "ou":
            for (var j in od[n][k]) {
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][13] == 0) {
                  delete od[n][k][j][i];
                } else {
                  var o11 = parseFloat(od[n][k][j][i][11]) - minus_b;
                  var o12 = parseFloat(od[n][k][j][i][12]) - minus_b;
                  if (o11 < 0 && o12 < 0) {
                    delete od[n][k][j][i];
                  } else {
                    od[n][k][j][i].push(od[n][k][j][i][11]);
                    od[n][k][j][i].push(od[n][k][j][i][12]);
                    if (od[n][k][j][i][11] !== 0) {
                      if (parlay) {
                        od[n][k][j][i][11] = (this.mark(ot, od[n][k][j][i][11], minus_b).toFixed(3) - od[n][k][j][i][17]).toFixed(3);
                      } else {
                        od[n][k][j][i][11] = this.mark(ot, od[n][k][j][i][11], minus_b).toFixed(3);
                      }
                    }
                    if (od[n][k][j][i][12] !== 0) {
                      if (parlay) {
                        od[n][k][j][i][12] = (this.mark(ot, od[n][k][j][i][12], minus_b).toFixed(3) - od[n][k][j][i][17]).toFixed(3);
                      } else {
                        od[n][k][j][i][12] = this.mark(ot, od[n][k][j][i][12], minus_b).toFixed(3);
                      }
                    }
                    od[n][k][j][i][11] = this.fm(od[n][k][j][i][11], false, show_digit);
                    od[n][k][j][i][12] = this.fm(od[n][k][j][i][12], false, show_digit);

                    // change from 1.01 to 1.05
                    if (parlay && (od[n][k][j][i][11] <= 1.05 || od[n][k][j][i][12] <= 1.05)) {
                      delete od[n][k][j][i];
                    } else {
                      od[n][k][j][i].push(od[n][k][j][i][9]);
                      od[n][k][j][i].push(od[n][k][j][i][10]);
                      od[n][k][j][i].push(this.smark(od[n][k][j][i][11]));
                      od[n][k][j][i].push(this.smark(od[n][k][j][i][12]));
                    }
                  }
                }
              }
            }
            break;
          case "oe":
          case "oehm":
          case "oeaw":
            for (var j in od[n][k]) {
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][8] == 0) {
                  delete od[n][k][j][i];
                } else {
                  var o5 = parseFloat(od[n][k][j][i][5]) - minus_b;
                  var o7 = parseFloat(od[n][k][j][i][7]) - minus_b;
                  if (o5 < 0 && o7 < 0) {
                    delete od[n][k][j][i];
                  } else {
                    od[n][k][j][i].push(od[n][k][j][i][5]);
                    od[n][k][j][i].push(od[n][k][j][i][7]);
                    if (od[n][k][j][i][5] !== 0) {
                      if (parlay) {
                        od[n][k][j][i][5] = (this.mark(ot, od[n][k][j][i][5], minus_b).toFixed(3) - od[n][k][j][i][12]).toFixed(4);
                      } else {
                        od[n][k][j][i][5] = this.mark(ot, od[n][k][j][i][5], minus_b).toFixed(3);
                      }
                    }
                    if (od[n][k][j][i][7] !== 0) {
                      if (parlay) {
                        od[n][k][j][i][7] = (this.mark(ot, od[n][k][j][i][7], minus_b).toFixed(3) - od[n][k][j][i][12]).toFixed(4);
                      } else {
                        od[n][k][j][i][7] = this.mark(ot, od[n][k][j][i][7], minus_b).toFixed(3);
                      }
                    }
                    od[n][k][j][i][5] = this.fm(od[n][k][j][i][5], false, show_digit);
                    od[n][k][j][i][7] = this.fm(od[n][k][j][i][7], false, show_digit);

                    // change from 1.01 to 1.05
                    if (parlay && (od[n][k][j][i][5] <= 1.05 || od[n][k][j][i][7] <= 1.05)) {
                      delete od[n][k][j][i];
                    } else {
                      od[n][k][j][i].push(0);
                      od[n][k][j][i].push(this.smark(od[n][k][j][i][5]));
                      od[n][k][j][i].push(od[n][k][j][i][6]);
                      od[n][k][j][i].push(this.smark(od[n][k][j][i][7]));
                    }
                  }
                }
              }
            }
            break;
          case "ml":
            for (var j in od[n][k]) {
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][8] == 0) {
                  delete od[n][k][j][i];
                } else {
                  od[n][k][j][i][17] = od[n][k][j][i][17].toString();
                  od[n][k][j][i].push(od[n][k][j][i][5]);
                  od[n][k][j][i].push(od[n][k][j][i][7]);
                  if (od[n][k][j][i][5] !== 0) {
                    if (parlay) {
                      od[n][k][j][i][5] = ((ot, od[n][k][j][i][5]).toFixed(3) - od[n][k][j][i][11]).toFixed(3);
                    } else {
                      od[n][k][j][i][5] = (ot, od[n][k][j][i][5]).toFixed(3);
                    }
                  }
                  if (od[n][k][j][i][7] !== 0) {
                    if (parlay) {
                      od[n][k][j][i][7] = ((ot, od[n][k][j][i][7]).toFixed(3) - od[n][k][j][i][11]).toFixed(3);
                    } else {
                      od[n][k][j][i][7] = (ot, od[n][k][j][i][7]).toFixed(3);
                    }
                  }
                  // hide all if less than 1.01
                  od[n][k][j][i][5] = this.fm(od[n][k][j][i][5], false, show_digit);
                  od[n][k][j][i][7] = this.fm(od[n][k][j][i][7], false, show_digit);
                  od[n][k][j][i].push(0);

                  if (parlay) {
                    if (od[n][k][j][i][5] <= 1.05 || od[n][k][j][i][7] <= 1.05) {
                      delete od[n][k][j][i];
                    } else {
                      od[n][k][j][i].push(od[n][k][j][i][5]);
                      od[n][k][j][i].push(od[n][k][j][i][6]);
                      od[n][k][j][i].push(od[n][k][j][i][7]);
                    }
                  } else {
                    if (od[n][k][j][i][5] < 1.01 || od[n][k][j][i][7] < 1.01) {
                      delete od[n][k][j][i];
                    } else {
                      od[n][k][j][i].push(od[n][k][j][i][5]);
                      od[n][k][j][i].push(od[n][k][j][i][6]);
                      od[n][k][j][i].push(od[n][k][j][i][7]);
                    }
                  }
                }
              }
            }
            break;
          case "oxt":
          case "tw":
            for (var j in od[n][k]) {
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][8] == 0) {
                  delete od[n][k][j][i];
                } else {
                  od[n][k][j][i][17] = od[n][k][j][i][17].toString();
                  var auto_digit = show_digit;

                  // add to support 3 decimal odds 1X2
                  if (typeof od[n][k][j][i][5] == "number") {
                    if (this.countDecimals(od[n][k][j][i][5]) >= 3) {
                      auto_digit = 4;
                    }
                  }
                  od[n][k][j][i].push(od[n][k][j][i][5]);
                  od[n][k][j][i].push(od[n][k][j][i][6]);
                  od[n][k][j][i].push(od[n][k][j][i][7]);
                  if (od[n][k][j][i][5] !== 0) {
                    if (parlay) {
                      od[n][k][j][i][5] = ((ot, od[n][k][j][i][5]).toFixed(3) - od[n][k][j][i][11]).toFixed(3);
                    } else {
                      od[n][k][j][i][5] = (ot, od[n][k][j][i][5]).toFixed(3);
                    }
                  }
                  if (od[n][k][j][i][6] !== 0) {
                    if (parlay) {
                      od[n][k][j][i][6] = ((ot, od[n][k][j][i][6]).toFixed(3) - od[n][k][j][i][11]).toFixed(3);
                    } else {
                      od[n][k][j][i][6] = (ot, od[n][k][j][i][6]).toFixed(3);
                    }
                  }
                  if (od[n][k][j][i][7] !== 0) {
                    if (parlay) {
                      od[n][k][j][i][7] = ((ot, od[n][k][j][i][7]).toFixed(3) - od[n][k][j][i][11]).toFixed(3);
                    } else {
                      od[n][k][j][i][7] = (ot, od[n][k][j][i][7]).toFixed(3);
                    }
                  }

                  // hide all if less than 1.01
                  od[n][k][j][i][5] = this.fm(od[n][k][j][i][5], false, auto_digit);
                  od[n][k][j][i][6] = this.fm(od[n][k][j][i][6], false, auto_digit);
                  od[n][k][j][i][7] = this.fm(od[n][k][j][i][7], false, auto_digit);

                  if (parlay) {
                    if (store.getters.data.match) {
                      // check if special sports will not check for 1.05
                      var sp = store.getters.data.match[od[n][k][j][i][1]][2];
                      if (config.racingList.includes(sp)) {
                        od[n][k][j][i].push(od[n][k][j][i][5]);
                        od[n][k][j][i].push(od[n][k][j][i][6]);
                        od[n][k][j][i].push(od[n][k][j][i][7]);
                      } else {
                        if (od[n][k][j][i][5] <= 1.05 || od[n][k][j][i][6] <= 1.05 || od[n][k][j][i][7] <= 1.05) {
                          delete od[n][k][j][i];
                        } else {
                          od[n][k][j][i].push(od[n][k][j][i][5]);
                          od[n][k][j][i].push(od[n][k][j][i][6]);
                          od[n][k][j][i].push(od[n][k][j][i][7]);
                        }
                      }
                    } else {
                      if (od[n][k][j][i][5] <= 1.05 || od[n][k][j][i][6] <= 1.05 || od[n][k][j][i][7] <= 1.05) {
                        delete od[n][k][j][i];
                      } else {
                        od[n][k][j][i].push(od[n][k][j][i][5]);
                        od[n][k][j][i].push(od[n][k][j][i][6]);
                        od[n][k][j][i].push(od[n][k][j][i][7]);
                      }
                    }
                  } else {
                    if (od[n][k][j][i][5] < 1.01 || od[n][k][j][i][6] < 1.01 || od[n][k][j][i][7] < 1.01) {
                      delete od[n][k][j][i];
                    } else {
                      od[n][k][j][i].push(od[n][k][j][i][5]);
                      od[n][k][j][i].push(od[n][k][j][i][6]);
                      od[n][k][j][i].push(od[n][k][j][i][7]);
                    }
                  }
                }
              }
            }
            break;
          //------------------------
          case "cs":
            for (var j in od[n][k]) {
              for (var i in od[n][k][j]) {
                // console.log(od[n][k][j][i]);
                if (parlay && od[n][k][j][i][8] == 0) {
                  delete od[n][k][j][i];
                } else {
                  od[n][k][j][i].push(od[n][k][j][i][5]);
                  if (od[n][k][j][i][5] !== 0) {
                    if (parlay) {
                      od[n][k][j][i][5] = ((ot, od[n][k][j][i][5]).toFixed(3) - od[n][k][j][i][10]).toFixed(3);
                    } else {
                      od[n][k][j][i][5] = (ot, od[n][k][j][i][5]).toFixed(3);
                    }
                  }
                  if (parlay && od[n][k][j][i][5] <= 1.05) {
                    delete od[n][k][j][i];
                  } else {
                    od[n][k][j][i][5] = this.fmcs(od[n][k][j][i][5]);
                  }
                }
              }
            }
            break;
          //------------------------
          case "dc":
          case "htft":
          case "hnb":
          case "anb":
          case "dnb":
          case "twtn":
          case "ouoe":
          case "htftoe":
            var nb2 = {};
            for (var j in od[n][k]) {
              nb2[j] = {
                dc: {
                  DA: false,
                  HA: false,
                  HD: false,
                },
                htft: {
                  HH: false,
                  HD: false,
                  HA: false,
                  DH: false,
                  DD: false,
                  DA: false,
                  AH: false,
                  AD: false,
                  AA: false,
                },
                hnb: {
                  A: false,
                  D: false,
                },
                anb: {
                  H: false,
                  D: false,
                },
                dnb: {
                  H: false,
                  A: false,
                },
                twtn: {
                  H: false,
                  A: false,
                },
                ouoe: {
                  OO: false,
                  OE: false,
                  UO: false,
                  UE: false,
                },
                htftoe: {
                  OO: false,
                  OE: false,
                  EO: false,
                  EE: false,
                },
              };
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][8] == 0) {
                  delete od[n][k][j][i];
                } else {
                  od[n][k][j][i].push(od[n][k][j][i][5]);
                  if (od[n][k][j][i][5] !== 0) {
                    if (parlay) {
                      od[n][k][j][i][5] = ((ot, od[n][k][j][i][5]).toFixed(3) - od[n][k][j][i][10]).toFixed(3);
                    } else {
                      od[n][k][j][i][5] = (ot, od[n][k][j][i][5]).toFixed(3);
                    }
                  }
                  if (parlay && od[n][k][j][i][5] <= 1.05) {
                    delete od[n][k][j][i];
                  } else {
                    od[n][k][j][i][5] = this.fm(od[n][k][j][i][5], true, 3);
                    nb2[j][k][od[n][k][j][i][6]] = true;
                  }
                }
              }
            }

            // Remove empty nested objects
            //----------------------------
            for (var n2 in nb2) {
              for (var n3 in nb2[n2][k]) {
                if (nb2[n2][k][n3] == false) {
                  delete od[n][k][n2];
                  break;
                }
              }
            }
            break;

          //------------------------
          case "oxtou":
          case "dcou":
          case "wm":
          case "bs":
          case "cl":
            var nb2 = {};
            for (var j in od[n][k]) {
              nb2[j] = {
                oxtou: {
                  "1O": false,
                  "1U": false,
                  XO: false,
                  XU: false,
                  "2O": false,
                  "2U": false,
                },
                dcou: {
                  HDO: false,
                  HDU: false,
                  HAO: false,
                  HAU: false,
                  DAO: false,
                  DAU: false,
                },
                wm: {
                  H1: false,
                  H2: false,
                  H3Up: false,
                  D: false,
                  A1: false,
                  A2: false,
                  A3Up: false,
                  NG: false,
                },
                bs: {
                  BOTH: false,
                  NG: false,
                  ONE: false,
                },
                cl: {
                  AN: false,
                  AY: false,
                  HN: false,
                  HY: false,
                },
              };
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][8] == 0) {
                  delete od[n][k][j][i];
                } else {
                  od[n][k][j][i].push(od[n][k][j][i][5]);
                  if (od[n][k][j][i][5] !== 0) {
                    if (parlay) {
                      od[n][k][j][i][5] = ((ot, od[n][k][j][i][5]).toFixed(3) - od[n][k][j][i][10]).toFixed(3);
                    } else {
                      od[n][k][j][i][5] = (ot, od[n][k][j][i][5]).toFixed(3);
                    }
                  }
                  if (parlay && od[n][k][j][i][5] <= 1.05) {
                    delete od[n][k][j][i];
                  } else {
                    od[n][k][j][i][5] = this.fm(od[n][k][j][i][5], true, 3);
                    nb2[j][k][od[n][k][j][i][6]] = true;
                  }
                }
              }
            }

            // Remove empty nested objects
            //----------------------------
            // if (["oxtou"].includes(k)) {
            //   for (var n2 in nb2) {
            //     for (var n3 in nb2[n2][k]) {
            //       if (nb2[n2][k][n3] == false) {
            //         console.log(od[n][k][n2]);
            //         delete od[n][k][n2];
            //         break;
            //       }
            //     }
            //   }
            // }
            break;
          //------------------------
          case "ehtg":
          case "eatg":
            var nb2 = {};
            for (var j in od[n][k]) {
              nb2[j] = {
                ehtg: {
                  0: false,
                  1: false,
                  2: false,
                  "3Up": false,
                },
                eatg: {
                  0: false,
                  1: false,
                  2: false,
                  "3Up": false,
                },
              };
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][8] == 0) {
                  delete od[n][k][j][i];
                } else {
                  od[n][k][j][i].push(od[n][k][j][i][5]);
                  if (od[n][k][j][i][5] !== 0) {
                    if (parlay) {
                      od[n][k][j][i][5] = ((ot, od[n][k][j][i][5]).toFixed(3) - od[n][k][j][i][10]).toFixed(3);
                    } else {
                      od[n][k][j][i][5] = (ot, od[n][k][j][i][5]).toFixed(3);
                    }
                  }
                  if (parlay && od[n][k][j][i][5] <= 1.05) {
                    delete od[n][k][j][i];
                  } else {
                    od[n][k][j][i][5] = this.fm(od[n][k][j][i][5], true, 3);
                    nb2[j][k][od[n][k][j][i][6]] = true;
                  }
                }
              }
            }

            // Remove empty nested objects
            //----------------------------
            // for (var n2 in nb2) {
            //   for (var n3 in nb2[n2][k]) {
            //     if (nb2[n2][k][n3] == false) {
            //       delete od[n][k][n2];
            //       break;
            //     }
            //   }
            // }
            break;
          //------------------------
          case "etg":
            var nb2 = {};
            for (var j in od[n][k]) {
              nb2[j] = {
                ETG: {
                  0: false,
                  1: false,
                  2: false,
                  3: false,
                  4: false,
                  5: false,
                  "6Up": false,
                },
                ETGH: {
                  0: false,
                  1: false,
                  2: false,
                  "3Up": false,
                },
              };
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][8] == 0) {
                  delete od[n][k][j][i];
                } else {
                  od[n][k][j][i].push(od[n][k][j][i][5]);
                  if (od[n][k][j][i][5] !== 0) {
                    if (parlay) {
                      od[n][k][j][i][5] = ((ot, od[n][k][j][i][5]).toFixed(3) - od[n][k][j][i][10]).toFixed(3);
                    } else {
                      od[n][k][j][i][5] = (ot, od[n][k][j][i][5]).toFixed(3);
                    }
                  }
                  if (parlay && od[n][k][j][i][5] <= 1.05) {
                    delete od[n][k][j][i];
                  } else {
                    od[n][k][j][i][5] = this.fm(od[n][k][j][i][5], true, 3);
                    nb2[j][od[n][k][j][i][4]][od[n][k][j][i][6]] = true;
                  }
                }
              }
            }

            // Remove empty nested objects
            //----------------------------
            // for (var n2 in nb2) {
            //   for (var n3 in nb2[n2]) {
            //     for (var n4 in nb2[n2][n3]) {
            //       if (nb2[n2][n3][n4] == false) {
            //         delete od[n][k][n2];
            //         break;
            //       }
            //     }
            //   }
            // }
            break;
          //------------------------
          case "cshtft":
          case "etghtft":
            for (var j in od[n][k]) {
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][38] == 0) {
                  delete od[n][k][j][i];
                } else {
                  var cols = [];
                  var odnkjin = 5;
                  while (odnkjin <= 35) {
                    od[n][k][j][i].push(od[n][k][j][i][odnkjin]);
                    if (od[n][k][j][i][odnkjin] != null) {
                      if (od[n][k][j][i][odnkjin] !== 0) {
                        if (parlay) {
                          od[n][k][j][i][odnkjin] = ((ot, od[n][k][j][i][odnkjin]).toFixed(3) - od[n][k][j][i][40]).toFixed(3);
                        } else {
                          od[n][k][j][i][odnkjin] = (ot, od[n][k][j][i][odnkjin]).toFixed(3);
                        }
                      }
                      if (parlay && od[n][k][j][i][odnkjin] <= 1.05) {
                        od[n][k][j][i][odnkjin] = null;
                      } else {
                        od[n][k][j][i][odnkjin] = this.fmcs(od[n][k][j][i][odnkjin]);
                        cols.push(odnkjin - 5);
                      }
                    }
                    odnkjin = odnkjin + 1;
                  }
                  od[n][k][j][i].push(cols);
                }
              }
            }
            break;
          default:
            for (var j in od[n][k]) {
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][8] == 0) {
                  delete od[n][k][j][i];
                } else {
                  od[n][k][j][i].push(od[n][k][j][i][5]);
                  if (od[n][k][j][i][5] !== 0) {
                    if (parlay) {
                      od[n][k][j][i][5] = ((ot, od[n][k][j][i][5]).toFixed(3) - od[n][k][j][i][10]).toFixed(3);
                    } else {
                      od[n][k][j][i][5] = (ot, od[n][k][j][i][5]).toFixed(3);
                    }
                  }
                  if (parlay && od[n][k][j][i][5] <= 1.05) {
                    delete od[n][k][j][i];
                  } else {
                    od[n][k][j][i][5] = this.fm(od[n][k][j][i][5], true, 3);
                  }
                }
              }
            }
            break;
        }
      }
    }
  },

  collate_mmo(c, ot, od, parlay) {
    var show_digit = 3;

    for (var n in od) {
      for (var k in od[n]) {
        switch (k) {
          case "hdp":
            for (var j in od[n][k]) {
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][13] == 0) {
                  delete od[n][k][j][i];
                } else {
                  od[n][k][j][i].push(od[n][k][j][i][22]);
                  od[n][k][j][i][23] = 0;
                  if (od[n][k][j][i][22] !== 0) {
                    od[n][k][j][i][22] = this.mark("MY", od[n][k][j][i][22], 0).toFixed(3);
                  }
                  od[n][k][j][i][22] = this.fm(od[n][k][j][i][22], false, show_digit);
                  od[n][k][j][i].push(this.smark(od[n][k][j][i][22]));
                }
              }
            }
            break;
          case "ou":
            for (var j in od[n][k]) {
              for (var i in od[n][k][j]) {
                if (parlay && od[n][k][j][i][13] == 0) {
                  delete od[n][k][j][i];
                } else {
                  od[n][k][j][i].push(od[n][k][j][i][23]);
                  od[n][k][j][i][22] = 0;
                  if (od[n][k][j][i][23] !== 0) {
                    od[n][k][j][i][23] = this.mark("MY", od[n][k][j][i][23], 0).toFixed(3);
                  }
                  od[n][k][j][i][23] = this.fm(od[n][k][j][i][23], false, show_digit);
                  od[n][k][j][i].push(this.smark(od[n][k][j][i][23]));
                }
              }
            }
            break;
        }
      }
    }

    // console.log(c, ot, od, parlay);
  },

  convertSingleToParlay(c, ot, od, parlay) {
    var minus_a = 0;
    var minus_b = 0;
    var show_digit = 3;

    switch (c) {
      case "B":
        minus_a = 0.005;
        minus_b = 0.05;
        show_digit = 4;
        break;
      case "C":
        minus_a = 0.01;
        minus_b = 0.1;
        break;
      case "D":
        minus_a = 0.015;
        minus_b = 0.15;
        show_digit = 4;
        break;
      case "E":
        minus_a = 0.02;
        minus_b = 0.2;
        break;
      case "F":
        minus_a = 0.025;
        minus_b = 0.25;
        show_digit = 4;
    }

    if (ot == "MY" || ot == "HK" || ot == "ID") {
      ot = "DEC";
    }
    switch (od[4].toLowerCase()) {
      case "hdp":
      case "hdph":
        if (od[23] !== 0) {
          if (parlay) {
            od[9] = (this.mark(ot, od[23], minus_b).toFixed(3) - od[16]).toFixed(3);
          } else {
            od[9] = this.mark(ot, od[23], minus_b).toFixed(3);
          }
        }
        if (od[24] !== 0) {
          if (parlay) {
            od[10] = (this.mark(ot, od[24], minus_b).toFixed(3) - od[16]).toFixed(3);
          } else {
            od[10] = this.mark(ot, od[24], minus_b).toFixed(3);
          }
        }
        od[9] = this.fm(od[9], false, show_digit);
        od[10] = this.fm(od[10], false, show_digit);
        break;
      case "ou":
      case "ouh":
        if (od[23] !== 0) {
          if (parlay) {
            od[11] = (this.mark(ot, od[23], minus_b).toFixed(3) - od[17]).toFixed(3);
          } else {
            od[11] = this.mark(ot, od[23], minus_b).toFixed(3);
          }
        }
        if (od[24] !== 0) {
          if (parlay) {
            od[12] = (this.mark(ot, od[24], minus_b).toFixed(3) - od[17]).toFixed(3);
          } else {
            od[12] = this.mark(ot, od[24], minus_b).toFixed(3);
          }
        }
        od[11] = this.fm(od[11], false, show_digit);
        od[12] = this.fm(od[12], false, show_digit);
        break;
      case "oe":
      case "oeh":
        if (od[18] !== 0) {
          if (parlay) {
            od[5] = (this.mark(ot, od[18], minus_b).toFixed(3) - od[12]).toFixed(4);
          } else {
            od[5] = this.mark(ot, od[18], minus_b).toFixed(3);
          }
        }
        if (od[19] !== 0) {
          if (parlay) {
            od[7] = (this.mark(ot, od[19], minus_b).toFixed(3) - od[12]).toFixed(4);
          } else {
            od[7] = this.mark(ot, od[19], minus_b).toFixed(3);
          }
        }
        od[5] = this.fm(od[5], false, show_digit);
        od[7] = this.fm(od[7], false, show_digit);
        break;
      case "ml":
      case "mlh":
        if (od[18] !== 0) {
          if (parlay) {
            od[5] = ((ot, od[18]).toFixed(3) - od[11]).toFixed(3);
          } else {
            od[5] = (ot, od[18]).toFixed(3);
          }
        }
        if (od[19] !== 0) {
          if (parlay) {
            od[7] = ((ot, od[19]).toFixed(3) - od[11]).toFixed(3);
          } else {
            od[7] = (ot, od[19]).toFixed(3);
          }
        }
        od[5] = this.fm(od[5], false, show_digit);
        od[7] = this.fm(od[7], false, show_digit);
        break;
      case "1x2":
      case "1x2hdp":
      case "1x2h":
        if (od[18] !== 0) {
          if (parlay) {
            od[5] = ((ot, od[18]).toFixed(3) - od[11]).toFixed(3);
          } else {
            od[5] = (ot, od[18]).toFixed(3);
          }
        }
        if (od[19] !== 0) {
          if (parlay) {
            od[6] = ((ot, od[19]).toFixed(3) - od[11]).toFixed(3);
          } else {
            od[6] = (ot, od[19]).toFixed(3);
          }
        }
        if (od[20] !== 0) {
          if (parlay) {
            od[7] = ((ot, od[20]).toFixed(3) - od[11]).toFixed(3);
          } else {
            od[7] = (ot, od[20]).toFixed(3);
          }
        }
        od[5] = this.fm(od[5], false, show_digit);
        od[6] = this.fm(od[6], false, show_digit);
        od[7] = this.fm(od[7], false, show_digit);
        break;
      case "cs":
      case "csh":
        if (od[17] !== 0) {
          if (parlay) {
            od[5] = ((ot, od[17]).toFixed(3) - od[10]).toFixed(3);
          } else {
            od[5] = (ot, od[17]).toFixed(3);
          }
        }
        od[5] = this.fmcs(od[5]);
        break;
      case "or":
        if (od[15] !== 0) {
          if (parlay) {
            od[5] = ((ot, od[15]).toFixed(3) - od[10]).toFixed(3);
          } else {
            od[5] = (ot, od[15]).toFixed(3);
          }
        }
        od[5] = this.fm(od[5], false, 3);
        break;
      default:
        if (od[17] !== 0) {
          if (parlay) {
            od[5] = ((ot, od[17]).toFixed(3) - od[10]).toFixed(3);
          } else {
            od[5] = (ot, od[17]).toFixed(3);
          }
        }
        od[5] = this.fm(od[5], true, 3);
        break;
    }

    return od;
  },

  mark(t, v, m) {
    var r = null;
    var odds = parseFloat(v) - parseFloat(m);
    if (odds < -10.0) {
      odds = 20 + odds;
    } else if (odds > 10.0) {
      odds = odds - 20;
    }
    switch (t) {
      case "MY":
        r = odds / 10;
        break;
      case "HK":
        if (odds > 0) {
          r = odds / 10;
        } else {
          r = this.truncate((10 / odds) * -1);
        }
        break;
      case "ID":
        r = this.truncate((10 / odds) * -1);
        break;
      case "DEC":
        if (odds > 0) {
          r = odds / 10 + 1;
        } else {
          if (odds == 0) {
            r = 0;
          } else {
            r = this.truncate((10 / odds) * -1 + 1);
          }
        }
        break;
      default:
        r = null;
        break;
    }
    // v = null;
    return r;
  },
};
