<template lang="pug">
.info-wrapper
  .info-title
    .info-icon
      i.fad.fa-envelope
    .page-title(aria-label='breadcrumb')
      ol.breadcrumb.p-0.m-0
        li.breadcrumb-item.active(aria-current='page') {{ $t("ui.message_history") }}
    SpinButton(text="" :loading="loading" css="btn-sm btn-info" @click="getData" img="fad fa-sync-alt w-16px")
  .info-tablewrap.magicZ
    ul.nav.nav-tabs(role='tablist')
      li.nav-item
        a#tab-normal.nav-link.active(
          data-toggle='tab'
          href='#normal'
          role='tab'
          aria-controls='normal'
          aria-selected='true'
          @click="getNormalAnnouncement"
        ) {{ $t("ui.normal_announcements") }}
      li.nav-item
        a#tab-special.nav-link(
          data-toggle='tab'
          href='#special'
          role='tab'
          aria-controls='special'
          aria-selected='false'
          @click="getSpecialAnnouncement"
        ) {{ $t("ui.special_announcements") }}
      li.nav-item
        a#tab-personal.nav-link(
          data-toggle='tab'
          href='#personal'
          role='tab'
          aria-controls='personal'
          aria-selected='false'
          @click="getPersonalMessage"
        ) {{ $t("ui.personal_message") }}
    .tab-content
      #normal.tab-pane.show.active(role='tabpanel' aria-labelledby='tab-normal')
        .result-selection.p-2
          .d-flex.flex-row
            .mr-2
              .input-group
                .input-group-prepend
                  span.input-group-text
                    i.fad.fa-calendar-alt
                date-picker.datepicker(v-model="dateFrom" :config="locale" @dp-change="getData")

            .mr-2
              span.text-common {{ $t("ui.to") }}
            .mr-2
              .input-group
                .input-group-prepend
                  span.input-group-text
                    i.fad.fa-calendar-alt
                date-picker.datepicker(v-model="dateTo" :config="locale" @dp-change="getData")

        table.table-info.table-message(width='100%')
          tbody
            tr
              th.text-center(scope='col' width='5%') {{ $t("ui.no/") }}
              th.text-center(scope='col' width='5%') {{ $t("ui.id") }}
              th(scope='col' width='20%') {{ $t("ui.date") }}
              th(scope='col' width='70%') {{ $t("ui.message") }}
            tr(v-for="(ann, index) in naList.list" :class="{ grey: index % 2 === 0 }")
              td.text-center(valign='top') {{ index+1 }}
              td.text-center(valign='top') {{ ann.an_id }}
              td(valign='top') {{ $dayjs(ann.an_date).format("MM/DD/YYYY hh:mm A") }}
              td
                tr
                  span.font-weight-bold {{ extractSport(ann[`msg_${$store.getters.language}`] || ann.msg_en) }}
                  span(v-if="checkMssg(ann[`msg_${$store.getters.language}`] || ann.msg_en) >= 2") :
                  span {{ extractMsg(ann[`msg_${$store.getters.language}`] || ann.msg_en) }}
        .no-message.mt-1(v-if="naList.list && naList.list.length == 0") {{ $t("message.no_information_available") }}
      #special.tab-pane(role='tabpanel' aria-labelledby='tab-special')
        table.table-info.table-message(width='100%')
          tbody
            tr
              th.text-center(scope='col' width='5%') {{ $t("ui.no/") }}
              th.text-center(scope='col' width='5%') {{ $t("ui.id") }}
              th(scope='col' width='20%') {{ $t("ui.date") }}
              th(scope='col' width='70%') {{ $t("ui.message") }}
            tr(v-for="(ann, index) in saList.list" :class="{ grey: index % 2 === 0 }")
              td.text-center(valign='top') {{ index+1 }}
              td.text-center(valign='top') {{ ann.an_id }}
              td(valign='top') {{ $dayjs(ann.an_date).format("MM/DD/YYYY hh:mm A") }}
              td
                tr
                  span.font-weight-bold {{ extractSport(ann[`msg_${$store.getters.language}`] || ann.msg_en) }}
                  span(v-if="checkMssg(ann[`msg_${$store.getters.language}`] || ann.msg_en) >= 2") :
                  span {{ extractMsg(ann[`msg_${$store.getters.language}`] || ann.msg_en) }}
        .no-message.mt-1(v-if="saList.list && saList.list.length == 0") {{ $t("message.no_information_available") }}
      #personal.tab-pane(role='tabpanel' aria-labelledby='tab-personal')
        .result-selection.p-2
          .d-flex.flex-row
            .mr-2
              .input-group
                .input-group-prepend
                  span.input-group-text
                    i.fad.fa-calendar-alt
                date-picker.datepicker(v-model="dateFrom" :config="locale" @dp-change="getData")

            .mr-2
              span.text-common {{ $t("ui.to") }}
            .mr-2
              .input-group
                .input-group-prepend
                  span.input-group-text
                    i.fad.fa-calendar-alt
                date-picker.datepicker(v-model="dateTo" :config="locale" @dp-change="getData")

        table.table-info.table-message(width='100%')
          tbody
            tr
              th.text-center(scope='col' width='5%') {{ $t("ui.no/") }}
              th.text-center(scope='col' width='5%') {{ $t("ui.id") }}
              th(scope='col' width='20%') {{ $t("ui.date") }}
              th(scope='col' width='70%') {{ $t("ui.message") }}
            tr(v-for="(ann, index) in pmList.list" :class="{ grey: index % 2 === 0 }")
              td.text-center(valign='top') {{ index+1 }}
              td.text-center(valign='top') {{ ann.an_id }}
              td(valign='top') {{ $dayjs(ann.an_date).format("MM/DD/YYYY hh:mm A") }}
              td
                tr
                  span.font-weight-bold {{ extractSport(ann[`msg_${$store.getters.language}`] || ann.msg_en) }}
                  span(v-if="checkMssg(ann[`msg_${$store.getters.language}`] || ann.msg_en) >= 2") :
                  span {{ extractMsg(ann[`msg_${$store.getters.language}`] || ann.msg_en) }}
        .no-message.mt-1(v-if="pmList.list && pmList.list.length == 0") {{ $t("message.no_information_available") }}
  .notes
    p.mb-0 {{ $t("ui.note") }}:
    ul
      li {{ $t("message.time_display_in_gmt_plus_8") }}
</template>
<script>
import config from "@/config";
import errors from "@/errors";
import service from "@/library/_xhr-message";
import SpinButton from "@/components/ui/SpinButton";

export default {
  components: {
    SpinButton,
  },
  data() {
    return {
      loading: false,
      dateFrom: new Date(new Date().setDate(new Date().getDate() - 7))
        .toISOString()
        .split("T")[0],
      dateTo: new Date().toISOString().split("T")[0],
      locale: {
        format: "YYYY-MM-DD",
        useCurrent: true,
        minDate: this.$dayjs(
          this.$dayjs().startOf("day").subtract(1, "year").toDate()
        ).format("YYYY-MM-DD"),
        maxDate: this.$dayjs().format("YYYY-MM-DD"),
      },
      naList: {},
      saList: {},
      pmList: {},
      selected: "normal",
      feedback: {
        success: false,
        status: errors.session.invalidSession,
      },
    };
  },
  watch: {
    dateFrom(val) {
      if (val >= this.dateTo) {
        this.dateFrom = this.dateTo;
      }
    },
    dateTo(val) {
      if (val <= this.dateFrom) {
        this.dateTo = this.dateFrom;
      }
    },
  },
  mounted() {
    if (this.$route.query.redir === "p") {
      $("#tab-personal").click();
      this.getPersonalMessage();
    }
    this.getNormalAnnouncement();
  },
  methods: {
    getData() {
      if (this.selected === "normal") this.getNormalAnnouncement();
      if (this.selected === "special") this.getSpecialAnnouncement();
      if (this.selected === "personal") this.getPersonalMessage();
    },
    getNormalAnnouncement() {
      this.selected = "normal";
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        datefrom: this.dateFrom,
        dateto: this.dateTo,
        an_type: 1,
      };

      this.loading = true;

      service.getAnnoucement(json).then(
        (result) => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.$set(this.naList, "list", result.data);
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getSpecialAnnouncement() {
      this.selected = "special";
      this.saList = [];
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        datefrom: this.dateFrom,
        dateto: this.dateTo,
        an_type: 2,
      };

      this.loading = true;

      service.getAnnoucement(json).then(
        (result) => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.$set(this.saList, "list", result.data);
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getPersonalMessage() {
      this.selected = "personal";
      this.pmList = [];
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        datefrom: this.dateFrom,
        dateto: this.dateTo,
      };

      this.loading = true;

      service.getPersonalMessage(json).then(
        (result) => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.$set(this.pmList, "list", result.data);
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    checkMssg(msg) {
      return msg.split(":").length;
    },
    extractSport(msg) {
      return msg.split(":")[0];
    },
    extractMsg(msg) {
      return msg.split(":").slice(1).join(":");
    },
  },
};
</script>
