import errors from "@/errors";
import service from "@/library/_xhr-user";

export default {
  namespaced: true,
  state: {
    balance: 0,
    account: {},
    rememberMe: null,
    isPublic: false
  },
  mutations: {
    // setLoadingState(state, payload) {
    //   const action = payload.action;
    //   const status = payload.status;
    //   state.loading[action] = status;
    // },
    updateAccount(state, payload) {
      // console.log(payload);
      state.account = payload;
      if (state.account.player_wallet) {
        state.balance = state.account.player_wallet.available_balance;
      }
    },
    deleteAccount(state) {
      state.account = {};
      state.balance = 0;
      state.mmoMode = false;
    },
    updateBalance(state, payload) {
      state.balance = payload.balance;
      if (state.account != null && state.account.player_wallet != null) {
        state.account.player_wallet.available_balance = payload.balance;
        state.account.player_wallet.cash_balance = payload.account_balance;
        state.account.player_wallet.frozen_balance = payload.outstanding_balance;
      }
    },
    updateNickname(state, payload) {
      if (state.account.player_info) {
        if (state.account.player_info) {
          state.account.player_info.nickname = payload;
          state.account.player_info.has_secondary_account = true;
        }
      }
    },
    updateRememberMe(state, payload) {
      state.rememberMe = payload;
    }
  },
  actions: {
    doLaunch(context, user) {
      return service.doLaunch(context, user);
    },
    doSwitch(context, user) {
      return service.doSwitch(context, user);
    },
    doLogin(context, user) {
      return service.doLogin(context, user);
    },
    doLogout(context) {
      const feedback = {
        success: true,
        status: errors.logout.succeed
      };
      return new Promise(resolve => {
        context.commit("deleteAccount");
        resolve(feedback);
      });
    },
    clear(context) {
      context.commit("deleteAccount");
    },
    reLogin(context) {
      return service.reLogin(context);
    },
    getBalance(context) {
      return service.getBalance(context);
    },
    setNickname(context, payload) {
      const feedback = {
        success: true,
        status: errors.request.succeed
      };
      return new Promise(resolve => {
        context.commit("updateNickname", payload);
        resolve(feedback);
      });
    },
    setSettings(context, payload) {
      return service.setSettings(context, payload);
    },
    setRememberMe(context, payload) {
      context.commit("updateRememberMe", payload);
    }
  }
};
