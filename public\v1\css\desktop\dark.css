@-webkit-keyframes blurIn {
	0% {
		-webkit-filter: blur(20px);
		-moz-filter: blur(20px);
		-o-filter: blur(20px);
		-ms-filter: blur(20px);
		filter: blur(20px);
	}
	100% {
		-webkit-filter: blur(0px);
		-moz-filter: blur(0px);
		-o-filter: blur(0px);
		-ms-filter: blur(0px);
		filter: blur(0px);
	}
}
@keyframes blurIn {
	0% {
		-webkit-filter: blur(20px);
		-moz-filter: blur(20px);
		-o-filter: blur(20px);
		-ms-filter: blur(20px);
		filter: blur(20px);
	}
	100% {
		-webkit-filter: blur(0px);
		-moz-filter: blur(0px);
		-o-filter: blur(0px);
		-ms-filter: blur(0px);
		filter: blur(0px);
	}
}
.snotifyToast {
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
}
.snotify-leftTop .fadeIn,.snotify-leftCenter .fadeIn,.snotify-leftBottom .fadeIn {
	-webkit-animation-name: fadeInLeft;
	animation-name: fadeInLeft;
}
.snotify-leftTop .fadeOut,.snotify-leftCenter .fadeOut,.snotify-leftBottom .fadeOut {
	-webkit-animation-name: fadeOutLeft;
	animation-name: fadeOutLeft;
}
.snotify-rightTop .fadeIn,.snotify-rightCenter .fadeIn,.snotify-rightBottom .fadeIn {
}
.snotify-rightTop .fadeOut,.snotify-rightCenter .fadeOut,.snotify-rightBottom .fadeOut {
}
.snotify-centerTop .fadeIn {
	-webkit-animation-name: fadeInDown;
	animation-name: fadeInDown;
}
.snotify-centerTop .fadeOut {
	-webkit-animation-name: fadeOutUp;
	animation-name: fadeOutUp;
}
.snotify-centerCenter .fadeIn {
	-webkit-animation-name: fadeIn;
	animation-name: fadeIn;
}
.snotify-centerCenter .fadeOut {
	-webkit-animation-name: fadeOut;
	animation-name: fadeOut;
}
.snotify-centerBottom .fadeIn {
	-webkit-animation-name: fadeInUp;
	animation-name: fadeInUp;
}
.snotify-centerBottom .fadeOut {
	-webkit-animation-name: fadeOutDown;
	animation-name: fadeOutDown;
}
@-webkit-keyframes fadeInLeft {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(-100%, 0, 0) scaleX(1.2);
		transform: translate3d(-100%, 0, 0) scaleX(1.2);
	}
	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}
@keyframes fadeInLeft {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(-100%, 0, 0) scaleX(1.2);
		transform: translate3d(-100%, 0, 0) scaleX(1.2);
	}
	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}
@-webkit-keyframes fadeInRight {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(100%, 0, 0) scaleX(1.2);
		transform: translate3d(100%, 0, 0) scaleX(1.2);
	}
	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}
@keyframes fadeInRight {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(100%, 0, 0) scaleX(1.2);
		transform: translate3d(100%, 0, 0) scaleX(1.2);
	}
	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}
@-webkit-keyframes fadeInUp {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, 100%, 0) scaleY(1.2);
		transform: translate3d(0, 100%, 0) scaleY(1.2);
	}
	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}
@keyframes fadeInUp {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, 100%, 0) scaleY(1.2);
		transform: translate3d(0, 100%, 0) scaleY(1.2);
	}
	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}
@-webkit-keyframes fadeInDown {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, -100%, 0) scaleY(1.2);
		transform: translate3d(0, -100%, 0) scaleY(1.2);
	}
	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}
@keyframes fadeInDown {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, -100%, 0) scaleY(1.2);
		transform: translate3d(0, -100%, 0) scaleY(1.2);
	}
	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}
@-webkit-keyframes fadeIn {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}
@keyframes fadeIn {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}
@-webkit-keyframes fadeOut {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
	}
}
@keyframes fadeOut {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
	}
}
@-webkit-keyframes fadeOutDown {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
		-webkit-transform: translate3d(0, 100%, 0);
		transform: translate3d(0, 100%, 0);
	}
}
@keyframes fadeOutDown {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
		-webkit-transform: translate3d(0, 100%, 0);
		transform: translate3d(0, 100%, 0);
	}
}
@-webkit-keyframes fadeOutLeft {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
		-webkit-transform: translate3d(-100%, 0, 0);
		transform: translate3d(-100%, 0, 0);
	}
}
@keyframes fadeOutLeft {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
		-webkit-transform: translate3d(-100%, 0, 0);
		transform: translate3d(-100%, 0, 0);
	}
}
@-webkit-keyframes fadeOutRight {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
		-webkit-transform: translate3d(100%, 0, 0);
		transform: translate3d(100%, 0, 0);
	}
}
@keyframes fadeOutRight {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
		-webkit-transform: translate3d(100%, 0, 0);
		transform: translate3d(100%, 0, 0);
	}
}
@-webkit-keyframes fadeOutUp {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
		-webkit-transform: translate3d(0, -100%, 0);
		transform: translate3d(0, -100%, 0);
	}
}
@keyframes fadeOutUp {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
		-webkit-transform: translate3d(0, -100%, 0);
		transform: translate3d(0, -100%, 0);
	}
}
@-webkit-keyframes appear {
	0% {
		max-height: 0;
	}
	100% {
		max-height: 50vh;
	}
}
@keyframes appear {
	0% {
		max-height: 0;
	}
	100% {
		max-height: 50vh;
	}
}
@-webkit-keyframes disappear {
	0% {
		opacity: 0;
		max-height: 50vh;
	}
	100% {
		opacity: 0;
		max-height: 0;
	}
}
@keyframes disappear {
	0% {
		opacity: 0;
		max-height: 50vh;
	}
	100% {
		opacity: 0;
		max-height: 0;
	}
}
@-webkit-keyframes async {
	0% {
		-webkit-transform: translate(0, -50%) rotate(0deg);
		transform: translate(0, -50%) rotate(0deg);
	}
	100% {
		-webkit-transform: translate(0, -50%) rotate(360deg);
		transform: translate(0, -50%) rotate(360deg);
	}
}
@keyframes async {
	0% {
		-webkit-transform: translate(0, -50%) rotate(0deg);
		transform: translate(0, -50%) rotate(0deg);
	}
	100% {
		-webkit-transform: translate(0, -50%) rotate(360deg);
		transform: translate(0, -50%) rotate(360deg);
	}
}
.snotify {
	display: block;
	position: fixed;
	width: 300px;
	z-index: 9999;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	pointer-events: none;
}
.snotify * {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}
.snotify-leftTop,.snotify-leftCenter,.snotify-leftBottom {
	left: 10px;
}
.snotify-rightTop,.snotify-rightCenter,.snotify-rightBottom {
	right: 10px;
}
.snotify-centerTop,.snotify-centerCenter,.snotify-centerBottom {
	left: calc(50% - 300px / 2);
}
.snotify-leftTop,.snotify-centerTop,.snotify-rightTop {
	top: 10px;
}
.snotify-leftCenter,.snotify-rightCenter,.snotify-centerCenter {
	top: 50%;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	    transform: translateY(-50%);
}
.snotify-leftBottom,.snotify-rightBottom,.snotify-centerBottom {
	bottom: 10px;
}
.snotify-backdrop {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: #000000;
	opacity: 0;
	z-index: 9998;
	-webkit-transition: opacity 0.3s;
	-o-transition: opacity 0.3s;
	transition: opacity 0.3s;
}
.snotifyToast {
	display: block;
	cursor: pointer;
	background-color: #082036e8;
	max-height: 300px;
	height: 100%;
	margin: 5px;
	opacity: 0;
	overflow: hidden;
	pointer-events: auto;
	border-radius: 8px;
	-webkit-box-shadow: 0 0 4px rgba(255, 255, 255, 0.2);
	        box-shadow: 0 0 4px rgba(255, 255, 255, 0.2);
}
.snotifyToast--in {
	-webkit-animation-name: appear;
	animation-name: appear;
}
.snotifyToast--out {
	-webkit-animation-name: disappear;
	animation-name: disappear;
}
.snotifyToast__inner {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-flow: column nowrap;
	flex-flow: column nowrap;
	-webkit-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	position: relative;
	padding: 15px 52px 20px 10px;
	font-size: 16px;
	color: #fff;
}
.snotifyToast__noIcon {
	padding: 15px 10px 20px 10px;
}
.snotifyToast__progressBar {
	position: relative;
	width: 100%;
	height: 5px;
	background-color: #000000dd;
}
.snotifyToast__progressBar__percentage {
	position: absolute;
	top: 0;
	left: 0;
	height: 5px;
	background-color: #ffffff33;
	max-width: 100%;
}
.snotifyToast__title {
	font-size: 1.1em;
	line-height: 1.1em;
	margin-bottom: 5px;
	color: #ffffffdd;
}
.snotifyToast__body {
	font-size: 0.9em;
	color: #ffffff88;
	padding-left: 2px;
}
.snotifyToast-show {
	-webkit-transform: translate(0, 0);
	-ms-transform: translate(0, 0);
	    transform: translate(0, 0);
	opacity: 1;
}
.snotifyToast-remove {
	max-height: 0;
	overflow: hidden;
	-webkit-transform: translate(0, 50%);
	-ms-transform: translate(0, 50%);
	    transform: translate(0, 50%);
	opacity: 0;
}
.snotify-confirm .snotifyToast__inner,.snotify-prompt .snotifyToast__inner {
	padding: 10px 15px;
}
.snotifyToast__buttons {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.snotifyToast__buttons button {
	position: relative;
	width: 100%;
	border-right: 1px solid rgba(255, 255, 255, 0.1);
	border-left: 1px solid rgba(255, 255, 255, 0.1);
	border-top: none;
	border-bottom: none;
	background: transparent;
	padding: 8px;
	text-transform: capitalize;
	color: #fff;
}
.snotifyToast__buttons button:hover,.snotifyToast__buttons button:focus {
	background: rgba(255, 255, 255, 0.1);
	outline: none;
}
.snotifyToast__buttons button:active {
	background: rgba(255, 255, 255, 0.15);
}
.snotifyToast__buttons button:last-child {
	border-right: none;
}
.snotifyToast__buttons button:first-child {
	border-left: none;
}
.snotifyToast__buttons--bold {
	font-weight: 700;
}
.snotify-icon {
	position: absolute;
	right: 10px;
	top: 50%;
	line-height: 0;
	-webkit-transform: translate(0, -50%);
	-ms-transform: translate(0, -50%);
	    transform: translate(0, -50%);
	max-height: 32px;
	max-width: 32px;
	width: 100%;
	height: 100%;
}
.snotify-icon--error {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20version=%221.1%22%20x=%220px%22%20y=%220px%22%20viewBox=%220%200%20512%20512%22%20fill=%22%23f44336%22%3E%3Cg%3E%3Cpath%20d=%22M437,75A256,256,0,1,0,75,437,256,256,0,1,0,437,75ZM416.43,416.43a226.82,226.82,0,0,1-320.86,0C7.11,328,7.11,184,95.57,95.57a226.82,226.82,0,0,1,320.86,0C504.89,184,504.89,328,416.43,416.43Z%22/%3E%3Cpath%20d=%22M368.81,143.19a14.5,14.5,0,0,0-20.58,0L256,235.42l-92.23-92.23a14.55,14.55,0,0,0-20.58,20.58L235.42,256l-92.23,92.23a14.6,14.6,0,0,0,10.24,24.89,14.19,14.19,0,0,0,10.24-4.31l92.23-92.23,92.23,92.23a14.64,14.64,0,0,0,10.24,4.31,14,14,0,0,0,10.24-4.31,14.5,14.5,0,0,0,0-20.58l-92-92.23,92.23-92.23A14.5,14.5,0,0,0,368.81,143.19Z%22/%3E%3C/g%3E%3C/svg%3E");
}
.snotify-icon--warning {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20version=%221.1%22%20x=%220px%22%20y=%220px%22%20viewBox=%220%200%20512%20512%22%20fill=%22%23ff9800%22%3E%3Cg%3E%3Cpath%20d=%22M256,512c141.15,0,256-114.84,256-256S397.15,0,256,0,0,114.84,0,256,114.85,512,256,512Zm0-480.49c123.79,0,224.49,100.71,224.49,224.49S379.79,480.49,256,480.49,31.51,379.79,31.51,256,132.21,31.51,256,31.51Z%22/%3E%3Ccircle%20cx=%22260.08%22%20cy=%22343.87%22%20r=%2226.35%22/%3E%3Cpath%20d=%22M254.68,278.39a15.76,15.76,0,0,0,15.75-15.75V128.72a15.75,15.75,0,1,0-31.51,0V262.63A15.76,15.76,0,0,0,254.68,278.39Z%22/%3E%3C/g%3E%3C/svg%3E");
}
.snotify-icon--info {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20version=%221.1%22%20x=%220px%22%20y=%220px%22%20viewBox=%220%200%20512%20512%22%20fill=%22%231e88e5%22%3E%3Cg%3E%3Cpath%20d=%22M256,0C114.84,0,0,114.84,0,256S114.84,512,256,512,512,397.16,512,256,397.15,0,256,0Zm0,478.43C133.35,478.43,33.57,378.64,33.57,256S133.35,33.58,256,33.58,478.42,133.36,478.42,256,378.64,478.43,256,478.43Z%22/%3E%3Cpath%20d=%22M251.26,161.24a22.39,22.39,0,1,0-22.38-22.39A22.39,22.39,0,0,0,251.26,161.24Z%22/%3E%3Cpath%20d=%22M286.84,357.87h-14v-160A16.79,16.79,0,0,0,256,181.05H225.17a16.79,16.79,0,0,0,0,33.58h14.05V357.87H225.17a16.79,16.79,0,0,0,0,33.57h61.67a16.79,16.79,0,1,0,0-33.57Z%22/%3E%3C/g%3E%3C/svg%3E");
}
.snotify-icon--success {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20version=%221.1%22%20x=%220px%22%20y=%220px%22%20viewBox=%220%200%20512%20512%22%20fill=%22%234caf50%22%3E%3Cg%3E%3Cpath%20d=%22M256,0C114.85,0,0,114.84,0,256S114.85,512,256,512,512,397.16,512,256,397.15,0,256,0Zm0,492.31c-130.29,0-236.31-106-236.31-236.31S125.71,19.69,256,19.69,492.31,125.71,492.31,256,386.29,492.31,256,492.31Z%22/%3E%3Cpath%20class=%22cls-1%22%20d=%22M376.64,151,225.31,321.24l-91.17-72.93a9.85,9.85,0,0,0-12.3,15.38l98.46,78.77a9.86,9.86,0,0,0,13.52-1.15L391.36,164.08A9.85,9.85,0,0,0,376.64,151Z%22/%3E%3C/g%3E%3C/svg%3E");
}
.snotify-icon--async {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20version=%221.1%22%20x=%220px%22%20y=%220px%22%20viewBox=%220%200%20512%20512%22%20fill=%22%231e88e5%22%3E%3Cg%3E%3Cpath%20d=%22M256,0a32,32,0,0,0-32,32V96a32,32,0,0,0,64,0V32A32,32,0,0,0,256,0Zm0,384a32,32,0,0,0-32,32v64a32,32,0,0,0,64,0V416A32,32,0,0,0,256,384ZM391.74,165.5,437,120.22A32,32,0,0,0,391.74,75L346.5,120.22a32,32,0,0,0,45.25,45.28Zm-271.52,181L75,391.74A32,32,0,0,0,120.22,437l45.25-45.25a32,32,0,0,0-45.25-45.25Zm0-271.52A32,32,0,1,0,75,120.22l45.25,45.28a32,32,0,1,0,45.25-45.28ZM391.74,346.5a32,32,0,0,0-45.25,45.25L391.74,437A32,32,0,0,0,437,391.74ZM480,224H416a32,32,0,0,0,0,64h64a32,32,0,0,0,0-64ZM128,256a32,32,0,0,0-32-32H32a32,32,0,0,0,0,64H96A32,32,0,0,0,128,256Z%22/%3E%3C/g%3E%3C/svg%3E");
	-webkit-animation: async 3s infinite linear;
	animation: async 3s infinite linear;
	-webkit-transform-origin: 50% 50%;
	-ms-transform-origin: 50% 50%;
	    transform-origin: 50% 50%;
}
.snotifyToast__input {
	position: relative;
	z-index: 1;
	display: inline-block;
	margin: 0;
	width: 100%;
	vertical-align: top;
	-webkit-transition: all 0.5s;
	-o-transition: all 0.5s;
	transition: all 0.5s;
	-webkit-transition-delay: 0.3s;
	-o-transition-delay: 0.3s;
	   transition-delay: 0.3s;
	-webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
	-o-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
	   transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}
.snotifyToast__input__field {
	position: relative;
	display: block;
	float: right;
	padding: 0.85em 0.5em;
	width: 100%;
	border: none;
	border-radius: 0;
	background: transparent;
	color: #333;
	font-weight: bold;
	-webkit-appearance: none;
	opacity: 0;
	-webkit-transition: opacity 0.3s;
	-o-transition: opacity 0.3s;
	transition: opacity 0.3s;
}
.snotifyToast__input__field:focus {
	outline: none;
}
.snotifyToast__input__label {
	display: inline-block;
	float: right;
	padding: 0 0.85em;
	width: 100%;
	color: #999;
	font-weight: bold;
	font-size: 70.25%;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	position: absolute;
	left: 0;
	height: 100%;
	text-align: left;
	pointer-events: none;
}
.snotifyToast__input__label::before,.snotifyToast__input__label::after {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	-webkit-transition: -webkit-transform 0.3s;
	transition: -webkit-transform 0.3s;
	-o-transition: transform 0.3s;
	transition: transform 0.3s;
	transition: transform 0.3s, -webkit-transform 0.3s;
}
.snotifyToast__input__label::before {
	border-top: 2px solid #4c4c4c;
	-webkit-transform: translate3d(0, 100%, 0) translate3d(0, -2px, 0);
	transform: translate3d(0, 100%, 0) translate3d(0, -2px, 0);
	-webkit-transition-delay: 0.3s;
	-o-transition-delay: 0.3s;
	   transition-delay: 0.3s;
}
.snotifyToast__input__label::after {
	z-index: -1;
	background: #eee;
	-webkit-transform: scale3d(1, 0, 1);
	transform: scale3d(1, 0, 1);
	-webkit-transform-origin: 50% 0;
	-ms-transform-origin: 50% 0;
	    transform-origin: 50% 0;
}
.snotifyToast__input__labelContent {
	position: relative;
	display: block;
	padding: 1em 0;
	width: 100%;
	-webkit-transition: -webkit-transform 0.3s 0.3s;
	transition: -webkit-transform 0.3s 0.3s;
	-o-transition: transform 0.3s 0.3s;
	transition: transform 0.3s 0.3s;
	transition: transform 0.3s 0.3s, -webkit-transform 0.3s 0.3s;
}
.snotifyToast__input--filled {
	margin-top: 2.5em;
}
.snotifyToast__input--filled:focus,.snotifyToast__input--filled .snotifyToast__input__field {
	opacity: 1;
	-webkit-transition-delay: 0.3s;
	-o-transition-delay: 0.3s;
	   transition-delay: 0.3s;
}
.snotifyToast__input__field:focus + .snotifyToast__input__label .snotifyToast__input__labelContent,.snotifyToast__input--filled .snotifyToast__input__labelContent {
	-webkit-transform: translate(0, -80%);
	-ms-transform: translate(0, -80%);
	    transform: translate(0, -80%);
	-webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
	-o-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
	   transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}
.snotifyToast__input__field:focus + .snotifyToast__input__label::before,.snotifyToast__input--filled .snotifyToast__input__label::before {
	-webkit-transition-delay: 0s;
	-o-transition-delay: 0s;
	   transition-delay: 0s;
}
.snotifyToast__input__field:focus + .snotifyToast__input__label::before,.snotifyToast__input--filled .snotifyToast__input__label::before {
	-webkit-transform: translate(0, 0);
	-ms-transform: translate(0, 0);
	    transform: translate(0, 0);
}
.snotifyToast__input__field:focus + .snotifyToast__input__label::after,.snotifyToast__input--filled .snotifyToast__input__label::after {
	-webkit-transform: scale(1, 1);
	-ms-transform: scale(1, 1);
	    transform: scale(1, 1);
	-webkit-transition-delay: 0.3s;
	-o-transition-delay: 0.3s;
	   transition-delay: 0.3s;
	-webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
	-o-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
	   transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}
.snotifyToast--invalid .snotifyToast__input__label::before {
	border-color: #f44336;
}
.snotifyToast--valid .snotifyToast__input__label::before {
	border-color: #4caf50;
}
.bet-infosub label {
	cursor: pointer;
}
.bet-infosub label input[type="checkbox"] {
	position: relative;
	width: 15px !important;
	height: 15px !important;
	color: #0f4f8c;
	border: 1px solid #0f4f8c;
	border-radius: 3px;
	-webkit-appearance: none;
	   -moz-appearance: none;
	        appearance: none;
	outline: 0;
	cursor: pointer;
	-webkit-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	-o-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
}
.bet-infosub label input[type="checkbox"]::before {
	position: absolute;
	content: "";
	display: block;
	top: 0;
	left: 4px;
	width: 5px !important;
	height: 10px !important;
	border-style: solid;
	border-color: #fff;
	border-width: 0 2px 2px 0;
	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	        transform: rotate(45deg);
	opacity: 0;
}
.bet-infosub label input[type="checkbox"]:checked {
	color: #fff;
	border-color: #0f4f8c;
	background: #0f4f8c;
}
.bet-infosub label input[type="checkbox"]:checked::before {
	opacity: 1;
}
.bet-infosub label input[type="checkbox"]:checked ~ label::before {
	-webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
	        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}
.live .bet-infosub label input[type="checkbox"] {
	color: #dc3545;
	border: 1px solid #dc3545;
}
.live .bet-infosub label input[type="checkbox"]:checked {
	color: #fff;
	border-color: #dc3545;
	background: #dc3545;
}
.content .left ul.subgroup input[type="checkbox"] {
	position: relative;
	width: 15px !important;
	height: 15px !important;
	color: #092a4b;
	border: 1px solid #fff aa;
	border-radius: 3px;
	-webkit-appearance: none;
	   -moz-appearance: none;
	        appearance: none;
	outline: 0;
	cursor: pointer;
	-webkit-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	-o-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
}
.content .left ul.subgroup input[type="checkbox"]::before {
	position: absolute;
	content: "";
	display: block;
	top: 0;
	left: 4px;
	width: 5px !important;
	height: 10px !important;
	border-style: solid;
	border-color: #fff aa;
	border-width: 0 2px 2px 0;
	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	        transform: rotate(45deg);
	opacity: 0;
}
.content .left ul.subgroup input[type="checkbox"]:checked {
	color: #fff;
	border-color: #092a4b;
	background: #092a4b;
}
.content .left ul.subgroup input[type="checkbox"]:checked::before {
	opacity: 1;
}
.content .left ul.subgroup input[type="checkbox"]:checked ~ label::before {
	-webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
	        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}
.cbx label {
	cursor: pointer;
}
.cbx label span {
	margin-left: 8px;
}
.cbx label input[type="checkbox"] {
	position: relative;
	width: 15px !important;
	height: 15px !important;
	color: #0f4f8c;
	border: 1px solid #0f4f8c;
	border-radius: 3px;
	-webkit-appearance: none;
	   -moz-appearance: none;
	        appearance: none;
	outline: 0;
	cursor: pointer;
	-webkit-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	-o-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
}
.cbx label input[type="checkbox"]::before {
	position: absolute;
	content: "";
	display: block;
	top: 0;
	left: 4px;
	width: 5px !important;
	height: 10px !important;
	border-style: solid;
	border-color: #fff;
	border-width: 0 2px 2px 0;
	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	        transform: rotate(45deg);
	opacity: 0;
}
.cbx label input[type="checkbox"]:checked {
	color: #fff;
	border-color: #0f4f8c;
	background: #0f4f8c;
}
.cbx label input[type="checkbox"]:checked::before {
	opacity: 1;
}
.cbx label input[type="checkbox"]:checked ~ label::before {
	-webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
	        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}
.v-select {
	width: 100%;
}
.vs__dropdown-toggle {
	border: 1px solid #bbb 66;
	width: 100%;
}
.vs__search, .vs__selected {
	color: #fff;
}
.vs__clear {
	fill: #fff;
}
.vs__open-indicator {
	fill: #fff;
	cursor: pointer;
}