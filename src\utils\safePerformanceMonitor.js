/**
 * Safe Performance Monitor Mixin
 * Avoids initialization timing issues by only monitoring methods
 */
import performanceMonitor from './performanceMonitor';

// Safe Vue mixin for performance monitoring
export const safePerformanceMonitorMixin = {
  created() {
    if (!performanceMonitor.enabled) return;
    
    try {
      // Only wrap watchers after component is created
      if (this.$options.watch) {
        Object.keys(this.$options.watch).forEach(key => {
          const watcher = this.$options.watch[key];
          if (typeof watcher === 'function') {
            this.$options.watch[key] = performanceMonitor.wrapWatcher(
              `${this.$options.name || 'Component'}.${key}`,
              watcher
            );
          }
        });
      }
    } catch (error) {
      console.warn('Safe performance monitoring setup failed:', error);
    }
  },
  
  mounted() {
    if (!performanceMonitor.enabled) return;
    
    // Add manual performance tracking methods to the component instance
    this.$perf = {
      startTiming: (name, type = 'method') => {
        return performanceMonitor.startTiming(`${this.$options.name || 'Component'}.${name}`, type);
      },
      
      monitor: (name, fn) => {
        return performanceMonitor.wrapMethod(`${this.$options.name || 'Component'}.${name}`, fn);
      }
    };
  }
};

export default safePerformanceMonitorMixin; 