<template lang="pug">
  .hx-col.d-block.h-100.w-40r
    .hx(:class="source.details['team'] == 1 ? 'team-red ' + cls : 'team-black ' + cls")
      span(:class="{ pointer : !disableClick }" @click="findEvent({ match_id: source.matchId, sports_type: source.sportsId, market_type: source.marketId })") {{ source.homeTeam }}
        //- small &nbsp;{{ source.matchId }}
      span.card(v-if="source.matchBody[24] > 0 && source.matchBody[4] == 3") {{ source.matchBody[24] }}
    .hx(:class="source.details['team'] == 0 ? 'team-red ' + cls : 'team-black ' + cls")
      span(:class="{ pointer : !disableClick }" @click="findEvent({ match_id: source.matchId, sports_type: source.sportsId, market_type: source.marketId })") {{ source.awayTeam }}
      span.card(v-if="source.matchBody[25] > 0 && source.matchBody[4] == 3") {{ source.matchBody[25] }}
    template(v-if="isDraw")
      .hx.draw(:class="cls" v-if="source.details['oxtn']") {{ $t("ui.draw") }}
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";

export default {
  props: {
    cls: {
      type: String,
      default: "w-100"
    },
    source: {
      type: Object
    },
    isDraw: {
      type: Boolean,
      default: false
    },
    disableClick: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    findEvent(e) {
      if (!this.disableClick) EventBus.$emit("FIND_EVENT", e);
    }
  }
};
</script>
