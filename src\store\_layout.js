import i18n from "@/i18n";
import Vue from "vue";
import config from "@/config";
import { STATUS_CODES } from "http";

export default {
  namespaced: true,
  state: {
    tour1: false,
    adsKey: 0,
    tour2: true,
    adsPopup4: true,
    network: "unknown",
    menuX: false,
    menuY: 0,
    menu0: "all",
    menu1: "today",
    menu2: 1,
    menu3: "hdpou",
    selectedDays: "1",
    minimizer: {
      header: true,
      sidebar: true,
      asidebar: true
    },
    pageDisplay: {
      language: config.defaultLang,
      oddsType: config.defaultOddsType,
      pageType: "2",
      showScoreMap: "false",
      showQuickOdds: "false",
      eventSorting: "1",
      marketType: "1",
      pageSize: "10",
      mmoType: "1"
    },
    betting: {
      defaultStake: "3",
      acceptBetterOdds: "false",
      acceptAnyOdds: "false",
      autoRefreshOdds: "true",
      quickBet: "false",
      defaultStakeAmount: "1",
      quickBetAmount: "1"
    },
    sports: {},
    league: {},
    leagueFiltered: false,
    order: {
      sports: [],
      default: {
        sports: []
      }
    },
    options: {
      live: {
        1: true
      },
      parlay: {
        1: true
      }
    },
    search: "",
    selectLeague: [],
    selectedMatch: null,
    favorite: [],
    menuList: {},
    radarId: null,
    channel: null,
    theme: config.defaultTheme
  },
  mutations: {
    updateTour1(state, payload) {
      state.tour1 = payload;
    },
    updateTour2(state, payload) {
      state.tour2 = payload;
    },
    removeFields(state, payload) {
      delete state.adsPopup2;
      // delete state.adsPopup2;
    },
    updateAdsKey(state, payload) {
      state.adsKey = payload;
      state.adsPopup4 = true;
    },
    updateAdsPopup(state, payload) {
      state.adsPopup4 = payload;
    },
    updateTheme(state, payload) {
      state.theme = payload;
    },
    updateNetwork(state, payload) {
      state.network = payload;
    },
    updateRadarId(state, payload) {
      state.radarId = payload;
    },
    updateChannel(state, payload) {
      state.channel = payload;
    },
    updateMenuList(state, payload) {
      state.menuList = payload;
    },
    resetState(state, payload) {
      // console.log(state);
      state.menuX = false;
      state.menuY = "0";
      state.menu0 = "all";
      state.menu1 = "today";
      state.menu2 = 1;
      state.menu3 = "hdpou";
      state.selectedDays = "1";
      state.selectedMatch = null;
      state.selectLeague = [];
      state.league = {};
      state.leagueFiltered = false;
      state.order.sports = [];
      state.order.default.sports = [];
    },
    clearSelectLeague(state, payload) {
      state.selectLeague = [];
      state.leagueFiltered = false;
    },
    updateSelectLeague(state, payload) {
      state.selectLeague = payload;
      state.leagueFiltered = true;
    },
    updateSelectedMatch(state, payload) {
      state.selectedMatch = payload;
    },
    updateSearch(state, payload) {
      state.search = payload;
    },
    updateOptions(state, payload) {
      state.options[payload.property] = payload.value;
    },
    updateSports(state, payload) {
      var defOrder = [];

      var cfgOrder = config.defaultSportOrder;
      if (payload["currency_code"] == "BDT") {
        cfgOrder = config.defaultSportOrder1;
      }
      for (var n in cfgOrder) {
        if (payload.items[cfgOrder[n]]) {
          defOrder.push(cfgOrder[n]);
        }
      }

      var tempOrder = [];
      for (var m in payload.items) {
        if (!cfgOrder.includes(parseInt(m))) {
          tempOrder.push({ key: m, value: payload.items[m] });
        }
      }
      tempOrder = Object.values(tempOrder).sort(function(a, b) {
        return a.value.toLowerCase() < b.value.toLowerCase() ? -1 : a.value.toLowerCase() > b.value.toLowerCase() ? 1 : 0;
      });

      for (var o in tempOrder) {
        defOrder.push(parseInt(tempOrder[o]["key"]));
      }

      state.order.default.sports = defOrder;
      if (state.order.sports.length <= 0) {
        state.order.sports = state.order.default.sports;
      }

      if (state.order.sports.length != defOrder.length) {
        state.order.sports = defOrder;
      }

      state.sports = payload.items;
    },
    updateLeague(state, payload) {
      state.league = payload;
    },
    updateOrder(state, payload) {
      state.order[payload.property] = payload.value;
    },
    updateLanguage(state, payload) {
      i18n.locale = payload;
      state.pageDisplay.language = payload;
    },
    updatePageDisplay(state, payload) {
      i18n.locale = payload.language;
      state.pageDisplay.language = payload.language;
      state.pageDisplay.oddsType = payload.oddsType;
      state.pageDisplay.pageType = payload.pageType;
      state.pageDisplay.marketType = payload.marketType;
      state.pageDisplay.showScoreMap = payload.showScoreMap;
      state.pageDisplay.showQuickOdds = payload.showQuickOdds;
      state.pageDisplay.eventSorting = payload.eventSorting;
      state.pageDisplay.pageSize = payload.pageSize;
      state.pageDisplay.mmoType = payload.mmoType;
    },
    updateOddsType(state, payload) {
      state.pageDisplay.oddsType = payload;
    },
    updateSingleBetting(state, payload) {
      state.betting[payload.property] = payload.value;
    },
    updateBetting(state, payload) {
      state.betting.defaultStake = payload.defaultStake;
      state.betting.acceptBetterOdds = payload.acceptBetterOdds;
      state.betting.acceptAnyOdds = payload.acceptAnyOdds;
      state.betting.autoRefreshOdds = payload.autoRefreshOdds;
      state.betting.quickBet = payload.quickBet;
      state.betting.defaultStakeAmount = payload.defaultStakeAmount;
      state.betting.quickBetAmount = payload.quickBetAmount;
    },
    updateMenu(state, payload) {
      Vue.set(state, payload.property, payload.value);
    },
    updateMenuItems(state, payload) {
      // console.log("updateMenuItems", state, payload);;
      for (var k in payload) {
        Vue.set(state, k, payload[k]);
      }
    },
    updateMinimizer(state, payload) {
      state.minimizer[payload.property] = payload.value;
    },
    updateSelectedDays(state, payload) {
      state.selectedDays = payload;
    },
    updateFavorite(state, payload) {
      if (payload) {
        for (var i in payload) {
          if (state.favorite) {
            if (state.favorite.indexOf(payload[i]) == -1) {
              state.favorite.push(payload[i]);
            }
          }
        }
      }
    },
    removeFavorite(state, payload) {
      if (payload) {
        for (var i in payload) {
          if (state.favorite) {
            var n = state.favorite.indexOf(payload[i]);
            if (n != -1) {
              state.favorite.splice(n, 1);
            }
          }
        }
      }
    },
    replaceFavorite(state, payload) {
      Vue.set(state, "favorite", payload);
    }
  },
  actions: {
    setTour1(context, payload) {
      context.commit("updateTour1", payload);
    },
    setTour2(context, payload) {
      context.commit("updateTour2", payload);
    },
    triggerRemoveFields(context, payload) {
      context.commit("removeFields", payload);
    },
    setAdsKey(context, payload) {
      context.commit("updateAdsKey", payload);
    },
    setAdsPopup(context, payload) {
      context.commit("updateAdsPopup", payload);
    },
    setTheme(context, payload) {
      context.commit("updateTheme", payload);
    },
    setNetwork(context, payload) {
      context.commit("updateNetwork", payload);
    },
    setRadarId(context, payload) {
      context.commit("updateRadarId", payload);
    },
    setChannel(context, payload) {
      context.commit("updateChannel", payload);
    },
    setMenuList(context, payload) {
      context.commit("updateMenuList", payload);
    },
    reset(context, payload) {
      context.commit("resetState", payload);
    },
    resetSelectLeague(context, payload) {
      context.commit("clearSelectLeague", payload);
    },
    setSelectLeague(context, payload) {
      context.commit("updateSelectLeague", payload);
    },
    setSelectedMatch(context, payload) {
      context.commit("updateSelectedMatch", payload);
    },
    setSearch(context, payload) {
      context.commit("updateSearch", payload);
    },
    setOptions(context, payload) {
      context.commit("updateOptions", payload);
    },
    setSports(context, payload) {
      // console.log(payload)
      context.commit("updateSports", payload);
    },
    setLeague(context, payload) {
      context.commit("updateLeague", payload);
    },
    setOrder(context, payload) {
      context.commit("updateOrder", payload);
    },
    setLanguage(context, payload) {
      context.commit("updateLanguage", payload);
    },
    setPageDisplay(context, payload) {
      context.commit("updatePageDisplay", payload);
    },
    setOddsType(context, payload) {
      // console.log(payload);
      context.commit("updateOddsType", payload);
    },
    setSingleBetting(context, payload) {
      context.commit("updateSingleBetting", payload);
    },
    setBetting(context, payload) {
      context.commit("updateBetting", payload);
    },
    setMenu(context, payload) {
      // console.log("setMenu", payload);
      context.commit("updateMenu", payload);
    },
    setMenuItems(context, payload) {
      // console.log("setMenuItems", payload);
      context.commit("updateMenuItems", payload);
    },
    setMinimizer(context, payload) {
      context.commit("updateMinimizer", payload);
    },
    setSelectedDays(context, payload) {
      context.commit("updateSelectedDays", payload);
    },
    setFavorite(context, payload) {
      context.commit("updateFavorite", payload);
    },
    delFavorite(context, payload) {
      context.commit("removeFavorite", payload);
    },
    copyFavorite(context, payload) {
      context.commit("replaceFavorite", payload);
    }
  }
};
