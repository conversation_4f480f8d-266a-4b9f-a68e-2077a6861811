<template lang="pug">
.video(ref="video")
  .error(v-if="error") Video Not Found
</template>

<script>
export default {
  props: {
    src: {
      type: String
    }
  },
  data() {
    return {
      player: null,
      obj: null,
      error: false,
      options: {
        controls: ["play-large", "play", "progress", "current-time", "mute", "volume", "captions", "settings", "airplay", "fullscreen"],
        quality: { default: 480, options: [4320, 2880, 2160, 1440, 1080, 720, 576, 480, 360, 240] }
      }
    };
  },
  destroyed() {
    this.destroyPlayer();
  },
  mounted() {
    this.createPlayer(true);
  },
  methods: {
    detectLink(e) {
      var result = e.includes("youtube");
      return result;
    },
    validateLink(e) {
      var result = e.includes(".mp4");
      return result;
    },
    destroyPlayer() {
      if (this.obj) {
        this.$emit("remove", this.player);
        this.player.destroy();
        $(this.$refs.video).empty();
        this.obj = null;
        this.player = null;
      }
    },
    createPlayer(autoplay) {
      this.$nextTick(() => {
        var n = $(this.$el);
        if (this.detectLink(this.src)) {
          var src = this.src;
          if (src.includes("watch?v=")) {
            var tk = src.split("&");
            src = tk[0].replace("watch?v=", "embed/");
          }
          var v =
            '<div class="plyr__video-embed"><iframe width="100%" height="100%" src="' +
            src +
            '" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope;" allowfullscreen></iframe><div>';
          this.obj = n.append(v).children();
          this.player = new Plyr(this.obj.get(0), this.options);
          this.player.on("play", event => {
            this.$emit("play", this.player);
          });
          this.$emit("add", this.player);
          if (autoplay) {
            this.player.play();
          }
        } else {
          if (this.validateLink(this.src)) {
            var poster = ""; // this.src.replace(".mp4", ".jpg");
            var v = '<video controls playsinline disablePictureInPicture data-poster="' + poster + '">';
            v += '<source type="video/mp4" src="' + this.src + '" />';
            v += "</video>";
            this.obj = n.append(v).children();
            this.player = new Plyr(this.obj.get(0), this.options);
            this.player.on("play", event => {
              this.$emit("play", this.player);
            });
            this.$emit("add", this.player);
            if (autoplay) {
              this.player.play();
            }
          } else {
            this.error = true;
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.video {
  width: 100%;
  height: 100%;
}
.error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff88;
  font-weight: bold;
  font-family: "Roboto";
}
</style>
