<template lang="pug">
#modal-tournament-ads-pop.modal(tabindex="-1", role="dialog")
  .ads-modal.modal-dialog.modal-dialog-centered(role="document" style="max-width: 576px !important;")
    .modal-content.m-0.p-0(style="width: 576px !important; height: 703px !important;")
      .modal-body.m-0.p-0
        .scroller
          .inner-content
            img(src="/v1/images/tournament_eurocup_v2.jpg")
        .reminder(@click="hideMe()")
          i.fas.fa-times
          span {{ $t('ui.close') }}
</template>

<script>
export default {
  data() {
    return {};
  },
  computed: {
    tour2() {
      return this.$store.state.layout.tour2;
    },
  },
  destroyed: function () {
    // $("#modal-tournament-ads-pop").off("hidden.bs.modal", this.hideMe);
    // $("#modal-tournament-ads-pop").off("shown.bs.modal", this.showMe);
  },
  mounted: function () {
    // $("#modal-tournament-ads-pop").on("hidden.bs.modal", this.hideMe);
    // $("#modal-tournament-ads-pop").on("shown.bs.modal", this.showMe);
    if (this.tour2 == true) {
      this.showMe();
    }
  },
  methods: {
    showMe() {
      $("#modal-tournament-ads-pop").modal("show");
    },
    hideMe() {
      $("#modal-tournament-ads-pop").modal("hide");
      this.$store.dispatch("layout/setTour2", false);
    },
  },
};
</script>
