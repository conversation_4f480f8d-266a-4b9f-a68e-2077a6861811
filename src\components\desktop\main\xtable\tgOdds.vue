<template lang="pug">
  .hx-main.tgOdds
    //- small.text-white {{ details }}
    .hx-table.hx-match.hx-compact(:class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }")
      .hx-cell.w-62
        .hx-row.h-100.hx-rows
          timePanel(:source="source")
      .hx-cell.flex-fill
        .hx-row.h-100.hx-rows
          xTeam(:source="source" isDraw=false)
          xFavorite(:source="source")
      template(v-for="item in cols")
        .hx-cell.w-240(v-if="item == 'tgo'")
          .hx-row.hx-rows.h-100
            .hx-col.hx-cols.h-100.w-60(v-for="i in tg")
              tgItem(:details="details" :oddsType="oddsType" :i="i" :item="item")
        .hx-cell.w-180(v-if="item == 'tgho'")
          .hx-row.hx-rows.h-100
            .hx-col.hx-cols.h-100.w-60(v-for="i in tgh")
              tgItem(:details="details" :oddsType="oddsType" :i="i" :item="item")
      .hx-cell.w-40
        .hx-row.h-100.hx-rows
          .hx-col.hx-cols.w-100.d-flex.align-items-center.justify-content-center
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

// import timePanel from "@/components/desktop/main/xtable/timePanel";
// import xTeam from "@/components/desktop/main/xtable/xitem/xTeam";
// import xFavorite from "@/components/desktop/main/xtable/xitem/xFavorite";
// import tgItem from "@/components/desktop/main/xtable/xitem/tgItem";

export default {
  components: {
    // timePanel,
    // xTeam,
    // xFavorite,
    // tgItem

    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    xTeam: () => import("@/components/desktop/main/xtable/xitem/xTeam"),
    xFavorite: () => import("@/components/desktop/main/xtable/xitem/xFavorite"),
    tgItem: () => import("@/components/desktop/main/xtable/xitem/tgItem")
  },
  mixins: [mixinHDPOUOdds],
  data() {
    return {
      cols: ['tgo', 'tgho'],
      tg: ['0-1', '2-3', '4-6', '7Up'],
      tgh: ['0-1', '2-3', '4Up'],
    };
  }
};
</script>