<template lang="pug">
.mb-1px
  .bet-info.grey.new-betslip(:class="{'live':item.market_type == 3 && status == 'waiting'}")
    .head.d-flex(v-if="item.bet_type == 'PARLAY'")
      .head-btn.collapsed.btn-side( :id="'heading-' + item.bet_id" data-toggle="collapse" :data-target="'#collapse-' + item.bet_id" role="button" aria-expanded="false" :aria-controls="'collapse-' + item.bet_id" @click="handleSelected(item.bet_id)")
        i.fal.fa-chevron-up
      .head-text.flex-fill.text-ellipsis
        span.ml-1.text-ellipsis(v-if="item.mmo" :title="$t('ui.mmo_parlay')")
          | {{ $t("ui.mmo_parlay") }}
        span.ml-1.text-ellipsis(v-else :title="betType.toUpperCase()")
          | {{ betType.toUpperCase() }}
    .head.d-flex(v-else)
      .head-text.flex-fill.text-ellipsis
        span.ml-1.text-ellipsis(:title="sportsType[item.sports_type] + ' - ' + betTypeDisplay()")
          | {{ sportsType[item.sports_type] }} - {{ betTypeDisplay() }}
    .bet-detail.blue
      template(v-if="!racingList.includes(item.sports_type)")
        .name(v-if="['HDP', 'HDPH', 'OU', 'OUH'].includes(item.bet_type)") {{ betDisplay }}
      .oddsdetail.py-1
        .d-flex.justify-content-between
          .d-flex.justify-content-between.flexfill
            template(v-if="racingList.includes(item.sports_type)")
              template(v-if="racingList1.includes(item.sports_type)")
                .selector-name(v-if="['OU','OE'].includes(item.bet_type)")
                  | {{ $t("m.GH_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                .selector-name(v-else)
                  | {{ $t("m.GH_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}
              template(v-if="racingList2.includes(item.sports_type)")
                .selector-name(v-if="['OU','OE'].includes(item.bet_type)")
                  | {{ $t("m.GC_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                template(v-else)
                  .selector-name(v-if="['CS'].includes(item.bet_type)")
                    | {{ item.criteria1 }}
                  .selector-name(v-else)
                    | {{ $t("m.GC_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}
              template(v-if="racingList3.includes(item.sports_type)")
                .selector-name(v-if="['OU','OE'].includes(item.bet_type)")
                  | {{ $t("m.GX_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                .selector-name(v-else)
                  | {{ $t("m.GX_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}
            template(v-else)
              .selector-name(v-if="['HDP', 'HDPH', 'OU', 'OUH', '1X2HDP', '1X2HDPH'].includes(item.bet_type)")
                template(v-if="isCriteria2")
                  | {{ ballDisplayMMO(item) }}({{ $numeral(item.criteria2).format("0") }})
                template(v-else)
                  | {{ ballDisplay }}
              .selector-name(v-else-if="['PARLAY'].includes(item.bet_type)")
              .selector-name(v-else) {{ betDisplay }}
            .selector-score(v-if="item.market_type == 3 && item.home_running_score != null") [{{ item.home_running_score }}-{{ item.away_running_score }}]
            .selector-other @
            .selector-odds.accent.small.flex-fill(:class="getNumberClass(item.odds_display)") {{ formatOddsDisplay(item.odds_display, item.bet_type) }}
          .stacks {{ stake }}
  .bet-list-scroll.match-info.collapse.magicY(
    v-if="item.bet_type == 'PARLAY'"
    :id="'collapse-' + item.bet_id"
    :data-parent="'#bet-' + prefix + '-accordion'"
    :aria-labelledby="'heading-' + item.bet_id"
    )
    .empty.match-info.white.text-center(v-if="loading")
      i.fad.fa-spinner.fa-spin
    betListItemDetails(v-if="detail && (loading != true)" :selectedId="item.bet_id" :items="items")
  .match-info.grey
    template(v-if="item.bet_type != 'PARLAY'")
      template(v-if="racingList.includes(item.sports_type)")
        template(v-if="racingList1.includes(item.sports_type)")
          span.name-home {{ getHomeTeam(item) }} &nbsp;
        template(v-if="racingList2.includes(item.sports_type)")
          template(v-if="['ML','1X2'].includes(item.bet_type)")
            span.name-home(v-if="item.home_away == 1") {{ getHomeTeam(item) }} &nbsp;
            span.name-home(v-else) {{ getAwayTeam(item) }} &nbsp;
          template(v-if="['OU','OE'].includes(item.bet_type)")
            span.name-home {{ getHomeTeam(item) }} &nbsp;
        template(v-if="racingList3.includes(item.sports_type)")
          span.name-home {{ getHomeTeam(item) }} &nbsp;
        span.name-home(v-if="racingList.includes(item.sports_type) && item.match_time") (No. {{ $dayjs(item.match_time).format("MMDDhhmm") }})
      template(v-else)
        span.name-home {{ getHomeTeam(item) }} &nbsp;
        span.name-away(v-if="item.bet_type != 'OR'") vs {{ getAwayTeam(item) }}
  .ticket-info.pt-1
    .d-flex.justify-content-between.align-content-center
      .ticket-id ID: {{ item.bet_id }}
      .ticket-status(v-if="item.bet_status" :class="status") {{ $t('ui.' + item.bet_status.toLowerCase()) }}
</template>

<script>
import naming from "@/library/_name";
import betListItemDetails from "@/components/desktop/left/betListItemDetails";
import xhr from "@/library/_xhr-betlist.js";
import calc from "@/library/_calculation.js";
import config from "@/config";

export default {
  components: {
    betListItemDetails
  },
  props: {
    prefix: {
      type: String
    },
    nopad: {
      type: Boolean
    },
    item: {
      type: Object
    },
    status: {
      type: String
    },
    detail: {
      type: Boolean
    }
  },
  data() {
    return {
      loading: false,
      items: []
    };
  },
  computed: {
    racingList() {
      return config.racingList;
    },
    racingList1() {
      return config.racingList1;
    },
    racingList2() {
      return config.racingList2;
    },
    racingList3() {
      return config.racingList3;
    },
    isCriteria2() {
      return this.item.hasOwnProperty("criteria2");
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
    betType() {
      return this.$t("m.BT_" + this.item.bet_type);
    },
    sportsType() {
      return this.$store.state.layout.sports;
    },
    stake() {
      var res = this.item.currency;
      if (res != null) {
        if (res == "UST") {
          res = res.replace("UST", "USDT");
        }
      }
      return res + " " + this.$numeral(this.item.bet_member).format("0,0.00");
    },
    betDisplay() {
      return naming.betDisplay(this.item, this, this.language);
    },
    ballDisplay() {
      return naming.ballDisplay(this.item, this);
    },
    isBallDisplay() {
      var result = naming.ballDisplay(this.item, this);
      if (["HDP", "HDPH"].includes(this.item.bet_type)) {
        return result != null && result != "0";
      } else {
        return false;
      }
    },
    language() {
      return this.$store.getters.language;
    }
  },
  methods: {
    ballDisplayMMO(e) {
      return naming.ballDisplayMMO2(e, this);
    },
    betTypeDisplay() {
      return this.$t("m.BS_" + this.item.bet_type);
    },
    getHomeTeam(e) {
      var r = e["home_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.home_team_name;
    },
    getAwayTeam(e) {
      var r = e["away_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.away_team_name;
    },
    getLeague(e) {
      var r = e["name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.league_name;
    },
    formatOddsDisplay(odds, bet_type) {
      if (bet_type == "CS")
        return calc.fmcs(odds);
      else
        return calc.format(odds.toFixed(3));
    },
    getNumberClass(e) {
      return parseFloat(e) >= 0 ? "" : "text-red";
    },
    handleSelected(e) {
      this.$emit("selected", e);
      this.populateList(e);
    },
    populateList(e, callback) {
      if (this.loading == true) return;
      if (this.isLoggedIn) {
        this.loading = true;
        var args = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          bet_id: e
        };
        switch (this.status) {
        case "running":
          xhr.getParlayAcceptDetails(args).then(
            res => {
              this.loading = false;
              if (res.success) {
                if (res.data) {
                  this.items = res.data;
                  if (callback) callback();
                }
              } else {
                if (this.$helpers.handleFeedback(res.status)) {
                  if (callback) callback();
                }
              }
            },
            err => {
              this.loading = false;
              if (this.$helpers.handleFeedback(err.status)) {
                if (callback) callback();
              }
            }
          );
          break;
        case "waiting":
          xhr.getParlayPendingDetails(args).then(
            res => {
              this.loading = false;
              if (res.success) {
                if (res.data) {
                  this.items = res.data;
                  if (callback) callback();
                }
              } else {
                if (this.$helpers.handleFeedback(res.status)) {
                  if (callback) callback();
                }
              }
            },
            err => {
              this.loading = false;
              if (this.$helpers.handleFeedback(err.status)) {
                if (callback) callback();
              }
            }
          );
          break;
        case "reject":
          xhr.getParlayRejectDetails(args).then(
            res => {
              this.loading = false;
              if (res.success) {
                if (res.data) {
                  this.items = res.data;
                  if (callback) callback();
                }
              } else {
                if (this.$helpers.handleFeedback(res.status)) {
                  if (callback) callback();
                }
              }
            },
            err => {
              this.loading = false;
              if (this.$helpers.handleFeedback(err.status)) {
                if (callback) callback();
              }
            }
          );
          break;
        }
      }
    }
  }
};
</script>