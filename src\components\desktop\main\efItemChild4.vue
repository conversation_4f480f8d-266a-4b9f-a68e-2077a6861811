<template lang="pug">
  .game-cell.game-cell-sm.w-152.bg-01.ef-bet.ef-gh(v-if="isHome")
    template(v-if="details['ml'] != null && details['ml'][0] != null")
      oddsItem(:odds="details['ml'][0]" idx=5 :typ="oddsType" dataType="2")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-152.bg-01.ef-bet.ef-gh(v-else)
    template(v-if="details['ml'] != null && details['ml'][0] != null")
      oddsItem(:odds="details['ml'][0]" idx=7 :typ="oddsType" dataType="2")
    template(v-else)
      .fad.fa-lock-alt.text-muted
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

export default {
  components: {
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem"),
  },
  mixins: [mixinHDPOUOdds],
  props: {
    source: {
      type: Object,
    },
    isHome: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
  },
  methods: {
  },
};
</script>
