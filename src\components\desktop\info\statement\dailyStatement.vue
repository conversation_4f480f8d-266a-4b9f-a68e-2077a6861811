<template lang="pug">
  table.table-info(width='100%')
    tbody
      tr
        th(scope='col', width='10%') {{ $t("ui.date") }}
        th(scope='col', width='42%') {{ $t("ui.remark") }}
        th.text-right(scope='col', width='8%') {{ $t("ui.opening_balance") }}
        th.text-right(scope='col', width='8%') {{ $t("ui.turnover") }}
        //- th.text-right(scope='col', width='8%') {{ $t("ui.valid_turnover") }}
        th.text-right(scope='col', width='8%') {{ $t("ui.win") }} / {{ $t("ui.loss") }}
        th.text-right(scope='col', width='8%') {{ $t("ui.transfer") }}
        th.text-right(scope='col', width='8%') {{ $t("ui.commission") }}
        th.text-right(scope='col', width='8%') {{ $t("ui.closing_balance") }}
        //- th(scope='col', width='6%')
      tr.grey(v-if="dailyStatementList.length == 0")
        td(colspan="7").text-center
          span {{ $t('message.no_information_available') }}
      tr(v-for="(item, index) in dailyStatementList"
        :class="{ grey: index % 2 === 0 }")
        td(@click="onClick(item.working_date)" class="clickable")
          div {{ $t("ui." + $dayjs(item.working_date).format("dddd").toLowerCase())  }}
          div {{ $dayjs(item.working_date).format("MM/DD/YYYY") }}

        td(@click="onClick(item.working_date)" class="clickable")
          div {{ $t("ui.betting_statement") }}
        td.text-right(@click="onClick(item.working_date)" class="clickable")
          span(:class="{ red: parseFloat(item.cash_opening) < 0 }") {{ $numeral(item.cash_opening).format("0,0.00") }}
        td.text-right(@click="onClick(item.working_date)" class="clickable")
          span {{ $numeral(item.turnover).format("0,0.00") }}
        td.text-right(@click="onClick(item.working_date)" class="clickable")
          span(:class="{ red: parseFloat(item.winlose) < 0 }") {{ $numeral(item.winlose).format("0,0.00") }}
        td.text-right(@click="onClickSettlement(item.working_date)" class="clickable")
          span {{ $numeral(item.settled_amount).format("0,0.00") }}
        td.text-right(@click="onClick(item.working_date)" class="clickable")
          span {{ $numeral(item.comm).format("0,0.00") }}
        td.text-right(@click="onClick(item.working_date)" class="clickable")
          span(:class="{ red: parseFloat(item.cash_closing) < 0 }") {{ $numeral(item.cash_closing).format("0,0.00") }}

</template>
<script>
export default {
  props: {
    dailyStatementList: {
      type: Array,
      default: []
    }
  },
  methods: {
    onClick: function (date) {
      // console.log(date);
      this.$emit("getPlatformWinlose", date);
    },
    onClickSettlement: function(date) {
      this.$emit("getSettlement", date);
    }
  }
}
</script>