<template lang="pug">
#modal-result.modal(tabindex="-1" role="dialog" ref="resultModal" data-backdrop="false")
  .modal-dialog.modal-dialog-centered.modal-lg(role="document")
    .modal-content
      .modal-header
        h5.modal-title {{ $t("ui.result") }}
        button.close(type="button" data-dismiss="modal" aria-label="Close")
          span(aria-hidden="true") ×
      .modal-body.px-2.py-1(v-if="loading")
        .empty.match-info.white.text-center(v-if="loading")
          i.fad.fa-spinner.fa-spin
      template(v-if="!loading")
        .modal-body.px-2.py-1(v-for="match in groupResult")
          .card(v-if="betType==='OR'")
            .card-header.px-2.py-1
              span.font-weight-bold {{ match.home_name_en }}
            table.table-info(width='100%')
              tbody
                tr
                  th.text-center(height="20") {{ $t("ui.result") }}
                tr
                  td.text-center.font-weight-bold(height="20") {{match.outright_teamwin?$t('ui.win'):$t('ui.lost')}}
          .card(v-else-if="betType==='FGLG'||betType==='FGLGH'")
            .card-header.px-2.py-1
              span.font-weight-bold {{ match.home_name_en }}
              span -vs- {{ match.away_name_en }}
            table.table-info(width='100%')
              tbody
                tr
                  th.text-center(width="50%" height="20") {{ $t("ui.first_half") }}
                  th.text-center(width="50%" height="20") {{ $t("ui.final") }}
                tr
                  td.text-center(height="20")
                    span.badge.badge-pill.badge-info.mr-1(v-if="match.FGH===1") 1F
                    span.badge.badge-pill.badge-danger.mr-1(v-if="match.LGH===1") 1L
                    span.badge.badge-pill.badge-info.mr-1(v-if="match.FGH===0") 1F
                    span.badge.badge-pill.badge-danger.mr-1(v-if="match.LGH===0") 1L
                  td.text-center(height="20")
                    span.badge.badge-pill.badge-info.mr-1(v-if="match.FG===1") F
                    span.badge.badge-pill.badge-danger.mr-1(v-if="match.LG===1") L
                    span.badge.badge-pill.badge-info.mr-1(v-if="match.FG===0") F
                    span.badge.badge-pill.badge-danger.mr-1(v-if="match.LG===0") L
          .card(v-else)
            .card-header.px-2.py-1
              span.font-weight-bolder {{ match.home_name_en }}&nbsp;
              span.badge.badge-pill.badge-info.mr-1(v-if="match.FGH===1") 1F
              span.badge.badge-pill.badge-danger.mr-1(v-if="match.LGH===1") 1L
              span.badge.badge-pill.badge-info.mr-1(v-if="match.FG===1") F
              span.badge.badge-pill.badge-danger.mr-1(v-if="match.LG===1") L
              span -vs-&nbsp;{{ match.away_name_en }}&nbsp;
              span.badge.badge-pill.badge-info.mr-1(v-if="match.FGH===0") 1F
              span.badge.badge-pill.badge-danger.mr-1(v-if="match.LGH===0") 1L
              span.badge.badge-pill.badge-info.mr-1(v-if="match.FG===0") F
              span.badge.badge-pill.badge-danger.mr-1(v-if="match.LG===0") L
              //- span.font-weight-bold.float-right.text-danger &nbsp;&nbsp;{{match.match_status===1?'FINISHED':'CANCELLED'}}
            table.table-info(width='100%')
              tbody
                tr
                  th.text-center(width="50%" height="20") {{ $t("ui.first_half") }}
                  th.text-center(width="50%" height="20") {{ $t("ui.final") }}
                tr
                  td.text-center.font-weight-bold(height="20") {{match.half_home}} - {{match.half_away}}
                  td.text-center.font-weight-bold(height="20") {{match.final_home}} - {{match.final_away}}
      .modal-footer
        button.btn.btn-secondary(type="button" data-dismiss="modal") {{ $t('ui.cancel') }}
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import service from "@/library/_xhr-result";

export default {
  props: {
    matchId: Number,
    betType: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      loading: false,
      result: [],
      feedback: {
        success: false,
        status: errors.session.invalidSession
      }
    };
  },
  computed: {
    groupResult() {
      if (this.result.length > 0) {
        var tempList = this.result.map(v =>
          Object.assign(
            {
              FG: v.FLG !== "" && v.FLG !== "NG" ? (v.FLG.split("-")[0] === "H" ? 1 : 0) : -1,
              LG: v.FLG !== "" && v.FLG !== "NG" ? (v.FLG.split("-")[1] === "H" ? 1 : 0) : -1,
              FGH: v.FLGH !== "" && v.FLGH !== "NG" ? (v.FLGH.split("-")[0] === "H" ? 1 : 0) : -1,
              LGH: v.FLGH !== "" && v.FLGH !== "NG" ? (v.FLGH.split("-")[1] === "H" ? 1 : 0) : -1
            },
            v
          )
        );
        return tempList;
      }

      return [];
    }
  },
  watch: {
    matchId: {
      immediate: true,
      handler() {
        if (this.betType === "PARLAY") this.getParlayResult();
        else this.getMatchResult();
      }
    }
  },
  methods: {
    getMatchResult() {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        match_id: this.matchId
      };
      this.loading = true;

      service.getSingleResult(json).then(
        result => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.result = result.data;
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        err => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getParlayResult() {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        bet_id: this.matchId
      };
      this.loading = true;

      service.getParlayResults(json).then(
        result => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.result = result.data;
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        err => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    }
  }
};
</script>