<template lang="pug">
#pills-waiting.tab-pane(role="tabpanel" aria-labelledby="pills-waiting-tab")
  .empty.p-0.m-0
    .btn.btn-sm.btn-refresh(v-if="this.refreshCounter > 0" @click="populateList()") {{ $t("ui.auto_refresh") }} ({{ 5 - refreshCounter }})
    .btn.btn-sm.btn-refresh(v-if="this.refreshCounter <= 0") {{ $t("ui.auto_refreshing") }}
  .empty.text-center(v-if="items == undefined || items.length <= 0")  {{ $t("message.no_waiting") }}
  #bet-pending-accordion.magicY.bet-list-scroll.magicY(v-if="items != undefined && items.length > 0")
    template(v-for="(item, index) in items")
      betListItem(
        :item="item"
        :key="item.bet_id"
        :nopad="index == items.length - 1"
        status="waiting"
        @selected="handleSelected"
        :detail="selectedId == item.bet_id"
        :loading="loading"
        prefix="pending")
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";
import xhr from "@/library/_xhr-betlist.js";
import betListItem from "@/components/desktop/left/betListItem";

export default {
  components: {
    betListItem
  },
  data() {
    return {
      loading: false,
      items: [],
      selectedId: null,
      refreshCounter: 0
    };
  },
  computed: {
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    }
  },
  destroyed() {
    clearInterval(this.puller);
  },
  mounted() {
    setInterval(this.puller, 1000);
    EventBus.betListPending = this.runner;
  },
  methods: {
    puller() {
      if (this.refreshCounter >= 5) {
        this.refreshCounter = 0;
        if ($("#pills-waiting-tab").hasClass("active")) {
          this.populateList();
        }
      } else {
        this.refreshCounter += 1;
      }
    },
    runner() {
      this.populateList();
      $("#collapse-mybet").collapse("show");
      $("#pills-waiting-tab").tab("show");
    },
    handleSelected(e) {
      this.selectedId = e;
    },
    populateList(callback) {
      if (this.loading == true) return;
      if (this.isLoggedIn) {
        this.loading = true;
        var args = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          page_size: 10
        };
        xhr.getBetPendingList(args).then(
          res => {
            this.loading = false;
            this.refreshCounter = 0;
            if (res.success) {
              if (res.data) {
                // console.log("PendingList: ", res.data);
                this.items = res.data;
                if (callback) callback();
              }
            } else {
              if (this.$helpers.handleFeedback(res.status)) {
                if (callback) callback();
              }
            }
          },
          err => {
            this.loading = false;
            this.refreshCounter = 0;
            if (this.$helpers.handleFeedback(err.status)) {
              if (callback) callback();
            }
          }
        );
      }
    }
  }
};
</script>
