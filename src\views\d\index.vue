<template lang="pug">
.landing(v-if="!whiteLabel" :class="brand.toLowerCase()")
  template(v-if="loader")
    .preloader.full-height
      .loader
      .logo
        .d-block
          span {{ result }}
  template(v-else)
    router-view
.landing.white-label(v-else :class="brand.toLowerCase()")
  .preloader.full-height
    .loader
    .logo
      .d-block
        logo
      .d-block
        span(style="color: #fff; text-shadow: 0 0 15px #000, 1px 1px 3px #000;") {{ result }}
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";
import config from "@/config";
import errors from "@/errors";

export default {
  components: {
    logo: () => import("@/components/desktop/logo"),
  },
  data() {
    return {
      whiteLabel: false,
      token: "",
      lang: "",
      theme: "",
      oddsType: "",
      feedback: {
        username: "",
        password: "",
        loading: false,
        timeout: null,
      },
      result: "",
      loading: true,
      loader: true,
    };
  },
  computed: {
    brand() {
      return config.brand;
    },
    isWhiteLabel() {
      return config.whiteLabel;
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
    accountId() {
      return this.$store.getters.accountId;
    },
    sessionToken() {
      return this.$store.getters.sessionToken;
    },
    rememberMe() {
      return this.$store.state.user.rememberMe;
    },
    currency_code() {
      return this.$store.getters.currencyCode;
    },
    defaultLang() {
      return config.defaultLang;
    },
    defaultOddsType() {
      return config.defaultOddsType;
    },
    defaultTheme() {
      return config.defaultTheme;
    },
  },
  destroyed() {
    $("body").removeClass("landing");
  },
  mounted() {
    $("body").addClass("landing");
    this.result = "Establish connection...";
    var q = this.$route.query;
    this.launch(q);
  },
  methods: {
    hardRefresh() {
      window.location.reload(true);
    },
    launch(q, redirectURL) {
      if (q.hasOwnProperty("a")) {
        // White Label token login
        //
        this.doWhiteLabel(q, redirectURL);
      } else if (q.hasOwnProperty("u") && q.hasOwnProperty("st")) {
        // Switch between Desktop and Mobile
        //
        this.doSwitch(q, redirectURL);
      } else if (q.hasOwnProperty("u") && q.hasOwnProperty("p")) {
        // URL Login
        //
        this.doLogin(q, redirectURL);
      } else {
        // Normal User Login
        //
        setTimeout(() => {
          this.loader = false;
          this.loading = false;
          if (q.view) {
          } else {
            if (this.isMobileTablet()) {
              var domain = this.getDomain(window.location.hostname, false);
              var test = "https://m." + domain;
              window.location.href = test;
            }
          }
        }, 1000);
      }
    },
    doWhiteLabel(q, redirectURL) {
      this.$store.dispatch("user/clear");
      this.token = q.a;
      this.whiteLabel = true;
      if (q.lang) {
        switch (q.lang.toLowerCase()) {
        case "cn":
        case "tw":
        case "th":
        case "vi":
        case "kr":
        case "id":
        case "jp":
        case "en":
        case "my":
          this.lang = q.lang.toLowerCase();
          break;
        default:
          this.lang = this.defaultLang;
          break;
        }
      }
      if (q.theme) {
        switch (q.theme.toLowerCase()) {
        case "color1":
        case "color2":
        case "color3":
        case "color4":
        case "color5":
          this.theme = q.theme.toLowerCase();
          break;
        default:
          this.theme = this.defaultTheme;
          break;
        }
      }
      if (q.ot) {
        switch (q.ot.toUpperCase()) {
        case "MY":
        case "HK":
        case "ID":
        case "DEC":
          this.oddsType = q.ot.toUpperCase();
          break;
        default:
          this.oddsType = this.defaultOddsType;
          break;
        }
      }
      if (this.lang != null && this.lang != "") {
        this.$store.dispatch("layout/setLanguage", this.lang);
      }
      if (this.theme != null && this.theme != "") {
        this.$store.dispatch("layout/setTheme", this.theme);
      }
      if (this.isWhiteLabel) {
        setTimeout(() => {
          this.result = "Authenticate user account...";
          this.$store.dispatch("layout/reset");
          this.$store.dispatch("user/doLaunch", this.token).then(
            (res) => {
              this.feedback.loading = false;
              if (res.success) {
                this.feedback.loading = true;
                this.$store.dispatch("user/getBalance").then(
                  (res) => {
                    this.feedback.loading = false;
                    if (res.success) {
                      this.$store.dispatch("layout/setRadarId", null);
                      this.$store.dispatch("layout/setSingleBetting", { property: "quickBet", value: false });

                      if (this.oddsType != null && this.oddsType != "") {
                        this.$store.dispatch("layout/setOddsType", this.oddsType);
                      }

                      if (redirectURL) {
                        this.$router
                          .push(redirectURL)
                          .then((res) => {
                            EventBus.$emit("INVALIDATE");
                          })
                          .catch((err) => {
                            console.trace(err);
                          });
                      } else {
                        if (q.match && q.sports && q.market) {
                          this.$router
                            .push("/desktop?e=" + q.match + "&s=" + q.sports + "&m=" + q.market)
                            .then((res) => {
                              EventBus.$emit("INVALIDATE");
                            })
                            .catch((err) => {
                              console.trace(err);
                            });
                        } else {
                          if (q.hl == "1") {
                            this.$router
                              .push("/highlight")
                              .then((res) => {
                                EventBus.$emit("INVALIDATE");
                              })
                              .catch((err) => {
                                console.trace(err);
                              });
                          } else {
                            this.$router
                              .push("/desktop")
                              .then((res) => {
                                EventBus.$emit("INVALIDATE");
                              })
                              .catch((err) => {
                                console.trace(err);
                              });
                          }
                        }
                      }
                    } else {
                      this.result = this.$t("error." + res.status);
                    }
                  },
                  (err) => {
                    this.feedback.loading = false;
                    this.result = this.$t("error." + res.status);
                  }
                );
              } else {
                this.result = this.$t("error." + res.status);
              }
            },
            (err) => {
              this.feedback.loading = false;
              this.result = this.$t("error." + err.status);
            }
          );
        }, 2000);
      } else {
        this.result = "Function not supported!";
      }
    },
    doSwitch(q, redirectURL) {
      this.$store.dispatch("user/doSwitch", { username: q.u, password: q.st }).then(
        (res) => {
          this.feedback.loading = false;
          if (res.success) {
            this.feedback.loading = true;
            this.$store.dispatch("user/getBalance").then(
              (res) => {
                this.feedback.loading = false;
                if (res.success) {
                  this.$store.dispatch("layout/setRadarId", null);
                  this.$store.dispatch("layout/setSingleBetting", { property: "quickBet", value: false });
                  this.$router
                    .push("/desktop")
                    .then((res) => {
                      EventBus.$emit("INVALIDATE");
                    })
                    .catch((err) => {
                      console.trace(err);
                    });
                } else {
                  this.result = this.$t("error." + res.status);
                }
              },
              (err) => {
                this.feedback.loading = false;
                this.result = this.$t("error." + res.status);
              }
            );
          } else {
            this.result = this.$t("error." + res.status);
          }
        },
        (err) => {
          this.feedback.loading = false;
          this.result = this.$t("error." + err.status);
        }
      );
    },
    doLogin(q, redirectURL) {
      this.$store.dispatch("user/clear");
      this.$store.dispatch("layout/reset");
      this.$store.dispatch("user/doLogin", { username: q.u, password: q.p }).then(
        (res) => {
          this.feedback.loading = false;
          if (res.success) {
            this.feedback.loading = true;
            this.$store.dispatch("user/getBalance").then(
              (res) => {
                this.feedback.loading = false;
                if (res.success) {
                  this.$store.dispatch("layout/setRadarId", null);
                  this.$store.dispatch("layout/setSingleBetting", { property: "quickBet", value: false });
                  this.$router
                    .push("/desktop?vg=49")
                    .then((res) => {
                      EventBus.$emit("INVALIDATE");
                    })
                    .catch((err) => {
                      console.trace(err);
                    });
                } else {
                  this.result = this.$t("error." + res.status);
                }
              },
              (err) => {
                this.feedback.loading = false;
                this.result = this.$t("error." + res.status);
              }
            );
          } else {
            this.result = this.$t("error." + res.status);
          }
        },
        (err) => {
          this.feedback.loading = false;
          this.result = this.$t("error." + err.status);
        }
      );
    },
    getDomain(url, subdomain) {
      subdomain = subdomain || false;

      url = url.replace(/(https?:\/\/)?(www.)?/i, "");

      if (!subdomain) {
        url = url.split(".");

        url = url.slice(url.length - 2).join(".");
      }

      if (url.indexOf("/") !== -1) {
        return url.split("/")[0];
      }

      return url;
    },
    isMobileTablet() {
      var check = false;
      (function (a) {
        if (
          /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(
            a
          ) ||
          /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(
            a.substr(0, 4)
          )
        )
          check = true;
      })(navigator.userAgent || navigator.vendor || window.opera);
      if (!check) {
        if (navigator.userAgent.match(/Mac/) && navigator.maxTouchPoints && navigator.maxTouchPoints > 2) {
          check = true;
        }
      }
      return check;
    },
    // setMenu2(e, f) {
    //   var choosenBetType = naming.chooseBetType(e, this.menu);
    //   var mi5 = {};
    //   mi5["menuX"] = false;
    //   mi5["menuY"] = "0";
    //   mi5["menu1"] = f ? f : this.menu1;
    //   mi5["menu0"] = "all";
    //   mi5["menu2"] = e;
    //   mi5["menu3"] = choosenBetType;

    //   var changed = false;
    //   if (mi5["menuX"] != this.menuX) {
    //     changed = true;
    //   }
    //   if (mi5["menuY"] != this.menuY) {
    //     changed = true;
    //   }
    //   if (mi5["menu0"] != this.menu0) {
    //     changed = true;
    //   }
    //   if (mi5["menu1"] != this.menu1) {
    //     changed = true;
    //   }
    //   if (mi5["menu2"] != this.menu2) {
    //     changed = true;
    //   }
    //   if (mi5["menu3"] != this.menu3) {
    //     changed = true;
    //   }

    //   if (changed) {
    //     this.setDays(parseInt(mi5["menu2"]));
    //     this.resetSelectLeague();
    //     this.clearSearch();
    //     mi5["src"] = "setMenu2";
    //     this.$store.dispatch("layout/setMenuItems", mi5);
    //     $(".market-menu").removeClass("active");
    //     setTimeout(() => {
    //       EventBus.$emit("INVALIDATE");
    //     }, 100);
    //   }
    // },
  },
};
</script>
