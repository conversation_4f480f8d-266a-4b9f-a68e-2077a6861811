<template lang="pug">
#modal-tournament-ads.modal(tabindex="-1", role="dialog")
  .ads-modal.modal-dialog.modal-dialog-centered(role="document")
    .modal-content.m-0.p-0
      .modal-body.m-0.p-0
        #player.frame
          iframe(
            v-if="youtube"
            src="https://www.youtube.com/embed/BZXeBK1ndCo?autoplay=1&controls=1&playlist=BZXeBK1ndCo&enablejsapi=1&loop=1&modestbranding=1&playsinline=1&mute=0&fs=1&rel=0&hd=1"
            frameborder="0"
            allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            loading="lazy"
            )
        .reminder(@click="closeMe()")
          i.fas.fa-times
          span {{ $t('ui.close') }}
</template>

<script>
export default {
  data() {
    return {
      youtube: false
    };
  },
  destroyed: function () {
    $("#modal-tournament-ads").off("hidden.bs.modal", this.hideMe);
    $("#modal-tournament-ads").off("shown.bs.modal", this.showMe);
  },
  mounted: function () {
    $("#modal-tournament-ads").on("hidden.bs.modal", this.hideMe);
    $("#modal-tournament-ads").on("shown.bs.modal", this.showMe);
  },
  methods: {
    showMe() {
      this.youtube = true;
    },
    hideMe() {
      this.youtube = false;
    },
    closeMe() {
      $("#modal-tournament-ads").modal("hide");
    },
  },
};
</script>
