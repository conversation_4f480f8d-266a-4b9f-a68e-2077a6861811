<template lang="pug">
  .hx-main.hdpouslMMO
    //- MMO
    .hx-table.hx-match.hx-compact.hx-sl.hx-mmox(v-if="Object.keys(mmoDetails).length > 0 && mmoDetails['tn'] > 0" :class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }")
      .hx-cell.w-62
        .hx-row.h-100.hx-rows
          timePanel(:source="source")
      .hx-cell.flex-fill
        .hx-row.h-100.hx-rows
          xTeam(:source="source" isDraw=false cls="w-320", disableClick=true)
          xFavorite(:source="source")

      .hx-cell.w-392
        .hx-row.hx-rows(v-for="(dn, i) in mmoDetails['kns']")
          .hx-col.hx-cols.hx-border.w-98
            .hx.hx-flex-c.h-100(v-if="mmoDetails['hdp'] != null && mmoDetails['hdp'][i] && mmoDetails['hdp'][i][22] != null && mmoDetails['hdp'][i][22] != 0 && mmoDetails['hdp'][i][22] != ''")
              .hxs.w-83
                mmoItemX(v-if="mmoDetails['hdp'][i][7] == 1" :odds="mmoDetails['hdp'][i]" idx=10 pos=22 :typ="oddsType" dataType="1" homeaway="H" :giving="mmoDetails['hdp'][i][7]")
                mmoItemX(v-if="mmoDetails['hdp'][i][7] == 0" :odds="mmoDetails['hdp'][i]" idx=9 pos=22 :typ="oddsType" dataType="1" homeaway="H" :giving="mmoDetails['hdp'][i][7]")
          .hx-col.hx-cols.hx-border.w-98
            .hx.hx-flex-c.h-100(v-if="mmoDetails['hdp'] != null && mmoDetails['hdp'][i] && mmoDetails['hdp'][i][22] != null && mmoDetails['hdp'][i][22] != 0 && mmoDetails['hdp'][i][22] != ''")
              .hxs.w-83
                mmoItemX(v-if="mmoDetails['hdp'][i][7] == 1" :odds="mmoDetails['hdp'][i]" idx=9 pos=22 :typ="oddsType" dataType="1" homeaway="A" :giving="mmoDetails['hdp'][i][7]")
                mmoItemX(v-if="mmoDetails['hdp'][i][7] == 0" :odds="mmoDetails['hdp'][i]" idx=10 pos=22 :typ="oddsType" dataType="1" homeaway="A" :giving="mmoDetails['hdp'][i][7]")
          .hx-col.hx-cols.hx-border.w-98
            .hx.hx-flex-c.h-100(v-if="mmoDetails['ou'] != null && mmoDetails['ou'][i] != null && mmoDetails['ou'][i][23] != null && mmoDetails['ou'][i][23] != 0 && mmoDetails['ou'][i][23] != ''")
              .hxs.w-83
                mmoItemX(:odds="mmoDetails['ou'][i]" idx=12 pos=23 :typ="oddsType" dataType="1" homeaway="O" :giving="mmoDetails['ou'][i][7]")
          .hx-col.hx-cols.hx-border.w-98
            .hx.hx-flex-c.h-100(v-if="mmoDetails['ou'] != null && mmoDetails['ou'][i] != null && mmoDetails['ou'][i][23] != null && mmoDetails['ou'][i][23] != 0 && mmoDetails['ou'][i][23] != ''")
              .hxs.w-83
                mmoItemX(:odds="mmoDetails['ou'][i]" idx=11 pos=23 :typ="oddsType" dataType="1" homeaway="U" :giving="mmoDetails['ou'][i][7]")

      .hx-cell.w-40
        .hx-row.h-100.hx-rows
          .hx-col.hx-cols.w-100.d-flex.align-items-center.justify-content-center
            template(v-if="details['momo'] > 0")
              .hx-more.collapsed(
                :id="'morehead_' + id"
                data-toggle='collapse'
                :aria-expanded="false"
                :data-target="'#morebet_' + id"
                :aria-controls="'morebet_' + id"
                @click="handleMore(matchId, $event.target)"
                )
                i.far.fa-chevron-up
                span &nbsp;{{ details['momo'] }}

    template(v-if="(details['momo'] > 0)")
      .hx-table.hx-match.hx-morebet.collapse(
        :id="'morebet_' + id"
        :aria-labelledby="'morehead_' + id"
        data-parent="#hdpou"
        :class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }"
        )
        morePanelX(
          v-if="source.matchId == selectedMatch"
          ref="morePanel"
          :uid="id"
          :details="moreItems"
          :child1Ids="details['child1']"
          :child2Ids="details['child2']"
          :matchId="matchId"
          :leagueId="leagueId"
          :marketType="marketType"
          :sportsType="sportsType"
          :betType="betType"
          :layoutIndex="layoutIndex"
        )
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";
export default {
  components: {
    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    morePanelX: () => import("@/components/desktop/main/xheader/morePanelX"),
    xTeam: () => import("@/components/desktop/main/xtable/xitem/xTeam"),
    xFavorite: () => import("@/components/desktop/main/xtable/xitem/xFavorite"),
    mmoItemX: () => import("@/components/desktop/main/mmo/mmoItemX")
  },
  mixins: [mixinHDPOUOdds],
  computed: {
    pageType() {
      return this.$store.getters.pageDisplay.pageType
    }
  }
};
</script>