<template lang="pug">
.col-2.hx-more-row(v-if="details['hdp'] != null")
  .card
    .card-header(
      :class="layoutIndex == 3 ? 'live': 'non-live'"
      )
      span.header-bettype(style="padding: 0 4px;") {{ leagueName.trim() }}
    .card-body.p-0
      .hx-table.hx-morebet-header(:class="marketType == 3 ? 'live' : 'non-live'").bl-1.br-1.bb-1.h-18
        .hx-cell.w-100.bl-1
          .hx-row.w-100
            .hx-col.w-100
              .hx.w-100.text-center(:title="$t('ui.hdp')") {{ $t("ui.hdp") }}
      .hx-table.hx-match.hx-morebet-body(:class="{ 'live': marketType == 3 }").bl-1.br-1.bb-1
        .hx-cell.w-100
          .hx-row.hx-rows.w-100(v-for="(dn, i) in details['tn']")
            hdpItem(:details="details" :oddsType="oddsType" :i="i" betType="hdp" cls="w-100")
</template>

<script>
import config from "@/config";
import hdpItem from "@/components/desktop/main/xtable/xitem/hdpItem";

export default {
  components: {
    hdpItem
  },
  props: {
    childId: {
      type: Number
    },
    uid: {
      type: String
    },
    details: {
      type: Object
    },
    matchId: {
      type: Number
    },
    leagueId: {
      type: Number
    },
    marketType: {
      type: Number
    },
    sportsType: {
      type: Number
    },
    betType: {
      type: String
    },
    layoutIndex: {
      type: Number
    }
  },
  computed: {
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
    leagueName() {
      var result = this.details["league"].split(" - ");
      if (result.length >= 2) {
        return result[result.length - 1];
      }
      return this.details["league"];
    }
  }
};
</script>
