.dropdown-menu.profile {
	min-width: 300px;
	padding: 6px;
	-webkit-transform: none !important;
	-ms-transform: none !important;
	transform: none !important;
	top: 53px !important;
	right: 0 !important;
	left: inherit !important;
	font-family: "Lato", sans-serif;
	font-weight: 400;
	font-size: 12px;
	color: #ffffff88;
	line-height: 1;
	text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
	border: 1px solid #5991C1;
	border-radius: 3px;
	background: url(/images/bg1.png) no-repeat;
	background-size: cover;
	box-shadow: 0px 3px 6px #00000029;
}
.dropdown-title {
	font-weight: 900;
	text-transform: uppercase;
	font-size: 12px;
	color: #FFFFFF;
}
.dropdown-icon {
	cursor: pointer;
	font-size: 16px;
}
.dropdown-icon:hover {
	color: #ffffff;
}
.dropdown-panel {
	display: block;
	border-radius: 3px;
	padding: 0;
	background: #FFFFFF;
	box-shadow: inset 0px 0px 5px #276FA8;
}
.dropdown-li {
	display: block;
	position: relative;
	padding: 6px 2px;
	border-left: 1px solid rgba(255, 255, 255, 0.05);
	border-right: 1px solid rgba(255, 255, 255, 0.2);
	border-top: 1px solid rgba(255, 255, 255, 0.05);
	border-radius: 0;
	margin: 0;
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.dropdown-li:first-child {
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
}
.dropdown-li:last-child {
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
}
.dropdown-li .caption {
	float: left;
	padding: 4px 6px;
	font-size: 10px;
	color: #4F85B3;
	text-transform: uppercase;
}
.dropdown-li .value .unit {
	font-weight: 500;
	text-align: right;
	font-size: 12px;
	margin-right: 4px;
	color: #000;
}
.dropdown-li .value {
	padding: 4px 6px;
	font-weight: 600;
	text-align: right;
	font-size: 14px;
	color: #014273;
}
.dropdown-li button {
	color: #ffffff88;
	font-size: 12px;
	padding: 0;
	margin: 0;
	line-height: 1;
	background: transparent;
	border: 0;
	text-transform: uppercase;
	font-weight: 300;
}
.dropdown-li button i {
	margin-right: 4px;
}
.dropdown-li button:hover {
	color: #ffffffee;
}
.dropdown-menu.custom-dropdown-menu {
	min-width: 6rem;
}
.dropdown-item.custom-dropdown-item {
	padding: 0.1rem 1rem;
	font-size: 13px;
}
.dropdown-menu.profile.small {
	top: 35px !important;
	left: auto !important;
	right: auto !important;
}
.balance {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	-webkit-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end;
}
.balance .caption {
	font-size: 10px;
	text-transform: uppercase;
	color: #666666;
	margin-right: 2px;
	padding: 0;
}
.balance .value {
	font-weight: 600;
	text-align: right;
	font-size: 13px;
	padding: 0;
}