<template lang="pug">
#landing
  template(v-if="!whiteLabel")
    .top-wrapper
      .top
        .logo(@click="hardRefresh()")
          logo
        loginPanel
  section.slider-wrap
    .bg-landing
      #rain-effect
      #rain-effect2
    #main-carousel.carousel.slide(data-ride="carousel")
      ol.carousel-indicators
        li.active(data-target="#main-carousel", data-slide-to="0")
        li(data-target="#main-carousel", data-slide-to="1")
        li(data-target="#main-carousel", data-slide-to="2")
      .carousel-inner
        #banner8.carousel-item.active
          .banner-text
            h1
              | V-GAMES,
            h2
              | PLAY THE GAMES, AND EXPERIENCE A DIFFERENT KIND OF VICTORY.
            p
              | Experience a whole new level of victory.
              br
              | Immerse yourself in a variety of games, from competitive to fighting, and explore new genres that will keep you engaged for hours.
          .banner-image
            img.img-fluid.animate-image(:src="getImage('banner-12-1.png')")
            img.img-fluid(:src="getImage('banner-12-2.png')")
        #banner5.carousel-item
          .banner-text
            h1
              | E-Fighting,
              br
              | Play the best fighting games
            p
              | Collect your favorite characters, throw down in competitive combat, enter the battlefield and feel the fight! The more you play, the more you win!
          .banner-image
            img.img-fluid.animate-image(:src="getImage('banner-08-2.png')")
            img.img-fluid(:src="getImage('banner-08-1.png')")
        #banner1.carousel-item
          .banner-text
            h1 PICK A SPORT, GET IN THE GAME
            p
              | With a comprehensive selection of over 90 sports and thousands of live matches to bet on, the adrenalin rush never ends. Don’t miss out on your chance to win with our offer of only the best odds and top sports picks on the market.
          .banner-image
            img.img-fluid.animate-image(:src="getImage('banner-01-2.png')")
            img.img-fluid(:src="getImage('banner-01-1.png')")
        #banner2.carousel-item
          .banner-text
            h1 ROLL THE DICE, HIT THE JACKPOT
            p
              | Rake in winnings, all while enjoying a rich and immersive casino gaming experience at any of our luxurious suites. Take a seat, play your favourite casino game, and be on the receiving end of quality interaction from live dealers.
          .banner-image
            img.img-fluid.animate-image(:src="getImage('banner-02-2.png')")
            img.img-fluid(:src="getImage('banner-02-1.png')")
        #banner3.carousel-item
          .banner-text
            h1
              | GAMING AT ITS FINEST,
              br
              | A REVOLUTIONARY EXPERIENCE
            p
              | An extensive selection of games, as far as the eye can see. Get a firsthand taste of it now.
          .banner-image
            img.img-fluid.animate-image(:src="getImage('banner-03-2.png')")
            img.img-fluid(:src="getImage('banner-03-1.png')")
        #banner4.carousel-item
          .banner-text
            h1
              | LIVE SPORTS & GAME HIGHLIGHTS,
              br
              | REACH YOUR COMMUNITY IN REAL TIME
            p
              | Watch the best free Live TV and Highlights on the web and mobile devices
              br
              | Get the latest sports scores anytime, anywhere.
          .banner-image
            img.img-fluid.animate-image(:src="getImage('banner-05-2.png')")
            img.img-fluid(:src="getImage('banner-05-1.png')")


      a.carousel-control-prev(href="#main-carousel", role="button", data-slide="prev")
        span.carousel-control-prev-icon(aria-hidden="true")
        span.sr-only Previous
      a.carousel-control-next(href="#main-carousel", role="button", data-slide="next")
        span.carousel-control-next-icon(aria-hidden="true")
        span.sr-only Next
  section.lobby-wrap
    .inner-wrap
      .lobbyinner
        //- .worldcup-timer
        //-   .timer-logo
        //-     img.img-fluid(:src="getImage('worldcup_timer_logo.png')")
        //-   .worldcup-timer-wrapper.flex-grow-1
        //-     span(style="font-size: 72px; font-weight: bold;") ON GOING

        .row
          .col-12.mobile
            .lobby-item
              .card-flip(data-toggle="modal", data-target="#login-modal")
                .front
                  .image-wrap
                    img(:src="getImage('sportsbook_mobile.jpg')")
                  .info
                    h1 SPORTSBOOK
                .back
                  h3 SPORTSBOOK
                  p
                    | Enjoy non-stop thrills and excitement with an endless variety of sporting competitions to wager on, both locally and from all across the world. With us, you’ll always have the best odds.
                  .card-footer
                    button.btn(data-toggle="modal", data-target="#login-modal") Play Now
          .col-6.col-md-4
            .lobby-item
              .card-flip(data-toggle="modal", data-target="#login-modal")
                .front
                  .image-wrap
                    img(:src="getImage('casino.jpg')")
                  .info
                    h1 CASINO
                .back
                  h3 CASINO
                  p
                    | Step inside any of our exclusive casino suites, indulge in your classic casino favourites, and lose yourself in an interactive experience featuring gorgeous live dealers.
                  .card-footer
                    button.btn(data-toggle="modal", data-target="#login-modal") Play Now
          .col-md-4.desktop
            .lobby-item.sport
              .card-flip(data-toggle="modal", data-target="#login-modal")
                .front
                  .image-wrap
                    img.desktop(:src="getImage('sportsbook.jpg')")
                  .info
                    h1 SPORTSBOOK
                .back
                  h3 SPORTSBOOK
                  p
                    | Enjoy non-stop thrills and excitement with an endless variety of sporting competitions to wager on, both locally and from all across the world. With us, you’ll always have the best odds.
                  .card-footer
                    button.btn(data-toggle="modal", data-target="#login-modal") Play Now
          .col-6.col-md-4
            .lobby-item
              .card-flip(data-toggle="modal", data-target="#login-modal")
                .front
                  .image-wrap
                    img(:src="getImage('games.jpg')")
                  .info
                    h1 GAMES
                .back
                  h3 GAMES
                  p
                    | Take a break from reality, step right through, and experience for yourself the fantasies that have been brought to life in our vividly detailed virtual games. You’re sure to get addicted.
                  .card-footer
                    button.btn(data-toggle="modal", data-target="#login-modal") Play Now
      .join-us
        h1 MAKE THIS YOUR WORLD
        p
          | With 24/7 action and a spectacular array of products to wager on, we cater to pretty much any and all types of preferences. Plus, we’ve got cool bonuses, ongoing promotions, and efficient payouts to ensure you enjoy the best gaming experience. Take the first step and sign up with us now. It’s quick, easy, and free.
        button.join-btn(v-if="!whiteLabel", data-toggle="modal", data-target="#login-modal") JOIN US NOW
  section.feature-wrap
    .deco1
      img(:src="getImage('football.png')")
    .deco2
      img(:src="getImage('footballplayer.png')")
    .row.featureinner
      .col-12.col-md-6.feature-item
        .icon
          img(:src="getImage('feature_icon1.svg')")
        .feature-content
          h1 Optimized for Mobile
          p
            | Our games are accessible on popular mobile platforms, so you can game on the go on your favourite device.
      .col-12.col-md-6.feature-item
        .icon
          img(:src="getImage('feature_icon2.svg')")
        .feature-content
          h1 Advance Security and Support
          p
            | Enjoy our games and transact with us with true peace of mind – our website is fully secure to safeguard your privacy and personal info.
    .row.featureinner
      .col-12.col-md-6.feature-item
        .icon
          img(:src="getImage('feature_icon3.svg')")
        .feature-content
          h1 Immersive Experience
          p
            | Indulgent, intuitive, and interactive – we have curated and selectively chosen premium products that will deliver the excitement you seek.
      .col-12.col-md-6.feature-item
        .icon
          img(:src="getImage('feature_icon4.svg')")
        .feature-content
          h1 Customer Support
          p
            | Your satisfaction is our priority. We would to hear from you should you have any feedback or queries that would help us serve you better.
  footer.footer(v-if="!whiteLabel")
    .license
      .image-wrap
        img(:src="getImage('license/01.png')")
      .image-wrap
        img(:src="getImage('license/02.png')")
      .image-wrap
        img(:src="getImage('license/03.png')")
      .image-wrap
        img(:src="getImage('license/04.png')")
      .image-wrap
        img(:src="getImage('license/05.png')")
      .image-wrap
        img(:src="getImage('license/06.png')")
      .image-wrap
        img(:src="getImage('license/07.png')")
      .image-wrap
        img(:src="getImage('license/08.png')")
    .tnc
      .icon
        img(:src="getImage('18plus.svg')")
      .tnc-content(v-if="brand == 'WBET'")
        h4 Underage Gambling
        p
          | Gambling is illegal for kids under the age of 18. Every state prohibits gambling by minors. {{ brand }} will carry out age verification randomly and winnings will be forfeited if the gambler is below 18 years old.© 2004-2019 {{ brand }}. {{ brand }} is An Internationally Registered Trademark (Licensed Sports Bookmaker). All Rights Reserved.
      .tnc-content(v-else)
        h4 Underage Gambling
        p
          | Gambling is illegal for kids under the age of 18. Every state prohibits gambling by minors. {{ brand }} will carry out age verification randomly and winnings will be forfeited if the gambler is below 18 years old.© 2004-2019 {{ brand }}. {{ brand }} is An Internationally Registered Trademark (Licensed Sports Bookmaker). All Rights Reserved.
</template>

<script>
import config from "@/config";
import errors from "@/errors";

function updateTimer() {
  // var future = Date.parse("Nov 21, 2022 00:00:00");
  // var now = new Date();
  // var diff = future - now;
  // var days = Math.floor(diff / (1000 * 60 * 60 * 24));
  // var hours = Math.floor(diff / (1000 * 60 * 60));
  // var mins = Math.floor(diff / (1000 * 60));
  // var secs = Math.floor(diff / 1000);
  // var d = days;
  // var h = hours - days * 24;
  // var m = mins - hours * 60;
  // var s = secs - mins * 60;
  // var elem = document.getElementById("timer");
  // if (elem) {
  //   elem.innerHTML = "<div>" + d + "<span>days</span></div>" + "<div>" + h + "<span>hours</span></div>" + "<div>" + m + "<span>minutes</span></div>" + "<div>" + s + "<span>seconds</span></div>";
  //   setTimeout(updateTimer, 1000);
  // }
}

function specialfx() {
  function animm(elm) {
    TweenMax.to(elm, R(6, 15), {
      y: h + 100,
      ease: Linear.easeNone,
      repeat: -1,
      delay: -5,
    });
    TweenMax.to(elm, R(2, 8), {
      x: "+=100",
      rotationZ: R(0, 180),
      repeat: -1,
      yoyo: true,
      ease: Sine.easeInOut,
    });
    TweenMax.to(elm, R(2, 8), {
      rotationX: R(0, 360),
      rotationY: R(0, 360),
      repeat: -1,
      yoyo: true,
      ease: Sine.easeInOut,
      delay: -5,
    });
  }

  function R(min, max) {
    return min + Math.random() * (max - min);
  }

  TweenLite.set("#rain-effect", { perspective: 600 });
  TweenLite.set(".rain-effect img", { xPercent: "-50%", yPercent: "-50%" });

  var total = 30;
  var warp = document.getElementById("rain-effect"),
      w = window.innerWidth,
      h = window.innerHeight;

  for (var i = 0; i < total; i++) {
    var Div = document.createElement("div");
    TweenLite.set(Div, {
      attr: { class: "rain-effect" },
      x: R(0, w),
      y: R(-200, -150),
      z: R(-200, 200),
    });
    warp.appendChild(Div);
    animm(Div);
  }

  TweenLite.set("#rain-effect2", { perspective: 600 });
  TweenLite.set(".rain-effect2 img", { xPercent: "-50%", yPercent: "-50%" });

  var total = 50;
  var warp = document.getElementById("rain-effect2"),
      w = window.innerWidth,
      h = window.innerHeight;

  for (var i = 0; i < total; i++) {
    var Div = document.createElement("div");
    TweenLite.set(Div, {
      attr: { class: "rain-effect2" },
      x: R(0, w),
      y: R(-200, -100),
      z: R(-200, 200),
    });
    warp.appendChild(Div);
    animm(Div);
  }

  // TweenLite.set("#rain-effect3", { perspective: 600 });
  // // TweenLite.set(".rain-effect3 img", { xPercent: "+50%", yPercent: "+50%" });

  // var total = 30;
  // var warp = document.getElementById("rain-effect3"),
  //   w = window.innerWidth,
  //   h = window.innerHeight;

  // for (var i = 0; i < total; i++) {
  //   var Div = document.createElement("div");
  //   TweenLite.set(Div, { attr: { class: "rain-effect3" }, x: R(0, w), y: R(-200, -150), z: R(-200, 200) });
  //   warp.appendChild(Div);
  //   animm(Div);
  // }

  // setTimeout(updateTimer, 1000);
}

export default {
  components: {
    logo: () => import("@/components/desktop/logo"),
    loginPanel: () => import("@/components/desktop/loginPanel"),
  },
  data() {
    return {
      hList: [],
      url1: "url(" + this.getImage("slider_bg.jpg") + ")",
      url2: "url(" + this.getImage("slider_casino_bg.jpg") + ")",
      url3: "url(" + this.getImage("slider_games_bg.jpg") + ")",
      url4: "url(" + this.getImage("slider_live_bg.jpg") + ")",
      url5: "url(" + this.getImage("slider_esport_bg.jpg") + ")",
      url6: "url(" + this.getImage("slider_live22_bg.jpg") + ")",
      url7: "url(" + this.getImage("slider_tournament_bg.jpg") + ")",
      url8: "url(" + this.getImage("slider_vgames_bg.jpg") + ")",
      url9: "url(" + this.getImage("slider_dragonboat_bg.jpg") + ")",
    };
  },
  computed: {
    brand() {
      return config.brand;
    },
    whiteLabel() {
      return config.whiteLabel;
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
  },
  destroyed() {
    $("#main-carousel").off("slide.bs.carousel");
    $(".carousel-control-next,carousel-control-prev").off("click");
  },
  mounted() {
    for (var i = 0; i < 10000; i++) {
      this.hList.push({
        label: "Option " + (i + 1),
      });
    }
    if (this.$route.query.redirect) {
      this.$swal("Info", this.$t("message.login"), "info").then((result) => {
        this.$router.push("/").catch((err) => {
          console.trace(err);
        });
      });
    }
    $("#main-carousel").on("slide.bs.carousel", (e) => {
      var bannerList = {
        banner1: this.url1,
        banner2: this.url2,
        banner3: this.url3,
        banner4: this.url4,
        banner5: this.url5,
        banner6: this.url6,
        banner7: this.url7,
        banner8: this.url8,
        banner9: this.url9,
      };
      // $(".bg-landing").addClass("fadeInSlider");
      // setTimeout(function () {
      //   $(".bg-landing").removeClass("fadeInSlider");
      // }, 1000);
      $(".bg-landing").css("background-image", bannerList[e.relatedTarget.id]);
    });

    // $(".carousel-control-next,carousel-control-prev").click(function () {
    //   $(".bg-landing").removeClass("fadeInSlider");
    // });

    this.$nextTick(() => {
      $(".bg-landing").css("background-image", this.url8);
      $(".carousel").carousel({
        interval: 5000,
      });
      $(document).on("scroll", function () {
        $(".lang-dropdown-wrap").hide();

        if ($(document).scrollTop() > 80) {
          $(".landing .top-wrapper").addClass("small-top");
        } else {
          $(".landing .top-wrapper").removeClass("small-top");
        }
      });
    });
  },
  methods: {
    getImage(fn) {
      return config.getLandingUrl(fn);
    },
    hardRefresh() {
      window.location.reload(true);
    },
    desktop() {
      this.$router.push("/desktop").catch((err) => {
        console.trace(err);
      });
    },
  },
};
</script>
