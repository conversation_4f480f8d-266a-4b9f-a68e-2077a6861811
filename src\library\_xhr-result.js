import config from "@/config";
import errors from "@/errors";
import Vue from "vue";

export default {
  loading: {
    getResult: false,
    getSingleResult: false,
    getParlayResult: false,
    getNgResult: false,
    getEFResult: false
  },

  getResult(args) {
    const url = config.matchResultUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getResult"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("working_date_from" in args)) {
        feedback.status = errors.result.workingDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("working_date_to" in args)) {
        feedback.status = errors.result.workingDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("sports_type" in args)) {
        feedback.status = errors.result.sportsTypeRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("league_id" in args)) {
        feedback.status = errors.result.leagueIdRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("is_outright" in args)) {
        feedback.status = errors.result.isOutrightRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.working_date_from) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.working_date_to) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.sports_type) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (args.league_id == null) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (args.is_outright == null) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading.getResult == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.getResult = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getResult = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getResult = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getSingleResult(args) {
    const url = config.singleMatchResultUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getSingleResult"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("match_id" in args)) {
        feedback.status = errors.result.matchIdRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.match_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading.getResult == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.getSingleResult = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getSingleResult = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getSingleResult = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getParlayResults(args) {
    const url = config.parlayMatchResultUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "getParlayResult"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("bet_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.bet_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      this.loading.getParlayResult = true;
      if (canRequest == true) {
        Vue.http.post(url, args).then(
          res => {
            this.loading.getParlayResult = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              // console.log(res);
              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.request.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getNgResult(args) {
    const url = config.ngResultUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getNgResult"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("league_id" in args)) {
        feedback.status = errors.result.leagueIdRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (args.league_id == null) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading.getNgResult == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.getNgResult = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getNgResult = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getNgResult = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getEFResult(args) {
    const url = config.efResultUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getEFResult"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("sports_type" in args)) {
        feedback.status = errors.result.sportsTypeRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (args.sports_type == null) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.getEFResult = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getEFResult = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getEFResult = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  }
};
