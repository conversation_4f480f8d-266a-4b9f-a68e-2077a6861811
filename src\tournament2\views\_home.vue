<template lang="pug">
.tournament-container
  //- small {{ room }}
  .tournament-wrapper
    //- left Panel
    //-
    //- Room List
    .tournament-content-left.magicY(v-if="room.mode === 0")
      .tournament-room
        .tournament-page-wrapper.flex-fill
          ul.nav.nav-pills
            li.nav-item
              .nav-link(:class="roomType===0?'active':''" @click="roomType=0")
                i.fad(:class="roomType===0?'fa-door-open':'fa-door-closed'")
                span {{ $t('m.ROOM_T0') }}
                span.ml-1(v-if="roomStats.hasOwnProperty(0)") ({{ roomStats[0].room_active }}/{{ roomStats[0].room_total }})
            li.nav-item#v-step-1
              .nav-link(:class="roomType===1?'active':''" @click="roomType=1")
                i.fad(:class="roomType===1?'fa-door-open':'fa-door-closed'")
                span {{ $t('m.ROOM_T1') }}
                span.ml-1(v-if="roomStats.hasOwnProperty(1)") ({{ roomStats[1].room_active }}/{{ roomStats[1].room_total }})
            li.nav-item#v-step-2
              .nav-link(:class="roomType===2?'active':''" @click="roomType=2")
                i.fad(:class="roomType===2?'fa-door-open':'fa-door-closed'")
                span {{ $t('m.ROOM_T2') }}
                span.ml-1(v-if="roomStats.hasOwnProperty(2)") ({{ roomStats[2].room_active }}/{{ roomStats[2].room_total }})
            li.nav-item#v-step-3
              .nav-link(:class="roomType===3?'active':''" @click="roomType=3")
                i.fad(:class="roomType===3?'fa-door-open':'fa-door-closed'")
                span {{ $t('m.ROOM_T3') }}
                span.ml-1(v-if="roomStats.hasOwnProperty(3)") ({{ roomStats[3].room_active }}/{{ roomStats[3].room_total }})

        .tournament-search.mr-0
          .input-group
            input.form-control(type="text", :placeholder="$t('ui.room_id')" v-model="searchId" :disabled="loading.getRoomList")
            .input-group-append
              button.btn.tournament-btn-search(type="button" @click="getRoomList" :disabled="loading.getRoomList")
                i.fas.fa-search

      .tournament-room
        .tournament-pagination.flex-fill
          v-pagination(
            :value="page.value"
            @input="changePage($event)"
            :page-count="page.count"
            :classes="bootstrapPaginationClasses"
            :labels="paginationAnchorTexts"
          )

        .tournament-search.mr-3.d-flex.align-items-center.justify-content-center
          button.tournament-pool-status.room_2.tournament-button-hover.thin-font-1(@click="replayIntro()") {{ $t("ui.introduction") }}
          button.tournament-pool-status.room_1.tournament-button-hover.thin-font-1(@click="replayTour()") {{ $t("ui.tutorial") }}

        .tournament-search.mr-3
          select(v-model="roomFilter" :disabled="loading.getRoomList")
            option(:class="roomFilter==0 ? 'text-white' : ''" value="0") {{ $t('m.ROOM_A') }}
            option(:class="roomFilter==1 ? 'text-white' : ''" value="1") {{ $t('m.ROOM_1') }}
            option(:class="roomFilter==2 ? 'text-white' : ''" value="2") {{ $t('m.ROOM_2') }}
            option(:class="roomFilter==3 ? 'text-white' : ''" value="3") {{ $t('m.ROOM_3') }}
            option(:class="roomFilter==4 ? 'text-white' : ''" value="4") {{ $t('m.ROOM_4') }}
            option(:class="roomFilter==10 ? 'text-white' : ''" value="10") {{ $t('m.ROOM_10') }}

        .check-content.mr-3
          .select-league-checkbox
            label.date-check
              input(type="checkbox" v-model="joined_only")
              span.checkmark-date
          .select-league-teams(style="cursor: auto;") {{ $t('ui.joined_only') }}

      template(v-if="loading.getRoomList")
        .tournament-container
          .loader-wrapper
            .loader
      template(v-else)
        template(v-if="roomList.length > 0")
          roomList(:roomList="roomList" @room-enter2="enterRoom")
        template(v-else)
          .tournament-room-list
            .tournament-pool-single
              .round-alert {{ $t('message.no_room') }}

    //- Join Page
    .tournament-content-left.magicY(v-if="room.mode === 1")
      roomJoinPage(:roomData="room.data" @room-leave2="leaveRoom" @room-data-updated="updateRoomData")

    //- Game Page
    .tournament-content-left.magicY(v-if="room.mode === 2")
      roomGame(:roomId="room.id" @room-leave2="leaveRoom")

    //- Right Panel
    //-
    //- Game Page
    .tournament-content-right.magicY(v-if="[1,2].includes(room.mode)")
      .tournament-user
        .tournament-username.flex-grow-1 {{ displayName }}
        .tournament-user-balance {{ currency_code }}
      roomBetSlip(:roomId="room.id" @room-leave2="leaveRoom" @room-join2="joinRoom")

    //- Room List, Join Page
    .tournament-content-right.magicY(v-else)
      .tournament-user
        .tournament-username.flex-grow-1 {{ displayName }}
        .tournament-user-balance {{ currency_code }}
      a(href="javascript:void(0);", data-toggle="modal", data-target="#room-rules")
        .d-flex.align-items-center.justify-content-end.m-1
          .tournament-text {{ $t('ui.rules') }}
          .tournament-icon
            i.fad.fa-question-circle
      template(v-if="privateRoom")
        //- a(href="javascript:void(0);", data-toggle="modal", data-target="#room-create")
        a(href="javascript:void(0);" @click="createRoom()")
          .tournament-create-room.tournament-button-hover
            .tournament-text {{ $t('ui.create_private_room') }}
            .tournament-icon
              i.fad.fa-plus-circle

</template>

<script>
import Vue from "vue";
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import service from "@/tournament2/library/_xhr.js";
import vPagination from "vue-plain-pagination";

export default {
  components: {
    vPagination,
    roomList: () => import("@/tournament2/components/roomList.vue"),
    roomJoinPage: () => import("@/tournament2/components/roomJoinPage.vue"),
    roomGame: () => import("@/tournament2/components/roomGame.vue"),
    roomBetSlip: () => import("@/tournament2/components/roomBetSlip.vue"),
  },
  data() {
    return {
      searchId: "",
      joined_only: false,
      room: {
        id: null,
        data: null,
        mode: 0,
      },
      roomType: 0,
      roomFilter: "0",
      privateRoom: false,
      page: {
        value: 1,
        count: 1,
        total: 0,
      },
      bootstrapPaginationClasses: {
        ul: "pagination",
        li: "page-item",
        liActive: "active",
        liDisable: "disabled",
        button: "page-link",
        buttonActive: "active",
        buttonDisable: "disable",
      },
      paginationAnchorTexts: {
        first: "<i class='fas fa-angle-double-left'></i>",
        prev: "<i class='fas fa-angle-left'></i>",
        next: "<i class='fas fa-angle-right'></i>",
        last: "<i class='fas fa-angle-double-right'></i>",
      },
      roomList: [],
      roomStats: {},
      loading: {
        getRoomList: false,
      },
    };
  },
  computed: {
    language() {
      return this.$store.getters.language;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
    displayName() {
      if (this.nickname) return this.nickname;
      else return this.account_id;
    },
    account_id() {
      return this.$store.getters.accountId;
    },
    nickname() {
      return this.$store.getters.nickName;
    },
    pageSize() {
      return 20;
    },
  },
  watch: {
    currency_code() {
      if (this.currency_code == null || this.currency_code == "") {
        // close this window with javascript
        window.close();
      }
    },
    roomFilter() {
      this.resetList();
      this.getRoomList();
    },
    roomType() {
      this.resetList();
      this.getRoomList();
    },
    joined_only() {
      this.resetList();
      this.getRoomList();
    },
  },
  destroyed() {
    EventBus.$off("roomEnterById2");
  },
  mounted() {
    this.getRoomList();
    EventBus.$on("roomEnterById2", (e) => {
      this.getRoomListById(e);
    });
  },
  methods: {
    replayIntro() {
      $("#modal-tournament-ads").modal("show");
    },
    replayTour() {
      this.$store.dispatch("layout/setTour1", false);
      this.$tours['tour1'].start();
    },
    createRoom() {
      EventBus.$emit("ROOM_CREATE");
    },
    resetList() {
      this.page.value = 1;
      this.page.count = 1;
      this.page.total = 0;
    },
    leaveRoom() {
      this.room.id = null;
      this.room.data = null;
      this.room.mode = 0;
      this.getRoomList();
    },
    enterRoom(room) {
      this.room.id = room.room_id;
      this.room.data = room;
      this.room.mode = 2;
    },
    joinRoom(room) {
      this.room.id = room.room_id;
      this.room.data = room;
      this.room.mode = 1;
    },
    getCountDown() {
      if (this.roomList.length > 0) {
        for (var n = 0; n < this.roomList.length; n++) {
          var now = this.$dayjs.utc();
          var difference = this.$dayjs
            .utc(this.roomList[n].start_time + "+0800")
            .diff(now);
          var days = Math.floor(difference / (1000 * 60 * 60 * 24));
          var hours = Math.floor((difference / (1000 * 60 * 60)) % 24);
          var minutes = Math.floor((difference / 1000 / 60) % 60);
          var seconds = Math.floor((difference / 1000) % 60);
          this.roomList[n].countdown =
            days + " days " + hours + " hrs " + minutes + " mins " + seconds + " s";
        }
      }
    },
    getRoomList() {
      // if searchId is not empty, then must be integer and greater than 0
      var room_id = 0;
      if (this.searchId != "") {
        if (!isNaN(this.searchId) && this.searchId > 0) {
          // convert searchId to integer and assing to room_id
          room_id = parseInt(this.searchId);
          this.roomFilter = "10";
        }
      }

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        room_status: parseInt(this.roomFilter),
        room_type: parseInt(this.roomType),
        room_id: room_id,
        joined_only: this.joined_only === true ? 1 : 0,
        pay_type: 2,
        page_number: this.page.value,
        page_size: this.pageSize,
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      this.loading.getRoomList = true;
      service.getRoomList(config.tournamentUrl().roomlist, json).then(
        (result) => {
          this.loading.getRoomList = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              var rn = result.data.extra;
              // loop through rn and assign to this.roomStats by room_type as key and item as value
              // check if this.roomStats[room_type] has not own property, then assign empty object
              // then assign item to this.roomStats[room_type]
              var allRoom = {
                room_type: 0,
                room_active: 0,
                room_count: 0,
                room_total: 0,
              };
              for (var i = 0; i < rn.length; i++) {
                var item = rn[i];
                allRoom.room_active += item.room_active;
                allRoom.room_count += item.room_count;
                allRoom.room_total += item.room_total;
                if (!this.roomStats.hasOwnProperty(item.room_type)) {
                  this.roomStats[item.room_type] = {};
                }
                this.roomStats[item.room_type] = item;
              }
              this.roomStats[0] = allRoom;

              // check if this.roomStats has own property 2, if yes, check if room_count if equal or greater than room_total, if yes then set this.privateRoom to true
              if (this.roomStats.hasOwnProperty(2)) {
                if (this.roomStats[2].room_active >= this.roomStats[2].room_total) {
                  this.privateRoom = false;
                } else {
                  this.privateRoom = true;
                }
              } else {
                this.privateRoom = true;
              }

              this.roomList = [];
              this.roomList = result.data.value;
              if (this.roomList.length > 0) {
                this.page.total = this.roomList[0].totalrows;
                if (this.page.total % this.pageSize != 0)
                  this.page.count = Math.ceil(
                    parseFloat(this.page.total) / parseFloat(this.pageSize)
                  );
                else
                  this.page.count =
                    parseFloat(this.page.total) / parseFloat(this.pageSize);
              } else {
                this.resetList();
              }

              if (this.room.id != null) {
                var m = false;
                for (var n = 0; n < this.roomList.length; n++) {
                  if (this.room.id === this.roomList[n].room_id) {
                    m = true;
                    break;
                  }
                }
                if (m === false) {
                  this.room.id = null;
                  this.room.data = null;
                  this.room.mode = 0;
                }
              }

              //- this.getCountDown();
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          this.loading.getRoomList = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getRoomListById(e) {
      var room_id = e;
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        room_status: 10,
        room_type: 0,
        room_id: room_id,
        joined_only: 0,
        pay_type: 2,
        page_number: 1,
        page_size: 1,
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      service.getRoomList(config.tournamentUrl().roomlist, json).then(
        (result) => {
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              var rn = result.data.extra;
              // loop through rn and assign to this.roomStats by room_type as key and item as value
              // check if this.roomStats[room_type] has not own property, then assign empty object
              // then assign item to this.roomStats[room_type]
              var allRoom = {
                room_type: 0,
                room_active: 0,
                room_count: 0,
                room_total: 0,
              };
              for (var i = 0; i < rn.length; i++) {
                var item = rn[i];
                allRoom.room_active += item.room_active;
                allRoom.room_count += item.room_count;
                allRoom.room_total += item.room_total;
                if (!this.roomStats.hasOwnProperty(item.room_type)) {
                  this.roomStats[item.room_type] = {};
                }
                this.roomStats[item.room_type] = item;
              }
              this.roomStats[0] = allRoom;

              // check if this.roomStats has own property 2, if yes, check if room_count if equal or greater than room_total, if yes then set this.privateRoom to true
              if (this.roomStats.hasOwnProperty(2)) {
                if (
                  this.roomStats[2].room_active + this.roomStats[2].room_count >=
                  this.roomStats[2].room_total
                ) {
                  this.privateRoom = false;
                } else {
                  this.privateRoom = true;
                }
              } else {
                this.privateRoom = true;
              }

              var room = result.data.value[0];
              if (room != null) {
                // if room_id is not found, then push to this.roomList
                // if room_id is found, then update this.roomList
                var m = false;
                for (var n = 0; n < this.roomList.length; n++) {
                  if (room_id === this.roomList[n].room_id) {
                    Vue.set(this.roomList, n, result.data.value[0]);
                    m = true;
                    break;
                  }
                }

                if (m === false) {
                  this.roomList.push(result.data.value[0]);
                }

                this.room.id = room.room_id;
                this.room.data = room;
                this.room.mode = 2;
              }
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    changePage(e) {
      this.page.value = e;
      this.getRoomList();
    },
    updateRoomData(updatedRoomData) {
      this.room.data = updatedRoomData;
    },
  },
};
</script>
