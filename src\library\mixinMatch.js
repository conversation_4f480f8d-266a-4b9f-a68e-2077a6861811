export default {
  methods: {
    getItems() {
      var match = [];
      for (var i in this.items) {
        match.push({ header: this.items[i], details: this.detailsORZ(this.items[i][2]) });
      }
      match.sort((a, b) => {
        if (a["details"] == null) return 0;
        if (a["details"]["orz"] == null) return 0;
        if (a["details"]["orz"][0] == null) return 0;
        if (a["details"]["orz"][0][15] < b["details"]["orz"][0][15]) return -1;
        if (a["details"]["orz"][0][15] > b["details"]["orz"][0][15]) return 1;
        return 0;
      });
      return match;
    },
    detailsORZ(e) {
      var odds = this.odds();
      var r = {};
      if (odds != null) {
        if (odds.hasOwnProperty("orz")) {
          if (odds["orz"][e]) {
            r["orz"] = odds["orz"][e].filter((v, i, r) => {
              return v[4] == "OR";
            });
          }
        }
      }
      this.result = null;
      this.result = r;
      return r;
    },
    gotMatch() {
      if (this.items != null) {
        return this.items.length > 0;
      } else {
        return false;
      }
    },
    match() {
      return this.$store.getters.data.match;
    },
    head() {
      return this.$store.getters.data.head;
    },
    child() {
      return this.$store.getters.data.child;
    },
    odds() {
      if (this.$store.getters.data.hasOwnProperty("odds")) {
        if (this.$store.getters.data.odds[this.layoutIndex] != null) {
          return this.$store.getters.data.odds[this.layoutIndex];
        } else {
          return {};
        }
      }
      return {};
    }
  }
};
