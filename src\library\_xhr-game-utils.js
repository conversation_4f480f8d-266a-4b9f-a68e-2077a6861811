import config from "@/config";
import errors from "@/errors";
import Vue from "vue";
import { GAME_PROVIDERS_CONFIG } from "@/config/game-providers";

// Validation helpers
const validateArgs = (args, requiredArgs) => {
  if (!args) {
    return { valid: false, status: errors.request.incompleted };
  }

  for (const requiredArg of requiredArgs) {
    if (!(requiredArg in args) || !args[requiredArg]) {
      return { valid: false, status: errors.request.incompleted };
    }
  }

  return { valid: true };
};

// Response handlers
const handleStatusResponse = (res, feedback) => {
  if (typeof res.data.status == "string") {
    feedback.success = res.data.status == "1";
  } else {
    feedback.success = res.data.status == 1;
  }
  feedback.status = res.data.statusdesc;
  return feedback;
};

const handleErrorIdResponse = (res, feedback) => {
  if (typeof res.data.errorId == "string") {
    feedback.success = res.data.errorId == "0";
  } else {
    feedback.success = res.data.errorId == 0;
  }
  feedback.status = res.data.message;
  return feedback;
};

const handleBodyResponse = (res, feedback) => {
  feedback.success = true;
  feedback.status = "ok";
  return feedback;
};

// Main utility function for game launching
export const createGameLauncher = (providerKey, pattern, loadingState, options = {}) => {
  return (args, launchOptions = {}) => {
    const providerConfig = GAME_PROVIDERS_CONFIG[pattern][providerKey];
    if (!providerConfig) {
      throw new Error(`Provider ${providerKey} not found in pattern ${pattern}`);
    }

    // Handle special cases
    let url;
    if (providerConfig.specialCase === 'conditionalUrl' && launchOptions.isLiveCasino === true) {
      url = config.launchPragmaticCasinoUrl();
    } else {
      url = config[providerConfig.urlMethod]();
    }

    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: providerKey,
    };

    return new Promise((resolve, reject) => {
      // Validate arguments
      let validation;

      validation = validateArgs(args, providerConfig.requiredArgs);

      if (!validation.valid) {
        feedback.status = validation.status;
        reject(feedback);
        return;
      }

      // Set loading state
      loadingState[providerConfig.loadingKey] = true;

      Vue.http.post(url, args).then(
        (res) => {
          loadingState[providerConfig.loadingKey] = false;

          if (res.data || res.body) {
            // Handle different response types
            if (providerConfig.responseType === 'status') {
              handleStatusResponse(res, feedback);
            } else if (providerConfig.responseType === 'errorId') {
              handleErrorIdResponse(res, feedback);
            } else if (providerConfig.responseType === 'body') {
              handleBodyResponse(res, feedback);
            }

            if (feedback.success === true) {
              try {
                // Extract data based on response type
                if (providerConfig.responseType === 'body') {
                  feedback.data = res.body;
                } else {
                  feedback.data = res.data[providerConfig.responseDataPath];
                }
                resolve(feedback);
              } catch (error) {
                feedback.success = false;
                feedback.status = errors.login.failed;
                reject(feedback);
              }
            } else {
              reject(feedback);
            }
          } else {
            reject(feedback);
          }
        },
        (err) => {
          loadingState[providerConfig.loadingKey] = false;
          feedback.status = errors.request.failed;
          feedback.error = err;
          reject(feedback);
        }
      );
    });
  };
};

// Utility function for game list retrieval
export const createGameListGetter = (providerKey, loadingState, options = {}) => {
  return (args) => {
    const providerConfig = GAME_PROVIDERS_CONFIG.gameList[providerKey];
    if (!providerConfig) {
      throw new Error(`Game list provider ${providerKey} not found`);
    }

    const url = config[providerConfig.urlMethod]();
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: `get${providerKey.charAt(0).toUpperCase() + providerKey.slice(1)}GameList`,
    };

    return new Promise((resolve, reject) => {
      // Validate arguments (skip validation for special cases like awc)
      if (!options.skipValidation && providerConfig.requiredArgs.length > 0) {
        const validation = validateArgs(args, providerConfig.requiredArgs);
        if (!validation.valid) {
          feedback.status = validation.status;
          reject(feedback);
          return;
        }
      }

      // Set loading state
      loadingState[providerConfig.loadingKey] = true;

      Vue.http.post(url, args).then(
        (res) => {
          loadingState[providerConfig.loadingKey] = false;

          if (res.body) {
            feedback.success = true;
            feedback.status = "ok";
            feedback.data = res.body;
            resolve(feedback);
          } else {
            reject(feedback);
          }
        },
        (err) => {
          loadingState[providerConfig.loadingKey] = false;
          feedback.status = errors.request.failed;
          feedback.error = err;
          reject(feedback);
        }
      );
    });
  };
};

// Factory function to create all utility functions
export const createGameUtils = (loadingState) => {
  const utils = {};

  // Create launch functions for player ID/password pattern
  Object.keys(GAME_PROVIDERS_CONFIG.playerIdPassword).forEach(provider => {
    utils[`launch${provider.toUpperCase()}`] = createGameLauncher(provider, 'playerIdPassword', loadingState);
  });

  // Create launch functions for username/session ID pattern
  Object.keys(GAME_PROVIDERS_CONFIG.usernameSessionId).forEach(provider => {
    utils[`launch${provider.charAt(0).toUpperCase() + provider.slice(1)}`] = createGameLauncher(provider, 'usernameSessionId', loadingState);
  });

  // Create game list functions
  Object.keys(GAME_PROVIDERS_CONFIG.gameList).forEach(provider => {
    utils[`get${provider.charAt(0).toUpperCase() + provider.slice(1)}GameList`] = createGameListGetter(provider, loadingState);
  });

  return utils;
}; 