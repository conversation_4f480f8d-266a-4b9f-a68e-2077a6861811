<template lang="pug">
.match-info.d-flex.flex-column(:class="[betslip.betStatus == 'false' ? 'cancelBet':'', betslip.marketType != 3 ? 'grey' : 'red']")
</template>

<script>
export default {
  props: {
    betslip: {
      type: Object
    }
  },
  computed: {
    isBallDisplay() {
      var result = naming.ballDisplay2(this.betslip.ballDisplay, this.betslip.giving, this.betslip.homeAway, this.betslip.betType, this);
      if (["HDP", "HDPH"].includes(this.betslip.betType)) {
        return result != null && result != "0";
      } else {
        return false;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.cancelBet{
  text-decoration: line-through;
}
</style>