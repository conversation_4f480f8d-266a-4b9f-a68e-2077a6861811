<template lang="pug">
  .hx-main.dcOdds
    .hx-table.hx-match.hx-compact(:class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }")
      .hx-cell.w-62
        .hx-row.h-100.hx-rows
          timePanel(:source="source")
      .hx-cell.flex-fill
        .hx-row.h-100.hx-rows
          xTeam(:source="source" isDraw=false)
          xFavorite(:source="source")
      template(v-for="item in cols")
        .hx-cell.w-206
          .hx-row.hx-rows.h-100
            .hx-col.hx-cols.h-100.w-69(v-for="i in dc")
              dcItem(:details="details" :oddsType="oddsType" :i="i" :item="item")
      .hx-cell.w-40
        .hx-row.h-100.hx-rows
          .hx-col.hx-cols.w-100.d-flex.align-items-center.justify-content-center
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

// import timePanel from "@/components/desktop/main/xtable/timePanel";
// import xTeam from "@/components/desktop/main/xtable/xitem/xTeam";
// import xFavorite from "@/components/desktop/main/xtable/xitem/xFavorite";
// import dcItem from "@/components/desktop/main/xtable/xitem/dcItem";

export default {
  components: {
    // timePanel,
    // xTeam,
    // xFavorite,
    // dcItem

    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    xTeam: () => import("@/components/desktop/main/xtable/xitem/xTeam"),
    xFavorite: () => import("@/components/desktop/main/xtable/xitem/xFavorite"),
    dcItem: () => import("@/components/desktop/main/xtable/xitem/dcItem")
  },
  mixins: [mixinHDPOUOdds],
  data() {
    return {
      cols: ['dco', 'dcho'],
      dc: ['HD', 'HA', 'DA']
    };
  }
};
</script>