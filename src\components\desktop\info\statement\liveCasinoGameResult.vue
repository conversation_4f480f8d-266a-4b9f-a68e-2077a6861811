<template lang="pug">
  div
    table.table-info(width='100%')
      tbody
        tr
          th.text-center(scope='col', width='6%') {{ $t("ui.no/") }}
          th.text-left(scope='col', width='20%') {{ $t("ui.trans_time") }}
          th(scope='col', width='30%') {{ $t("ui.event") }}
          th.text-right(scope='col', width='10%') {{ $t("ui.stake") }}
          th.text-right(scope='col', width='12%') {{ $t("ui.win") }} / {{ $t("ui.loss") }}
          th.text-left(scope='col', width='12%') {{ $t("ui.status") }}
        tr.grey(v-if="gameResultList.length == 0")
          td(colspan="7").text-center
            span {{ $t('message.no_information_available') }}
        tr(v-for="(item, index) in gameResultList" :class="{ grey: index % 2 === 0 }")
          td.text-center(valign='top') {{ ((currentPage - 1) * $store.getters.pageSize + index + 1) }}
          td.text-left(valign='top')
            div {{ $t("ui.ref_no") }}: {{ item.result_id }}
            div {{ $dayjs(item.bet_time).format("MM/DD/YYYY hh:mm:ss A") }}
          td.text-left(valign='top')
            .bet-info
              .bet-type.blue {{ item.game_name }}
              .bet-detail
                .name {{ item.bet_id }}
          td.text-right(valign='top') {{ $numeral(item.turnover).format("0,0.00") }}
          td.text-right(valign='top')
            div
              span(
                :class="{ red: parseFloat(item.winlose) < 0 }"
                ) {{ $numeral(item.winlose).format("0,0.00") }}
            div {{ item.comm ? parseFloat(item.comm).toFixed(2) : '0.00' }}
          td.text-left(valign='top')
            div {{ parseFloat(item.winlose) != 0 ? (parseFloat(item.winlose) < 0 ? $t("ui.lost") : $t("ui.won")) : $t("ui.draw") }}

            template(v-if="['PP-C', 'AI', 'SEXYBCRT'].includes(item.game_provider)")

              template(v-if="item.game_provider == 'PP-C'")
                a.icon-info(v-if="!loading.pragmatic" href="javascript:void(0);" title='Result' @click="getResultPragmaticCasino(item.bet_id)")
                  i.fad.fa-poll
                a.icon-info(v-else href="javascript:void(0);")
                  i.fa.fa-spin.fa-spinner

              template(v-if="item.game_provider == 'AI'")
                a.icon-info(v-if="!loading.ai" href="javascript:void(0);" title='Result' @click="getResultAICasino(item.bet_id)")
                  i.fad.fa-poll
                a.icon-info(v-else href="javascript:void(0);")
                  i.fa.fa-spin.fa-spinner

              template(v-if="item.game_provider == 'SEXYBCRT'")
                a.icon-info(v-if="!loading.awc" href="javascript:void(0);" title='Result' @click="getResultSexyBacarrat(item.bet_id)")
                  i.fad.fa-poll
                a.icon-info(v-else href="javascript:void(0);")
                  i.fa.fa-spin.fa-spinner

            template(v-else)
              a.icon-info(:href="resultUrl(item.result_id)" title='Result' target="result" onclick="window.open(this.href,'result','top=10,height=600,width=400,status=no,toolbar=no,menubar=no,location=no');return false;")
                i.fad.fa-poll
    table.table-total(width='100%' v-if="isTotal")
      tbody
        tr
          td.text-right(valign='top' width='66%') {{ $t("ui.subtotal") }} ({{ parseFloat(gameResultSummary.winlose) < 0 ? $t("ui.lost") : $t("ui.won") }})
          td.text-right(valign='top' width='12%')
            span(
              :class="{ red: parseFloat(gameResultSummary.winlose) < 0 }"
              ) {{ $numeral(gameResultSummary.winlose).format("0,0.00") }}
          td.text-right(valign='top' width='12%')  
        tr
          td.text-right(valign='top') {{ $t("ui.subtotal") }} ({{ $t("ui.commission") }})
          td.text-right(valign='top') {{ $numeral(gameResultSummary.comm).format("0,0.00") }}
          td  
        tr
          td.text-right(valign='top') {{ $t("ui.total") }}
          td.text-right(valign='top')
            span(
              :class="{ red: total < 0 }"
              ) {{ $numeral(total).format("0,0.00") }}
          td
    .mt-2
      v-pagination(
        :value="currentPage"
        :page-count="gameResultTotalPages"
        :classes="bootstrapPaginationClasses"
        :labels="paginationAnchorTexts"
        @input="changedPage($event)"
        v-if="gameResultTotalPages"
      )
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import vPagination from "vue-plain-pagination";

export default {
  components: { vPagination },
  props: {
    currentGameResultPage: {
      type: Number,
    },
    gameResultList: {
      type: Array,
      default: [],
    },
    gameResultSummary: {
      type: Object,
      default: {},
    },
    currSportType: {
      type: String,
      default: "",
    },
    gameResultTotalPages: {
      type: Number,
    },
  },
  data() {
    return {
      currentPage: 1,
      bootstrapPaginationClasses: {
        ul: "pagination justify-content-center",
        li: "page-item",
        liActive: "active",
        liDisable: "disabled",
        button: "page-link",
        buttonActive: "active",
        buttonDisable: "disable",
      },
      paginationAnchorTexts: {
        first: "<i class='fas fa-angle-double-left'></i>",
        prev: "<i class='fas fa-angle-left'></i>",
        next: "<i class='fas fa-angle-right'></i>",
        last: "<i class='fas fa-angle-double-right'></i>",
      },
      loading: {
        pragmatic: false,
        ai: false,
        awc: false
      },
    };
  },
  computed: {
    isTotal() {
      if (this.gameResultSummary != null) {
        return Object.keys(this.gameResultSummary).length > 0;
      } else {
        return false;
      }
    },
    total() {
      if (this.gameResultSummary != null) {
        return parseFloat(this.gameResultSummary.winlose) + parseFloat(this.gameResultSummary.comm);
      } else {
        return 0;
      }
    },
  },
  mounted() {
    this.currentPage = this.currentGameResultPage;
    this.changedPage(1, "game_result");
  },
  methods: {
    getResultPragmaticCasino(e) {
      if (this.loading.pragmatic) return;
      var account_id = this.$store.getters.accountId;
      var session_token = this.$store.getters.sessionToken;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        userid: account_id,
        language: "en",
      };
      this.loading.pragmatic = true;
      this.$http.post(config.pragmaticCasinoPath, args).then(
        (res) => {
          this.loading.pragmatic = false;
          if (res.data) {
            url = res.data.history_url;
            if (url) {
              window.open(url, "resultppgc", "top=10,height=400,width=600,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.pragmatic = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultAICasino(e) {
      if (this.loading.ai) return;
      var account_id = this.$store.getters.accountId;
      var session_token = this.$store.getters.sessionToken;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        userid: account_id,
      };
      this.loading.ai = true;
      this.$http.post(config.aiPath, args).then(
        (res) => {
          this.loading.ai = false;
          if (res.data) {
            url = res.data.detail_url;
            if (url) {
              window.open(url, "resultai", "top=10,height=800,width=600,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.ai = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultSexyBacarrat(e) {
      console.log('Sexy')
      if (this.loading.awc) return;
      var account_id = this.$store.getters.accountId;
      var session_token = this.$store.getters.sessionToken;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        userid: account_id,
      };
      this.loading.awc = true;
      this.$http.post(config.awcPath, args).then(
        (res) => {
          this.loading.awc = false;
          if (res.data) {
            url = res.data.detail_url;
            if (url) {
              window.open(url, "resultawc", "top=10,height=648,width=1024,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.awc = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    resultUrl(e) {
      var account_id = this.$store.getters.accountId;
      var session_token = this.$store.getters.sessionToken;
      var result = config.r1ResultUrl() + "result_id=" + e + "&login_id=" + account_id + "&auth_token=" + session_token;

      return encodeURI(result);
    },
    changedPage: function (pageNo) {
      this.currentPage = pageNo;
      this.$emit("changedPage", pageNo, "game_result");
    },
  },
};
</script>
