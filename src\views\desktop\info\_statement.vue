<template lang="pug">
.info-wrapper
  .info-title
    .info-icon
      i.fad.fa-file-alt
    .page-title(aria-label='breadcrumb')
      ol.breadcrumb.p-0.m-0
        li.breadcrumb-item(aria-current='page' @click="showSummaryStatement" :class="{clickable: currLvl != 1, active: currLvl == 1 }") {{ $t("ui.statement") }}
        li.breadcrumb-item(aria-current='page' :class="{ active: currLvl == 2 }" v-if="currLvl == 2 || currLvl == 3")
          a.clickable(@click="showBettingStatement") {{ $t("ui.betting_statement") }}
        li.breadcrumb-item(aria-current='page' :class="{ active: currLvl == 2.5 }" v-if="currLvl == 2.5")
          a.clickable(@click="showSettlement") {{ $t("ui.settled_amount") }}
        li.breadcrumb-item(aria-current='page' :class="{ active: currLvl == 3 }" v-if="currLvl == 3")
          a.clickable {{ $t("ui." + currSportType) }}
    button.btn.btn-sm.btn-danger.mr-2(type="button" v-if="filterMatchId" @click="clearFilter")
      i.fal.fa-times.w-16px
    SpinButton(text="" :loading="loading" css="btn-sm btn-info" @click="refreshList" img="fad fa-sync-alt w-16px")
  .info-tablewrap.magicZ
    .tab-content
      dailyStatement(
        @getPlatformWinlose="getPlatformWinlose"
        @getSettlement="getSettlement"
        v-if="currLvl == 1"
        :dailyStatementList="dailyStatementList")
      platformWinlose(
        @getGameResult="setGameResult"
        v-if="currLvl == 2"
        :platformWinloseList="platformWinloseList"
        :currDate="currDate")
      settlementHistory(
        v-if="currLvl == 2.5"
        :settlementList="settlementList"
        :currDate="currDate")
      liveCasinoGameResult(
        @changedPage="changedPage"
        v-if="currLvl == 3 && currSportType == 'livecasino'"
        :gameResultList="gameResultList"
        :gameResultSummary="gameResultSummary"
        :currSportType="currSportType"
        :gameResultTotalPages="gameResultTotalPages"
        :currentGameResultPage="currentGameResultPage")
      slotsGameResult(
        @changedPage="changedPage"
        v-if="currLvl == 3 && currSportType == 'slots'"
        :gameResultList="gameResultList"
        :gameResultSummary="gameResultSummary"
        :currSportType="currSportType"
        :gameResultTotalPages="gameResultTotalPages"
        :currentGameResultPage="currentGameResultPage")
      tournamentGameResult(
        @changedPage="changedPage"
        @filterMatch="filterGameResultByMatch"
        @getRefundList="getRefundList"
        v-if="currLvl == 3 && currSportType == 'tournament'"
        :ploading="loading"
        :gameResultList="gameResultList"
        :gameResultSummary="gameResultSummary"
        :gameSummary="gameSummary"
        :gameResultTotalPages="gameResultTotalPages"
        :currentGameResultPage="currentGameResultPage"
        :refundList="refundList"
        :refundListSummary="refundListSummary"
        :refundListTotalPage="refundListTotalPage"
        :currentRefundListPage="currentRefundListPage"
        )
      sportsbookGameResult(
        @changedPage="changedPage"
        @filterMatch="filterGameResultByMatch"
        @getSummary="getGameResultSummary"
        @getCancelledBet="getCancelledBetList"
        v-if="currLvl == 3 && currSportType == 'sportsbook'"
        :ploading="loading"
        :gameResultList="gameResultList"
        :gameResultSummary="gameResultSummary"
        :gameSummary="gameSummary"
        :gameResultTotalPages="gameResultTotalPages"
        :currentGameResultPage="currentGameResultPage"
        :cancelledBetList="cancelledBetList"
        :cancelledBetTotalPage="cancelledBetTotalPage")
      adjustmentGameResult(
        @changedPage="changedPage"
        @filterMatch="filterGameResultByMatch"
        @getSummary="getGameResultSummary"
        @getCancelledBet="getCancelledBetList"
        v-if="currLvl == 3 && currSportType == 'adjustment'"
        :ploading="loading"
        :gameResultList="gameResultList"
        :gameResultSummary="gameResultSummary"
        :gameSummary="gameSummary"
        :gameResultTotalPages="gameResultTotalPages"
        :currentGameResultPage="currentGameResultPage"
        :cancelledBetList="cancelledBetList"
        :cancelledBetTotalPage="cancelledBetTotalPage")
      lotteryGameResult(
        @changedPage="changedPage"
        @getCancelledBet="getCancelledBetList"
        v-if="currLvl == 3 && currSportType == 'lottery'"
        :currSportType="currSportType"
        :ploading="loading"
        :gameResultList="gameResultList"
        :gameResultSummary="gameResultSummary"
        :gameResultTotalPages="gameResultTotalPages"
        :currentGameResultPage="currentGameResultPage"
        :cancelledBetList="cancelledBetList"
        :cancelledBetTotalPage="cancelledBetTotalPage")
      esports2GameResult(
        @changedPage="changedPage"
        @filterMatch="filterGameResultByMatch"
        @getSummary="getGameResultSummary"
        @getCancelledBet="getCancelledBetList"
        v-if="currLvl == 3 && currSportType == 'esports2'"
        :ploading="loading"
        :gameResultList="gameResultList"
        :gameResultSummary="gameResultSummary"
        :gameSummary="gameSummary"
        :gameResultTotalPages="gameResultTotalPages"
        :currentGameResultPage="currentGameResultPage"
        :cancelledBetList="cancelledBetList"
        :cancelledBetTotalPage="cancelledBetTotalPage")
  .notes
    p.mb-0 {{ $t("ui.note") }}:
    ul
      li {{ $t("message.view_outstanding_bets_in_bet_list") }}
      li {{ $t("message.time_display_in_gmt_plus_8") }}
</template>
<script>
import config from "@/config";
import errors from "@/errors";
import service from "@/library/_xhr-statement";
import SpinButton from "@/components/ui/SpinButton";

export default {
  components: {
    dailyStatement: () => import("@/components/desktop/info/statement/dailyStatement.vue"),
    settlementHistory: () => import("@/components/desktop/info/statement/settlementHistory.vue"),
    platformWinlose: () => import("@/components/desktop/info/statement/platformWinlose.vue"),
    liveCasinoGameResult: () => import("@/components/desktop/info/statement/liveCasinoGameResult.vue"),
    slotsGameResult: () => import("@/components/desktop/info/statement/slotsGameResult.vue"),
    sportsbookGameResult: () => import("@/components/desktop/info/statement/sportsbookGameResult.vue"),
    adjustmentGameResult: () => import("@/components/desktop/info/statement/adjustmentGameResult.vue"),
    tournamentGameResult: () => import("@/components/desktop/info/statement/tournamentGameResult.vue"),
    lotteryGameResult: () => import("@/components/desktop/info/statement/lotteryGameResult.vue"),
    esports2GameResult: () => import("@/components/desktop/info/statement/esports2GameResult.vue"),
    SpinButton,
  },
  data() {
    return {
      filterMatchId: null,
      filterBetType: "",
      filterSportType: "",
      loading: false,
      dailyStatementList: [],
      platformWinloseList: [],
      settlementList: [],
      currDate: null,
      currSportType: "slots",
      gameResultList: [],
      gameResultSummary: {},
      gameSummary: [],
      cancelledBetList: [],
      refundList: [],
      refundListSummary: {},
      currLvl: 1,
      currentGameResultPage: 1,
      gameResultTotalRow: 0,
      gameResultTotalPages: 0,
      currentCancelledBetPage: 1,
      cancelledBetTotalRow: 0,
      cancelledBetTotalPage: 0,
      currentRefundListPage: 1,
      refundListTotalRow: 0,
      refundListTotalPage: 0,
      customList: [],
      feedback: {
        success: false,
        status: errors.session.invalidSession,
      },
    };
  },
  computed: {
    getPageSize() {
      return this.$store.getters.pageSize;
      // return 2;
    },
    language() {
      return this.$store.getters.language;
    },
  },
  watch: {
    currLvl(val) {
      if (val != 3) {
        this.filterMatchId = null;
        this.filterBetType = "";
        this.filterSportType = "";
      }
    },
  },
  mounted() {
    this.getDailyStatement();
  },
  methods: {
    getDailyStatement() {
      this.currLvl = 1;

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
      };

      this.loading = true;
      this.dailyStatementList = [];

      service.getDailyStatement(json).then(
        (result) => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.dailyStatementList = result.data;
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getPlatformWinlose(date) {
      this.currLvl = 2;
      this.currentGameResultPage = 1;

      this.currDate = date;

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        start_date: date,
        end_date: date,
      };

      this.loading = true;
      this.platformWinloseList = [];

      service.getPlatformWinlose(json).then(
        (result) => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              //
              this.platformWinloseList = result.data;
              if (this.platformWinloseList == null) {
                this.platformWinloseList = [];
              }
              if (this.platformWinloseList.length <= 0) {
                this.platformWinloseList.push({
                  product: "LOTTERY",
                });
                this.platformWinloseList.push({
                  product: "SPORTSBOOK",
                });
              } else {
                var gotSB = false;
                var gotLT = false;
                for (var i = 0; i < this.platformWinloseList.length; i++) {
                  if (this.platformWinloseList[i].product.toUpperCase() == "SPORTSBOOK") {
                    gotSB = true;
                  }
                  if (this.platformWinloseList[i].product.toUpperCase() == "LOTTERY") {
                    gotLT = true;
                  }
                }
                if (gotSB == false) {
                  this.platformWinloseList.push({
                    product: "SPORTSBOOK",
                  });
                }
              }
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getSettlement(date) {
      this.currLvl = 2.5;
      this.currDate = date;

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        working_date: date,
      };

      this.loading = true;
      this.settlementList = [];

      service.getSettlement(json).then(
        (result) => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.settlementList = result.data;
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    setGameResult(product) {
      this.currLvl = 3;
      this.currSportType = product.toLowerCase();
    },
    getGameResult(product) {
      this.currLvl = 3;
      this.currSportType = product.toLowerCase();

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        start_date: this.currDate,
        end_date: this.currDate,
        category_type: this.currSportType,
        page_number: this.currentGameResultPage,
        page_size: this.getPageSize,
        match_id: this.filterMatchId,
        bet_type: this.filterBetType,
        sport_type: this.filterSportType,
      };

      this.loading = true;
      this.gameResultList = [];
      this.gameResultSummary = {};
      this.gameResultTotalRow = 0;
      this.gameResultTotalPages = 0;

      service.getGameResult(json).then(
        (result) => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              var g = result.data["Table"];

              this.gameResultList = g;
              // only sportsbook game result will have names
              //
              if (product.includes("sportsbook", "adjustment") && result.names) {
                var mm = g.reduce((groups, item) => {
                  const val = item["match_id"];
                  groups[val] = groups[val] || [];
                  groups[val].push(item);
                  return groups;
                }, {});

                var ll = g.reduce((groups, item) => {
                  const val = item["league_id"];
                  groups[val] = groups[val] || [];
                  groups[val].push(item);
                  return groups;
                }, {});

                var bb = g.reduce((groups, item) => {
                  const val = item["bet_type"];
                  groups[val] = groups[val] || [];
                  groups[val].push(item);
                  return groups;
                }, {});

                var m = result.names["Table"];
                var l = result.names["Table1"];

                if (m) {
                  for (var i = 0; i < m.length; i++) {
                    var hn = m[i]["home_name_" + this.language];
                    var an = m[i]["away_name_" + this.language];
                    var mid = m[i]["match_id"];
                    if (hn || an) {
                      for (var j = 0; j < mm[mid].length; j++) {
                        if (hn) mm[mid][j]["home_team_name"] = hn;
                        if (an) mm[mid][j]["away_team_name"] = an;
                      }
                    }
                  }
                }

                if (l) {
                  for (var i = 0; i < l.length; i++) {
                    var ln = l[i]["name_" + this.language];
                    var lid = l[i]["league_id"];

                    if (ln) {
                      for (var j = 0; j < ll[lid].length; j++) {
                        ll[lid][j]["league_name"] = ln;
                      }
                    }
                  }
                }

                if (bb) {
                  if (bb["CSHTFT"]) {
                    for (var k = 0; k < bb["CSHTFT"].length; k++) {
                      if (bb["CSHTFT"][k]["criteria2"]) {
                        bb["CSHTFT"][k]["special"] = JSON.parse(bb["CSHTFT"][k]["criteria2"]);
                        bb["CSHTFT"][k]["criteria2"] = null;
                        bb["CSHTFT"][k]["odds_col_criteria"] = bb["CSHTFT"][k]["special"]["odds_col_criteria"];
                      }
                    }
                  }
                  if (bb["ETGHTFT"]) {
                    for (var k = 0; k < bb["ETGHTFT"].length; k++) {
                      if (bb["ETGHTFT"][k]["criteria2"]) {
                        bb["ETGHTFT"][k]["special"] = JSON.parse(bb["ETGHTFT"][k]["criteria2"]);
                        bb["ETGHTFT"][k]["criteria2"] = null;
                        bb["ETGHTFT"][k]["odds_col_criteria"] = bb["ETGHTFT"][k]["special"]["odds_col_criteria"];
                      }
                    }
                  }
                }
              }
              this.gameResultTotalRow = this.gameResultList.length > 0 ? this.gameResultList[0].totalrows : 0;
              this.gameResultSummary = result.data["Table1"][0];
              if (this.gameResultTotalRow % this.getPageSize != 0) {
                this.gameResultTotalPages = Math.ceil(parseFloat(this.gameResultTotalRow) / parseFloat(this.getPageSize));
              } else {
                this.gameResultTotalPages = parseFloat(this.gameResultTotalRow) / parseFloat(this.getPageSize);
              }
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getGameResultSummary() {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        start_date: this.currDate,
        end_date: this.currDate,
      };

      this.loading = true;
      this.gameSummary = [];

      service.getGameResultSummary(json).then(
        (result) => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              var g = result.data["Table"];
              this.gameSummary = g;
              if (result.names) {
                var mm = g.reduce((groups, item) => {
                  const val = item["match_id"];
                  groups[val] = groups[val] || [];
                  groups[val].push(item);
                  return groups;
                }, {});

                var m = result.names["Table"];
                if (m) {
                  for (var i = 0; i < m.length; i++) {
                    var hn = m[i]["home_name_" + this.language];
                    var an = m[i]["away_name_" + this.language];
                    var mid = m[i]["match_id"];

                    if (hn || an) {
                      for (var j = 0; j < mm[mid].length; j++) {
                        if (hn) mm[mid][j]["home_team_name"] = hn;
                        if (an) mm[mid][j]["away_team_name"] = an;
                      }
                    }
                  }
                }
              }
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getCancelledBetList(mode) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        working_date: this.currDate,
        page_number: this.currentCancelledBetPage,
        page_size: this.getPageSize,
        mode: mode,
      };

      this.loading = true;
      this.cancelledBetList = [];
      this.cancelledBetTotalRow = 0;
      this.cancelledBetTotalPage = 0;

      service.getCancelledBetList(json).then(
        (result) => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              var g = result.data;
              this.cancelledBetList = g;
              if (result.names) {
                var mm = g.reduce((groups, item) => {
                  const val = item["match_id"];
                  groups[val] = groups[val] || [];
                  groups[val].push(item);
                  return groups;
                }, {});

                var ll = g.reduce((groups, item) => {
                  const val = item["league_id"];
                  groups[val] = groups[val] || [];
                  groups[val].push(item);
                  return groups;
                }, {});

                var m = result.names["Table"];
                var l = result.names["Table1"];
                if (m) {
                  for (var i = 0; i < m.length; i++) {
                    var hn = m[i]["home_name_" + this.language];
                    var an = m[i]["away_name_" + this.language];
                    var mid = m[i]["match_id"];

                    if (hn || an) {
                      for (var j = 0; j < mm[mid].length; j++) {
                        if (hn) mm[mid][j]["home_team_name"] = hn;
                        if (an) mm[mid][j]["away_team_name"] = an;
                      }
                    }
                  }
                }

                if (l) {
                  for (var i = 0; i < l.length; i++) {
                    var ln = l[i]["name_" + this.language];
                    var lid = l[i]["league_id"];

                    if (ln) {
                      for (var j = 0; j < ll[lid].length; j++) {
                        ll[lid][j]["league_name"] = ln;
                      }
                    }
                  }
                }
              }

              // this.cancelledBetList = result.data;
              this.cancelledBetTotalRow = this.cancelledBetList.length > 0 ? this.cancelledBetList[0].totalrows : 0;
              if (this.cancelledBetTotalRow % this.getPageSize != 0) this.cancelledBetTotalPage = Math.ceil(parseFloat(this.cancelledBetTotalRow) / parseFloat(this.getPageSize));
              else this.cancelledBetTotalPage = parseFloat(this.cancelledBetTotalRow) / parseFloat(this.getPageSize);
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    showSummaryStatement() {
      if (this.currLvl != 1) this.getDailyStatement();
    },
    showBettingStatement() {
      this.getPlatformWinlose(this.currDate);
    },
    showSettlement() {
      this.getSettlement(this.currDate);
    },
    refreshList() {
      this.currLvl = 1;
      this.getDailyStatement();
    },
    changedPage(pageNo, type) {
      if (type == "game_result") {
        this.currentGameResultPage = pageNo;
        this.getGameResult(this.currSportType);
      } else {
        this.currentCancelledBetPage = pageNo;
        this.getCancelledBetList(this.currSportType);
      }
    },
    filterGameResultByMatch(e) {
      this.filterMatchId = e.id;
      this.filterBetType = e.bet_type;
      this.filterSportType = e.sports_category;
      this.currentGameResultPage = 1;
      this.getGameResult(this.currSportType);
    },
    clearFilter() {
      this.filterMatchId = null;
      this.filterBetType = "";
      this.filterSportType = "";
      this.getGameResult(this.currSportType);
    },
    getRefundList() {
      this.currLvl = 3;
      this.currSportType = "tournament";

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        start_date: this.currDate,
        end_date: this.currDate,
        category_type: this.currSportType,
        page_number: this.currentGameResultPage,
        page_size: this.getPageSize,
        match_id: this.filterMatchId,
        bet_type: this.filterBetType,
        sport_type: this.filterSportType,
      };

      this.loading = true;
      this.refundList = [];
      this.refundListSummary = {};
      this.refundListTotalRow = 0;
      this.refundListTotalPage = 0;

      service.getRefundList(json).then(
        (result) => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              var g = result.data["Table"];
              this.refundList = g;
              this.refundListTotalRow = this.refundList.length > 0 ? this.refundList[0].totalrows : 0;
              if (this.refundListTotalRow % this.getPageSize != 0) this.refundListTotalPage = Math.ceil(parseFloat(this.refundListTotalRow) / parseFloat(this.getPageSize));
              else this.refundListTotalPage = parseFloat(this.refundListTotalRow) / parseFloat(this.getPageSize);
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
  },
};
</script>
