<template lang="pug">
  table.table-info(width='100%')
    tbody
      tr
        th(scope='col', width='10%') {{ $t("ui.transaction_date") }}
        th(scope='col', width='12%') {{ $t("ui.transfer_method") }}
        th(scope='col', width='30%') {{ $t("ui.remark") }}
        th.text-right(scope='col', width='15%') {{ $t("ui.amount") }}
        th.text-right(scope='col', width='15%') {{ $t("ui.amount_before_balance") }}
        th.text-right(scope='col', width='15%') {{ $t("ui.amount_after_balance") }}
      tr.grey(v-if="settlementList.length == 0")
        td(colspan="6").text-center
          span {{ $t('message.no_information_available') }}
      tr(v-for="(item, index) in settlementList"
        :class="{ grey: index % 2 === 0}")
        td
          div {{ $t("ui." + $dayjs(item.working_date).format("dddd").toLowerCase())  }}
          div {{ $dayjs(item.working_date).format("MM/DD/YYYY") }}
        td
          div {{ item.transfer_method }}
        td
          div {{ item.remark }}
        td.text-right
          span(:class="{ red: parseFloat(item.settled_amount) < 0 }") {{ $numeral(item.settled_amount).format("0,0.00") }}
        td.text-right
          span(:class="{ red: parseFloat(item.account_before_balance) < 0 }") {{ $numeral(item.account_before_balance).format("0,0.00") }}
        td.text-right
          span(:class="{ red: parseFloat(item.account_after_balance) < 0 }") {{ $numeral(item.account_after_balance).format("0,0.00") }}

</template>
<script>
export default {
  props: {
    settlementList: {
      type: Array,
      default: []
    }
  },
  methods: {
    // onClick: function (date) {
    //   // console.log(date);
    //   this.$emit("getPlatformWinlose", date);
    // },
    // onClickSettlement: function() {
    //   this.$emit("getSettlement", date);
    // }
  }
}
</script>