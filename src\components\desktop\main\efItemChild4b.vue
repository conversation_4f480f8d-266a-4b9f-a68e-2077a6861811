<template lang="pug">
.game-row(v-if="isHome")
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
    template(v-if="details['cs'] != null && details['cs'][5] != null && details['cs'][5][5] != 0")
      oddsItem(:odds="details['cs'][5]" idx=5 :typ="oddsType" dataType="3")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
    template(v-if="details['cs'] != null && details['cs'][4] != null && details['cs'][4][5] != 0")
      oddsItem(:odds="details['cs'][4]" idx=5 :typ="oddsType" dataType="3")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
    template(v-if="details['cs'] != null && details['cs'][3] != null && details['cs'][3][5] != 0")
      oddsItem(:odds="details['cs'][3]" idx=5 :typ="oddsType" dataType="3")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
    template(v-if="details['oxt'] != null && details['oxt'][0] != null && details['oxt'][0][5] != 0")
      oddsItem(:odds="details['oxt'][0]" idx=5 :typ="oddsType" dataType="2")
    template(v-else)
      .fad.fa-lock-alt.text-muted
.game-row(v-else)
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
    template(v-if="details['cs'] != null && details['cs'][0] != null && details['cs'][0][5] != 0")
      oddsItem(:odds="details['cs'][0]" idx=5 :typ="oddsType" dataType="3")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
    template(v-if="details['cs'] != null && details['cs'][1] != null && details['cs'][1][5] != 0")
      oddsItem(:odds="details['cs'][1]" idx=5 :typ="oddsType" dataType="3")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
    template(v-if="details['cs'] != null && details['cs'][2] != null && details['cs'][2][5] != 0")
      oddsItem(:odds="details['cs'][2]" idx=5 :typ="oddsType" dataType="3")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
      template(v-if="details['oxt'] != null && details['oxt'][0] != null && details['oxt'][0][7] != 0")
        oddsItem(:odds="details['oxt'][0]" idx=7 :typ="oddsType" dataType="2")
      template(v-else)
        .fad.fa-lock-alt.text-muted
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

export default {
  components: {
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem"),
  },
  mixins: [mixinHDPOUOdds],
  props: {
    source: {
      type: Object,
    },
    isHome: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
  },
  methods: {
  },
};
</script>
