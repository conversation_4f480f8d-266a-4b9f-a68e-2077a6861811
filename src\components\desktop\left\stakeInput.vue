<template lang="pug">
input#stake-input.form-control.form-control-sm(
  type="text"
  :value="checkLoading"
  @input="handleInput"
  @keypress="validateStake($event)"
  @blur="handleStake"
  autofocus
  ref="stake"
  autocomplete="off"
  style="font-weight: 900;text-align: right;"
  )
</template>

<script>
import StakeCheck from "@/library/_stakeinput.js";
import { EventBus } from "@/library/_event-bus.js";
export default {
  props: {
    value: {
      type: [Number, String],
    },
    loadbet: {
      type: Boolean,
    },
  },
  data() {
    return {
      autoSelect: true,
    };
  },
  computed: {
    checkLoading() {
      if (!this.loadbet) {
        if (this.autoSelect) {
          this.xSelect();
        }
        return this.value;
      }
      return '';
    },
  },
  destroyed() {
    EventBus.$off("STAKE_FOCUS", this.xFocus);
  },
  mounted() {
    this.xFocus();
    this.xSelect();
    EventBus.$on("STAKE_FOCUS", this.xFocus);
  },
  methods: {
    xFocusTouchEnd() {
      var si3 = $("#stake-input");
      if (si3) {
        si3.focus();
      }
      document.body.removeEventListener("touchend", this.xFocusTouchEnd);
    },
    xFocus() {
      var si = $("#stake-input");
      if (si) {
        si.focus();
        document.body.addEventListener("touchend", this.xFocusTouchEnd);
        setTimeout(() => {
          var si2 = $("#stake-input");
          if (si2) {
            si2.focus();
          }
        }, 1000);
      }
    },
    getCaret(element) {
      if (element.selectionStart) return element.selectionStart;
      else if (document.selection) {
        //IE specific
        element.focus();
        element.select();

        var r = document.selection.createRange();
        if (r == null) return 0;

        var re = element.createTextRange(),
            rc = re.duplicate();
        re.moveToBookmark(r.getBookmark());
        rc.setEndPoint("EndToStart", re);
        return rc.text.length;
      }

      return 0;
    },
    validateStake(evt) {
      StakeCheck.validate(evt);
    },
    handleInput(e) {
      var chk = StakeCheck.validateWord(e);
      if (!chk) {
        if ($("#stake-input") != undefined) {
          $("#stake-input").value = this.value;
        }
      }
      this.autoSelect = false;
      this.$emit("input", e.target.value);
    },
    handleStake() {
      this.autoSelect = true;
      this.$emit("handleStake");
    },
    xSelect() {
      setTimeout(() => {
        if ($("#stake-input") != undefined) {
          $("#stake-input").select();
        }
      }, 100);
    },
  },
};
</script>
