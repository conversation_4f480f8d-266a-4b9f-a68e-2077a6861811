<template lang="pug">
.col-6.hx-more-row
  .card
    .card-header(
      :id="'heading-eatg-' + uid"
      data-toggle="collapse"
      :data-target="'#collapse-eatg-' + uid"
      :aria-controls="'collapse-eatg-' + uid"
      aria-expanded=true
      :class="layoutIndex == 3 ? 'live': 'non-live'"
    )
      i.fad.fa-chevron-circle-down
      span.header-bettype {{ $t("m.BT_EATG") }}
    .collapse.show(
      :aria-labelledby="'heading-eatg-' + uid"
      :id="'collapse-eatg-' + uid"
    )
      .card-body.p-0(:id="'accordian-eatg-' + uid")
        .hx-table.hx-match.hx-more-bet(:class="{ 'live': marketType == 3 }")
          .d-flex.flex-column.w-100
            .hx-cell.w-100.h-18
              .hx-row.hx-more-col.hx-more-col5.header.bl-1.br-1.bb-1
                .hx-col(v-for="item in col")
                  .hx {{ $t("m.BT_" + item) }}
            .d-flex.flex-row
              .hx-cell.w-100
                .hx-row.hx-more-col.hx-more-col5.body.bl-1.br-1.bb-1
                  .hx-col(v-for="item in col")
                    .hx
                      .hxs(v-if="details != null && details[item] != null && details[item][5] != null && details[item][5] != ''").hx-flex-c
                        oddsItem(:odds="details[item]" idx=5 :typ="oddsType" dataType="3" cls="more-value hx-w80")
</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";
import config from "@/config";

export default {
  components: {
    oddsItem,
  },
  props: {
    uid: {
      type: String,
    },
    details: {
      type: Object,
    },
    matchId: {
      type: Number,
    },
    leagueId: {
      type: Number,
    },
    marketType: {
      type: Number,
    },
    sportsType: {
      type: Number,
    },
    betType: {
      type: String,
    },
    layoutIndex: {
      type: Number,
    },
    firstHalf: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      col: ["0", "1", "2", "3Up"],
    };
  },
  computed: {
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
  },
  methods: {},
};
</script>
