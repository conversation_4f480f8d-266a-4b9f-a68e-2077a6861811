<template lang="pug">
.col-3.hx-more-row(v-if="details['ou'] != null || details['oe'] != null")
  .card
    .card-header(
      :class="layoutIndex == 3 ? 'live': 'non-live'"
    )
      span.header-bettype(style="padding: 0 4px;") {{ leagueName.trim() }}
    .card-body.p-0
      .hx-table.hx-morebet-header(:class="marketType == 3 ? 'live' : 'non-live'").bl-1.br-1.bb-1.h-18
        .hx-cell.w-100.bl-1
          .hx-row.w-100
            .hx-col.w-50
              .hx.w-100.text-center(title="Full Time Big/Small" v-if="details['ou'][0][8] == 4.5") {{ $t("ui.bs") }}
              .hx.w-100.text-center(title="Full Time Over/Under" v-else) {{ $t("ui.ou") }}
            .hx-col.w-50.bl-1
              .hx.w-100.text-center(title="Full Time Odd/Even") {{ $t("ui.oe") }}
      .hx-table.hx-match.hx-morebet-body(:class="{ 'live': marketType == 3 }").bl-1.br-1.bb-1
        .hx-cell.w-100.bl-1
          ouoeItem(:details="details" :oddsType="oddsType" :i="0")
</template>

<script>
import config from "@/config";
import ouoeItem from "@/components/desktop/main/xtable/xitem/ouoeItem";

export default {
  components: {
    ouoeItem
  },
  props: {
    childId: {
      type: Number
    },
    uid: {
      type: String
    },
    details: {
      type: Object
    },
    matchId: {
      type: Number
    },
    leagueId: {
      type: Number
    },
    marketType: {
      type: Number
    },
    sportsType: {
      type: Number
    },
    betType: {
      type: String
    },
    layoutIndex: {
      type: Number
    }
  },
  computed: {
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
    leagueName() {
      var result = this.details["league"].split(" - ");
      if (result.length >= 2) {
        return result[result.length - 1];
      }
      return this.details["league"];
    }
  }
};
</script>
