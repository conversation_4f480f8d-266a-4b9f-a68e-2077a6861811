<template lang="pug">
.game-row
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
    template(v-if="details['ou'] != null && details['ou'][0] != null && details['ou'][0][12] != 0")
      oddsItem(:odds="details['ou'][0]" idx=12 :typ="oddsType" dataType="1")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
    template(v-if="details['ou'] != null && details['ou'][0] != null && details['ou'][0][11] != 0")
      oddsItem(:odds="details['ou'][0]" idx=11 :typ="oddsType" dataType="1")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
    template(v-if="details['oe'] != null && details['oe'][0] != null && details['oe'][0][5] != 0")
      oddsItem(:odds="details['oe'][0]" idx=5 :typ="oddsType" dataType="2")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-90.bg-01.ef-bet.ef-gh
    template(v-if="details['oe'] != null && details['oe'][0] != null && details['oe'][0][7] != 0")
      oddsItem(:odds="details['oe'][0]" idx=7 :typ="oddsType" dataType="2")
    template(v-else)
      .fad.fa-lock-alt.text-muted
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

export default {
  components: {
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem"),
  },
  mixins: [mixinHDPOUOdds],
  props: {
    source: {
      type: Object,
    },
  },
  computed: {
  },
  methods: {
  },
};
</script>
