<template lang="pug">
.info-wrapper
  .info-title
    .info-icon
      i.fad.fa-poll-people
    .page-title(aria-label="breadcrumb")
      ol.breadcrumb.p-0.m-0
        li.breadcrumb-item.active(aria-current="page") {{ $t('ui.result') }}
  .info-tablewrap.magicZ
    ul.nav.nav-tabs(role="tablist")
      li.nav-item
        a#tab-result.nav-link.active(data-toggle="tab", href="#result", role="tab", aria-controls="result", aria-selected="true", @click="switchTab('default')") {{ $t('ui.result') }}
      li.nav-item
        a#tab-outright.nav-link(data-toggle="tab", href="#outright", role="tab", aria-controls="outright", aria-selected="false", @click="switchTab('OR')") {{ $t('ui.outright') }}
    .tab-content
      #result.tab-pane.show.active(role="tabpanel", aria-labelledby="tab-result")
        template(v-if="selected === 'default'")
          .result-selection.p-2
            .d-flex.flex-row.mb-1
              .mr-2
                .input-group
                  .input-group-prepend
                    span.input-group-text
                      i.fad.fa-calendar-alt
                  date-picker.datepicker(v-model="resultXDate", :config="locale", @input="refreshResult()")

              .mr-2
                button.btn.btn-secondary.btn-result(type="button", @click="setResultDate(12)") {{ $t('ui.today') }}
              .mr-2
                button.btn.btn-secondary.btn-result(type="button", @click="setResultDate(36)") {{ $t('ui.yesterday') }}
              .mr-2.dropdown
                button#dropdown-resultsport.btn.btn-secondary.dropdown-toggle.btn-result(type="button", data-display="static", data-toggle="dropdown", aria-haspopup="true", aria-expanded="false") {{ sports[resultSportFilterKey] }}
                .dropdown-menu.scroll-dropdown(aria-labelledby="dropdown-resultsport")
                  a.dropdown-item(href="javascript:void(0);", @click="resultDDLOnChange(key, 'sports', false)", v-for="(item, key, index) in sports", :class="{ active: resultSportFilterKey == key }") {{ item }}
              .mr-2.dropdown
                button#dropdown-leagues.btn.btn-secondary.dropdown-toggle.btn-result(type="button", data-display="static", data-toggle="dropdown", aria-haspopup="true", aria-expanded="false") {{ resultLeagueFilterKey == 0 ? $t('ui.all_league') : leagues[resultLeagueFilterKey] }}
                .dropdown-menu.scroll-dropdown(aria-labelledby="dropdown-leagues")
                  a.dropdown-item(href="javascript:void(0);", @click="resultDDLOnChange(0, 'leagues', false)", :class="{ active: resultLeagueFilterKey == 0 }") {{ $t('ui.all_league') }}
                  a.dropdown-item(
                    v-for="(item, key, index) in leagueEntries",
                    href="javascript:void(0);",
                    @click="resultDDLOnChange(parseInt(item[0]), 'leagues', true)",
                    :class="{ active: resultLeagueFilterKey == parseInt(item[0]) }"
                  ) {{ item[1] }}
              //- .mr-2.flex-fill
              //-   v-select(:options="leagueOptions" :clearable="false")
          table.table-betresult(width="100%")
            tbody
              tr
                th(scope="col", width="15%") {{ $t('ui.kickoff') }} {{ $t('ui.time') }}
                th(scope="col", width="45%") {{ $t('ui.match') }}
                th.text-center(scope="col", width="10%") {{ $t('ui.first_half') }}
                th.text-center(scope="col", width="10%") {{ $t('ui.final') }}
                //- th.text-center(scope='col' width='10%')  
                th(scope="col", width="10%") {{ $t('ui.status') }}
            tbody
              tr.odd(v-if="resultList.length == 0")
                td.text-center(colspan="6")
                  i.fad.fa-spin.fa-spinner(v-if="loading")
                  span(v-else) {{ $t('message.no_information_available') }}
            template(v-if="resultList.length > 0")
              tbody(v-for="(item, key, index) in groupResultList")
                tr.odd
                  td(colspan="6") {{ key }}
                tr(v-for="(info, indexChild) in item", :class="{ other: indexChild % 2 === 0, even: indexChild % 2 !== 0 }")
                  td(width="15%", style="word-wrap: break-word;") {{ $dayjs(info.match_time).format('MM/DD/YYYY hh:mm A') }}
                  td(width="45%")
                    span {{ getHomeTeam(info) }}&nbsp;
                    span.badge.badge-pill.badge-info.mr-1(v-if="info.FGH === 1") 1F
                    span.badge.badge-pill.badge-danger.mr-1(v-if="info.LGH === 1") 1L
                    span.badge.badge-pill.badge-info.mr-1(v-if="info.FG === 1") F
                    span.badge.badge-pill.badge-danger.mr-1(v-if="info.LG === 1") L
                    template(v-if="!racingList.includes(parseInt(resultSportFilterKey))")
                      span -vs-&nbsp;{{ getAwayTeam(info) }}&nbsp;
                      span.badge.badge-pill.badge-info.mr-1(v-if="info.FGH === 0") 1F
                      span.badge.badge-pill.badge-danger.mr-1(v-if="info.LGH === 0") 1L
                      span.badge.badge-pill.badge-info.mr-1(v-if="info.FG === 0") F
                      span.badge.badge-pill.badge-danger.mr-1(v-if="info.LG === 0") L
                  td.text-center(width="10%")
                    template(v-if="info.half_match_status == 0 || info.half_match_status == 1 || info.half_match_status == 5") {{ info.half_home }}-{{ info.half_away }}
                    template(v-else) {{ $t(match[info.half_match_status]) }}

                  td.text-center(width="10%")
                    template(v-if="info.match_status == 0 || info.match_status == 1 || info.match_status == 5")
                      template(v-if="!racingList.includes(parseInt(resultSportFilterKey))") {{ info.final_home }}-{{ info.final_away }}
                      template(v-else) {{ info.final_home }}
                    template(v-else) {{ $t(match[info.match_status]) }}

                  td(width="10%")
                    span {{ $t(getMatchStatus(info.half_match_status, info.match_status)) }}
      #outright.tab-pane(role="tabpanel", aria-labelledby="tab-outright")
        template(v-if="selected === 'OR'")
          .result-selection.p-2
            .d-flex.flex-row.mb-1
              .mr-2
                .input-group
                  .input-group-prepend
                    span.input-group-text
                      i.fad.fa-calendar-alt
                  date-picker.datepicker(v-model="resultFromDate", :config="locale", @input="refreshResult()")

              .mr-2
                .input-group
                  .input-group-prepend
                    span.input-group-text
                      i.fad.fa-calendar-alt
                  date-picker.datepicker(v-model="resultToDate", :config="locale", @input="refreshResult()")

              .dropdown.mr-2
                button#dropdown-resultsport.btn.btn-secondary.dropdown-toggle.btn-result(type="button", data-display="static", data-toggle="dropdown", aria-haspopup="true", aria-expanded="false") {{ sports[resultSportFilterKey] }}
                .dropdown-menu.scroll-dropdown(aria-labelledby="dropdown-resultsport")
                  a.dropdown-item(href="javascript:void(0);", @click="resultDDLOnChange(key, 'sports', true, 1000)", v-for="(item, key, index) in sports", :class="{ active: resultSportFilterKey == key }") {{ item }}
              .dropdown
                button#dropdown-leagues.btn.btn-secondary.dropdown-toggle.btn-result(type="button", data-display="static", data-toggle="dropdown", aria-haspopup="true", aria-expanded="false") {{ resultLeagueFilterKey == 0 ? $t('ui.all_league') : leagues[resultLeagueFilterKey] }}
                .dropdown-menu.scroll-dropdown(aria-labelledby="dropdown-leagues")
                  a.dropdown-item(href="javascript:void(0);", @click="resultDDLOnChange(0, 'leagues', true, 1000)", :class="{ active: resultLeagueFilterKey == 0 }") {{ $t('ui.all_league') }}
                  a.dropdown-item(
                    v-for="(item, key, index) in leagueEntries",
                    href="javascript:void(0);",
                    @click="resultDDLOnChange(parseInt(item[0]), 'leagues', true, 1000)",
                    :class="{ active: resultLeagueFilterKey == parseInt(item[0]) }"
                  ) {{ item[1] }}
          table.table-betresult(width="100%")
            tbody
              tr
                th(scope="col") {{ $t('ui.time') }}
                th(scope="col") {{ $t('ui.team') }}
                th.text-center(scope="col") {{ $t('ui.result') }}
                //- th.text-center(scope='col' width='10%') {{ $t("ui.status") }}
              tr.odd(v-if="resultList.length == 0")
                td.text-center(colspan="4")
                  i.fad.fa-spin.fa-spinner(v-if="loading")
                  span(v-else) {{ $t('message.no_information_available') }}
            template(v-if="resultList.length > 0")
              tbody(v-for="(item, key, index) in groupResultList")
                tr.odd
                  td(colspan="6") {{ key }}
                tr(v-for="(info, indexChild) in item", :class="{ other: indexChild % 2 === 0, even: indexChild % 2 !== 0 }")
                  td(width="15%") {{ $dayjs(info.match_time).format('MM/DD/YYYY hh:mm A') }}
                  td(width="45%") {{ getHomeTeam(info) }}
                  td.text-center(width="10%", :class="info.outright_teamwin ? 'font-weight-bold' : ''") {{ info.outright_teamwin ? $t('ui.win') : $t('ui.lost') }}
  .notes
    p.mb-0 {{ $t('ui.note') }}:
    ul
      li {{ $t('message.view_outstanding_bets_in_bet_list') }}
      li {{ $t('message.time_display_in_gmt_plus_8') }}
</template>
<script>
import config from "@/config";
import errors from "@/errors";
import service from "@/library/_xhr-result";
import fglg from "@/library/_fglg";
import naming from "@/library/_name";
import vPagination from "vue-plain-pagination";

export default {
  components: { vPagination },
  data() {
    return {
      loading: false,
      dt: null,
      dtt: null,
      dtx: null,
      selected: "default",
      leagues_distinct: [],
      // outrightDateFrom: new Date(),
      // outrightDateTo: new Date(),
      // lotteryDate: new Date(),
      locale: {
        format: "YYYY-MM-DD",
        useCurrent: true,
        minDate: this.$dayjs
          .utc()
          .tz("Asia/Kuala_Lumpur")
          .startOf("day")
          .subtract(14, "day")
          .format("YYYY-MM-DD"),
        maxDate: this.$dayjs.utc().tz("Asia/Kuala_Lumpur").format("YYYY-MM-DD"),
      },
      sortOption: [
        { text: "Bet Types", value: "betTypes" },
        { text: "All", value: "all" },
        { text: "Accepted", value: "accepted" },
        { text: "Pending", value: "pending" },
        { text: "Void", value: "void" },
      ],
      resultList: [],
      resultSportFilterKey: 1,
      resultLeagueFilterKey: 0,
      outrightList: [],
      outrightSportFilterKey: 1,
      outrightLeagueFilterKey: 0,
      lotteryList: [],
      lotteryFilterKey: {},
      feedback: {
        success: false,
        status: errors.session.invalidSession,
      },
      currentPage: 1,
      totalPages: 0,
      bootstrapPaginationClasses: {
        ul: "pagination justify-content-center",
        li: "page-item",
        liActive: "active",
        liDisable: "disabled",
        button: "page-link",
        buttonActive: "active",
        buttonDisable: "disable",
      },
      paginationAnchorTexts: {
        first: "<i class='fas fa-angle-double-left'></i>",
        prev: "<i class='fas fa-angle-left'></i>",
        next: "<i class='fas fa-angle-right'></i>",
        last: "<i class='fas fa-angle-double-right'></i>",
      },
      old: {
        account_id: null,
        session_token: null,
        working_date_from: null,
        working_date_to: null,
        sports_type: null,
        league_id: null,
        is_outright: null,
      },
    };
  },
  computed: {
    racingList() {
      return config.racingList;
    },
    groupResultList() {
      if (this.resultList.length > 0) {
        var tempList = this.resultList.reduce((r, v, i, a, k = v.league_name) => {
          (r[k] || (r[k] = [])).push(Object.assign(v, fglg.fglgGenerate(v.FLGH, v.FLG)));
          return r;
        }, {});
        return tempList;
      }

      return [];
    },
    resultXDate: {
      get() {
        if (this.dtx) return this.dtx;
        else
          return this.$dayjs
            .utc()
            .tz("Asia/Kuala_Lumpur")
            .subtract(12, "hour")
            .format("YYYY-MM-DD");
      },
      set(value) {
        this.dtx = value;
      },
    },
    resultFromDate: {
      get() {
        if (this.dt) return this.dt;
        else
          return this.$dayjs
            .utc()
            .tz("Asia/Kuala_Lumpur")
            .subtract(14, "day")
            .format("YYYY-MM-DD");
      },
      set(value) {
        this.dt = value;
      },
    },
    resultToDate: {
      get() {
        if (this.dtt) return this.dtt;
        else
          return this.$dayjs
            .utc()
            .tz("Asia/Kuala_Lumpur")
            .add("12", "hours")
            .format("YYYY-MM-DD");
      },
      set(value) {
        this.dtt = value;
      },
    },
    sports() {
      return this.$store.state.layout.sports;
    },
    leagues: {
      get() {
        return this.leagues_distinct;
      },
      set(value) {
        this.leagues_distinct = value;
      },
    },
    leagueEntries() {
      var result = Object.entries(this.leagues).sort((a, b) => {
        if (a[1] < b[1]) return -1;
        if (a[1] > b[1]) return 1;
        return 0;
      });
      return result;
    },
    leagueOptions() {
      var result = [];
      for (var i = 0; i < this.leagueEntries.length; i++) {
        result.push({
          label: this.leagueEntries[i][1],
          code: this.leagueEntries[i][0],
        });
      }
      return result;
    },
    match() {
      return config.matchStatusName;
    },
    language() {
      return this.$store.getters.language;
    },
  },
  watch: {
    resultSportFilterKey() {
      this.resultLeagueFilterKey = 0;
      this.getResult(this.selected === "OR");
    },
  },
  mounted() {
    this.getResult(false);
  },
  methods: {
    getHomeTeam(e) {
      var result = e["home_name_" + this.language];
      if (result) {
        return result;
      } else {
        return e["home_name_en"];
      }
    },
    getAwayTeam(e) {
      var result = e["away_name_" + this.language];
      if (result) {
        return result;
      } else {
        return e["away_name_en"];
      }
    },
    setResultDate(offset) {
      this.resultXDate = this.$dayjs
        .utc()
        .tz("Asia/Kuala_Lumpur")
        .subtract(offset, "hour")
        .format("YYYY-MM-DD");
    },
    switchTab(val) {
      this.selected = val;
      this.resultList = [];
      this.getResult(val === "OR");
    },
    resultDDLOnChange(id, type, isOR, delay) {
      if (type == "sports") this.resultSportFilterKey = id;
      else if (type == "leagues") this.resultLeagueFilterKey = id;

      if (delay) {
        setTimeout(() => {
          this.getResult(this.selected === "OR");
        }, delay);
      } else {
        this.getResult(this.selected === "OR");
      }
    },
    getResult(isOR) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        working_date_from:
          this.selected == "default"
            ? this.$dayjs(this.resultXDate).format("MM/DD/YYYY")
            : this.$dayjs(this.resultFromDate).format("MM/DD/YYYY"),
        working_date_to:
          this.selected == "default"
            ? this.$dayjs(this.resultXDate).format("MM/DD/YYYY")
            : this.$dayjs(this.resultToDate).format("MM/DD/YYYY"),
        sports_type: this.resultSportFilterKey,
        league_id: this.resultLeagueFilterKey,
        is_outright: isOR,
      };

      if (
        this.old.account_id === json.account_id &&
        this.old.session_token === json.session_token &&
        this.old.working_date_from === json.working_date_from &&
        this.old.working_date_to === json.working_date_to &&
        this.old.sports_type === json.sports_type &&
        this.old.league_id === json.league_id &&
        this.old.is_outright === json.is_outright
      ) {
        this.old.account_id = json.account_id;
        this.old.session_token = json.session_token;
        this.old.working_date_from = json.working_date_from;
        this.old.working_date_to = json.working_date_to;
        this.old.sports_type = json.sports_type;
        this.old.league_id = json.league_id;
        this.old.is_outright = json.is_outright;
        return;
      }

      this.loading = true;

      this.resultList = [];

      service.getResult(json).then(
        (result) => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.resultList = result.data;
              this.resultList.sort((a, b) =>
                a.league_name > b.league_name ? 1 : b.league_name > a.league_name ? -1 : 0
              );

              // get distinct league name to assign to dropdown..
              if (this.resultLeagueFilterKey == 0) {
                var unique = {};
                var distinct = {};
                result.data.forEach((x) => {
                  if (!unique[x.league_id]) {
                    distinct[x.league_id] = x.league_name;
                    unique[x.league_id] = true;
                  }
                });
                this.leagues_distinct = distinct;
              }
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    refreshResult: function () {
      this.resultLeagueFilterKey = 0;
      this.getResult(this.selected === "OR");
    },
    getMatchStatus(ht, ft) {
      return naming.matchStatus(ht, ft);
    },
  },
};
</script>
