import Vue from "vue";

export default {
  namespaced: true,
  state: {
    stake: 0,
    confirmBet: false
  },
  mutations: {
    updateData(state, payload) {
      for (var n in payload) {
        // if (state.hasOwnProperty(n)) {
          Vue.set(state, n, payload[n]);
        // } else {
        //   console.log(n);
        // }
      }
    },
    createData(state, payload) {
      for (var n in payload) {
        Vue.set(state, n, payload[n]);
      }
    },
    deleteData(state, payload) {
      for (var n in state) {
        Vue.delete(state, n);
      }
    },
    updateStake(state, payload) {
      Vue.set(state, 'stake', payload);
    },
    updateConfirm(state, payload) {
      Vue.set(state, 'confirmBet', payload);
    }
  },
  actions: {
    setData(context, payload) {
      context.commit("updateData", payload);
    },
    newData(context, payload) {
      context.commit("deleteData");
      context.commit("createData", payload);
    },
    clearData(context, payload) {
      context.commit("deleteData");
    },
    setStake(context, payload) {
      context.commit("updateStake", payload);
    },
    setConfirm(context, payload) {
      context.commit("updateConfirm", payload);
    }
  }
};
