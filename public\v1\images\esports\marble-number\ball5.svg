<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 50 50">
  <defs>
    <style>
      .cls-1 {
        fill: orange;
      }

      .cls-1, .cls-2, .cls-3 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: #e5e5e5;
      }

      .cls-3 {
        fill: #fff;
      }
    </style>
  </defs>
  <circle class="cls-2" cx="25" cy="25" r="25"/>
  <g>
    <circle class="cls-1" cx="25" cy="25" r="19.5"/>
    <path class="cls-3" d="M20.1,34.6c-1.4-.4-2.5-.8-3.4-1.4-.2,0-.3-.3-.3-.5s0-.3.2-.6l1.3-2.2c.1-.3.3-.4.5-.4s.4,0,.6.2c1.4.8,3.1,1.2,5.1,1.2s2.1-.3,2.7-.8c.7-.5,1-1.3,1-2.2s-.3-1.9-.8-2.3-1.3-.7-2.4-.7-1.2,0-1.8.2c-.6.1-1.1.2-1.5.4-.5.2-1,.3-1.5.3s-1.4-.2-1.9-.6-.7-1-.7-1.7l.5-7.4c0-.4.2-.8.4-1,.2-.2.6-.3,1.1-.3h12.1c.5,0,.9.1,1.1.3s.3.6.3,1.1v1.4c0,.5,0,.9-.3,1.1s-.6.3-1.1.3h-8.8v2.4c.2,0,.8-.2,1.4-.2s1.2-.1,1.7-.1c2.6,0,4.6.5,6,1.6s2.1,2.8,2.1,5.2-.4,2.8-1.2,3.8c-.8,1.1-1.9,1.9-3.3,2.5s-3,.9-4.8.9-2.9-.2-4.3-.5Z"/>
  </g>
</svg>