<template lang="pug">
  .main-item
    .single-match
      .header
        .action-block(@click="resetSingle()")
          i.far.fa-chevron-left
        .name-block
          span {{ sports }}
      .content.scoreboard(v-if="matchList[single.match_id].radarId")
        img.bg-block(:src="getBg(single.sports_type)", :class="'s' + single.sports_type")
        .info-block
          .league-block
            .name-part {{ matchList[single.match_id].leagueName }}
            .date-part {{ $dayjs(matchList[single.match_id].matchTime).format('dddd, Do MMMM YYYY, h:mm A') }}
          .event-block
            .home-block {{ matchList[single.match_id].homeTeam }}
            .match-block
              .running-time-block(v-if="matchList[single.match_id].runningTime" :class="getStyle(matchList[single.match_id].runningTime)") {{ getLabel(matchList[single.match_id].runningTime) }}
              .running-score-block {{ matchList[single.match_id].runningScore }}
            .away-block {{ matchList[single.match_id].awayTeam }}
        template(v-if="sportsradar")
          .scoreboard-frame(v-if="matchList[single.match_id].radarId")
            iframe(:src="getScoreboard(matchList[single.match_id].radarId)", frameborder="0", allowfullscreen="true", scrolling="no")
      .content(v-else)
        img.bg-block(:src="getBg(single.sports_type)", :class="'s' + single.sports_type")
        .info-block
          .league-block
            .name-part {{ matchList[single.match_id].leagueName }}
            .date-part {{ $dayjs(matchList[single.match_id].matchTime).format('dddd, Do MMMM YYYY, h:mm A') }}
          .event-block
            .home-block {{ matchList[single.match_id].homeTeam }}
            .match-block
              .running-time-block(v-if="matchList[single.match_id].runningTime" :class="getStyle(matchList[single.match_id].runningTime)") {{ getLabel(matchList[single.match_id].runningTime) }}
              .running-score-block {{ matchList[single.match_id].runningScore }}
            .away-block {{ matchList[single.match_id].awayTeam }}
</template>

<script>
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {},
  props: {
    single: {
      type: Object
    },
    sports: {
      type: String
    },
    matchList: {
      type: Object
    }
  },
  data() {
    return {};
  },
  computed: {
    newFeatures() {
      return config.newFeatures;
    },
    sportsradar() {
      return config.sportsradar;
    },
  },
  methods: {
    getStyle(e) {
      switch (e) {
      case "LIVE":
        return "danger";
        break;
      case "HT":
        return "info";
        break;
      case "DELAYED":
      case "PEN":
        return "warn";
        break;
      default:
        return "label";
        break;
      }
    },
    getLabel(e) {
      if (/\d/.test(e)) {
        return e.trim();
      } else {
        var m = e.trim();
        if (m) return this.$t("m.BT_" + e.trim());
        else return null;
      }
    },
    resetSingle() {
      EventBus.$emit("RESET_SINGLE");
    },
    getScoreboard(e) {
      return config.scoreboardUrl() + e + "&u=" + this.$store.getters.accountId + "&t=" + this.$store.getters.sessionToken;
    },
    getBg(e) {
      var path = config.resourceUrl + "/match/";
      var url = path + "0.jpg";
      switch (e) {
      case 1:
        url = path + "1.jpg";
        break;
      case 2:
        url = path + "2.jpg";
        break;
      case 3:
        url = path + "3.jpg";
        break;
      case 4:
        url = path + "4.jpg";
        break;
      case 7:
        url = path + "7.jpg";
        break;
      case 20:
        url = path + "20.jpg";
        break;
      case 41:
        url = path + "41.jpg";
        break;
      }
      return url;
    }
  }
};
</script>

<style></style>
