<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 19.2.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 50 50" style="enable-background:new 0 0 50 50;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{fill:#E8E8E8;}
	.st2{fill:url(#XMLID_2_);}
	.st3{fill:url(#XMLID_3_);}
	.st4{fill:url(#XMLID_4_);}
	.st5{fill:url(#XMLID_5_);}
	.st6{fill:url(#XMLID_6_);}
	.st7{fill:url(#XMLID_7_);}
	.st8{fill:url(#SVGID_2_);}
	.st9{fill:#557A84;}
	.st10{clip-path:url(#XMLID_9_);fill:url(#XMLID_10_);}
	.st11{fill:#DFF0F4;}
</style>
<g>
	<radialGradient id="SVGID_1_" cx="25.0723" cy="28.8221" r="25.1562" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#28B8CE"/>
		<stop  offset="0.5" style="stop-color:#28B8CE"/>
		<stop  offset="0.6714" style="stop-color:#1896A9"/>
		<stop  offset="0.8884" style="stop-color:#07707F"/>
		<stop  offset="1" style="stop-color:#00616F"/>
	</radialGradient>
	<path class="st0" d="M25.3,49.1c-13.1,0-23.7-10.6-23.7-23.7c0-13.1,10.6-23.7,23.7-23.7S49,12.4,49,25.4
		C49,38.5,38.3,49.1,25.3,49.1z"/>
	<path class="st1" d="M25.3,2.2c12.8,0,23.2,10.4,23.2,23.2S38.1,48.6,25.3,48.6S2.1,38.2,2.1,25.4S12.5,2.2,25.3,2.2 M25.3,1.2
		C11.9,1.2,1.1,12.1,1.1,25.4s10.9,24.2,24.2,24.2s24.2-10.9,24.2-24.2S38.6,1.2,25.3,1.2L25.3,1.2z"/>
</g>
<linearGradient id="XMLID_2_" gradientUnits="userSpaceOnUse" x1="2.1781" y1="28.5409" x2="17.0496" y2="28.5409">
	<stop  offset="0" style="stop-color:#6F2B54"/>
	<stop  offset="7.192680e-002" style="stop-color:#6F2B54"/>
	<stop  offset="0.2612" style="stop-color:#843D6E"/>
	<stop  offset="0.6" style="stop-color:#B164A5"/>
	<stop  offset="0.9943" style="stop-color:#BB7CB3"/>
	<stop  offset="1" style="stop-color:#BB7CB3"/>
</linearGradient>
<path id="XMLID_1191_" class="st2" d="M5.1,17.3c-1.8,0.9-1.1,3.4,0.5,3.8c1.5,0.4,1.7,1.2,0.9,1.1c-1.4-0.1-2.7-0.9-3.8-2.1
	c-0.2,1-0.4,2.1-0.5,3.2c1.4,1,3.1,1.7,5.1,2.6c5.7,2.5,7.2,9.9,7.6,14.1c0.6-0.3,1.2-0.6,1.9-0.9c-0.1,0-0.1,0.1-0.2,0.1
	c1-3.4,1.3-9.3-5.5-17.3c-0.8-1-1.6-2-2.3-3.1c0,1.5-0.2,2.4-0.6,2.4c-0.2,0-0.6-0.1-0.3-1.4c0.2-0.9-0.5-2.8-1.9-2.8
	C5.8,17.1,5.4,17.2,5.1,17.3"/>
<linearGradient id="XMLID_3_" gradientUnits="userSpaceOnUse" x1="2.1047" y1="30.9833" x2="16.9948" y2="30.9833">
	<stop  offset="0" style="stop-color:#88CDD3"/>
	<stop  offset="7.824219e-002" style="stop-color:#7CCAD1"/>
	<stop  offset="0.2188" style="stop-color:#5DC3CC"/>
	<stop  offset="0.3966" style="stop-color:#2DB8C5"/>
	<stop  offset="0.5948" style="stop-color:#18A8BA"/>
	<stop  offset="0.8306" style="stop-color:#069AB0"/>
	<stop  offset="1" style="stop-color:#0095AD"/>
</linearGradient>
<path id="XMLID_1189_" class="st3" d="M8.8,26.3c-0.4,0.3-0.8,0.2-0.4-1.2C9,23.4,7.6,22,6,22.6c-2.1,0.8-1.3,3.4,0.1,3.7
	c1.2,0.2,1.5,0.9,0.8,1c-1.4,0.2-3.1-0.2-4.8-0.8c0,1.1,0.2,2.1,0.3,3.1c0.8,0.3,1.7,0.5,2.6,0.8c6.4,1.7,5.5,10,5.3,12.8
	c0,0,0,0,0,0c1.8-1.6,3.8-3,6.1-3.9c0,0,0,0,0.1,0c0.2-0.7,0.4-1.6,0.5-2.6c-2.1-2.3-4.4-5.4-4.8-10.5c-0.1-0.8-0.2-2-0.3-3.2
	c-0.3-0.3-0.5-0.7-0.8-1c-0.8-1-1.6-2-2.3-3.2C9.5,21.7,10.1,25.3,8.8,26.3"/>
<linearGradient id="XMLID_4_" gradientUnits="userSpaceOnUse" x1="6.2125" y1="24.3244" x2="22.3318" y2="24.3244">
	<stop  offset="0" style="stop-color:#F9B233"/>
	<stop  offset="0.3306" style="stop-color:#F9B233"/>
	<stop  offset="0.7961" style="stop-color:#F28712"/>
	<stop  offset="1" style="stop-color:#EE7203"/>
</linearGradient>
<path id="XMLID_1187_" class="st4" d="M6.2,12.2c0.4,3.2,2.4,6.9,4.8,9.7c6.8,7.9,6.5,13.8,5.5,17.3c0.9-0.4,1.9-0.7,2.8-1
	c-0.5-1.2,2.9-3.1,3-5.2c0.1-1.5-2.2-4.6-2.8-5.8c-2-3.9-2.7-7.4-2.5-11.4c0-0.1,0-0.2,0-0.3c-0.3,1-0.7,1.7-1.3,2.3
	c-0.4,0.4-0.9,0.2-0.3-1.2c0.7-1.7-0.5-3.2-2.1-2.8c-2.2,0.5-1.7,3.2-0.4,3.7c1.1,0.4,1.3,1.1,0.6,1.1c-3,0.2-5.2-5.5-5.1-9.1
	C7.6,10.3,6.9,11.2,6.2,12.2"/>
<linearGradient id="XMLID_5_" gradientUnits="userSpaceOnUse" x1="33.8505" y1="29.2515" x2="48.4025" y2="29.2515">
	<stop  offset="0" style="stop-color:#6E012C"/>
	<stop  offset="0.1373" style="stop-color:#6E012C"/>
	<stop  offset="0.3558" style="stop-color:#900F37"/>
	<stop  offset="0.7928" style="stop-color:#CE2A4A"/>
	<stop  offset="1" style="stop-color:#E73452"/>
</linearGradient>
<path id="XMLID_1185_" class="st5" d="M42.6,20.5c0.5,1.2,0.3,1.7-0.4,1.4c-0.5-0.2-0.8-0.6-1.1-1c-1.4,2.1-3,3.8-4.4,5.1
	c-3,2.9-3.5,8.6-2.4,13.3c0.9,0.4,1.8,0.9,2.6,1.4c0-2.8,0.5-8.7,8.3-14.2c1.3-0.9,2.3-1.8,3.1-2.8c-0.1-1.1-0.2-2.2-0.5-3.3
	c-1.8,2.4-3.4,2.4-3.7,2.1c-0.1-0.1-0.4-0.6,0.8-1c1.2-0.4,1.9-2.7,0.1-3.6c-0.3-0.1-0.6-0.2-0.8-0.2C43.1,17.8,42.1,19.3,42.6,20.5
	"/>
<linearGradient id="XMLID_6_" gradientUnits="userSpaceOnUse" x1="28.1992" y1="24.4357" x2="44.3589" y2="24.4357">
	<stop  offset="0" style="stop-color:#B41412"/>
	<stop  offset="3.437500e-003" style="stop-color:#B41412"/>
	<stop  offset="0.2397" style="stop-color:#C52C0F"/>
	<stop  offset="0.7032" style="stop-color:#EC6608"/>
	<stop  offset="1" style="stop-color:#EC6608"/>
</linearGradient>
<path id="XMLID_1183_" class="st6" d="M37.2,20.4c-0.9,0.2-0.9-0.5,0.3-1.3c1.4-0.9,1-3.4-0.8-3.6c-1.9-0.2-2.9,1.7-1.9,3.1
	c0.8,1.2,0.4,1.5,0,1.5c-0.3,0-0.7-0.2-1.3-0.5c-0.3,2.5-1,5-2.4,7.6c-0.6,1.2-2.9,4.3-2.8,5.8c0.1,2.2,3.5,4,3,5.2c0,0,0,0,0,0
	c1.1,0.3,2.1,0.7,3.1,1.1c0,0,0,0,0,0c-1.1-4.8-0.5-10.4,2.4-13.3c2.8-2.7,6.8-7,7.6-13.7c-0.7-1-1.4-1.9-2.2-2.7
	C42.1,13,40,19.7,37.2,20.4"/>
<linearGradient id="XMLID_7_" gradientUnits="userSpaceOnUse" x1="36.5825" y1="23.144" x2="46.157" y2="39.7276">
	<stop  offset="0" style="stop-color:#83D0F5"/>
	<stop  offset="1" style="stop-color:#00B1EB"/>
</linearGradient>
<path id="XMLID_1181_" class="st7" d="M41.8,20.4c0,0-0.1,0-0.1,0c-1.7,0.1-2.4,2.1-1.4,3.4c0.8,1.1-0.6,1-1.6,0.2
	c-0.3,0.4-1.2,1.2-1.7,1.8c0.9,5-2.3,9.3-3.1,10.3c0.1,1.1,0.2,2.2,0.5,3.3c0,0,0,0,0,0c-0.1,0-0.1-0.1-0.2-0.1
	c2.2,1,4.2,2.2,5.9,3.8c0,0,0.1,0.1,0.1,0.1c-0.3-2.9-1-12.3,4-14.6c1.9-0.8,3.2-1.6,4.2-2.4c0-0.2,0-0.5,0-0.7c0-0.7,0-1.5-0.1-2.2
	c-1.6,1.5-4.1,2.8-5.9,2.3c-0.2,0-1.2-0.5,0-1.1C44.5,23.2,43.6,20.4,41.8,20.4C41.8,20.4,41.8,20.4,41.8,20.4"/>
<g id="XMLID_2353_">
	<defs>
		<path id="XMLID_1179_" d="M0.5,25.8C0.5,25.8,0.5,25.8,0.5,25.8l0,0.1c0,0,0,0,0,0v0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0s0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0.1,0,0.1,0.1,0.2c0,0,0,0,0,0
			C3.4,39.3,8,44.7,14,47.8c0,0,0,0,0,0c0,0,0.1,0,0.1,0.1l0.8-0.2c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0.4-0.3,0.7-0.6,1.1-0.8C7.9,43.3,2.1,35,2.1,25.4c0-12.8,10.4-23.2,23.2-23.2s23.2,10.4,23.2,23.2
			c0,9.6-5.8,17.9-14.2,21.4c0.4,0.3,0.8,0.5,1.1,0.8c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l0,0l0.9,0.2
			c0,0,0,0,0.1,0c0,0,0.1,0,0.1,0c8-4.1,13.4-12.4,13.4-22c0-4.8-1.4-9.3-3.7-13C41.9,5.7,34.1,1,25.3,1C11.6,1,0.5,12.1,0.5,25.8"
			/>
	</defs>
	<clipPath id="XMLID_8_">
		<use xlink:href="#XMLID_1179_"  style="overflow:visible;"/>
	</clipPath>
</g>
<radialGradient id="SVGID_2_" cx="25" cy="22.0142" r="15.5243" gradientUnits="userSpaceOnUse">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="0.1864" style="stop-color:#FAFBFC"/>
	<stop  offset="0.4181" style="stop-color:#EAF1F2"/>
	<stop  offset="0.6737" style="stop-color:#D1E0E3"/>
	<stop  offset="0.9448" style="stop-color:#AEC8CE"/>
	<stop  offset="1" style="stop-color:#A6C2C9"/>
</radialGradient>
<path class="st8" d="M31.2,29.2c2.1-4.1,2.8-7.9,2.6-12.2c-0.1-1.3-0.8-2.5-1.9-3.5c0.1-2.2,0.1-4.8-1.8-5.7
	c-0.5-0.2-1.1-0.3-1.7-0.3c0.2,0,0.3,0,0.5-0.1c1.3-0.2,2.2-0.6,2.2-0.9c0,0,0,0,0,0c0,0,0,0,0,0.1l0.8-2.1C31.9,4.4,32,4.3,32,4.1
	c0-1.2-3.1-2.2-7-2.2c-3.9,0-7,0.9-7,2.2c0,0.2,0,0.3,0.1,0.5l0.8,2.1h0c0,0,0,0,0-0.1c0,0,0,0,0,0c0.1,0.4,0.9,0.7,2.2,0.9
	c0,0,0,0,0,0c0.1,0,0.3,0,0.4,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
	c-0.6,0-1.3,0-1.8,0.3c-1.9,0.9-1.9,3.5-1.8,5.7c-1.1,1-1.8,2.2-1.9,3.5c-0.2,4.3,0.6,8.1,2.6,12.2c0.6,1.3,3.1,4.6,3,6.2
	c-0.1,2.3-3.7,4.3-3.2,5.6c0.5,1.3,3.7,1.1,6.3,1.1c0,0,0,0,0,0c0,0,0,0,0,0c2.6,0,5.8,0.1,6.3-1.1c0.5-1.3-3.1-3.3-3.2-5.6
	C28,33.8,30.5,30.5,31.2,29.2z M22.1,11.6c-0.9,0.2-1.8,0.5-2.5,0.9c-0.1-1.1-0.1-2.6,0.6-3.3c0.5-0.5,1.4-0.7,2.2-0.4
	c0,0.2,0,0.4,0,0.6C22.4,10.5,22.2,11.2,22.1,11.6z M27.5,9.4c0-0.2,0-0.4,0-0.6c0.7-0.3,1.8-0.2,2.3,0.4c0.7,0.8,0.7,2.2,0.6,3.3
	c-0.8-0.4-1.6-0.7-2.6-0.9C27.7,11.2,27.5,10.4,27.5,9.4z"/>
<path id="XMLID_1226_" class="st9" d="M23.8,16.4c0-0.6,0.5-1.1,1.2-1.1c0.7,0,1.2,0.5,1.2,1.1c0,0.6-0.5,1.1-1.2,1.1
	C24.3,17.5,23.8,17,23.8,16.4 M23.4,16.4c0,0.8,0.7,1.4,1.6,1.4c0.9,0,1.6-0.6,1.6-1.4c0-0.8-0.7-1.4-1.6-1.4
	C24.1,15,23.4,15.6,23.4,16.4"/>
<g id="XMLID_1216_">
	<defs>
		<path id="XMLID_32_" d="M9,44.5c1.5,1.3,3.2,2.4,4.9,3.3c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0v0c2.9-2.8,6.9-4.5,11.1-4.5c4.3,0,8.2,1.7,11.2,4.4l0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			c1.8-0.9,3.5-2.1,5-3.4c-4.1-4.3-9.8-7-16.2-7C18.9,37.4,13.1,40.1,9,44.5"/>
	</defs>
	<clipPath id="XMLID_9_">
		<use xlink:href="#XMLID_32_"  style="overflow:visible;"/>
	</clipPath>
	
		<radialGradient id="XMLID_10_" cx="-445.9319" cy="296.0813" r="0.3672" gradientTransform="matrix(40.2195 0 0 -40.2195 17960.6289 11945.6328)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#C7D64F"/>
		<stop  offset="0.3019" style="stop-color:#C7D64F"/>
		<stop  offset="1" style="stop-color:#95C23D"/>
	</radialGradient>
	<rect id="XMLID_33_" x="9" y="37.4" class="st10" width="32.5" height="10.5"/>
</g>
<path id="XMLID_38_" class="st11" d="M18.1,4.1c0,0.2,0,0.3,0.1,0.5L19,6.6h0c0,0,0,0,0-0.1c0-0.7,2.7-1.2,6.1-1.2
	c3.4,0,6.1,0.5,6.1,1.2c0,0,0,0,0,0.1l0.8-2.1C31.9,4.4,32,4.3,32,4.1c0-1.2-3.1-2.2-7-2.2S18,2.8,18.1,4.1"/>
</svg>
