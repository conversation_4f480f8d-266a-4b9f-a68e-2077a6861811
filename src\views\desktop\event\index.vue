<template lang="pug">
div
  header.short
  .info-wrapper.tour-wrapper
    .info-title
      .page-logo
        img(src="/v1/images/result/wbeteuro20.png")
      .page-title(aria-label='breadcrumb')
        ol.breadcrumb.p-0.m-0
          li.breadcrumb-item.active(aria-current='page')
            span.h1(v-if="schedule.Events.length > 0") {{ schedule.Events[pageId].event_name }}
      SpinButton(text="" :loading="feedback.loading" css="btn-sm btn-info" @click="query" img="fad fa-sync-alt w-16px")
    .info-tablewrap.tour-wrapper
      ul.nav.nav-tabs(role='tablist')
        li.nav-item
          a#tab-bracket.nav-link.active(
            data-toggle='tab'
            href='#bracket'
            role='tab'
            aria-controls='bracket'
            aria-selected='true'
            ) {{ $t("ui.brackets") }}
        li.nav-item
          a#tab-standing.nav-link(
            data-toggle='tab'
            href='#standing'
            role='tab'
            aria-controls='standing'
            aria-selected='true'
            ) {{ $t("ui.standing") }}
        li.nav-item
          a#tab-result.nav-link(
            data-toggle='tab'
            href='#result'
            role='tab'
            aria-controls='result'
            aria-selected='true'
            ) {{ $t("ui.result") }}
      .tab-content.tour-wrapper.tournament(v-if="loading.Brackets || loading.Standing || loading.Events")
        .preloader
          .loader
      .tab-content.tour-wrapper.tournament(v-else)
        #bracket.tab-pane.show.active(role='tabpanel' aria-labelledby='tab-bracket')
          .tournament-bracket.tournament-bracket-rounded
            .tournament-bracket-round.tournament-bracket-round-quarterfinals(v-if="schedule.Brackets.hasOwnProperty(16)")
              h3.tournament-bracket-round-title Round of 16
              ul.tournament-bracket-list
                li.tournament-bracket-item(v-for="item in schedule.Brackets[16]")
                  .tournament-bracket-match
                    .tournament-bracket-table
                      .tournament-bracket-caption {{ $dayjs(item.match_date).format("D, MMM YYYY h:mm A") }}
                      .tournament-bracket-content
                        .tournament-bracket-team.tournament-bracket-team-winner
                          .tournament-bracket-country
                            .tournament-bracket-code(:title="item.home_team") {{ item.home_team }}
                            span.tournament-bracket-flag.flag-icon(:class="'flag-icon-' + item.home_team.replace(' ', '-').replace('(V)', 'v').toLowerCase()")
                          .tournament-bracket-score
                            span.tournament-bracket-number {{ item.home_score == null ? "-" : item.home_score }}
                            span.tournament-bracket-number.red-card-score(v-if="item.home_score_p") {{ item.home_score_p }}
                        .tournament-bracket-team
                          .tournament-bracket-country
                            .tournament-bracket-code(:title="item.away_team") {{ item.away_team }}
                            span.tournament-bracket-flag.flag-icon(:class="'flag-icon-' + item.away_team.replace(' ', '-').replace('(V)', 'v').toLowerCase()")
                          .tournament-bracket-score
                            span.tournament-bracket-number {{ item.away_score == null ? "-" : item.away_score }}
                            span.tournament-bracket-number.red-card-score(v-if="item.away_score_p") {{ item.away_score_p }}

            .tournament-bracket-round.tournament-bracket-round--semifinals(v-if="schedule.Brackets.hasOwnProperty(8)")
              h3.tournament-bracket-round-title Quarter Finals
              ul.tournament-bracket-list
                li.tournament-bracket-item(v-for="item in schedule.Brackets[8]")
                  .tournament-bracket-match
                    .tournament-bracket-table
                      .tournament-bracket-caption {{ $dayjs(item.match_date).format("D, MMM YYYY h:mm A") }}
                      .tournament-bracket-content
                        .tournament-bracket-team.tournament-bracket-team-winner
                          .tournament-bracket-country
                            .tournament-bracket-code(:title="item.home_team") {{ item.home_team }}
                            span.tournament-bracket-flag.flag-icon(:class="'flag-icon-' + item.home_team.replace(' ', '-').replace('(V)', 'v').toLowerCase()")
                          .tournament-bracket-score
                            span.tournament-bracket-number {{ item.home_score == null ? "-" : item.home_score }}
                            span.tournament-bracket-number.red-card-score(v-if="item.home_score_p") {{ item.home_score_p }}
                        .tournament-bracket-team
                          .tournament-bracket-country
                            .tournament-bracket-code(:title="item.away_team") {{ item.away_team }}
                            span.tournament-bracket-flag.flag-icon(:class="'flag-icon-' + item.away_team.replace(' ', '-').replace('(V)', 'v').toLowerCase()")
                          .tournament-bracket-score
                            span.tournament-bracket-number {{ item.away_score == null ? "-" : item.away_score }}
                            span.tournament-bracket-number.red-card-score(v-if="item.away_score_p") {{ item.away_score_p }}

            .tournament-bracket-round.tournament-bracket-round--bronze(v-if="schedule.Brackets.hasOwnProperty(4)")
              h3.tournament-bracket-round-title Semi Finals
              ul.tournament-bracket-list
                li.tournament-bracket-item(v-for="item in schedule.Brackets[4]")
                  .tournament-bracket-match
                    .tournament-bracket-table
                      .tournament-bracket-caption {{ $dayjs(item.match_date).format("D, MMM YYYY h:mm A") }}
                      .tournament-bracket-content
                        .tournament-bracket-team.tournament-bracket-team-winner
                          .tournament-bracket-country
                            .tournament-bracket-code(:title="item.home_team") {{ item.home_team }}
                            span.tournament-bracket-flag.flag-icon(:class="'flag-icon-' + item.home_team.replace(' ', '-').replace('(V)', 'v').toLowerCase()")
                          .tournament-bracket-score
                            span.tournament-bracket-number {{ item.home_score == null ? "-" : item.home_score }}
                            span.tournament-bracket-number.red-card-score(v-if="item.home_score_p") {{ item.home_score_p }}
                        .tournament-bracket-team
                          .tournament-bracket-country
                            .tournament-bracket-code(:title="item.away_team") {{ item.away_team }}
                            span.tournament-bracket-flag.flag-icon(:class="'flag-icon-' + item.away_team.replace(' ', '-').replace('(V)', 'v').toLowerCase()")
                          .tournament-bracket-score
                            span.tournament-bracket-number {{ item.away_score == null ? "-" : item.away_score }}
                            span.tournament-bracket-number.red-card-score(v-if="item.away_score_p") {{ item.away_score_p }}

            .tournament-bracket-round.tournament-bracket-round--gold(v-if="schedule.Brackets.hasOwnProperty(2)")
              h3.tournament-bracket-round-title Final
              ul.tournament-bracket-list
                li.tournament-bracket-item(v-for="item in schedule.Brackets[2]")
                  .tournament-bracket-match
                    .tournament-bracket-table
                      .tournament-bracket-caption {{ $dayjs(item.match_date).format("D, MMM YYYY h:mm A") }}
                      .tournament-bracket-content
                        .tournament-bracket-team.tournament-bracket-team-winner
                          .tournament-bracket-country
                            .tournament-bracket-code(:title="item.home_team") {{ item.home_team }}
                            span.tournament-bracket-flag.flag-icon(:class="'flag-icon-' + item.home_team.replace(' ', '-').replace('(V)', 'v').toLowerCase()")
                          .tournament-bracket-score
                            span.tournament-bracket-number {{ item.home_score == null ? "-" : item.home_score }}
                            span.tournament-bracket-number.red-card-score(v-if="item.home_score_p") {{ item.home_score_p }}
                        .tournament-bracket-team
                          .tournament-bracket-country
                            .tournament-bracket-code(:title="item.away_team") {{ item.away_team }}
                            span.tournament-bracket-flag.flag-icon(:class="'flag-icon-' + item.away_team.replace(' ', '-').replace('(V)', 'v').toLowerCase()")
                          .tournament-bracket-score
                            span.tournament-bracket-number {{ item.away_score == null ? "-" : item.away_score }}
                            span.tournament-bracket-number.red-card-score(v-if="item.away_score_p") {{ item.away_score_p }}


          .clearfix
        #standing.tab-pane(role='tabpanel' aria-labelledby='tab-standing')
          .row.mx-0.mt-3.p-0
            .col-12.col-lg-6.px-0.block-group(v-for="(grp, gk, gi) in schedule.Standing")
              .bg-light.m-2.px-2
                .block-title Group {{ gk }}
                .panel.panel-point
                  .panel-content
                    .table-group-wrap
                      .table-row.table-heading
                        .table-item.table-group Club
                        .table-item(title="MP") MP
                        .table-item(title="W") W
                        .table-item(title="D") D
                        .table-item(title="L") L
                        .table-item(title="GF") GF
                        .table-item(title="GA") GA
                        .table-item(title="GD") GD
                        .table-item(title="Pts") Pts
                      .table-row(v-for="team in grp")
                        .table-item.table-group
                          span.flag-icon.mr-2(:class="'flag-icon-' + team.team_id.replace(' ', '-').replace('(V)', 'v').toLowerCase()")
                          span(:title="team.team_id") {{ team.team_id }}
                        .table-item {{ team.mp }}
                        .table-item {{ team.win }}
                        .table-item {{ team.draw }}
                        .table-item {{ team.lose }}
                        .table-item {{ team.gf }}
                        .table-item {{ team.ga }}
                        .table-item {{ team.gd }}
                        .table-item
                          span.text-highlight {{ team.pts }}

        #result.tab-pane(role='tabpanel' aria-labelledby='tab-result')
          .row.mx-0.mt-3.p-0
            .col-12.col-md-6.col-lg-4.px-0.block-group(v-for="(grp, gk, gi) in schedule.Result")
              .bg-dark.m-2.px-2
                .block-title.block-small Group {{ gk }}

                .match-result(v-for="team in grp")
                  .match-number
                    .text-match M{{ team.match_number }}
                  .match-team
                    .d-flex.flex-row.justify-content-center.align-items-center.team-wrap
                      .d-flex.flex-column.flex-fill.align-items-center.text-center.team-name
                        .team-flag
                          span.flag-icon.mr-2(:class="'flag-icon-' + team.home_team.replace(' ', '-').replace('(V)', 'v').toLowerCase()")
                        .team-text
                          span(:title="team.home_team") {{ team.home_team }}
                      .text-center.team-score.home {{ team.home_score }}
                      .d-flex.flex-column.justify-content-center.align-items-center.team-vs
                        .team-date {{ $dayjs(team.match_time).format("MM/DD") }}
                        .team-time {{ $dayjs(team.match_time).format("h:mm A") }}
                        .team-status
                      .text-center.team-score.away {{ team.away_score }}
                      .d-flex.flex-column.flex-fill.align-items-center.text-center.team-name
                        .team-flag
                          span.flag-icon.mr-2(:class="'flag-icon-' + team.away_team.replace(' ', '-').replace('(V)', 'v').toLowerCase()")
                        .team-text
                          span(:title="team.away_team") {{ team.away_team }}





</template>

<script>
import config from "@/config";
import errors from "@/errors";
import SpinButton from "@/components/ui/SpinButton";
import mixinExt from "@/library/mixinExt.js";

export default {
  components: {
    SpinButton
  },
  mixins: [mixinExt],
  data() {
    return {
      pageName: 1,
      pageId: 0
    };
  },
  created() {
    $("body").addClass("wbet-bg");
  },
  destroyed() {},
  mounted() {
    setTimeout(() => {
      this.query();
    }, 500);
  },
  methods: {
    query() {
      this.getEvents();
      this.getBrackets(this.pageName, 0);
      this.getStanding(this.pageName);
      this.getResult(this.pageName);

      $("#tab-bracket").tab("show");
    },
    setPage(n, e) {
      this.pageId = n;
      this.pageName = e;
      this.query();
    }
  }
};
</script>
