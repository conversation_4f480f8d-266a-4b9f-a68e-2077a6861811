<template lang="pug">
#filter-bar.filter-area.d-flex.align-items-center.justify-content-center
  .filter-item.highlight-button(
    style="min-width: 32px; width: 40px; max-width: 40px;"
    :title="$t('ui.my_favorites')"
    @click="toggleFavorite()"
    :class="{ 'active' : menu0 == 'favorite' }")
    i.fad.fa-star
  search1
  .filter-item(v-if="menu2 == 1")
    #dropdown-market.d-flex.dropdown(data-toggle="dropdown" aria-haspopup="true" aria-expanded="false")
      .filter-icon
        i.fal(:class="marketType.icon")
      .flex-fill.text-filter {{ marketType.name }}
      .filter-down
        i.far.fa-chevron-down
      .dropdown-menu(aria-labelledby="dropdown-market")
        a.dropdown-item(href="javascript:void(0);" @click="setMarketType('1')" :class="{ active: pageDisplay.marketType == 1}")
          .d-flex
            .filter-icon
              i.fal.fa-plus
            div {{ $t("ui.all_markets") }}
        a.dropdown-item(href="javascript:void(0);" @click="setMarketType('2')" :class="{ active: pageDisplay.marketType == 2}")
          .d-flex
            .filter-icon
              i.fal.fa-minus
            div {{ $t("ui.main_markets") }}
        a.dropdown-item(href="javascript:void(0);" @click="setMarketType('3')" :class="{ active: pageDisplay.marketType == 3}")
          .d-flex
            .filter-icon
              i.fal.fa-ellipsis-h
            div {{ $t("ui.other_markets") }}

  .filter-item
    #dropdown-sorting.d-flex.dropdown(data-toggle="dropdown" aria-haspopup="true" aria-expanded="false")
      .filter-icon
        i.fad(:class="eventSorting.icon")
      .flex-fill.text-filter {{ eventSorting.name }}
      .filter-down
        i.far.fa-chevron-down
      .dropdown-menu(aria-labelledby="dropdown-sorting")
        a.dropdown-item(href="javascript:void(0);" @click="setEventSorting('1')" :class="{ active: pageDisplay.eventSorting == 1}")
          .d-flex
            .filter-icon
              i.fad.fa-sort-alpha-down
            div {{ $t("ui.normal_sorting") }}
        a.dropdown-item(href="javascript:void(0);" @click="setEventSorting('2')" :class="{ active: pageDisplay.eventSorting == 2}")
          .d-flex
            .filter-icon
              i.fad.fa-sort-numeric-down
            div {{ $t("ui.sort_by_time") }}

  .filter-item
    #popup-league.dropdown.d-flex(data-toggle="modal" data-target="#modal-leagues")
      .filter-icon
        i.fad.fa-th-list
      .flex-fill.text-filter {{ $t("ui.select_leagues") }}
      .filter-down
        i.far.fa-ellipsis-h
  .filter-item(v-if="isFilterTime")
    #dropdown-time.d-flex.dropdown(data-toggle="dropdown" aria-haspopup="true" aria-expanded="false")
      .filter-icon
        i.fad.fa-clock
      .flex-fill.text-filter(:class="{ 'active' : filterMode }") {{ $t("ui.ftmode" + filterMode) }}
      .filter-down
        i.far.fa-chevron-down
      .dropdown-menu(aria-labelledby="dropdown-time")
        template(v-for="ft in [0,9,1,2,3,4,5]")
          a.dropdown-item(href="javascript:void(0);" @click="setFilterMode(ft)" :class="{ active: $store.state.cache.filterMode == ft}")
            .d-flex
              .filter-icon
                i.fad.fa-clock
              div {{ $t("ui.ftmode" + ft) }}

  .filter-item
    #dropdown-odds-type.d-flex.dropdown(data-toggle="dropdown" aria-haspopup="true" aria-expanded="false")
      //- .filter-icon
      //-   i.fad(:class="oddsType.icon")
      .flex-fill.text-filter {{ oddsType.name }}
      .filter-down
        i.far.fa-chevron-down
      .dropdown-menu(aria-labelledby="dropdown-odds-type")
        a.dropdown-item(href="javascript:void(0);" @click="setOddsType('MY')" :class="{ active: pageDisplay.oddsType == 'MY'}")
          .d-flex
            div {{ $t("ui.my_odds") }}
        a.dropdown-item(href="javascript:void(0);" @click="setOddsType('HK')" :class="{ active: pageDisplay.oddsType == 'HK'}")
          .d-flex
            div {{ $t("ui.hk_odds") }}
        a.dropdown-item(href="javascript:void(0);" @click="setOddsType('ID')" :class="{ active: pageDisplay.oddsType == 'ID'}")
          .d-flex
            div {{ $t("ui.id_odds") }}
        a.dropdown-item(href="javascript:void(0);" @click="setOddsType('DEC')" :class="{ active: pageDisplay.oddsType == 'DEC'}")
          .d-flex
            div {{ $t("ui.dec_odds") }}


  refreshButton(v-if="!isNonLive" :state="state.GET_LIVE" :counter="counter.GET_LIVE" @refresh="func.GET_LIVE()" cls="live")
  refreshButton(:state="state.GET_NONLIVE" :counter="counter.GET_NONLIVE" @refresh="func.GET_NONLIVE()")

</template>

<script>
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";
import StakeCheck from "@/library/_stakeinput.js";
import mixinDelay from "@/library/mixinDelay";
import refreshButton from "@/components/desktop/main/xtable/xitem/refreshButton";
import search1 from "@/components/desktop/main/xtable/xitem/search1";

export default {
  components: {
    refreshButton,
    search1,
  },
  mixins: [mixinDelay],
  props: {},
  data() {
    return {
      preferenceFlag: false,
      quickBetAmount: 0,
      state: {
        GET_LIVE: false,
        GET_NONLIVE: false,
        GET_MARKET: false,
        INVALIDATE: false,
      },
      counter: {
        GET_LIVE: 0,
        GET_NONLIVE: 0,
        GET_MARKET: 0,
        INVALIDATE: 0,
      },
      func: {
        GET_LIVE: null,
        GET_NONLIVE: null,
        GET_MARKET: null,
        INVALIDATE: null,
      },
      isFilterLive: false,
    };
  },
  computed: {
    isFilterTime() {
      return this.$store.state.cache.isFilterTime;
    },
    filterMode() {
      return this.$store.state.cache.filterMode;
    },
    isNonLive() {
      if (["all"].includes(this.menu0)) {
        if (["orz"].includes(this.menu3)) {
          return true;
        } else {
          if (["early"].includes(this.menu1)) {
            return true;
          }
        }
      } else {
        return false;
      }
      return false;
    },
    favLeague() {
      return this.$store.state.layout.favorite;
    },
    syncQuickStake() {
      return this.$store.state.layout.betting.quickBetAmount;
    },
    menu0() {
      return this.$store.getters.menu0;
    },
    menu1() {
      return this.$store.getters.menu1;
    },
    menu2() {
      return this.$store.getters.menu2;
    },
    isPageType() {
      return (
        (this.$store.getters.menu3 == "hdpou" || this.$store.getters.menu3 == "cs") &&
        this.$store.getters.menu2 == 1
      );
    },
    isPageType1() {
      return this.$store.getters.menu3 == "hdpou";
    },
    isPageType2() {
      return true;
    },
    isPageType3() {
      return true;
    },
    isPageType4() {
      return true;
    },
    pageDisplay() {
      return this.$store.getters.pageDisplay;
    },
    betting() {
      return this.$store.getters.betting;
    },
    menu3() {
      return this.$store.getters.menu3;
    },
    oddsType() {
      var result = {
        icon: "MY",
        name: this.$t("ui.odds_type"),
      };
      switch (this.pageDisplay.oddsType.toUpperCase()) {
      case "MY":
        result = {
          icon: "MY",
          name: this.$t("ui.my_odds"),
        };
        break;
      case "HK":
        result = {
          icon: "HK",
          name: this.$t("ui.hk_odds"),
        };
        break;
      case "ID":
        result = {
          icon: "ID",
          name: this.$t("ui.id_odds"),
        };
        break;
      case "DEC":
        result = {
          icon: "DEC",
          name: this.$t("ui.dec_odds"),
        };
        break;
      }
      return result;
    },
    marketType() {
      var result = {
        icon: "fa-filter",
        name: this.$t("ui.market_type"),
      };
      switch (parseInt(this.pageDisplay.marketType)) {
      case 1:
        result = {
          icon: "fa-plus",
          name: this.$t("ui.all_markets"),
        };
        break;
      case 2:
        result = {
          icon: "fa-minus",
          name: this.$t("ui.main_markets"),
        };
        break;
      case 3:
        result = {
          icon: "fa-ellipsis-h",
          name: this.$t("ui.other_markets"),
        };
        break;
      }
      return result;
    },
    eventSorting() {
      var result = {
        icon: "fa-sort-amount-down",
        name: this.$t("ui.event_sorting"),
      };
      switch (parseInt(this.pageDisplay.eventSorting)) {
      case 1:
        result = {
          icon: "fa-sort-alpha-down",
          name: this.$t("ui.normal_sorting"),
        };
        break;
      case 2:
        result = {
          icon: "fa-sort-numeric-down",
          name: this.$t("ui.sort_by_time"),
        };
        break;
      }
      return result;
    },
    pageType() {
      var result = {
        icon: "fa-grip-lines",
        name: this.$t("ui.page_type"),
      };
      switch (parseInt(this.pageDisplay.pageType)) {
      case 1:
        result = {
          icon: "fa-minus",
          name: this.$t("ui.single_line"),
        };
        break;
      case 2:
        result = {
          icon: "fa-grip-lines",
          name: this.$t("ui.double_line"),
        };
        break;
      case 3:
        result = {
          icon: "fa-hourglass-start",
          name: this.$t("ui.full_time"),
        };
        break;
      case 4:
        result = {
          icon: "fa-hourglass-half",
          name: this.$t("ui.half_time"),
        };
        break;
      }
      return result;
    },
  },
  watch: {
    // syncQuickStake(newVal) {
    //   this.quickBetAmount = newVal;
    // }
  },
  destroyed() {
    EventBus.$off("GET_LIVE_COUNT", this.handleLive);
    EventBus.$off("GET_NONLIVE_COUNT", this.handleNonLive);
    EventBus.$off("GET_MARKET_COUNT", this.handleMarket);
    EventBus.$off("GET_LIVE", this.runLive);
    EventBus.$off("GET_NONLIVE", this.runNonLive);
    EventBus.$off("GET_MARKET", this.runMarket);
    EventBus.$off("INVALIDATE", this.runInvalidate);
  },
  mounted() {
    // console.log("filter mounted");
    $("#dropdown-quickbet .dropdown-menu").on("click", function () {
      event.stopPropagation();
    });
    if (this.betting.quickBet) {
      this.getQcAmount();
    }

    EventBus.$on("GET_LIVE_COUNT", this.handleLive);
    EventBus.$on("GET_NONLIVE_COUNT", this.handleNonLive);
    EventBus.$on("GET_MARKET_COUNT", this.handleMarket);
    EventBus.$on("GET_LIVE", this.runLive);
    EventBus.$on("GET_NONLIVE", this.runNonLive);
    EventBus.$on("GET_MARKET", this.runMarket);
    EventBus.$on("INVALIDATE", this.runInvalidate);
    this.func.GET_LIVE = this.debounce(this.getLive, 500);
    this.func.GET_NONLIVE = this.debounce(this.getNonLive, 500);
    this.func.GET_MARKET = this.debounce(this.getMarket, 500);
    this.func.INVALIDATE = this.debounce(this.invalidate, 500);
  },
  methods: {
    setFilterMode(e) {
      this.$store.dispatch("cache/setFilterMode", e);
      EventBus.$emit("INVALIDATE");
    },
    runLive(e) {
      this.state.GET_LIVE = true;
      setTimeout(() => {
        this.state.GET_LIVE = false;
      }, 1000);
    },
    runNonLive(e) {
      // this.state.GET_LIVE = true;
      this.state.GET_NONLIVE = true;
      setTimeout(() => {
        // this.state.GET_LIVE = false;
        this.state.GET_NONLIVE = false;
      }, 2000);
    },
    runMarket(e) {
      this.state.GET_LIVE = true;
      this.state.GET_NONLIVE = true;
      this.state.GET_MARKET = true;
      setTimeout(() => {
        this.state.GET_LIVE = false;
        this.state.GET_NONLIVE = false;
        this.state.GET_MARKET = false;
      }, 3000);
    },
    runInvalidate(e) {
      this.state.GET_LIVE = true;
      this.state.GET_NONLIVE = true;
      this.state.GET_MARKET = true;
      this.state.INVALIDATE = true;
      setTimeout(() => {
        this.state.GET_LIVE = false;
        this.state.GET_NONLIVE = false;
        this.state.GET_MARKET = false;
        this.state.INVALIDATE = false;
      }, 5000);
      this.isFilterLive = this.$store.state.cache.isFilterLive;
    },
    handleLive(e) {
      this.counter.GET_LIVE = e;
    },
    handleNonLive(e) {
      this.counter.GET_NONLIVE = e;
    },
    handleMarket(e) {
      this.counter.GET_MARKET = e;
    },
    getLive() {
      EventBus.$emit("GET_LIVE");
    },
    getNonLive() {
      EventBus.$emit("GET_NONLIVE");
    },
    getMarket() {
      EventBus.$emit("GET_MARKET");
    },
    invalidate() {
      EventBus.$emit("INVALIDATE");
    },
    prevPage() {
      this.$emit("prevPage");
    },
    nextPage() {
      this.$emit("nextPage");
    },
    changePage(e) {
      this.$emit("changePage", parseInt(e.target.value));
    },
    settings() {
      $("#modal-settings").modal("show");
    },
    bindClick() {
      if (!this.preferenceFlag) {
        setTimeout(() => {
          var $this = this;
          $(window).on("click", function (event) {
            event.preventDefault();
            var $target = $(event.target);
            if (
              !$target.closest(".dropdown-preference.show").length &&
              $this.preferenceFlag
            ) {
              $(".dropdown-preference").removeClass("show");
              $this.preferenceFlag = false;
              $(this).off(event);
            }
          });
        }, 10);
      }
    },
    showPreference(flag = true) {
      if (flag) {
        this.bindClick();
        if (this.preferenceFlag) {
          $(".dropdown-preference").removeClass("show");
        } else {
          this.preferenceFlag = true;
          setTimeout(() => {
            $(".dropdown-preference").addClass("show");
          }, 10);
        }
      }
    },
    go(e) {
      var routeData = this.$router.resolve(e);
      this.$helpers.info(routeData.href);
    },
    toggleQuickBet() {
      var payload = Object.assign({}, this.betting);
      payload.quickBet = !payload.quickBet;
      if (payload.quickBet) {
        this.getQcAmount();
      }
      this.$store.dispatch("layout/setBetting", payload);
      EventBus.$emit("STOP_QUICKBET");
      this.showPreference(false);
    },
    setMarketType(e) {
      var payload = Object.assign({}, this.pageDisplay);
      payload.marketType = e;
      this.$store.dispatch("layout/setPageDisplay", payload);
      EventBus.$emit("INVALIDATE");
    },
    setEventSorting(e) {
      var payload = Object.assign({}, this.pageDisplay);
      payload.eventSorting = e;
      this.$store.dispatch("layout/setPageDisplay", payload);
      EventBus.$emit("INVALIDATE");
    },
    setOddsType(e) {
      var payload = Object.assign({}, this.pageDisplay);
      payload.oddsType = e;
      this.$store.dispatch("layout/setPageDisplay", payload);
      EventBus.$emit("INVALIDATE");
    },
    setPageType(e) {
      // console.log(e)
      var payload = Object.assign({}, this.pageDisplay);
      payload.pageType = e;
      this.$store.dispatch("layout/setPageDisplay", payload);
      EventBus.$emit("INVALIDATE");
    },
    toggleFavorite() {
      // console.log("favorite")
      if (this.menu0 != "favorite") {
        if (EventBus.setMenu0) {
          EventBus.setMenu0("favorite");
        }
      } else {
        if (EventBus.setMenu0) {
          EventBus.setMenu0("all");
        }
      }
    },
    toggleLiveOnly() {
      this.isFilterLive = !this.isFilterLive;
      this.$store.dispatch("cache/setFilterLive", this.isFilterLive);
      if (this.menu0 != "live") {
        if (EventBus.setMenu0) {
          EventBus.setMenu0("live");
          $("#collapse-live").collapse("show");
        }
      } else {
        EventBus.$emit("INVALIDATE");
      }
    },
    getQcAmount() {
      this.quickBetAmount = this.$store.state.layout.betting.quickBetAmount;
    },
    saveQuickBet() {
      setTimeout(() => {
        this.$store.dispatch("layout/setSingleBetting", {
          property: "quickBetAmount",
          value: this.quickBetAmount,
        });
      }, 10);
    },
    validateStake(evt) {
      StakeCheck.validate(evt);
    },
    handleInput(e) {
      var chk = StakeCheck.validateWord(e);
      // if (!chk) {
      //   this.$refs.stake2.value = this.quickBetAmount;
      // }
    },
  },
};
</script>
