<template lang="pug">
.tournament-pool-body
  template(v-if="isActive")
    .tournament-details-row(v-for="(item, key) in participants")
      .tournament-details-content.pointer(@click="showRecords(item, key)")
        a
          .tournament-details-left
            .icon-player
              i.fas.fa-user-circle
            .tournament-position(style="color: #ffffffcc;") {{ item.member_id }}
          .tournament-details-right(v-if="item.bet_list.length > 0")
            i.fas.fa-angle-right.pl-1
  template(v-else)
    .tournament-pool-body-wrapper
      .text-center
        span {{ $t('message.no_information_available') }}
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";

export default {
  props: {
    participants: {
      type: Object,
    },
    roomId: {
      type: Number,
    },
  },
  data() {
    return {};
  },
  computed: {
    isActive() {
      return this.participants && Object.keys(this.participants).length > 0;
    },
  },
  methods: {
    showRecords(item, key) {
      var r = {
        id: key,
        member_id: item.member_id,
        record: item,
        ranking: null,
        prize: null,
        room_id: this.roomId,
      };
      EventBus.$emit("TN_PLAYER_BETSLIP2", r);
    },
  },
};
</script>
