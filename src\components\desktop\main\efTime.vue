<template lang="pug">
.e-event(:class="horiz ? 'horiz' : ''")
  .e-date(:class="part1css") {{ part1 }}
  .e-time(:class="part2css") {{ part2 }}
  .e-no.flex-fill.d-flex.justify-content-end.align-items-center(v-if="racingList.includes(source.sportsId) && source.matchTime") No. {{ part3 }}
</template>

<script>
import config from "@/config";

export default {
  components: {},
  props: {
    source: {
      type: Object,
    },
    horiz: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      countDown: 0,
      ticker: null,
    };
  },
  computed: {
    racingList() {
      return config.racingList;
    },
    currency_code() {
      return this.$store.getters.currencyCode;
    },
    part1css() {
      var mt = this.source.marketId;
      var score = this.source.runningScore;
      var label = this.source.runningTime;
      if (mt < 3) {
        return label != null ? (label.trim().length > 0 ? this.getStyle(label.trim()) : "duration") : "duration";
      } else {
        if (label != null) {
          if (label.trim().length > 0) {
            return score != null ? (score.trim().length > 0 ? "score" : "duration") : "duration";
          } else {
            return "duration";
          }
        } else {
          return "duration";
        }
      }
    },
    part2css() {
      var mt = this.source.marketId;
      var score = this.source.runningScore;
      var label = this.source.runningTime;
      if (mt < 3) {
        return "duration";
      } else {
        return label != null ? (label.trim().length > 0 ? this.getStyle(label.trim()) : "duration") : "duration";
      }
    },
    part1() {
      var mdate = this.$dayjs(this.source.matchTime).format("MM/DD");
      switch (this.currency_code) {
      case "MMK":
      case "MMO":
        mdate = this.$dayjs(this.source.matchTime).subtract(90, "minute").format("MM/DD");
        break;
      case "NGN":
        mdate = this.$dayjs(this.source.matchTime).subtract(420, "minute").format("MM/DD");
        break;
      }
      var mt = this.source.marketId;
      var score = this.source.runningScore;
      var label = this.source.runningTime;
      if (mt < 3) {
        if (label != null) {
          if (label.trim().length > 0) {
            return this.getLabel(label);
          } else {
            return mdate;
          }
        } else {
        }
        return mdate;
      } else {
        if (label != null) {
          if (label.trim().length > 0) {
            return score != null ? (score.trim().length > 0 ? score.trim() : mdate) : mdate;
          } else {
            return mdate;
          }
        } else {
          return mdate;
        }
      }
    },
    part2() {
      var mdate = this.$dayjs(this.source.matchTime).format("hh:mm A");
      switch (this.currency_code) {
      case "MMK":
      case "MMO":
        mdate = this.$dayjs(this.source.matchTime).subtract(90, "minute").format("hh:mm A");
        break;
      case "NGN":
        mdate = this.$dayjs(this.source.matchTime).subtract(420, "minute").format("hh:mm A");
        break;
      }
      var mt = this.source.marketId;
      var score = this.source.runningScore;
      var label = this.source.runningTime;
      if (mt < 3) {
        return mdate;
      } else {
        return label != null ? (label.trim().length > 0 ? this.getLabel(label) : mdate) : mdate;
      }
    },
    part3() {
      var mdate = this.$dayjs(this.source.matchTime).format("MMDDhhmm");
      return mdate;
    },
  },
  destroyed() {
    if (this.ticker) {
      clearTimeout(this.ticker);
    }
  },
  mounted() {},
  methods: {
    isEFight(e) {
      if (e && e.sportsId) return config.vg1.includes(e.sportsId);
      else return false;
    },
    getStyle(e) {
      switch (e) {
      case "LIVE":
        return "danger";
        break;
      case "HT":
        return "info";
        break;
      case "DELAYED":
      case "PEN":
        return "warn";
        break;
      default:
        return "label";
        break;
      }
    },
    getLabel(e) {
      if (/\d/.test(e)) {
        return e.trim();
      } else {
        return this.$t("m.BT_" + e.trim());
      }
    },
  },
};
</script>
