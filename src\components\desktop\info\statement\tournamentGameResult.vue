<template lang="pug">
div
  ul.nav.nav-tabs(role='tablist')
    li.nav-item
      a#tab-tn-betlist.nav-link.active(data-toggle='tab', href='#tn-betlist', role='tab', aria-controls='tn-betlist', aria-selected='true') {{ $t("ui.bet_list") }}
    li.nav-item
      a#tab-tn-refund.nav-link(
        @click="$emit('getRefundList')"
        data-toggle='tab', href='#tn-refund', role='tab', aria-controls='tn-refund', aria-selected='false') {{ $t("ui.refund") }}
  .tab-content
    #tn-betlist.tab-pane.show.active(role='tabpanel', aria-labelledby='tab-tn-betlist' )
      table.table-info(width='100%')
        tbody
          tr
            th.text-center(scope='col', width='6%') {{ $t("ui.no/") }}
            th.text-left(scope='col', width='20%') {{ $t("ui.trans_time") }}
            th.text-left(scope='col', width='24%') {{ $t("ui.event") }}
            th.text-right(scope='col', width='10%') {{ $t("ui.stake") }}
            th.text-right(scope='col', width='12%') {{ $t("ui.win") }} / {{ $t("ui.loss") }}
            th.text-right(scope='col', width='10%') {{ $t("ui.jackpot") }}
            th.text-right(scope='col', width='10%') {{ $t("ui.points") }}
            th(scope='col', width='8%') {{ $t("ui.status") }}
          tr.grey(v-if="gameResultList.length == 0")
            td(colspan="7").text-center
              span {{ $t('message.no_information_available') }}
          tr(v-for="(item, index) in gameResultList" :class="{ grey: index % 2 === 0 }")
            td.text-center(valign='top') {{index + 1}}
            td.text-left(valign='top')
              div {{ $t("ui.ref_no") }}: {{ item.result_id }}
              div {{ $dayjs(item.bet_time).format("MM/DD/YYYY hh:mm:ss A") }}
            td.text-left(valign='top')
              .bet-info
                .bet-type.blue(v-if="item.remark") {{ $t('m.' + item.remark) }}
                .bet-detail
                  .name {{ $t('m.ROOM_T' + item.room_type) }}
                  .name {{ $t('ui.room_id') }}: {{ item.room_id }}
            td.text-right(valign='top')
              div
                span {{ $numeral(item.turnover).format("0,0.00") }}
            td.text-right(valign='top')
              div
                span(:class="{ red: parseFloat(item.winlose) < 0 }") {{ $numeral(item.winlose).format("0,0.00") }}
            td.text-right(valign='top')
              template(v-if="item.jackpot_payout")
                div
                  span {{ $numeral(item.jackpot_payout).format("0,0.00") }}
                div
                  small ({{ $t("ui.prize") }} x {{ $numeral(item.jackpot_value).format("0,0") }})
            td.text-right(valign='top')
              div
                span {{ $numeral(item.bet_payout).format("0,0.00") }}
            td.text-left(valign='top')
              div
                span {{ parseFloat(item.winlose) != 0 ? (parseFloat(item.winlose) < 0 ? $t("ui.lost") : $t("ui.won")) : $t("ui.draw") }}
      table.table-total(width='100%' v-if="isTotal")
        tbody
          tr
            td.text-right(valign='top' width='50%') {{ $t("ui.subtotal") }} ({{ parseFloat(gameResultSummary.winlose) < 0 ? $t("ui.lost") : $t("ui.won") }})
            td.text-right(valign='top' width='10%')
              span(
                :class="{ red: parseFloat(gameResultSummary.turnover) < 0 }"
                ) {{ $numeral(gameResultSummary.turnover).format("0,0.00") }}
            td.text-right(valign='top' width='12%')
              span(
                :class="{ red: parseFloat(gameResultSummary.winlose) < 0 }"
                ) {{ $numeral(gameResultSummary.winlose).format("0,0.00") }}
            td.text-right(valign='top' width='10%')
              span(
                :class="{ red: parseFloat(gameResultSummary.jackpot_payout) < 0 }"
                ) {{ $numeral(gameResultSummary.jackpot_payout).format("0,0.00") }}
            td.text-right(valign='top' width='10%')
              span(
                :class="{ red: parseFloat(gameResultSummary.bet_payout) < 0 }"
                ) {{ $numeral(gameResultSummary.bet_payout).format("0,0") }}
            td.text-right(valign='top' width='8%')
          tr
            td.text-right(valign='top') {{ $t("ui.total") }}
            td.text-right(valign='top')
              span(
                :class="{ red: totalTurnover < 0 }"
                ) {{ $numeral(totalTurnover).format("0,0.00") }}
            td.text-right(valign='top')
              span(
                :class="{ red: totalWinLose < 0 }"
                ) {{ $numeral(totalWinLose).format("0,0.00") }}
            td.text-right(valign='top')
              span(
                :class="{ red: totalJackpotPayout < 0 }"
                ) {{ $numeral(totalJackpotPayout).format("0,0.00") }}
            td.text-right(valign='top')
              span(
                :class="{ red: totalBetPayout < 0 }"
                ) {{ $numeral(totalBetPayout).format("0,0") }}
            td
      .mt-2
        v-pagination(
          v-model="currentPage"
          :page-count="gameResultTotalPages"
          :classes="bootstrapPaginationClasses"
          :labels="paginationAnchorTexts"
          @input="changedPage($event)"
          v-if="gameResultTotalPages"
        )
    #tn-refund.tab-pane(role='tabpanel', aria-labelledby='tab-tn-refund' )
      table.table-info(width='100%')
        tbody
          tr
            th.text-center(scope='col', width='6%') {{ $t("ui.no/") }}
            th.text-left(scope='col', width='20%') {{ $t("ui.trans_time") }}
            th.text-left(scope='col', width='34%') {{ $t("ui.event") }}
            th.text-right(scope='col', width='10%') {{ $t("ui.stake") }}
            th.text-right(scope='col', width='12%') {{ $t("ui.win") }} / {{ $t("ui.loss") }}
            th.text-right(scope='col', width='10%') {{ $t("ui.points") }}
            th(scope='col', width='8%') {{ $t("ui.status") }}
          tr.grey(v-if="refundList.length == 0")
            td(colspan="7").text-center
              span {{ $t('message.no_information_available') }}
          tr(v-for="(item, index) in refundList" :class="{ grey: index % 2 === 0 }")
            td.text-center(valign='top') {{index + 1}}
            td.text-left(valign='top')
              div {{ $t("ui.ref_no") }}: {{ item.result_id }}
              div {{ $dayjs(item.bet_time).format("MM/DD/YYYY hh:mm:ss A") }}
            td.text-left(valign='top')
              .bet-info
                .bet-type.blue(v-if="item.remark") {{ $t('m.' + item.remark) }}
                .bet-detail
                  .name {{ $t('m.ROOM_T' + item.room_type) }}
                  .name {{ $t('ui.room_id') }}: {{ item.room_id }}
            td.text-right(valign='top')
              div
                span {{ $numeral(item.bet_member).format("0,0.00") }}
            td.text-right(valign='top')
              div
                span -
                //- span(:class="{ red: parseFloat(item.winlose) < 0 }") {{ $numeral(item.winlose).format("0,0.00") }}
            td.text-right(valign='top')
              div
                span -
                //- span {{ $numeral(item.bet_payout).format("0,0") }}
            td.text-left(valign='top')
              div
                span {{ $t("ui.refund") }}


</template>
<script>
import config from "@/config";
import errors from "@/errors";
import vPagination from "vue-plain-pagination";

export default {
  components: { vPagination },
  props: {
    currentGameResultPage: {
      type: Number
    },
    gameResultList: {
      type: Array,
      default: []
    },
    gameResultSummary: {
      type: Object,
      default: {}
    },
    currSportType: {
      type: String,
      default: ""
    },
    gameResultTotalPages: {
      type: Number
    },
    currentRefundListPage: {
      type: Number
    },
    refundList: {
      type: Array,
      default: []
    },
    refundListSummary: {
      type: Object,
      default: {}
    },
    refundListTotalPage: {
      type: Number
    }
  },
  data() {
    return {
      currentPage: 1,
      bootstrapPaginationClasses: {
        ul: "pagination justify-content-center",
        li: "page-item",
        liActive: "active",
        liDisable: "disabled",
        button: "page-link",
        buttonActive: "active",
        buttonDisable: "disable"
      },
      paginationAnchorTexts: {
        first: "<i class='fas fa-angle-double-left'></i>",
        prev: "<i class='fas fa-angle-left'></i>",
        next: "<i class='fas fa-angle-right'></i>",
        last: "<i class='fas fa-angle-double-right'></i>"
      },
      loading: {
        arcardia: false,
        pgsoft: false,
        pragmatic: false,
        joker: false
      }
    };
  },
  computed: {
    account_id() {
      return this.$store.getters.accountId;
    },
    isTotal() {
      if (this.gameResultSummary != null) {
        return Object.keys(this.gameResultSummary).length > 0;
      } else {
        return false;
      }
    },
    totalWinLose() {
      if (this.gameResultSummary != null) {
        return parseFloat(this.gameResultSummary.winlose);
      } else {
        return 0;
      }
    },
    totalTurnover() {
      if (this.gameResultSummary != null) {
        return parseFloat(this.gameResultSummary.turnover);
      } else {
        return 0;
      }
    },
    totalBetPayout() {
      if (this.gameResultSummary != null) {
        return parseFloat(this.gameResultSummary.bet_payout);
      } else {
        return 0;
      }
    },
    totalJackpotPayout() {
      if (this.gameResultSummary != null) {
        return parseFloat(this.gameResultSummary.jackpot_payout);
      } else {
        return 0;
      }
    },

  },
  mounted() {
    this.currentPage = this.currentGameResultPage;
    this.changedPage(1, "game_result");
  },
  methods: {
    getResult(e) {
      var result = config.r2ResultUrl() + e;
      if (e.includes("LOSG_") || e.includes("LCB_")) {
        var result = config.r3ResultUrl() + e;
      }

      // console.log(e, result);
      return result;
    },
    getResultAR(e) {
      if (this.loading.arcardia) return;
      var id = e.replace("AR_", "");
      // console.log(id);
      var url = "";
      const args = {
        cert: config.arcadeCert,
        transId: id.toString()
      };
      this.loading.arcardia = true;
      this.$http.post(config.arcadePath, args).then(
        res => {
          this.loading.arcardia = false;
          if (res.data) {
            url = res.data.url;
            if (url) {
              window.open(url, "resultar", "top=10,height=600,width=400,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        err => {
          this.loading.arcardia = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultPGS(e) {
      if (this.loading.pgsoft) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        language: "en"
      };
      this.loading.pgsoft = true;
      this.$http.post(config.pgsPath, args).then(
        res => {
          this.loading.pgsoft = false;
          if (res.data) {
            url = res.data.history_url;
            if (url) {
              window.open(url, "resultpgs", "top=10,height=414,width=737,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        err => {
          this.loading.pgsoft = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultPragmatic(e) {
      if (this.loading.pragmatic) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        userid: this.account_id,
        language: "en"
      };
      this.loading.pragmatic = true;
      this.$http.post(config.pragmaticPath, args).then(
        res => {
          this.loading.pragmatic = false;
          if (res.data) {
            url = res.data.history_url;
            if (url) {
              window.open(url, "resultppg", "top=10,height=520,width=800,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        err => {
          this.loading.pragmatic = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultJoker(e) {
      if (this.loading.joker) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        userid: this.account_id,
        language: "en"
      };
      this.loading.joker = true;
      this.$http.post(config.jokerPath, args).then(
        res => {
          this.loading.joker = false;
          if (res.data) {
            url = res.data.history_url;
            if (url) {
              window.open(url, "resultjkr", "top=10,height=520,width=800,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        err => {
          this.loading.joker = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    changedPage: function(pageNo) {
      this.currentPage = pageNo;
      this.$emit("changedPage", pageNo, "game_result");
    }
  }
};
</script>