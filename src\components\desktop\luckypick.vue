<template lang="pug">
  .luckybox
    .row.mt-1
      .col-12
        .d-flex.align-items-center.justify-content-center(style="font-size: 11px; line-height: 1;")
          .luckyslider-label(style="width: 40px;") ODDS
          .flex-fill.d-flex.align-items-center.justify-content-between
            span {{ $numeral(value[0]).format("0,0.00") }}
            span {{ $numeral(value[1]).format("0,0.00") }}
        .d-flex.align-items-center.justify-content-center.mb-2
          .luckyslider-label(style="width: 50px;")
          .luckyslider
            vue-slider(:min="1.01" :max="10.00" :interval="0.01" v-model="value" :adsorb="false" :marks="marks" :included="true" :hide-label="true")
        .d-flex.align-items-center.justify-content-center.mb-2
          .luckyslider-label(style="width: 50px;")
          .flex-fill.d-flex.align-items-center.justify-content-center
            .switch-wrap
              .label ALL
              label.switch
                input(type="checkbox" :checked="bt0" @click="toggleAll()")
                span.slider.round
            .switch-wrap
              .label HDP
              label.switch
                input(type="checkbox" v-model="bt2[0]")
                span.slider.round
            .switch-wrap
              .label O/U
              label.switch
                input(type="checkbox" v-model="bt2[1]")
                span.slider.round
            .switch-wrap
              .label 1X2
              label.switch
                input(type="checkbox" v-model="bt2[2]")
                span.slider.round
      .col-12.d-flex.align-items-center.justify-content-center
        //- button.btn.btn-sm.btn-block.btn-1.btn-warning.text-ellipsis(@click="addPick()") Add Pick
        //- button.btn.btn-sm.btn-block.btn-1.btn-warning.text-ellipsis(@click="randomPick()") Random Pick
        button.btn.btn-sm.btn-block.btn-1.btn-warning(type="button" @click="clearItems()" :disabled="loading" style="width: 48px; overflow: hidden;")
          i.fal.fa-times(v-if="!loading" style="margin-top: 1px;")
          .spinner-grow.spinner-grow-sm.text-white(v-else)
        SpinButton(css="btn btn-sm btn-block btn-2 btn-warning text-ellipsis", @click="addPick()", :text="$t('ui.add_pick')", :loading="loading")
        SpinButton(css="btn btn-sm btn-block btn-2 btn-warning text-ellipsis", @click="randomPick()", :text="$t('ui.random_pick')", :loading="loading")
    .row.mt-1
      .col-12
        .luckyitem.d-flex.align-items-center.justify-content-start.px-2(v-for="(item, index) in data" :class="{ 'luckyeven' : index % 2 == 0 }")
          .d-flex.flex-column.flex-fill.align-self-stretch
            .luckyteam(:class="{ luckytarget: item.homeId == item.betTeamId && !item.display }") {{ item.homeName }}
            .d-flex.align-items-center.justify-content-start
              .luckyvs vs
              .luckyteam(:class="{ luckytarget: item.awayId == item.betTeamId && !item.display }") {{ item.awayName }}
          .d-flex.flex-column.align-items-end.align-self-stretch
            .luckybettype {{ $t("m.BS_" + item.betType) }}
              span(v-if="item.home_running_score != null") &nbsp;[{{ item.home_running_score }} - {{ item.away_running_score }}]
            .d-flex.flex-row.align-items-center.justify-content-end.align-self-stretch
              .luckyodds.luckymuted {{ item.display }}&nbsp;
              .luckyodds.luckytext(v-if="item.ballDisplay2") {{ item.ballDisplay2 }}&nbsp;
              .luckyodds.luckymuted @&nbsp;
              .luckyodds {{ item.fm_odds }}
          .d-flex.flex-column.align-items-start.justify-content-start.align-self-stretch
            .luckydelete.fal.fa-times(@click="deleteItem(index)")
        .luckyitem.d-flex.align-items-center.justify-content-between.px-2(v-if="data != null && data.length > 0")
            .luckyteam {{ $t('ui.mix_parlay') }}
            .luckyodds(style="padding-right: 28px;") {{ $numeral(getTotal()).format('0,0.00[0]') }}

</template>

<script>
import VueSlider from "vue-slider-component";
import "vue-slider-component/theme/antd.css";
import SpinButton from "@/components/ui/SpinButton";
import xhr from "@/library/_xhr-game.js";
import xhrUtils from "@/library/_xhr-utils.js";
import calc from "@/library/_calculation.js";
import { EventBus } from "@/library/_event-bus.js";
import naming from "@/library/_name";

export default {
  components: {
    VueSlider,
    SpinButton
  },
  data: function() {
    return {
      value: [1.01, 5.0],
      loading: false,
      data: null,
      bt0: false,
      bt1: ["HDP", "OU", "1X2"],
      bt2: [true, false, false],
      marks: [1.01, 1.2, 1.4, 1.6, 1.8, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0, 5.5, 6.0, 6.5, 7.0, 7.5, 8.0, 8.5, 9.0, 9.5, 10.0]
    };
  },
  computed: {
    whiteLabel() {
      return this.$store.getters.whiteLabel;
    },
    language() {
      return this.$store.getters.language;
    },
    commType() {
      return this.$store.getters.commType;
    },
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType.toUpperCase();
    },
    defStake() {
      if (this.$store.state.layout.betting.defaultStake != "3") {
        return this.$store.state.layout.betting.defaultStakeAmount;
      } else {
        return 0;
      }
    },
    order() {
      return this.$store.state.layout.order.sports;
    }
  },
  watch: {
    bt2(n, o) {
      if (n[0] && n[1] && n[2]) {
        this.bt0 = true;
      } else if (!n[0] || !n[1] || !n[2]) {
        this.bt0 = false;
      }
    }
  },
  methods: {
    getTotal() {
      if (this.data == null || this.data.length <= 0) {
        return 0;
      }

      var total = 1.0;
      for (var i = 0; i < this.data.length; i++) {
        if (this.data[i].fm_odds) {
          total = total * parseFloat(this.data[i].fm_odds);
        }
      }

      return total;
    },
    ballDisplay2(e) {
      return naming.ballDisplay2(e.ballDisplay, e.giving, e.homeAway, e.betType, this);
    },
    toggleAll() {
      this.bt0 = !this.bt0;
      for (var i = 0; i < this.bt2.length; i++) {
        this.bt2[i] = this.bt0;
      }
    },
    getHomeTeam(e) {
      var r = e["home_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.home_name_en;
    },
    getAwayTeam(e) {
      var r = e["away_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.away_name_en;
    },
    cache() {
      return this.$store.getters.data;
    },
    addPick() {
      if (EventBus.addPick && this.data != null) {
        let data = JSON.parse(JSON.stringify(this.data));
        EventBus.addPick(data, this.oddsType, this.defStake);
        this.clearItems();
      }
    },
    clearItems(e) {
      this.data = [];
    },
    addItem(e) {
      this.data.push(e);
    },
    deleteItem(e) {
      this.data.splice(e, 1);
    },
    randomPick() {
      if (this.loading == true) return;

      var bt = [];
      for (var i = 0; i < this.bt2.length; i++) {
        if (this.bt2[i]) {
          bt.push(this.bt1[i]);
        }
      }

      var args = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        min: this.value[0],
        max: this.value[1],
        bet_type: bt.join(),
        comm_type: this.commType,
        count: "10"
      };

      this.loading = true;
      xhrUtils.prg(args).then(
        res => {
          setTimeout(() => {
            this.loading = false;
            if (res.success == true) {
              this.clearItems();
              var array = [];
              var data = res.data;
              var keys = Object.keys(data);
              var show_digits = ['B','D','F'].includes(this.commType) ? 4 : 3;
              for (var i = 0; i < keys.length; i++) {
                var n = data[keys[i]];
                for (var j = 0; j < n.length; j++) {
                  switch (n[j].bet_type) {
                  case "HDP":
                  case "HDPH":
                    if (n[j].odds_e >= 1.01 && n[j].odds_g >= 1.01) {
                      array.push(n[j]);
                    }
                    break;
                  case "OU":
                  case "OUH":
                    if (n[j].odds_o >= 1.01 && n[j].odds_u >= 1.01) {
                      array.push(n[j]);
                    }
                    break;
                  case "1X2":
                  case "1X2H":
                    if (n[j].odds_1 > 1.05 && n[j].odds_X > 1.05 && n[j].odds_2 > 1.05) {
                      array.push(n[j]);
                    }
                    break;
                  }
                }
              }
              for (var i = array.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [array[i], array[j]] = [array[j], array[i]];
              }

              var mid = [];
              var itm = [];
              var j = 0;
              for (var i = 0; i < array.length; i++) {
                if (j >= 10) {
                  break;
                }

                if (!mid.includes(array[i].match_id)) {
                  array[i].homeName = this.getHomeTeam(array[i]);
                  array[i].awayName = this.getAwayTeam(array[i]);
                  array[i].homeId = array[i].home_team_id;
                  array[i].awayId = array[i].away_team_id;
                  array[i].parlay = 1;
                  array[i].giving = array[i].home_giving;
                  array[i].ballDisplay = array[i].ball_display;
                  array[i].betDisplay = "";
                  array[i].betType = array[i].bet_type;

                  switch (array[i].bet_type) {
                  case "HDP":
                  case "HDPH":
                    if (array[i].odds_e >= this.value[0] && array[i].odds_g >= this.value[0] && array[i].odds_e <= this.value[1] && array[i].odds_g <= this.value[1]) {
                      array[i].target = Math.floor(Math.random() * 2);
                      if (array[i].target == 0) {
                        array[i].pick_odds = array[i].odds_e;
                        array[i].pick_idx = "9";
                        array[i].betTeamId = array[i].giving == 1 ? array[i].awayId : array[i].homeId;
                        array[i].betTeamName = array[i].giving == 1 ? array[i].awayName : array[i].homeName;
                        array[i].origin = array[i].odds_e2;
                        array[i].homeAway = array[i].giving == 1 ? 2 : 1;
                      } else {
                        array[i].pick_odds = array[i].odds_g;
                        array[i].pick_idx = "10";
                        array[i].betTeamId = array[i].giving == 1 ? array[i].homeId : array[i].awayId;
                        array[i].betTeamName = array[i].giving == 1 ? array[i].homeName : array[i].awayName;
                        array[i].origin = array[i].odds_g2;
                        array[i].homeAway = array[i].giving == 1 ? 1 : 2;
                      }
                    } else {
                      if (array[i].odds_e >= this.value[0] && array[i].odds_e <= this.value[1]) {
                        array[i].target = 0;
                        array[i].pick_odds = array[i].odds_e;
                        array[i].pick_idx = "9";
                        array[i].betTeamId = array[i].giving == 1 ? array[i].awayId : array[i].homeId;
                        array[i].betTeamName = array[i].giving == 1 ? array[i].awayName : array[i].homeName;
                        array[i].origin = array[i].odds_e2;
                        array[i].homeAway = array[i].giving == 1 ? 2 : 1;
                      } else {
                        array[i].target = 1;
                        array[i].pick_odds = array[i].odds_g;
                        array[i].pick_idx = "10";
                        array[i].betTeamId = array[i].giving == 1 ? array[i].homeId : array[i].awayId;
                        array[i].betTeamName = array[i].giving == 1 ? array[i].homeName : array[i].awayName;
                        array[i].origin = array[i].odds_g2;
                        array[i].homeAway = array[i].giving == 1 ? 1 : 2;
                      }
                    }
                    array[i].ballDisplay2 = this.ballDisplay2(array[i]);
                    array[i].display = "";
                    array[i].betDisplay = array[i].betTeamName;
                    array[i].fm_odds = calc.fm(array[i].pick_odds.toFixed(3), false, show_digits);
                    mid.push(array[i].match_id);
                    j = j + 1;
                    itm.push(array[i]);
                    break;
                  case "OU":
                  case "OUH":
                    if (array[i].odds_o >= this.value[0] && array[i].odds_u >= this.value[0] && array[i].odds_o <= this.value[1] && array[i].odds_u <= this.value[1]) {
                      array[i].target = Math.floor(Math.random() * 2);
                      if (array[i].target == 0) {
                        array[i].pick_odds = array[i].odds_o;
                        array[i].pick_idx = "12";
                        array[i].betTeamId = array[i].homeId;
                        array[i].betTeamName = array[i].homeName;
                        array[i].betDisplay = this.$t("ui.over");
                        array[i].origin = array[i].odds_o2;
                        array[i].homeAway = 1;
                      } else {
                        array[i].pick_odds = array[i].odds_u;
                        array[i].pick_idx = "11";
                        array[i].betTeamId = array[i].awayId;
                        array[i].betTeamName = array[i].awayName;
                        array[i].betDisplay = this.$t("ui.under");
                        array[i].origin = array[i].odds_u2;
                        array[i].homeAway = 2;
                      }
                    } else {
                      if (array[i].odds_o >= this.value[0] && array[i].odds_o <= this.value[1]) {
                        array[i].target = 0;
                        array[i].pick_odds = array[i].odds_o;
                        array[i].pick_idx = "12";
                        array[i].betTeamId = array[i].homeId;
                        array[i].betTeamName = array[i].homeName;
                        array[i].betDisplay = this.$t("ui.over");
                        array[i].origin = array[i].odds_o2;
                        array[i].homeAway = 1;
                      } else {
                        array[i].target = 1;
                        array[i].pick_odds = array[i].odds_u;
                        array[i].pick_idx = "11";
                        array[i].betTeamId = array[i].awayId;
                        array[i].betTeamName = array[i].awayName;
                        array[i].betDisplay = this.$t("ui.under");
                        array[i].origin = array[i].odds_u2;
                        array[i].homeAway = 2;
                      }
                    }
                    array[i].ballDisplay2 = this.ballDisplay2(array[i]);
                    array[i].display = array[i].betDisplay;
                    array[i].fm_odds = calc.fm(array[i].pick_odds.toFixed(3), false, show_digits);
                    mid.push(array[i].match_id);
                    j = j + 1;
                    itm.push(array[i]);
                    break;
                  case "1X2":
                  case "1X2H":
                    array[i].giving = 0;
                    array[i].ballDisplay = "0";
                    if (
                      array[i].odds_1 >= this.value[0] &&
                      array[i].odds_X >= this.value[0] &&
                      array[i].odds_2 >= this.value[0] &&
                      array[i].odds_1 <= this.value[1] &&
                      array[i].odds_X <= this.value[1] &&
                      array[i].odds_2 <= this.value[1]
                    ) {
                      array[i].target = Math.floor(Math.random() * 3);
                      if (array[i].target == 0) {
                        array[i].pick_odds = array[i].odds_1;
                        array[i].pick_idx = "5";
                        array[i].betTeamId = array[i].homeId;
                        array[i].betTeamName = array[i].homeName;
                        array[i].betDisplay = array[i].betType == "1X2" ? this.$t("m.BT_FT1") : this.$t("m.BT_HT1");
                        // array[i].display = this.$t("ui.home");
                        array[i].origin = array[i].odds_1_ori;
                        array[i].homeAway = 1;
                      } else {
                        if (array[i].target == 1) {
                          array[i].pick_odds = array[i].odds_X;
                          array[i].pick_idx = "6";
                          array[i].betTeamId = null;
                          array[i].betTeamName = null;
                          array[i].betDisplay = array[i].betType == "1X2" ? this.$t("m.BT_FTX") : this.$t("m.BT_HTX");
                          // array[i].display = this.$t("ui.draw");
                          array[i].origin = array[i].odds_X_ori;
                          array[i].homeAway = 3;
                        } else {
                          array[i].pick_odds = array[i].odds_2;
                          array[i].pick_idx = "7";
                          array[i].betTeamId = array[i].awayId;
                          array[i].betDisplay = array[i].betType == "1X2" ? this.$t("m.BT_FT2") : this.$t("m.BT_HT2");
                          // array[i].display = this.$t("ui.away");
                          array[i].origin = array[i].odds_2_ori;
                          array[i].homeAway = 2;
                        }
                      }
                    } else {
                      if (array[i].odds_1 >= this.value[0] && array[i].odds_1 <= this.value[1]) {
                        array[i].target = 0;
                        array[i].pick_odds = array[i].odds_1;
                        array[i].pick_idx = "5";
                        array[i].betTeamId = array[i].homeId;
                        array[i].betTeamName = array[i].homeName;
                        array[i].betDisplay = array[i].betType == "1X2" ? this.$t("m.BT_FT1") : this.$t("m.BT_HT1");
                        // array[i].display = this.$t("ui.home");
                        array[i].origin = array[i].odds_1_ori;
                        array[i].homeAway = 1;
                      } else if (array[i].odds_X >= this.value[0] && array[i].odds_X <= this.value[1]) {
                        array[i].target = 1;
                        array[i].pick_odds = array[i].odds_X;
                        array[i].pick_idx = "6";
                        array[i].betTeamId = null;
                        array[i].betTeamName = null;
                        array[i].betDisplay = array[i].betType == "1X2" ? this.$t("m.BT_FTX") : this.$t("m.BT_HTX");
                        // array[i].display = this.$t("ui.draw");
                        array[i].origin = array[i].odds_X_ori;
                        array[i].homeAway = 3;
                      } else {
                        array[i].target = 1;
                        array[i].pick_odds = array[i].odds_2;
                        array[i].pick_idx = "7";
                        array[i].pick_odds = array[i].odds_2;
                        array[i].pick_idx = "7";
                        array[i].betTeamId = array[i].awayId;
                        array[i].betDisplay = array[i].betType == "1X2" ? this.$t("m.BT_FT2") : this.$t("m.BT_HT2");
                        // array[i].display = this.$t("ui.away");
                        array[i].origin = array[i].odds_2_ori;
                        array[i].homeAway = 2;
                      }
                    }
                    array[i].display = array[i].betDisplay;
                    array[i].fm_odds = calc.fm(array[i].pick_odds.toFixed(3), false, show_digits);
                    mid.push(array[i].match_id);
                    j = j + 1;
                    itm.push(array[i]);
                    break;
                  }
                }
              }

              this.data = itm;

              // this.data.sort((a, b) => a.match_id < b.match_id ? -1 : 1);
              // var lst = {};
              // for (var i = 0; i < itm.length; i++) {
              //   lst[itm[i].match_id] = itm[i];
              // }
              // for (var j in lst) {
              //   this.addItem(lst[j]);
              // }
            }
          }, 1000);
          if (res) {
          } else {
            this.error = this.$t("error." + res.status);
          }
        },
        err => {
          this.loading = false;
          this.error = this.$t("error." + err.status);
        }
      );

      setTimeout(() => {
        if (EventBus.setMenu0) {
          EventBus.setMenu0("parlay", false);
        }
      }, 3000);
    }
  }
};
</script>

<style scoped></style>
