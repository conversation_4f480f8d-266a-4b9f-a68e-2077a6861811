<template lang="pug">
.col-12.hx-more-row
  .card.hx-card
    .card-header(
      :id="'heading-cshtft-' + uid"
      data-toggle="collapse"
      :data-target="'#collapse-cshtft-' + uid"
      :aria-controls="'collapse-cshtft-' + uid"
      aria-expanded=true
      :class="layoutIndex == 3 ? 'live': 'non-live'"
    )
      i.fad.fa-chevron-circle-down
      span.header-bettype {{ $t("m.BT_CSHTFT") }}
    .collapse.show(
      :aria-labelledby="'heading-cshtft-' + uid"
      :id="'collapse-cshtft-' + uid"
    )
      //- div {{ Object.keys(details) }}
      //- div(style="color: red;") {{ scores.running }}
      .card-body.p-0(:id="'accordian-cshtft-' + uid")
        .hx-table.hx-table-xhtft-head(
          :class="marketType == 3 ? 'live' : ''"
          )
          .d-flex.w-100
            .d-flex.flex-column(style="width: 235px;")
              .hx-xhtft-caption {{ $t('ui.half_time') }}
              button.btn.dropdown-toggle.btn-toggle-field(
                type="button"
                data-toggle="dropdown"
                aria-expanded="false"
                ) {{ ht }}
              .dropdown-menu.hx-dropdown-menu
                .dropdown-item.hx-dropdown-item(
                  @click="selectHalfTime(item)"
                  v-for="(item, index) in htKeys"
                  ) {{ item }}
            .d-flex.flex-column(style="width: 235px;")
              .hx-xhtft-caption {{ $t('ui.full_time') }}
              button.btn.dropdown-toggle.btn-toggle-field(
                type="button"
                data-toggle="dropdown"
                aria-expanded="false"
                ) {{ CSHTFTX[ft] }}
              .dropdown-menu.hx-dropdown-menu-2(v-if="details[ht] && details[ht][78] && details[ht][78].length > 0")
                .dropdown-item.hx-dropdown-item(
                  @click="selectFullTime(item)"
                  v-for="(item, index) in details[ht][78]"
                  ) {{ CSHTFTX[item] }}
            .d-flex.flex-column.flex-fill
              .hx-xhtft-caption {{ $t('ui.odds') }}
              .hx-xhtft-value
                oddsColumnItem(:odds="details[ht]" :idx="ft" :typ="oddsType")
            .d-flex.flex-column.align-items-center.justify-content-center(style="width: 118px;")
              .hx-xhtft-caption &nbsp;
              button.btn.btn-toggle.text-ellipsis(
                :class="view_all ? 'btn-toggle-shown' : ''"
                @click="toggleViewAll()"
              )
                span {{ $t("ui.view_all") }}
                .fa.fa-chevron-up.ml-1(v-if="view_all")
                .fa.fa-chevron-down.ml-1(v-else)

        .hx-table.hx-more-bet.hx-table-xhtft(
          :class="marketType == 3 ? 'live' : ''"
          )
          .hx-wrap-xhtft(
            v-if="view_all"
            )
            template(v-for="(i,n) in ftKeys")
              template(v-if="!scores.running.includes(i)")
                .hx-item-xhtft(v-for="j in details[i][78]")
                  .hx-field-xhtft(:class="n % 2 == 0 ? '' : 'hx-alter-xhtft'")
                    .hx-label-xhtft {{ i }} /  {{ CSHTFTX[j] }}
                    oddsColumnItem(:odds="details[i]" :idx="j" :typ="oddsType" cls="hx-value-xhtft")
</template>

<script>
import oddsColumnItem from "@/components/desktop/main/xtable/oddsColumnItem";
import config from "@/config";

export default {
  components: {
    oddsColumnItem,
  },
  props: {
    uid: {
      type: String,
    },
    details: {
      type: Object,
    },
    matchId: {
      type: Number,
    },
    leagueId: {
      type: Number,
    },
    marketType: {
      type: Number,
    },
    sportsType: {
      type: Number,
    },
    betType: {
      type: String,
    },
    layoutIndex: {
      type: Number,
    },
  },
  data() {
    return {
      view_all: false,
      ht: null,
      ft: null,
    };
  },
  computed: {
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
    htKeys() {
      var A = this.scores.running;
      var B = Object.keys(this.details);

      const ASet = new Set(A);
      const result = [];
      for (let i = 0; i < B.length; i++) {
        if (!ASet.has(B[i])) {
          result.push(B[i]);
        }
      }

      return result;
    },
    ftKeys() {
      return Object.keys(this.details);
    },
    CSHTFTX() {
      return config.CSHTFTX;
    },
    match() {
      return this.$store.getters.data.match[this.matchId];
    },
    scores() {
      var home_score = -1;
      var away_score = -1;
      var score = this.match[11];

      if (score && score.trim() !== "") {
        var result = score.split(" - ");
        if (result.length === 2) {
          home_score = isNaN(parseInt(result[0].trim(), 10)) ? -1 : parseInt(result[0].trim(), 10);
          away_score = isNaN(parseInt(result[1].trim(), 10)) ? -1 : parseInt(result[1].trim(), 10);
        }
      }

      // console.log(home_score, away_score);

      var running = [];
      const t = config.CSHTFTX;

      for (var t1 = 0; t1 < t.length; t1++) {
        var t2 = t[t1].split("-");
        if (t2.length === 2) {
          var th = isNaN(parseInt(t2[0].trim(), 10)) ? -1 : parseInt(t2[0].trim(), 10);
          var ta = isNaN(parseInt(t2[1].trim(), 10)) ? -1 : parseInt(t2[1].trim(), 10);
          if (th < home_score) {
            running.push(t[t1]);
          } else {
            if (th == home_score && ta < away_score) {
              running.push(t[t1]);
            }
          }
        }
      }

      var r = {
        home_score,
        away_score,
        running,
      };

      // console.log(r);

      return r;
    },
  },
  mounted() {
    this.selectHalfTime(this.htKeys[0]);
  },
  methods: {
    toggleViewAll() {
      this.view_all = !this.view_all;
    },
    selectHalfTime(e) {
      this.ht = e;
      this.ft = this.details[this.ht][78][0];
    },
    selectFullTime(e) {
      this.ft = e;
    },
  },
};
</script>
