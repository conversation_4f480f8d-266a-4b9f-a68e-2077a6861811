<template lang="pug">
#modal-ads.modal(tabindex="-1", role="dialog", ref="adsModal")
  .ads-modal.modal-dialog.modal-dialog-centered
    //-(role="document" style="max-width: 600px !important;")
    .modal-content.m-0.p-0
      //- (style="width: 600px !important; height: 600px !important;")
      .modal-body.m-0.p-0
        //- .scroller
        //-   .inner-content
        //-     a(href="javascript:void(0);" @click="bringMe")
        //-       img.img-fluid(src="/images/tournament_eurocup.png")
        #player.frame(v-if="youtube")
          //- src="https://www.youtube.com/embed/BZXeBK1ndCo?autoplay=1&controls=1&playlist=BZXeBK1ndCo&enablejsapi=1&loop=1&modestbranding=1&playsinline=1&mute=1&fs=1&rel=0&hd=1"
          iframe(
            src="https://www.youtube.com/embed/XfIniZWihUA?autoplay=1&controls=1&playlist=XfIniZWihUA&enablejsapi=1&loop=1&modestbranding=1&playsinline=1&mute=1&fs=1&rel=0&hd=1"
            frameborder="0"
            allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            loading="lazy"
            )
        .reminder(@click="closeMe()")
          i.fas.fa-times
          span {{ $t('message.dnsta') }}
</template>

<script>

export default {
  data() {
    return {
      youtube: true,
    };
  },
  destroyed: function () {
    $(this.$refs.adsModal).off("hidden.bs.modal", this.hideMe);
  },
  mounted: function () {
    this.$store.dispatch("layout/triggerRemoveFields");
    $(this.$refs.adsModal).on("hidden.bs.modal", this.hideMe);
    setTimeout(() => {
      $(this.$refs.adsModal).modal("show");
    }, 1000);
  },
  methods: {
    bringMe() {
      $(this.$refs.adsModal).modal("hide");
      window.open('/tournament2','tourplus','height=700,width=1440,status=no,toolbar=no,menubar=no,location=no');
    },
    hideMe() {
      this.youtube = false;
    },
    closeMe() {
      this.$store.dispatch("layout/setAdsPopup", false);
      $(this.$refs.adsModal).modal("hide");
    },
  },
};
</script>
