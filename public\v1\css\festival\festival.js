function rain1() {
  TweenLite.set("#effect-rain1", { perspective: 600 });
  TweenLite.set(".effect-rain1 img", { xPercent: "-50%", yPercent: "-50%" });

  var total = 40;
  var warp = document.getElementById("effect-rain1"),
    w = window.innerWidth,
    h = window.innerHeight;

  for (i = 0; i < total; i++) {
    var Div = document.createElement("div");
    TweenLite.set(Div, { attr: { class: "effect-rain1" }, x: R(0, w), y: R(-200, -150), z: R(-200, 200) });
    warp.appendChild(Div);
    animm(Div);
  }

  function animm(elm) {
    TweenMax.to(elm, R(6, 15), { y: h + 100, ease: Linear.easeNone, repeat: -1, delay: -5 });
    TweenMax.to(elm, R(2, 8), { x: "+=100", rotationZ: R(0, 180), repeat: -1, yoyo: true, ease: Sine.easeInOut });
    TweenMax.to(elm, R(2, 8), { rotationX: R(0, 360), rotationY: R(0, 360), repeat: -1, yoyo: true, ease: Sine.easeInOut, delay: -5 });
  }

  function R(min, max) {
    return min + Math.random() * (max - min);
  }
}

function rain2() {
  TweenLite.set("#effect-rain2", { perspective: 600 });
  TweenLite.set(".effect-rain2 img", { xPercent: "0%", yPercent: "0%" });

  var total = 10;
  var warp = document.getElementById("effect-rain2"),
    w = window.innerWidth,
    h = window.innerHeight;

  for (i = 0; i < total; i++) {
    var Div = document.createElement("div");
    TweenLite.set(Div, { attr: { class: "effect-rain2" }, x: R(0, w), y: R(-200, -100), z: R(-200, 200) });
    warp.appendChild(Div);
    animm(Div);
  }

  function animm(elm) {
    TweenMax.to(elm, R(6, 15), { y: h + 100, ease: Linear.easeNone, repeat: -1, delay: -5 });
    TweenMax.to(elm, R(2, 8), { x: "+=100", rotationZ: R(0, 180), repeat: -1, yoyo: true, ease: Sine.easeInOut });
    TweenMax.to(elm, R(2, 8), { rotationX: R(0, 0), rotationY: R(0, 0), repeat: -1, yoyo: true, ease: Sine.easeInOut, delay: -5 });
  }

  function R(min, max) {
    return min + Math.random() * (max - min);
  }
}
