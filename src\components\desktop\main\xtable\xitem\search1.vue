<template lang="pug">
#new-searchbar.filter-item.searchbar.flex-fill
  .input-group
    .input-group-prepend
      .input-group-text
        i.fas.fa-search
    input.form-control(type="text", :placeholder="$t('ui.search_team')", v-model="search1", @keyup.enter="handleSearch")
    .input-group-prepend
      span.input-group-text.clickable(v-if="search1", @click="handleSearch")
        i.fal.fa-check
      span.input-group-text.clickable(v-if="search", @click="clear")
        i.fal.fa-times
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";

export default {
  props: {},
  data() {
    return {
      search1: ""
    };
  },
  computed: {
    search() {
      return this.$store.getters.search;
    },
    menu1() {
      return this.$store.getters.menu1;
    }
  },

  mounted() {
    // this.clear();
    this.search1 = "";
    this.$store.dispatch("layout/setSearch", this.search1);
  },
  methods: {
    handleSearch() {
      this.$store.dispatch("layout/setSearch", this.search1);
      if (this.search1 == "" || this.search1 == null) {
        this.setMenu("handleSearch");
      } else {
        this.setMenu2("search");
      }
    },
    clear() {
      this.search1 = "";
      this.$store.dispatch("layout/setSearch", this.search1);
      this.setMenu("clear");
      EventBus.$emit("INVALIDATE");
    },
    setMenu(e) {
      // console.log("search1.setMenu", e);
      var mi5 = {};
      mi5["menuX"] = false;
      mi5["menuY"] = "0";
      mi5["menu0"] = "all";
      mi5["menu1"] = "today";
      mi5["menu3"] = "hdpou";
      this.$store.dispatch("layout/setSelectedDays", "0");
      this.$store.dispatch("layout/resetSelectLeague");
      mi5["src"] = "search.setMenu";
      this.$store.dispatch("layout/setMenuItems", mi5);
      EventBus.$emit("INVALIDATE");
    },
    setMenu2() {
      // console.log("search1.setMenu2");
      var mi5 = {};
      mi5["menuX"] = false;
      mi5["menuY"] = "0";
      mi5["menu0"] = "search";
      mi5["menu1"] = "today";
      mi5["menu3"] = "hdpou";
      this.$store.dispatch("layout/setSelectedDays", "0");
      this.$store.dispatch("layout/resetSelectLeague");
      mi5["src"] = "search.setMenu2";
      this.$store.dispatch("layout/setMenuItems", mi5);
      EventBus.$emit("INVALIDATE");
    }
  }
};
</script>
