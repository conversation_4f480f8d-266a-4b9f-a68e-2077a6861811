import Vue from "vue";
import VueI18n from "vue-i18n";
import en from "@/locales/en";
import cn from "@/locales/cn";
import vi from "@/locales/vi";
import th from "@/locales/th";
import id from "@/locales/id";
import kr from "@/locales/kr";
import my from "@/locales/my";
import jp from "@/locales/jp";
import tw from "@/locales/tw";

import config from "@/config";

Vue.use(VueI18n);

const messages = {
  en: en,
  cn: cn,
  vi: vi,
  th: th,
  id: id,
  kr: kr,
  my: my,
  jp: jp,
  tw: tw
};

const i18n = new VueI18n({
  locale: "en",
  fallbackLocale: "en",
  messages: messages,
  silentTranslationWarn: true,
  numberFormats: {
    en: {
      currency: {
        style: "currency",
        currency: "MYR",
        currencyDisplay: "code"
      }
    }
  },
  dateTimeFormats: {
    en: {
      short: {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false
      },
      long: {
        year: "numeric",
        month: "short",
        day: "numeric",
        weekday: "short",
        hour: "numeric",
        minute: "numeric",
        second: "numeric",
        hour12: true
      }
    }
  }
});

export default i18n;

// for (var n in i18n.messages) {
//   for (var m in i18n.messages[n]) {
//     console.log(n + "/" + m + ".json", JSON.stringify(i18n.messages[n][m], undefined, 2));
//   }
// }
