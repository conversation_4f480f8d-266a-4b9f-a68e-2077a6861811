<template lang="pug">
.col-12.hx-more-row
  .card.hx-card
    .card-header(
      :id="'heading-etghtft-' + uid"
      data-toggle="collapse"
      :data-target="'#collapse-etghtft-' + uid"
      :aria-controls="'collapse-etghtft-' + uid"
      aria-expanded=true
      :class="layoutIndex == 3 ? 'live': 'non-live'"
    )
      i.fad.fa-chevron-circle-down
      span.header-bettype {{ $t("m.BT_ETGHTFT") }}
    .collapse.show(
      :aria-labelledby="'heading-etghtft-' + uid"
      :id="'collapse-etghtft-' + uid"
    )
      //- div(style="color: blue;") {{ htKeys }} - {{ ftKeys }}
      //- div(style="color: green;") {{ ht }} - {{ ft }}
      //- div(style="color: red;") {{ scores }}
      .card-body.p-0(:id="'accordian-etghtft-' + uid")
        .hx-table.hx-table-xhtft-head(
          :class="marketType == 3 ? 'live' : ''"
          )
          .d-flex.w-100
            //- Half Time dropdown
            .d-flex.flex-column(style="width: 235px;")
              .hx-xhtft-caption {{ $t('ui.half_time') }}
              button.btn.dropdown-toggle.btn-toggle-field(
                type="button"
                data-toggle="dropdown"
                aria-expanded="false"
                ) {{ ht }}
              .dropdown-menu.hx-dropdown-menu
                .dropdown-item.hx-dropdown-item(
                  @click="selectHalfTime(item)"
                  v-for="(item, index) in htKeys"
                  ) {{ item }}
            //- Full Time dropdown
            .d-flex.flex-column(style="width: 235px;")
              .hx-xhtft-caption {{ $t('ui.full_time') }}
              button.btn.dropdown-toggle.btn-toggle-field(
                type="button"
                data-toggle="dropdown"
                aria-expanded="false"
                ) {{ ETGHTFTX[ft] }}
              .dropdown-menu.hx-dropdown-menu-2(v-if="details[ht] && details[ht][78] && details[ht][78].length > 0")
                .dropdown-item.hx-dropdown-item(
                  @click="selectFullTime(item)"
                  v-for="(item, index) in details[ht][78]"
                  ) {{ ETGHTFTX[item] }}
            //- Odds column
            .d-flex.flex-column.flex-fill
              .hx-xhtft-caption {{ $t('ui.odds') }}
              .hx-xhtft-value
                oddsColumnItem(:odds="details[ht]" :idx="ft" :typ="oddsType")
            //- View all button
            .d-flex.flex-column.align-items-center.justify-content-center(style="width: 118px;")
              .hx-xhtft-caption &nbsp;
              button.btn.btn-toggle.text-ellipsis(
                :class="view_all ? 'btn-toggle-shown' : ''"
                @click="toggleViewAll()"
              )
                span {{ $t("ui.view_all") }}
                .fa.fa-chevron-up.ml-1(v-if="view_all")
                .fa.fa-chevron-down.ml-1(v-else)

        .hx-table.hx-more-bet.hx-table-xhtft(
          :class="marketType == 3 ? 'live' : ''"
          )
          .hx-wrap-xhtft(
            v-if="view_all"
            )
            template(v-for="(i,n) in ftKeys")
              template(v-if="!scores.running.includes(i)")
                .hx-item-xhtft(v-for="j in details[i][78]")
                  .hx-field-xhtft(:class="n % 2 == 0 ? '' : 'hx-alter-xhtft'")
                    .hx-label-xhtft {{ i }} /  {{ ETGHTFTX[j] }}
                    oddsColumnItem(:odds="details[i]" :idx="j" :typ="oddsType" cls="hx-value-xhtft")
</template>

<script>
import oddsColumnItem from "@/components/desktop/main/xtable/oddsColumnItem";
import config from "@/config";

export default {
  components: {
    oddsColumnItem,
  },
  props: {
    uid: {
      type: String,
    },
    details: {
      type: Object,
    },
    matchId: {
      type: Number,
    },
    leagueId: {
      type: Number,
    },
    marketType: {
      type: Number,
    },
    sportsType: {
      type: Number,
    },
    betType: {
      type: String,
    },
    layoutIndex: {
      type: Number,
    },
  },
  data() {
    return {
      view_all: false,
      ht: null,
      ft: null,
    };
  },
  computed: {
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
    htKeys() {
      var A = this.scores.running;
      var B = Object.keys(this.details);

      const ASet = new Set(A);
      const result = [];
      for (let i = 0; i < B.length; i++) {
        if (!ASet.has(B[i])) {
          result.push(B[i]);
        }
      }

      return result;
    },
    ftKeys() {
      return Object.keys(this.details);
    },
    ETGHTFTX() {
      return config.ETGHTFTX;
    },
    match() {
      return this.$store.getters.data.match[this.matchId];
    },
    scores() {
      var home_score = -1;
      var away_score = -1;
      var score = this.match[11];

      if (score && score.trim() !== "") {
        var result = score.split(" - ");
        if (result.length === 2) {
          home_score = isNaN(parseInt(result[0].trim(), 10)) ? -1 : parseInt(result[0].trim(), 10);
          away_score = isNaN(parseInt(result[1].trim(), 10)) ? -1 : parseInt(result[1].trim(), 10);
        }
      }

      var total_goal = home_score + away_score;
      var running = [];

      // Generate half-time scores that should be excluded (less than total_goal)
      if (total_goal <= 7) {
        for (var ht = 0; ht < total_goal; ht++) {
          running.push(ht.toString());
        }
      } else {
        // For total_goal > 7, exclude 0-6 only, "7+" should be available
        for (var ht = 0; ht < 7; ht++) {
          running.push(ht.toString());
        }
      }

      return {
        home_score,
        away_score,
        total_goal,
        running,
      };
    },
  },
  mounted() {
    // Set initial ht and ft based on total_goal
    var total_goal = this.scores.total_goal;
    var initial_ht = total_goal >= 0 ? (total_goal >= 7 ? "7+" : total_goal.toString()) : "0";
    
    // Check if the calculated ht exists in htKeys, otherwise use first available
    if (this.htKeys.includes(initial_ht)) {
      this.selectHalfTime(initial_ht);
    } else if (this.htKeys.length > 0) {
      this.selectHalfTime(this.htKeys[0]);
    }
  },
  methods: {
    toggleViewAll() {
      this.view_all = !this.view_all;
    },
    selectHalfTime(e) {
      this.ht = e;
      this.ft = this.details[this.ht][78][0];
    },
    selectFullTime(e) {
      this.ft = e;
    },
  },
};
</script>
