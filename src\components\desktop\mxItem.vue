<template lang="pug">
  .main-item(v-if="show" :class="source.ptype == 30 ? ['c-match', source.categoryId, source.groupId, 'collapse', grouper(source.groupId) == false ? 'show' : ''] : ''")
    template(v-if="source.ptype == 10")
      .hx-market(:id="source.id" @click="toggleGroup(source.id)")
        hdpoumx(:source="source")
    template(v-if="source.ptype == 20")
      league(:source="source")
    template(v-if="source.ptype == 30")
      template(v-if="menuX && menu1 == 'parlay' && menu3 == 'parlay'")
        hdpouMMOX(
          v-if="source.betTypeId == 'hdpou' && [1].includes(source.sportsId) && source.hasOwnProperty('details')"
          :index="index"
          :source="source"
          :single="single"
        )
</template>

<script>
import league from "@/components/desktop/main/xheader/league";
import hdpoumx from "@/components/desktop/main/xheader/hdpoumx";
import hdpouMMOX from "@/components/desktop/main/mmo/hdpouMMOX";

export default {
  components: {
    league,
    hdpoumx,
    hdpouMMOX,
  },
  props: {
    index: {
      type: Number
    },
    source: {
      type: Object,
      default() {
        return {};
      }
    },
    single: {
      type: Boolean,
      default: false
    },
    lmc: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      timer: null,
      show: false
    };
  },
  computed: {
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
    parlayMode() {
      return this.$store.state.cache.parlayMode;
    },
    menuX() {
      return this.$store.state.layout.menuX;
    },
    menu1() {
      return this.$store.state.layout.menu1;
    },
    menu3() {
      return this.$store.state.layout.menu3;
    },
    pageType() {
      return this.$store.getters.pageDisplay.pageType;
    },
    mmoType() {
      return this.$store.getters.pageDisplay.mmoType;
    },
    selectLeague() {
      return this.$store.getters.selectLeague;
    },
    leagueFiltered() {
      return this.$store.state.layout.leagueFiltered;
    }
  },
  mounted() {
    if (this.source.marketId == 3) {
      this.show = true;
    } else {
      if (this.$store.getters.menu1 == "today") {
        var timing = 500;
        if (this.$store.getters.menu2 == 1 && !["orz", "parlay"].includes(this.$store.getters.menu3)) {
          if (this.lmc > 10) {
            timing = 2500 + (this.lmc * 5);
          }
        }
        setTimeout(() => {
          this.show = true;
        }, timing);
      } else {
        this.show = true;
      }
    }
  },
  methods: {
    grouper(e) {
      var test = $("#" + e).hasClass("collapsed");
      return test;
    },
    toggleGroup(e) {
      var ln = $(".c-league." + e);
      var m = $(".c-match." + e);

      var elem = $("#" + e);
      if (elem.hasClass("collapsed")) {
        elem.removeClass("collapsed").attr("aria-expanded", "true");
        ln.removeClass("collapsed").attr("aria-expanded", "true");
        setTimeout(() => {
          m.addClass("show");
        }, 100);
      } else {
        elem.addClass("collapsed").attr("aria-expanded", "false");
        ln.addClass("collapsed").attr("aria-expanded", "false");
        setTimeout(() => {
          m.removeClass("show");
        }, 100);
      }
    }
  }
};
</script>
