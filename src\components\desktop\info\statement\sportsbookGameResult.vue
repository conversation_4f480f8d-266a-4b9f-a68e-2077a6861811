<template lang="pug">
div
  ul.nav.nav-tabs(role='tablist')
    li.nav-item
      a#tab-betlist.nav-link.active(data-toggle='tab', href='#betlist', role='tab', aria-controls='betlist', aria-selected='true') {{ $t("ui.bet_list") }}
    li.nav-item
      a#tab-betsummary.nav-link(
        @click="$emit('getSummary')"
        data-toggle='tab', href='#betsummary', role='tab', aria-controls='betsummary', aria-selected='false') {{ $t("ui.bet_summary") }}
    li.nav-item
      a#tab-betreject.nav-link(
        @click="$emit('getCancelledBet')"
        data-toggle='tab', href='#betreject', role='tab', aria-controls='betreject', aria-selected='false') {{ $t("ui.bet_reject") }}
  .tab-content
    #betlist.tab-pane.show.active(role='tabpanel', aria-labelledby='tab-betlist' )
      table.table-info(width='100%' id="statement-accordion")
        tbody(v-if="ploading")
          tr
            .empty.match-info.text-center.p-4
              i.fad.fa-spinner.fa-spin
        tbody(v-else)
          tr
            th.text-center(scope='col', width='5%') {{ $t("ui.no/") }}
            th.text-left(scope='col', width='20%') {{ $t("ui.trans_time") }}
            th.text-left(scope='col', width='42%') {{ $t("ui.event") }}
            th.text-right(scope='col', width='7%') {{ $t("ui.odds") }}
            th.text-right(scope='col', width='8%') {{ $t("ui.stake") }}
            th.text-right(scope='col', width='10%') {{ $t("ui.win") }} / {{ $t("ui.loss") }}
            th.text-left(scope='col', width='8%') {{ $t("ui.status") }}
          tr.grey(v-if="gameResultList.length == 0")
            td(colspan="7").text-center
              span {{ $t('message.no_information_available') }}
          tr(v-for="(item, index) in gameResultList" :class="{ grey: index % 2 === 0 }")
            td.text-center(valign='top') {{ ((currentPage - 1) * $store.getters.pageSize + index + 1) }}
            td.text-left(valign='top')
              div(v-if="item.ori_result_id") {{ $t("ui.ref_no") }}:
                span &nbsp;{{ item.ori_result_id }}
                span.text-uppercase.text-danger &nbsp;({{ $t("ui.void") }})
              div(v-else) {{ $t("ui.ref_no") }}: {{ item.result_id }}
              div {{ $dayjs(item.bet_time).format("MM/DD/YYYY hh:mm:ss A") }}
            td.text-left(valign='top')
              .bet-info
                template(v-if="racingList.includes(item.sports_type)")
                  .bet-type.blue {{ sports[item.sports_type] }}
                template(v-else)
                  .bet-type.blue(v-if="item.bet_type != 'PARLAY'") {{ sports[item.sports_type] }} - {{ getBetTypeName(item.bet_type) }}
                .d-flex.justify-content-between.align-content-center(v-if="item.bet_type == 'PARLAY'" :id="'heading-' + item.result_id")
                  .bet-type.blue(v-if="item.bet_type_share == 'PARLAYMMO'") {{ $t("ui.mmo_parlay") }}
                    span.black.font-weight-bolder.ml-1(v-if="item.bet_type == 'PARLAY'") @ {{ item.odds_display_bet }}
                  .bet-type.blue(v-else) {{ getBetTypeName(item.bet_type) }}
                    span.black.font-weight-bolder.ml-1(v-if="item.bet_type == 'PARLAY'") @ {{ item.odds_display_bet }}
                  .bg-blue.x-blue.collapsed(data-toggle="collapse" :data-target="'#collapse-' + item.result_id" role="button" aria-expanded="false"
                    :aria-controls="'collapse-' + item.result_id" @click="getParlayDetails(item.result_id)" :ref="'collapse-' + item.result_id")
                    i.far.fa-chevron-up
                .bet-detail.blue.pl-2
                  template(v-if="racingList.includes(item.sports_type)")
                    template(v-if="racingList1.includes(item.sports_type)")
                      .name(v-if="['OU','OE'].includes(item.bet_type)")
                        | {{ $t("m.GH_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                      .name(v-else)
                        | {{ $t("m.GH_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}
                    template(v-if="racingList2.includes(item.sports_type)")
                      .name(v-if="['OU','OE'].includes(item.bet_type)")
                        | {{ $t("m.GC_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                      template(v-else)
                        .name(v-if="['CS'].includes(item.bet_type)")
                          | {{ item.criteria1 }}
                        .name(v-else)
                          | {{ $t("m.GC_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}
                    template(v-if="racingList3.includes(item.sports_type)")
                      .name(v-if="['OU','OE'].includes(item.bet_type)")
                        | {{ $t("m.GX_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                      .name(v-else)
                        | {{ $t("m.GX_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}


                  template(v-else)
                    .name {{ getBetDetail(item) }}
                      //- sup.text-muted &nbsp;(1)
                      template(v-if="['1X2OU', 'DCOU', 'OUOE'].includes(item.bet_type)") &nbsp;({{ item.criteria2 }})
                    .oddsdetail
                      .d-flex.justify-content-start
                        template(v-if="isMMO(item)")
                          .selector-name(v-if="['HDP', 'HDPH', 'OU', 'OUH', '1X2HDP', '1X2HDPH'].includes(item.bet_type)") {{ ballDisplayMMO(item) }}({{ $numeral(item.criteria2).format("0") }})
                        template(v-else)
                          .selector-name(v-if="['HDP', 'HDPH', 'OU', 'OUH', '1X2HDP', '1X2HDPH'].includes(item.bet_type)") {{ ballDisplay(item) }}
                        template(v-if="item.market_type == 3")
                          span(v-if="item.special != null") [{{ item.special.bet_home_score_ht }}-{{ item.special.bet_away_score_ht }}]
                          span(v-if="item.home_running_score != null") [{{ item.home_running_score }}-{{ item.away_running_score }}]
              .bet-list-scroll.match-info.collapse(v-if="item.bet_type == 'PARLAY'" :id="'collapse-' + item.result_id" :data-parent="'#statement-accordion'" :aria-labelledby="'heading-' + item.result_id")
                .empty.match-info.white.text-center(v-if="loading")
                  i.fad.fa-spinner.fa-spin
                betDetails(:items="parlayItems")
              .match-info.d-flex.flex-column.pl-2
                template(v-if="racingList.includes(item.sports_type)")
                  template(v-if="racingList1.includes(item.sports_type)")
                    .name-home {{ item.home_team_name }} &nbsp;
                  template(v-if="racingList2.includes(item.sports_type)")
                    .name-home
                      template(v-if="['ML','1X2'].includes(item.bet_type)")
                        .name-home(v-if="item.home_away == 1") {{ item.home_team_name }} &nbsp;
                        .name-home(v-else) {{ item.away_team_name }} &nbsp;
                      template(v-if="['OU','OE'].includes(item.bet_type)")
                        .name-home {{ item.home_team_name }} &nbsp;
                  template(v-if="racingList3.includes(item.sports_type)")
                    .name-home {{ item.home_team_name }} &nbsp;
                  .name-league(v-if="item.match_time") (No. {{ $dayjs(item.match_time).format("MMDDhhmm") }})

                template(v-else)
                  .name-home {{ item.home_team_name }}
                  .name-away(v-if="item.bet_type != 'OR'") {{ item.away_team_name }}
                  .name-league.font-weight-bold {{ item.league_name }}
            td.text-right(valign='top')
              div(:class="{ red: parseFloat(item.odds_display) < 0 }") {{ item.odds_display == 0 ? item.odds_display : formatOddsDisplay(item.odds_display, item.bet_type) }}
              div(v-if="isMMO(item) || item['bet_type_share'] == 'PARLAYMMO'") {{ $t(odds[5]) }}
              div(v-else) {{ $t(odds[item.odds_type]) }}
            td.text-right(valign='top') {{ $numeral(item.bet_member).format("0,0.00") }}
            td.text-right(valign='top')
              div
                span(:class="{ red: parseFloat(item.winlose) < 0 }") {{ $numeral(item.winlose).format("0,0.00") }}
              div {{  $numeral(item.member_commission).format("0,0.00") }}
            td.text-left(valign='top')
              div {{ parseFloat(item.winlose) != 0 ? (parseFloat(item.winlose) < 0 ? $t("ui.lost") : $t("ui.won")) : $t("ui.draw") }}
              template(v-if="!racingList.includes(item.sports_type)")
                .icon-info(data-toggle='modal', data-target='#modal-result', title='Result' @click="getSelectedMatch(item)")
                  i.fad.fa-poll
              div
      table.table-total(width='100%' v-if="isTotal")
        tbody
          tr
            td.text-right(valign='top' width='81%') {{ $t("ui.subtotal") }} ({{ parseFloat(gameResultSummary.winlose) < 0 ? $t("ui.lost") : $t("ui.won") }})
            td.text-right(valign='top' width='10%')
              span(
                :class="{ red: parseFloat(gameResultSummary.winlose) < 0 }"
                ) {{ $numeral(gameResultSummary.winlose).format("0,0.00") }}
            td.text-right(valign='top' width='8%')  
          tr
            td.text-right(valign='top') {{ $t("ui.subtotal") }} ({{ $t("ui.commission") }})
            td.text-right(valign='top') {{ $numeral(gameResultSummary.member_commission).format("0,0.00") }}
            td  
          tr
            td.text-right(valign='top') {{ $t("ui.total") }}
            td.text-right(valign='top')
              span(
                :class="{ red: total < 0 }"
                ) {{ $numeral(total).format("0,0.00") }}
            td
      .mt-2
        v-pagination(
          v-model="currentPage"
          :page-count="gameResultTotalPages"
          :classes="bootstrapPaginationClasses"
          :labels="paginationAnchorTexts"
          @input="changedPage($event, 'game_result')"
          v-if="gameResultTotalPages"
        )
    #betsummary.tab-pane(role='tabpanel', aria-labelledby='tab-betsummary')
      table.table-info(width='100%')
        tbody
          tr
            th.text-left(scope='col', width='10%') {{ $t("ui.sports") }}
            th.text-left(scope='col', width='15%') {{ $t("ui.bet_types") }}
            th.text-left(scope='col', width='45%') {{ $t("ui.event") }}
            th.text-right(scope='col', width='10%') {{ $t("ui.stake") }}
            th.text-right(scope='col', width='10%') {{ $t("ui.credit") }} / {{ $t("ui.debit") }}
            th.text-right(scope='col', width='10%') {{ $t("ui.commission") }}
          tr.grey(v-if="gameSummary.length == 0")
            td(colspan="5").text-center
              span {{ $t('message.no_information_available') }}
          tr.clickable(
            @click="filterMatch(item.match_id, item.bet_type, item.sports_category)"
            v-for="(item, index) in gameSummary" :class="{ grey: index % 2 === 0 }"
          )
            td.text-left(valign='top')
              span(v-if="item.sports_category == 'PARLAY'") {{ $t("ui.parlay") }}
              template(v-else)
                span(v-if="item.sports_category == 'PARLAYMMO'") {{ $t("ui.mmo_parlay") }}
                span(v-else) {{ sports[item.sports_type] }}
            td.text-left(valign='top')
              template(v-if="racingList.includes(item.sports_type)")
                template(v-if="racingList1.includes(item.sports_type)")
                  span(v-if="['OU','OE'].includes(item.bet_type)")
                    | {{ $t("m.GH_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                  span(v-else)
                    | {{ $t("m.GH_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}
                template(v-if="racingList2.includes(item.sports_type)")
                  span(v-if="['OU','OE'].includes(item.bet_type)")
                    | {{ $t("m.GC_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                  template(v-else)
                    span(v-if="['CS'].includes(item.bet_type)")
                      | {{ item.criteria1 }}
                    span(v-else)
                      | {{ $t("m.GC_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}
                template(v-if="racingList3.includes(item.sports_type)")
                  span(v-if="['OU','OE'].includes(item.bet_type)")
                    | {{ $t("m.GX_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                  span(v-else)
                    | {{ $t("m.GX_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}


              template(v-else)
                span(v-if="item.sports_category == 'PARLAYMMO'") {{ $t("ui.mmo_parlay") }}
                span(v-else) {{ getBetTypeName(item.bet_type) }}
            td.text-left(valign='top')
              template(v-if="racingList.includes(item.sports_type)")
                span {{ item.home_team_name }} &nbsp;
              template(v-else)
                span(v-if="item.bet_type != 'PARLAY'") {{ item.home_team_name }}
                span.color-vs(v-if="item.bet_type!='PARLAY' && item.bet_type != 'OR'") &nbsp;-vs-&nbsp;
                span(v-if="item.bet_type != 'PARLAY' && item.bet_type != 'OR'") {{ item.away_team_name }}
            td.text-right(valign='top') {{ $numeral(item.bet_member).format("0,0.00") }}
            td.text-right(valign='top')
              span(
                :class="{ red: parseFloat(item.winlose) < 0 }"
                ) {{ $numeral(item.winlose).format("0,0.00") }}
            td.text-right(valign='top') {{ $numeral(item.member_commission).format("0,0.00") }}
    #betreject.tab-pane(role='tabpanel', aria-labelledby='tab-betreject' )
      table.table-info(width='100%')
        tbody
          tr
            th.text-center(scope='col', width='5%') {{ $t("ui.no/") }}
            th.text-left(scope='col', width='20%') {{ $t("ui.trans_time") }}
            th.text-left(scope='col', width='42%') {{ $t("ui.event") }}
            th.text-right(scope='col', width='7%') {{ $t("ui.odds") }}
            th.text-right(scope='col', width='18%') {{ $t("ui.stake") }}
            th(scope='col', width='8%') {{ $t("ui.status") }}
          tr.grey(v-if="cancelledBetList == 'undefined' || cancelledBetList.length <= 0")
            td(colspan="6").text-center
              span {{ $t('message.no_information_available') }}
          tr(v-for="(item, index) in cancelledBetList" :class="{ grey: index % 2 === 0 }")
            td.text-center(valign='top') {{ ((currentPage2 - 1) * $store.getters.pageSize + index + 1) }}
            td.text-left(valign='top')
              div {{ $t("ui.ref_no") }}: {{ item.bet_id }}
              div {{ $dayjs(item.bet_time).format("MM/DD/YYYY hh:mm:ss A") }}
            td.text-left(valign='top')
              .bet-info
                template(v-if="racingList.includes(item.sports_type)")
                  .bet-type.blue {{ sports[item.sports_type] }}
                template(v-else)
                  .bet-type.blue(v-if="item.bet_type != 'PARLAY'") {{ sports[item.sports_type] }} - {{ getBetTypeName(item.bet_type) }}
                .d-flex.justify-content-between.align-content-center(v-if="item.bet_type == 'PARLAY'" :id="'heading-' + item.bet_id")
                  .bet-type.blue(v-if="item.bet_type_share == 'PARLAYMMO'") {{ $t("ui.mmo_parlay") }}
                    span.black.font-weight-bolder.ml-1(v-if="item.bet_type == 'PARLAY'") @ {{ item.odds_display }}
                  .bet-type.blue(v-else) {{ getBetTypeName(item.bet_type) }}
                    span.black.font-weight-bolder.ml-1(v-if="item.bet_type == 'PARLAY'") @ {{ item.odds_display }}
                  .bg-blue.x-blue.collapsed(data-toggle="collapse" :data-target="'#collapse-' + item.bet_id" role="button" aria-expanded="false"
                    :aria-controls="'collapse-' + item.bet_id" @click="getParlayDetails(item.bet_id)" :ref="'collapse-' + item.bet_id")
                    i.far.fa-chevron-up
                .bet-detail.blue.pl-2
                  template(v-if="racingList.includes(item.sports_type)")
                    template(v-if="racingList1.includes(item.sports_type)")
                      span(v-if="['OU','OE'].includes(item.bet_type)")
                        | {{ $t("m.GH_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                      span(v-else)
                        | {{ $t("m.GH_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}
                    template(v-if="racingList2.includes(item.sports_type)")
                      span(v-if="['OU','OE'].includes(item.bet_type)")
                        | {{ $t("m.GC_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                      template(v-else)
                        span(v-if="['CS'].includes(item.bet_type)")
                          | {{ item.criteria1 }}
                        span(v-else)
                          | {{ $t("m.GC_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}
                    template(v-if="racingList3.includes(item.sports_type)")
                      span(v-if="['OU','OE'].includes(item.bet_type)")
                        | {{ $t("m.GX_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                      span(v-else)
                        | {{ $t("m.GX_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}

                  template(v-else)
                    .name {{ getBetDetail(item) }}
                      // sup.text-muted &nbsp;(2)
                    .oddsdetail
                      .d-flex.justify-content-start
                        template(v-if="isMMO(item)")
                          .selector-name(v-if="['HDP', 'HDPH', 'OU', 'OUH', '1X2HDP', '1X2HDPH'].includes(item.bet_type)") {{ ballDisplayMMO(item) }}({{ $numeral(item.criteria2).format("0") }})
                        template(v-else)
                          .selector-name(v-if="['HDP', 'HDPH', 'OU', 'OUH', '1X2HDP', '1X2HDPH'].includes(item.bet_type)") {{ ballDisplay(item) }}
                        span(v-if="item.market_type == 3 && item.home_running_score != null") [{{ item.home_running_score }}-{{ item.away_running_score }}]
              .bet-list-scroll.match-info.collapse.magicY(v-if="item.bet_type == 'PARLAY'" :id="'collapse-' + item.bet_id" :data-parent="'#statement-accordion'" :aria-labelledby="'heading-' + item.bet_id")
                .empty.match-info.white.text-center(v-if="loading")
                  i.fad.fa-spinner.fa-spin
                betDetails(:items="parlayItems")
              .match-info.d-flex.flex-column.pl-2
                template(v-if="racingList.includes(item.sports_type)")
                  .name-home {{ item.home_team_name }}
                template(v-else)
                  .name-home {{ item.home_team_name }}
                  .name-away(v-if="item.bet_type != 'OR'") {{ item.away_team_name }}
                  .name-league {{ item.league_name }}
            td.text-right(valign='top')
              div(:class="{ red: parseFloat(item.odds_display) < 0 }") {{ item.odds_display == 0 ? item.odds_display : formatOddsDisplay(item.odds_display, item.bet_type) }}
              div(v-if="isMMO(item) || item['bet_type_share'] == 'PARLAYMMO'") {{ $t(odds[5]) }}
              div(v-else) {{ $t(odds[item.odds_type]) }}
            td.text-right(valign='top') {{ $numeral(item.bet_member).format("0,0.00") }}

            td.text-left(valign='top')
              div {{ (item.bet_status_code == 1) ? $t('ui.reject') : $t('ui.void') }}

      .mt-2
        v-pagination(
          v-model="currentPage2"
          :page-count="cancelledBetTotalPage"
          :classes="bootstrapPaginationClasses"
          :labels="paginationAnchorTexts"
          @input="changedPage($event, 'cancelled_bet')"
          v-if="cancelledBetTotalPage"
        )
  resultModal(v-if="selectedMatch" :matchId="selectedMatch" :betType="selectedBetType")
</template>
<script>
import vPagination from "vue-plain-pagination";
import config from "@/config";
import naming from "@/library/_name";
import service from "@/library/_xhr-statement";
import calc from "@/library/_calculation";
import resultModal from "@/components/desktop/info/statement/resultModal";
import betDetails from "@/components/desktop/info/betList/betDetails";

export default {
  components: { vPagination, resultModal, betDetails },
  props: {
    gameResultList: {
      type: Array,
      default: [],
    },
    gameResultSummary: {
      type: Object,
      default: {},
    },
    gameSummary: {
      type: Array,
      default: [],
    },
    gameResultTotalPages: {
      type: Number,
    },
    ploading: {
      type: Boolean,
      default: false,
    },
    cancelledBetList: {
      type: Array,
      default: [],
    },
    cancelledBetTotalPage: {
      type: Number,
    },
  },
  data() {
    return {
      loading: false,
      parlayItems: [],
      selectedBetType: "",
      selectedMatch: 0,
      currentPage: 1,
      currentPage2: 1,
      bootstrapPaginationClasses: {
        ul: "pagination justify-content-center",
        li: "page-item",
        liActive: "active",
        liDisable: "disabled",
        button: "page-link",
        buttonActive: "active",
        buttonDisable: "disable",
      },
      paginationAnchorTexts: {
        first: "<i class='fas fa-angle-double-left'></i>",
        prev: "<i class='fas fa-angle-left'></i>",
        next: "<i class='fas fa-angle-right'></i>",
        last: "<i class='fas fa-angle-double-right'></i>",
      },
    };
  },
  computed: {
    racingList() {
      return config.racingList;
    },
    racingList1() {
      return config.racingList1;
    },
    racingList2() {
      return config.racingList2;
    },
    racingList3() {
      return config.racingList3;
    },
    language() {
      return this.$store.getters.language;
    },
    sports() {
      return this.$store.state.layout.sports;
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
    odds() {
      return config.oddsTypeLocale;
    },
    isTotal() {
      if (this.gameResultSummary != null) {
        return Object.keys(this.gameResultSummary).length > 0;
      } else {
        return false;
      }
    },
    total() {
      if (this.gameResultSummary != null) {
        return parseFloat(this.gameResultSummary.winlose) + parseFloat(this.gameResultSummary.member_commission);
      } else {
        return 0;
      }
    },
    sortedGameResultList() {
      var result = new Array();
      this.gameResultList.forEach((x) => {
        var idx = result.findIndex((y) => y.bet_type === x.bet_type && y.home_team_name === x.home_team_name && y.away_team_name === x.away_team_name);
        if (idx < 0)
          result.push(
            new Object({
              bet_type: x.bet_type,
              home_team_name: x.home_team_name,
              away_team_name: x.away_team_name,
              bet_member: x.bet_member,
              winlose: x.winlose,
            })
          );
        else {
          result[idx].bet_member = result[idx].bet_member + x.bet_member;
          result[idx].winlose = result[idx].winlose + x.winlose;
        }
      });
      return result;
    },
  },
  mounted() {
    this.currentPage = this.currentGameResultPage;
    this.changedPage(1, "game_result");
  },
  methods: {
    isMMO(e) {
      return (e['criteria2'] && ['HDP', 'HDPH', 'OU', 'OUH'].includes(e['bet_type'].toUpperCase()))
    },
    getBetTypeName(e) {
      return this.$t("m.BT_" + e);
    },
    getBetDetail(bet) {
      // console.log(bet);
      return naming.betDisplay(bet, this);
    },
    formatOddsDisplay(odds, bet_type) {
      if (bet_type == "cs") return calc.fmcs(odds);
      else return calc.fmt(odds);
    },
    getBall(bet) {
      if ((bet.team_g == bet.home_team_id && bet.bet_team_id == bet.home_team_id) || (bet.team_g == bet.away_team_id && bet.bet_team_id == bet.away_team_id)) return -bet.ball;
      else return bet.ball;
    },
    ballDisplay(e) {
      return naming.ballDisplay1(e, this);
    },
    ballDisplayMMO(e) {
      return naming.ballDisplayMMO1(e, this);
    },
    getParlayDetails(result_id) {
      var detailPanel = this.$refs["collapse-" + result_id][0].className;
      // call API on expansion only..
      if (typeof detailPanel != "undefined" && detailPanel.indexOf("collapsed") > -1) {
        if (this.loading == true) return;

        if (this.isLoggedIn) {
          this.loading = true;
          this.parlayItems = [];

          var json = {
            account_id: this.$store.getters.accountId,
            session_token: this.$store.getters.sessionToken,
            bet_id: result_id,
          };
          service.getParlayDetails(json).then(
            (res) => {
              this.loading = false;
              if (res) {
                if (res.success == true) {
                  var g = res.data;
                  this.parlayItems = g;
                  if (res.names) {
                    var mm = g.reduce((groups, item) => {
                      const val = item["match_id"];
                      groups[val] = groups[val] || [];
                      groups[val].push(item);
                      return groups;
                    }, {});

                    var ll = g.reduce((groups, item) => {
                      const val = item["league_id"];
                      groups[val] = groups[val] || [];
                      groups[val].push(item);
                      return groups;
                    }, {});

                    var m = res.names["Table"];
                    var l = res.names["Table1"];
                    if (m) {
                      for (var i = 0; i < m.length; i++) {
                        var hn = m[i]["home_name_" + this.language];
                        var an = m[i]["away_name_" + this.language];
                        var mid = m[i]["match_id"];

                        if (hn || an) {
                          for (var j = 0; j < mm[mid].length; j++) {
                            if (hn) mm[mid][j]["home_team_name"] = hn;
                            if (an) mm[mid][j]["away_team_name"] = an;
                          }
                        }
                      }
                    }

                    if (l) {
                      for (var i = 0; i < l.length; i++) {
                        var ln = l[i]["name_" + this.language];
                        var lid = l[i]["league_id"];

                        if (ln) {
                          for (var j = 0; j < ll[lid].length; j++) {
                            ll[lid][j]["league_name"] = ln;
                          }
                        }
                      }
                    }
                  }
                } else {
                  this.$helpers.handleFeedback(res.status);
                }
              }
            },
            (err) => {
              this.loading = false;
              this.$helpers.handleFeedback(err.status);
            }
          );
        }
      }
    },
    getSelectedMatch(item) {
      this.selectedBetType = item.bet_type;
      if (item.bet_type === "PARLAY") this.selectedMatch = item.result_id;
      else this.selectedMatch = item.match_id;
    },
    changedPage(pageNo, type) {
      if (type == "game_result") this.currentPage = pageNo;
      else this.currentPage2 = pageNo;

      this.$emit("changedPage", pageNo, type);
    },
    filterMatch(id, bet, sports) {
      $("#tab-betlist").click();
      this.currentPage = 1;
      this.$emit(
        "filterMatch",
        new Object({
          id: id,
          bet_type: bet,
          sports_category: sports,
        })
      );
    },
  },
};
</script>
