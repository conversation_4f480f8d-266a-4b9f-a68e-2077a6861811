<template lang="pug">
.info-wrapper
  .info-title
    .info-icon
      i.fad.fa-cog
    .flex-fill.page-title(aria-label="breadcrumb")
      ol.breadcrumb.p-0.m-0
        li.breadcrumb-item.active(aria-current="page") {{ $t("ui." + activeTab) }}
  .info-tablewrap.magicZ
    .info-settings.d-flex.flex-column.w-100.flex-fill.p-0.m-0
      ul.nav.nav-tabs
        li.nav-item(v-if="!whiteLabel.mode")
          a.nav-link.text-ellipsis(
            href="javascript:void(0);"
            @click="activeTab = 'account'"
            :class="{ active: activeTab === 'account' }"
            :title="$t('ui.account')"
          ) {{ $t("ui.account") }}
        li.nav-item
          a.nav-link.text-ellipsis(
            href="javascript:void(0);"
            @click="activeTab = 'page_display'"
            :class="{ active: activeTab === 'page_display' }"
            :title="$t('ui.page_display')"
          ) {{ $t("ui.page_display") }}
        //- li.nav-item
        //-   a.nav-link.text-ellipsis(
        //-     href="javascript:void(0);"
        //-     @click="activeTab = 'customize_sports_order'"
        //-     :class="{ active: activeTab === 'customize_sports_order' }"
        //-     :title="$t('ui.customize_sports_order')"
        //-   ) {{ $t("ui.customize_sports_order") }}
        li.nav-item
          a.nav-link.text-ellipsis(
            href="javascript:void(0);"
            @click="activeTab = 'betting'"
            :class="{ active: activeTab === 'betting' }"
            :title="$t('ui.betting')"
          ) {{ $t("ui.betting") }}
      Account(v-if="activeTab == 'account' && whiteLabel.mode == false")
      PageDisplay(v-if="activeTab == 'page_display'")
      //- CustomizeSportsOrder(v-if="activeTab == 'customize_sports_order'")
      Betting(v-if="activeTab == 'betting'")
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {
    Account: () => import("@/components/desktop/info/account.vue"),
    PageDisplay: () => import("@/components/desktop/info/pageDisplay.vue"),
    CustomizeSportsOrder: () => import("@/components/desktop/info/customizeSportsOrder.vue"),
    Betting: () => import("@/components/desktop/info/betting.vue"),
  },
  data() {
    return {
      loading: false,
      activeTab: "account"
    };
  },
  computed: {
    whiteLabel() {
      return this.$store.getters.whiteLabel;
    }
  },
  mounted() {
    if (this.whiteLabel.mode) {
      this.activeTab = "page_display";
    } else {
      this.activeTab = "account";
    }
    $(this.$refs.settingsModal).on("hidden.bs.modal", () => {
      if ((this.activeTab = "account")) {
        EventBus.$emit("RESET_INPUTS");
      } else {
        if (this.whiteLabel.mode) {
          this.activeTab = "page_display";
        } else {
          this.activeTab = "account";
        }
      }
    });
  },
  methods: {
    changeTab(name) {
      this.activeTab = name;
    }
  }
};
</script>
