<template lang="pug">
.slider-nav
  slot
</template>

<script>
export default {
  data() {
    return {
      options: {
        slidesToShow: 4,
        slidesToScroll: 1,
        dots: false,
        variableWidth: false,
        infinite: false,
        focusOnSelect: true,
        centerMode: false
      }
    };
  },
  destroyed() {
    this.destroy();
  },
  mounted() {
    this.create();
  },
  methods: {
    create(e) {
      const $slick = $(this.$el);
      // $slick.on("afterChange", this.onAfterChange);
      // $slick.on('beforeChange', this.onBeforeChange);
      // $slick.on('breakpoint', this.onBreakpoint);
      // $slick.on('destroy', this.onDestroy);
      // $slick.on('edge', this.onEdge);
      // $slick.on('init', this.onInit);
      // $slick.on('reInit', this.onReInit);
      // $slick.on('setPosition', this.onSetPosition);
      // $slick.on('swipe', this.onSwipe);
      // $slick.on('lazyLoaded', this.onLazyLoaded);
      // $slick.on('lazyLoadError', this.onLazyLoadError);
      $slick.slick(this.options);
      if (e) {
        $slick.slick("slickGoTo", e, true);
      }
    },
    destroy() {
      const $slick = $(this.$el);
      $slick.off("afterChange", this.onAfterChange);
      // $slick.off('beforeChange', this.onBeforeChange);
      // $slick.off('breakpoint', this.onBreakpoint);
      // $slick.off('destroy', this.onDestroy);
      // $slick.off('edge', this.onEdge);
      // $slick.off('init', this.onInit);
      // $slick.off('reInit', this.onReInit);
      // $slick.off('setPosition', this.onSetPosition);
      // $slick.off('swipe', this.onSwipe);
      // $slick.off('lazyLoaded', this.onLazyLoaded);
      // $slick.off('lazyLoadError', this.onLazyLoadError);
      $(this.$el).slick("unslick");
    },
    // onAfterChange(event, slick, currentSlide) {
    //   this.$emit("afterChange", event, slick, currentSlide);
    // }
  }
};
</script>
