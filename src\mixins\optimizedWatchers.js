/**
 * Optimized Watchers Mixin
 * Provides debounced and throttled watchers for expensive operations
 */
import { EventBus } from '@/library/_event-bus.js';

export default {
  data() {
    return {
      // Debounce timers for watchers
      watcherTimers: {},
      // Cache for previous values to prevent unnecessary updates
      watcherCache: {},
    };
  },
  
  methods: {
    /**
     * Debounced event emission to prevent cascading updates
     * @param {string} event - Event name
     * @param {number} delay - Debounce delay in milliseconds
     * @param {...any} args - Event arguments
     */
    debouncedEmit(event, delay = 300, ...args) {
      const timerId = `emit_${event}`;
      
      if (this.watcherTimers[timerId]) {
        clearTimeout(this.watcherTimers[timerId]);
      }
      
      this.watcherTimers[timerId] = setTimeout(() => {
        EventBus.$emit(event, ...args);
        delete this.watcherTimers[timerId];
      }, delay);
    },
    
    /**
     * Throttled event emission for high-frequency updates
     * @param {string} event - Event name
     * @param {number} interval - Throttle interval in milliseconds
     * @param {...any} args - Event arguments
     */
    throttledEmit(event, interval = 100, ...args) {
      const timerId = `throttle_${event}`;
      
      if (!this.watcherTimers[timerId]) {
        EventBus.$emit(event, ...args);
        this.watcherTimers[timerId] = setTimeout(() => {
          delete this.watcherTimers[timerId];
        }, interval);
      }
    },
    
    /**
     * Smart watcher that only triggers when values actually change
     * @param {string} key - Cache key
     * @param {any} newVal - New value
     * @param {Function} callback - Callback function
     * @param {Function} compareFn - Custom comparison function
     */
    smartWatch(key, newVal, callback, compareFn = null) {
      const oldVal = this.watcherCache[key];
      
      let hasChanged = false;
      if (compareFn) {
        hasChanged = !compareFn(oldVal, newVal);
      } else {
        hasChanged = oldVal !== newVal;
      }
      
      if (hasChanged) {
        this.watcherCache[key] = newVal;
        callback(newVal, oldVal);
      }
    },
    
    /**
     * Batch multiple watcher updates to reduce re-renders
     * @param {Array} updates - Array of update functions
     * @param {number} delay - Batch delay in milliseconds
     */
    batchWatcherUpdates(updates, delay = 50) {
      const timerId = 'batch_updates';
      
      if (this.watcherTimers[timerId]) {
        clearTimeout(this.watcherTimers[timerId]);
      }
      
      this.watcherTimers[timerId] = setTimeout(() => {
        this.$nextTick(() => {
          updates.forEach(update => {
            if (typeof update === 'function') {
              update();
            }
          });
        });
        delete this.watcherTimers[timerId];
      }, delay);
    },
  },
  
  beforeDestroy() {
    // Clean up all watcher timers
    Object.values(this.watcherTimers).forEach(timer => {
      clearTimeout(timer);
    });
    this.watcherTimers = {};
    this.watcherCache = {};
  },
}; 