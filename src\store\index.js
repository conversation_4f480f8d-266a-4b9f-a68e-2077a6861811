import Vue from "vue";
import Vuex from "vuex";
import VueResource from "vue-resource";
import VuexPersistence from "vuex-persist";
import VuexSharedMutations from "vuex-shared-mutations";

// import Cookies from "js-cookie";
import user from "@/store/_user";
import layout from "@/store/_layout";
import cache from "@/store/_cache";
import betsingle from "@/store/_betsingle";
import betparlay from "@/store/_betparlay";
import betsinglemmo from "@/store/_betsinglemmo";
import betparlaymmo from "@/store/_betparlaymmo";

import config from "@/config";

Vue.use(VueResource);
Vue.use(Vuex);

// const STORAGE_KEY = "sb2019110803";
// const STORAGE_KEY = "E1CA691527219";
const STORAGE_KEY1 = "vvmeXhv2efHppj5C5f8X9TzTgV2J5PU2" + (config.whiteLabel ? "jP9Z9rSLBR" : "zbmk9uJgbU");
const STORAGE_KEY2 = "WLIHRoiaRvYj6ugJxsVA3W8Gux4TSDuq" + (config.whiteLabel ? "LJDf2Osr9f" : "vT3v2kuLGB");

var cookieStorage = null;

cookieStorage = new VuexPersistence({
  key: STORAGE_KEY2,
  storage: window.localStorage,
  reducer: (state) => ({
    user: {
      account: state.user.account,
      rememberMe: state.user.rememberMe,
    },
  }),
});

const sessionStorage = new VuexPersistence({
  key: STORAGE_KEY1,
  storage: window.localStorage,
  reducer: (state) => ({
    layout: state.layout,
  }),
});

const store = new Vuex.Store({
  strict: true,
  modules: {
    user,
    layout,
    cache,
    betsingle,
    betparlay,
    betsinglemmo,
    betparlaymmo,
  },
  state: {
    defaultInterval: config.defaultInterval,
    interval: config.initialInterval,
    defaultDelay: config.defaultDelay,
  },
  mutations: {
    updateMode(state, payload) {
      state.interval = state.defaultInterval * payload;
    },
  },
  actions: {
    setMode(context, payload) {
      context.commit("updateMode", payload);
    },
  },
  getters: {
    adsPopup: (state) => {
      return state.layout.adsPopup4;
    },
    adsKey: (state) => {
      return state.layout.adsKey;
    },
    header: (state) => {
      if (config.whiteLabel) {
        return false;
      } else {
        return state.layout.minimizer.header;
      }
    },
    whiteLabel: (state) => {
      if (config.whiteLabel) {
        return {
          mode: true,
          theme: state.layout.theme,
        };
      } else {
        return {
          mode: false,
          theme: "",
        };
      }
    },
    data: (state) => {
      return state.cache;
    },
    selectedMatch: (state) => {
      return state.layout.selectedMatch;
    },
    selectLeague: (state) => {
      return state.layout.selectLeague;
    },
    selectedDays: (state) => {
      return state.layout.selectedDays;
    },
    search: (state) => {
      return state.layout.search;
    },
    betting: (state) => {
      return state.layout.betting;
    },
    pageDisplay: (state) => {
      return state.layout.pageDisplay;
    },
    language: (state) => {
      return state.layout.pageDisplay.language;
    },
    odds: (state) => {
      return state.layout.odds;
    },
    menuX: (state) => {
      return state.layout.menuX;
    },
    menu0: (state) => {
      return state.layout.menu0;
    },
    menu1: (state) => {
      return state.layout.menu1;
    },
    menu2: (state) => {
      return state.layout.menu2;
    },
    menu3: (state) => {
      return state.layout.menu3;
    },
    minimizer: (state) => {
      return state.layout.minimizer;
    },
    pageSize: (state) => {
      return state.layout.pageDisplay.pageSize;
    },
    playerInfo: (state) => {
      if (state.user.account) {
        if (state.user.account.player_info) {
          if (state.user.account.player_info) {
            return state.user.account.player_info;
          }
        }
      }
      return {};
    },
    playerWallet: (state) => {
      if (state.user.account) {
        if (state.user.account.player_wallet) {
          if (state.user.account.player_wallet) {
            return state.user.account.player_wallet;
          }
        }
      }
      return {};
    },
    playerBetLimit: (state) => {
      if (state.user.account) {
        if (state.user.account.player_bet_limit) {
          if (state.user.account.player_bet_limit) {
            return state.user.account.player_bet_limit;
          }
        }
      }
      return {};
    },
    operatorType: (state, getters) => {
      if ("operator_type" in getters.playerInfo) return getters.playerInfo.operator_type;
      return null;
    },
    parentId: (state, getters) => {
      if ("parent_id" in getters.playerInfo) return getters.playerInfo.parent_id;
      return null;
    },
    sessionToken: (state, getters) => {
      if ("session_token" in getters.playerInfo) return getters.playerInfo.session_token;
      return null;
    },

    balance: (state, getters) => {
      return state.user.balance;
    },
    accountId: (state, getters) => {
      if ("account_id" in getters.playerInfo) return getters.playerInfo.account_id;
      return null;
    },
    mmoMode: (state, getters) => {
      if (getters.currencyCode) {
        return config.mmoSupport && ["MMK", "MMO"].includes(getters.currencyCode);
      } else {
        return false;
      }
    },
    currencyCode: (state, getters) => {
      if ("currency_code" in getters.playerWallet) return getters.playerWallet.currency_code;
      return null;
    },
    commType: (state, getters) => {
      if ("comm_type" in getters.playerInfo) return getters.playerInfo.comm_type;
      return null;
    },
    nickName: (state, getters) => {
      if ("account_id" in getters.playerInfo) return getters.playerInfo.nickname;
      return null;
    },
    hasSecondaryAccount: (state, getters) => {
      if ("account_id" in getters.playerInfo) return getters.playerInfo.has_secondary_account;
      return null;
    },
    isLoggedIn: (state, getters) => {
      if (getters.sessionToken) {
        return true;
      } else {
        return false;
      }
    },
    favorite: (state, getters) => {
      return state.layout.favorite;
    },
    getStake: (state, getters) => {
      return state.betsingle.stake;
    },
    rememberMe: (state, getters) => {
      return state.user.rememberMe;
    },
  },
  plugins: [
    sessionStorage.plugin,
    cookieStorage.plugin,
    VuexSharedMutations({
      predicate: [
        "user/updateAccount",
        "user/deleteAccount",
        "user/updateBalance",
        "user/updateNickname",
        "user/updateRememberMe",
        "layout/updateTour1",
        "layout/updateMenuList",
        "layout/resetState",
        "layout/clearSelectLeague",
        "layout/updateSelectLeague",
        "layout/updateSelectedMatch",
        "layout/updateSearch",
        "layout/updateOptions",
        "layout/updateSports",
        "layout/updateLeague",
        "layout/updateOrder",
        "layout/updateLanguage",
        "layout/updatePageDisplay",
        "layout/updateSingleBetting",
        "layout/updateBetting",
        "layout/updateMenu",
        "layout/updateMenuItems",
        "layout/updateMinimizer",
        "layout/updateSelectedDays",
        "layout/updateFavorite",
        "layout/removeFavorite",
        "layout/replaceFavorite",
      ],
    }),
  ],
});

export default store;
