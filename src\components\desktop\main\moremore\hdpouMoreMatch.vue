<template lang="pug">
.col-12.hx-more-row(v-if="details['oxt'] != null || details['hdp'] != null || details['ou'] != null || details['oe'] != null")
  //- small.text-primary {{ childId }}
  //- small.text-primary {{ mmoDetails['tn'] > 0 }}
  .card
    .card-header(
      :id="'heading-hdpou-more-' + uid + childId",
      data-toggle="collapse",
      :data-target="'#collapse-hdpou-more-' + uid + childId",
      :aria-controls="'collapse-hdpou-more-' + uid + childId",
      aria-expanded,
      :class="layoutIndex == 3 ? 'live' : 'non-live'"
    )
      i.fad.fa-chevron-circle-down
      span.header-bettype {{ leagueName.trim() }}
    .collapse.show(:aria-labelledby="'heading-hdpou-more-' + uid + childId", :id="'collapse-hdpou-more-' + uid + childId")
      .card-body.p-0(:id="'accordian-hdpou-more-' + uid + childId")
        //- MMO
        template(v-if="menu1 != 'parlay' && menu3 != 'parlay'")
          .hx-table.hx-morebet-header.bl-1.br-1.bb-1.h-18(v-if="Object.keys(mmoDetails).length > 0 && mmoDetails['tn'] > 0", :class="marketType == 3 ? 'live' : 'non-live'")
            .hx-cell.flex-fill
              .hx-row
                .hx-col
                  .hx.text-left {{ $t('ui.full_time') }}
            .hx-cell.w-328.bl-1
              .hx-row
                .hx-col.w-66
                  .hx.w-100.text-center.text-muted {{ $t('ui.oxt') }}
                .hx-col.w-98.bl-1
                  .hx.w-100.text-center {{ $t('ui.hdp') }}
                .hx-col.w-98.bl-1
                  .hx.w-100.text-center {{ $t('ui.ou') }}
                .hx-col.w-66.bl-1
                  .hx.w-100.text-center.text-muted {{ $t('ui.oe') }}
          .hx-table.hx-match.hx-morebet-body.hx-mmo.bl-1.br-1.bb-1(v-if="Object.keys(mmoDetails).length > 0 && mmoDetails['tn'] > 0", :class="{ live: marketType == 3 }")
            .hx-cell.w-62
              .hx-row.h-100.hx-rows
                timePanel(:source="source")
            .hx-cell.flex-fill.w-457
              .hx-row.h-100.hx-rows
                .hx-col.d-block.h-100.w-100.text-ellipsis
                  .hx.hx-ellipsis(:class="details['team'] == 1 ? 'team-red' : 'team-black'") {{ source.homeTeam }}
                  .hx.hx-ellipsis(:class="details['team'] == 0 ? 'team-red' : 'team-black'") {{ source.awayTeam }}
            .hx-cell.w-328
              .hx-row.hx-rows(v-for="(dn, i) in mmoDetails['tn']")
                .hx-col.hx-cols.w-66
                hdpMMO(:details="mmoDetails", :oddsType="oddsType", :i="i", betType="hdp")
                ouMMO(:details="mmoDetails", :oddsType="oddsType", :i="i", betType="ou")
                .hx-col.hx-cols.w-66

        //- NORMAL
        .hx-table.hx-morebet-header.bl-1.br-1.bb-1.h-18(:class="marketType == 3 ? 'live' : 'non-live'")
          .hx-cell.flex-fill
            .hx-row
              .hx-col
                .hx.text-left {{ $t('ui.full_time') }}
          .hx-cell.w-328.bl-1
            .hx-row
              .hx-col.w-66
                .hx.w-100.text-center {{ $t('ui.oxt') }}
              .hx-col.w-98.bl-1
                .hx.w-100.text-center {{ $t('ui.hdp') }}
              .hx-col.w-98.bl-1
                .hx.w-100.text-center {{ $t('ui.ou') }}
              .hx-col.w-66.bl-1
                .hx.w-100.text-center {{ $t('ui.oe') }}
        .hx-table.hx-match.hx-morebet-body.hx-non-mmo.bl-1.br-1.bb-1(:class="{ live: marketType == 3 }")
          .hx-cell.w-62
            .hx-row.h-100.hx-rows
              timePanel(:source="source")
          .hx-cell.flex-fill.w-457
            .hx-row.h-100.hx-rows
              .hx-col.d-block.h-100.w-100.text-ellipsis
                .hx.hx-ellipsis(:class="details['team'] == 1 ? 'team-red' : 'team-black'") {{ source.homeTeam }}
                .hx.hx-ellipsis(:class="details['team'] == 0 ? 'team-red' : 'team-black'") {{ source.awayTeam }}
                .hx.draw(v-if="details['oxtn']") {{ $t('ui.draw') }}
          .hx-cell.w-328
            .hx-row.hx-rows(v-for="(dn, i) in details['tn']", :class="{ 'h-66': details['oxt'] != null && details['oxt'][i] != null }")
              oxtItem(:details="details", :oddsType="oddsType", :i="i", betType="oxt")
              hdpItem(:details="details", :oddsType="oddsType", :i="i", betType="hdp")
              ouItem(:details="details", :oddsType="oddsType", :i="i", betType="ou")
              oeItem1(:details="details", :oddsType="oddsType", :i="i", betType="oe")
        template(v-if="details['oxth'] != null || details['hdph'] != null || details['ouh'] != null || details['oeh'] != null")
          .hx-table.hx-morebet-header.bl-1.br-1.bb-1.h-18(:class="marketType == 3 ? 'live' : 'non-live'")
            .hx-cell.flex-fill
              .hx-row
                .hx-col
                  .hx.text-left {{ $t('ui.half_time') }}
            .hx-cell.w-328.bl-1
              .hx-row
                .hx-col.w-66
                  .hx.w-100.text-center {{ $t('ui.oxt') }}
                .hx-col.w-98.bl-1
                  .hx.w-100.text-center {{ $t('ui.hdp') }}
                .hx-col.w-98.bl-1
                  .hx.w-100.text-center {{ $t('ui.ou') }}
                .hx-col.w-66.bl-1
                  .hx.w-100.text-center {{ $t('ui.oe') }}
          .hx-table.hx-match.hx-morebet-body.hx-non-mmo.bl-1.br-1.bb-1(:class="{ live: marketType == 3 }")
            .hx-cell.w-62
              .hx-row.h-100.hx-rows
                timePanel(:source="source")
            .hx-cell.flex-fill.w-457
              .hx-row.h-100.hx-rows
                .hx-col.d-block.h-100.w-100.text-ellipsis
                  .hx.hx-ellipsis(:class="details['team'] == 1 ? 'team-red' : 'team-black'") {{ source.homeTeam }}
                  .hx.hx-ellipsis(:class="details['team'] == 0 ? 'team-red' : 'team-black'") {{ source.awayTeam }}
                  .hx.draw(v-if="details['oxtn']") {{ $t('ui.draw') }}
            .hx-cell.w-328
              .hx-row.hx-rows(v-for="(dn, i) in details['tn']", :class="{ 'h-66': details['oxth'] != null && details['oxth'][i] != null }")
                oxtItem(:details="details", :oddsType="oddsType", :i="i", betType="oxth")
                hdpItem(:details="details", :oddsType="oddsType", :i="i", betType="hdph")
                ouItem(:details="details", :oddsType="oddsType", :i="i", betType="ouh")
                oeItem1(:details="details", :oddsType="oddsType", :i="i", betType="oeh")
</template>

<script>
import config from "@/config";

export default {
  components: {
    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    hdpItem: () => import("@/components/desktop/main/xtable/xitem/hdpItem"),
    ouItem: () => import("@/components/desktop/main/xtable/xitem/ouItem"),
    oxtItem: () => import("@/components/desktop/main/xtable/xitem/oxtItem"),
    oeItem1: () => import("@/components/desktop/main/xtable/xitem/oeItem1"),
    hdpMMO: () => import("@/components/desktop/main/mmo/xitem/hdpMMO"),
    ouMMO: () => import("@/components/desktop/main/mmo/xitem/ouMMO"),
  },
  props: {
    childId: {
      type: Number,
    },
    uid: {
      type: String,
    },
    details: {
      type: Object,
    },
    matchId: {
      type: Number,
    },
    leagueId: {
      type: Number,
    },
    marketType: {
      type: Number,
    },
    sportsType: {
      type: Number,
    },
    betType: {
      type: String,
    },
    layoutIndex: {
      type: Number,
    },
  },
  computed: {
    menuX() {
      return this.$store.state.layout.menuX;
    },
    menu1() {
      return this.$store.state.layout.menu1;
    },
    menu3() {
      return this.$store.state.layout.menu3;
    },
    mmoDetails() {
      return this.details["mmo"];
    },
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
    leagueName() {
      var result = this.details["league"].split(" - ");
      if (result.length >= 2) {
        return result[result.length - 1];
      }
      return this.details["league"];
    },
    source() {
      return {
        marketId: this.details.match[4],
        matchTime: this.details.match[8],
        runningScore: this.details.match[11],
        runningTime: this.details.match[12],
        homeTeam: this.details.match[5],
        awayTeam: this.details.match[6],
      };
    },
  },
};
</script>
