export default {
  OK: "확인",
  CLOSE: "귀하의 계정이 폐쇄되었습니다!",
  BLOCK: "계정이 차단되었습니다!",
  FREEZE: "계정이 동결되었습니다!",
  SUSPEND: "귀하의 계정은 VIEW로만 변경되었습니다.",
  passwordRequired: "비밀번호가 필요합니다!",
  usernameRequired: "사용자 이름이 필요합니다!",
  incompletedRequest: "문제가 발생했습니다 ...",

  session_not_exists: "세션이 만료되었습니다! 계속하려면 다시 로그인하십시오...",
  session_expired: "세션이 만료되었습니다! 계속하려면 다시 로그인하십시오...",
  sessionExpired: "세션이 만료되었습니다! 계속하려면 다시 로그인하십시오...",
  invalidSession: "세션이 만료되었습니다! 계속하려면 다시 로그인하십시오...",
  accountIdRequired: "세션이 만료되었습니다! 계속하려면 다시 로그인하십시오...",
  NoUserAccessRight: "권한이 거부되었습니다! 계속하려면 업 라인에 문의하십시오 ...",
  account_not_exists: "유효하지 않은 비밀번호",
  invalid_password: "유효하지 않은 비밀번호",
  secondary_account_exists: "닉네임이 이미 생성되었습니다.",
  systemError: "내부 시스템 오류.",
  new_login_id_exists: "닉네임이 잘못되었습니다. 다른 닉네임을 설정하십시오.",
  loginLimit: "너무 자주 로그인하셨습니다. 1 분 후에 다시 시도하십시오",
  requestLimit: "You have request too frequent.",

  insufficient_balance: "잔액 불충분",
  loginFailed: "로그인 실패, 나중에 다시 시도하십시오",
  requestFailed: "연결이 불안정합니다. 연결을 확인하고 다시 시도하십시오.",
  requestPending: "요청 보류",
  close: "계정이 폐쇄되었습니다! 계속하려면 업 라인에 문의하십시오",
  suspend: "계정이 정지되었습니다! 계속하려면 업 라인에 문의하십시오",
  freeze: "계정이 정지되었습니다! 계속하려면 업 라인에 문의하십시오",
  alphaNumOnly: "영숫자 만",
  // vuelidate
  isRequired: "를 입력하십시오 {fname}.",
  isMinValue: "{fname} 최소한이 있어야합니다 {fvalue}.",
  isMaxValue: "{fname} must be less than or equal to {fvalue}.",
  isMinLength: "{fname} 최소한 있어야합 {fvalue} 문자.",
  isMaxLength: "{fname} must be less than or equal to {fvalue} characters.",
  isAlpha: "알파벳 만 허용",
  isAlphaNum: "{fname} 영숫자 만 허용",
  isNumeric: "{fname} 숫자 만 허용",
  isEmail: "{fname} 유효한 이메일 주소 만 수락",
  isIpAddress: "{fname} 유효한 IPv4 주소 만 수락",
  isSameAs: "{fname} 와 같아야합니다 {fname2}.",
  isUrl: "{fname} URL 만 수락",
  containAlphaNum: "{fname} 알파벳과 숫자가 하나 이상 있어야합니다.",
  selectOption: "를 선택하십시오 {fname}",
  notSameAs: "{fname}과 {fname2}은 (는) 동일 할 수 없습니다.",
  currPasswordRequired: "현재 비밀번호가 필요합니다",
  newPasswordRequired: "새로운 비밀번호가 필요합니다!",
  confirmNewPasswordRequired: "새 비밀번호가 필요하다는 것을 확인하십시오!",
  passwordsNotMatch: "비밀번호가 일치하지 않습니다!",
  nickNameRequired: "닉네임이 필요합니다",
  startDateRequired: "시작일이 필요합니다!",
  endDateRequired: "종료일이 필요합니다!",
  pageSizeRequired: "페이지 크기가 필요합니다!",
  pageNumberRequired: "페이지 번호가 필요합니다!",
  sportsTypeRequired: "스포츠 타입이 필요합니다!",
  workingDateRequired: "근무일이 필요합니다!",
  leagueIdRequired: "리그 ID가 필요합니다!",
  isOutrightRequired: "Is outright is required!",

  duplicate_debit_record: "Please wait a moment while odds is updating...",
  invalidOddsBall: "Please wait a second while odds is updating...",
  oddsDisplayInvalid: "Please wait while odds is updating...",
  oddsIsUpdating: "Odds Is Updating...",
  unableToGetBalanceAtTheMoment: "인터넷 서버 오류",
  atLeastTwoMatchToBetParlay: "팔레이에서 베팅하려면 2 경기 이상을 혼합하십시오.",
  maxParlayTicket: "Reach maximum ticket per parlay!",
  matchHide: "이 시장은 사용할 수 없습니다. 다른 시장을 시도하십시오.",
  matchNotAllowBet: "이 시장은 사용할 수 없습니다. 다른 시장을 시도하십시오.",
  invalidBets: "이 시장은 사용할 수 없습니다. 다른 시장을 시도하십시오.",
  invalidBet: "이 시장은 사용할 수 없습니다. 다른 시장을 시도하십시오.",
  betOverMaxPerMatch: "Your stake exceeds the max stake.",
  betOverMax: "Your stake exceeds the max stake.",
  betLowerMin: "Your stake less than the min stake.",
  betOverMaxPayout: "Your stake exceeds the max payout.",
  invalidBetType: "이 시장은 사용할 수 없습니다. 다른 시장을 시도하십시오.",
  memberInactive: "이 계정 상태가 활성화되지 않았습니다. 업 라인에 문의하십시오.",
  memberCommissionNotSetup: "커미션이 설정되지 않았습니다",
  matchNotActive: "이 시장은 사용할 수 없습니다. 다른 시장을 시도하십시오.",
  invalidBetTeam: "이 시장은 사용할 수 없습니다. 다른 시장을 시도하십시오.",
  invalidOdds: "이 시장은 사용할 수 없습니다. 다른 시장을 시도하십시오.",
  betOverMaxBet: "베팅 금액이 최대 한도를 초과했습니다. 내기 한도를 확인하십시오.",
  invalidCurrency: "베팅 할 유효하지 않은 통화",
  betOverLimit: "Your stake exceeds the limit.",
  selectLeague: "리그를 1 개 이상 선택하십시오",
  game_maintenance: "게임 유지 보수 중",
  betTypeRequired: "베팅 유형이 필요합니다",

  liveCasinoDisabled: "라이브 카지노 비활성화",
  lotteryDisabled: "Lottery Disabled",
  MiniGameDisabled: "Mini Game Disabled"
};
