<template lang="pug">
  .hx-col.hx-cols.w-66
    template(v-if="details[betType] != null && details[betType][i] != null")
      .hx
        .hxs.w-100
          oddsItem(:odds="details[betType][i]" idx=5 :typ="oddsType" dataType="2")
      .hx
        .hxs.w-100
          oddsItem(:odds="details[betType][i]" idx=7 :typ="oddsType" dataType="2")
      .hx
        .hxs.w-100
          oddsItem(:odds="details[betType][i]" idx=6 :typ="oddsType" dataType="2")
</template>

<script>
// import oddsItem from "@/components/desktop/main/xtable/oddsItem";

export default {
  components: {
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem")
  },
  props: {
    details: {
      type: Object
    },
    oddsType: {
      type: String
    },
    i: {
      type: Number
    },
    betType: {
      type: String
    }
  }
}
</script>