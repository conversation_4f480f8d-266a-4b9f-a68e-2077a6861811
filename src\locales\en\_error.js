export default {
  OK: "OK",
  CLOSE: "Account has been closed!",
  <PERSON><PERSON><PERSON><PERSON>: "Account has been blocked!",
  FREEZE: "Account has been freezed!",
  SUSPEND: "Your account had changed to VIEW only.",
  passwordRequired: "Password is required!",
  usernameRequired: "Username is required!",
  incompletedRequest: "Something goes wrong...",
  unauthenticatedUser: "Invalid user authentication!",
  session_not_exists: "Session expired! Please login again to continue...",
  session_expired: "Session expired! Please login again to continue...",
  sessionExpired: "Session expired! Please login again to continue...",
  invalidSession: "Session expired! Please login again to continue...",
  accountIdRequired: "Session expired! Please login again to continue...",
  NoUserAccessRight: "Permission denied! Please contact your upline to continue...",
  account_not_exists: "Invalid password.",
  invalid_password: "Invalid password.",
  secondary_account_exists: "Your nickname already created.",
  systemError: "Internal System Error.",
  new_login_id_exists: "Invalid nickname, please try to set different nickname.",
  loginLimit: "You have login too frequent, please try again after 1 minute...",
  requestLimit: "You have request too frequent.",
  insufficient_balance: "Insufficient Balance",
  insufficientBalance: "Insufficient Balance",
  loginFailed: "Login failed, please try again later...",
  requestFailed: "Your connection unstable. Please check your connection and try again.",
  requestPending: "Request pending...",
  close: "Account is closed! Please contact your upline to continue...",
  suspend: "Account is suspend! Please contact your upline to continue...",
  freeze: "Account is freeze! Please contact your upline to continue...",
  alphaNumOnly: "Alphanumeric only",
  isRequired: "Please enter a {fname}.",
  isMinValue: "{fname} must have minimal of {fvalue}.",
  isMaxValue: "{fname} must be less than or equal to {fvalue}.",
  isMinLength: "{fname} must have at least {fvalue} characters.",
  isMaxLength: "{fname} must be less than or equal to {fvalue} characters.",
  isAlpha: "{fname} accept only alphabet.",
  isAlphaNum: "{fname} accept only alphanumerics.",
  isNumeric: "{fname} accept only numerics.",
  isEmail: "{fname} accept only valid email addresses.",
  isIpAddress: "{fname} accept only valid IPv4 addresses.",
  isSameAs: "{fname} must be same as {fname2}.",
  isUrl: "{fname} accept only URLs.",
  containAlphaNum: "{fname} must have at least one alphabet and one number.",
  selectOption: "Please select a {fname}",
  notSameAs: "{fname} and {fname2} cannot be the same.",
  currPasswordRequired: "Current password is required!",
  newPasswordRequired: "New password is required!",
  confirmNewPasswordRequired: "Confirm new password is required!",
  passwordsNotMatch: "Passwords do not match!",
  nickNameRequired: "Nickname is required!",
  startDateRequired: "Start date is required!",
  endDateRequired: "End date is required!",
  pageSizeRequired: "Page size is required!",
  pageNumberRequired: "Page number is required!",
  sportsTypeRequired: "Sports type is required!",
  workingDateRequired: "Working date is required!",
  leagueIdRequired: "League ID is required!",
  isOutrightRequired: "Is outright is required!",
  duplicate_debit_record: "Please wait a moment while odds is updating...",
  invalidOddsBall: "Please wait a second while odds is updating...",
  oddsDisplayInvalid: "Please wait while odds is updating...",
  oddsIsUpdating: "Odds Is Updating...",
  unableToGetBalanceAtTheMoment: "Internal Server Error.",
  atLeastTwoMatchToBetParlay: "At least mix 2 matches to bet in parlay.",
  atLeastThreeMatchToBetParlay: "At least mix 3 matches to bet in parlay.",
  maxParlayTicket: "Reach maximum ticket per parlay!",
  matchHide: "This match is not available, please try other matches. (318)",
  matchNotAllowBet: "This match is not available, please try other matches. (311)",
  matchNotActive: "This match is not available, please try other matches. (308)",
  invalidBets: "This market is not available, please try other markets. (340)",
  invalidBet: "This market is not available, please try other markets. (340)",
  invalidBetType: "This market is not available, please try other markets. (314)",
  invalidBetTeam: "This market is not available, please try other markets. (328)",
  invalidOdds: "This market is not available, please try other markets. (313)",
  betOverMaxPerMatch: "Your stake exceeds the max stake.",
  betOverMax: "Your stake exceeds the max stake.",
  betLowerMin: "Your stake less than the min stake.",
  betOverMaxPayout: "Your stake exceeds the max payout.",
  memberInactive: "This account status is not active, please check contact your upline.",
  memberCommissionNotSetup: "Commission not setup.",
  betOverMaxBet: "Bet amount over Max limit. Please check your bet limit.",
  invalidCurrency: "Invalid currency to bet.",
  betOverLimit: "Your stake exceeds the limit.",
  selectLeague: "Please select at least 1 league",
  game_maintenance: "Game is under maintenance.",
  betTypeRequired: "Bet type is required.",
  betAmountRequired: "Bet amount is required.",
  invalidBetAmount: "Invalid bet amount.",
  currencyNotSupported: "Currency Not Supported",
  forbiddenAccess: "Forbidden Access",
  connectionFailed: "Operator API failed to response.",
  liveCasinoDisabled: "Live Casino Disabled",
  lotteryDisabled: "Lottery Disabled",
  MiniGameDisabled: "Mini Game Disabled",
  EsportsDisabled: "E-Sports Game Disabled",
  login_id_not_exists: "Invalid login, please try to login again.",
  noMatchSelected: "Please select at least 1 match.",
  invalidRoomRate: "Invalid room rate.",
  invalidRoomLimit: "Invalid room limit.",
  invalidRoomPassword: "Invalid PIN Code. PIN code must be 6 digit numbers.",
  InvalidRoomPassword: "Invalid PIN Code. PIN code must be 6 digit numbers.",
  invalidPassword: "Invalid Password",
  InvalidPassword: "Invalid Password.",
  roomPasswordRequired: "PIN Code is required.",
  minMatch: "Minimum 3 matches selected!",
  maxMatch: "Maximum {max} matches selected!",
  roomIdRequired: "Room ID is required.",
  roomTypeRequired: "Room type is required.",
  sessionTokenRequired: "Session token is required.",
  matchDateRequired: "Match date is required.",
  matchRequired: "Match is required.",
  roomLimitRequired: "Room limit is required.",
  roomRateRequired: "Room rate is required.",
  minRoomRate: "Entry fee must be equal or more than 10.",
  maxRoomRate: "Entry fee must be equal or less than 1000.",
  minRoomLimit: "No. of Players must be equal or more than 3.",
  maxRoomLimit: "No. of Players must be equal or less than 30.",
  invalidRoomType: "Invalid room type.",
  invalidMatch: "Invalid match.",
  dataError: "Data Error",
  MemberExists: "Member already exist.",
  invalidMember: "Invalid member.",
  invalidleague: "Invalid league.",
  "createRoomFailed-1": "Create room failed 1.",
  "createRoomFailed-2": "Create room failed 2.",
  "createRoomFailed-3": "Create room failed 3.",
  invalidOperator: "Invalid operator.",
  roomFull: "Room is already full.",
  memberExists: "Member already exist.",
  "joinFailed-1": "Join room failed 1.",
  "joinFailed-2": "Join room failed 2.",
  IPJoinedBefore: "IP joined before.",
  memberJoinedBefore: "You have already joined before, please take note that public free room only can join once.",
  roomNotExists: "Room not exist.",
  roomIsPublicFree: "Room is public free.",
  roomEndedOrCancelled: "Room ended or cancelled.",
  roomEnded: "Room already ended.",
  matchLowerMin: "Match lower min error occured.",
  matchOverMax: "Match over max error occured.",
  pointsOverLimit: "Points over limit error occured.",
  roomOverMax: "Room over max error occured.",
  invalidRoom: "Invalid room.",
  roomLimitOverMax: "Room limit over max error occured.",
  rateLowerMin: "Rate lower min error occured.",
  rateOverMax: "Rate over max error occured.",
  liveMatchNotAllowed: "Live match not allowed.",
  roomLimitLowerMin: "Room limit lower min error occured.",
  roomClosed: "Room closed.",
  tournamentLeagueExists: "Tournament league exists.",
  roomExists: "Not allow to create more than 1 private room.",
  RoomExists: "Not allow to create more than 1 private room.",
  betExists: "Bet already exists.",
  betNotExists: "Please place a valid bet to join the tournament.",
  roomClosedNotAllowed: "Room closed not allowed.",
  tournamentMatchExists: "Tournament match exists.",
  account_id_required: "Account ID is required.",
  agent_comm_rate_required: "Agent commission rate is required.",
  agent_id_required: "Agent ID is required.",
  bet_amount_required: "Bet amount is required.",
  bet_id_required: "Bet ID is required.",
  credit_amount_required: "Credit amount is required.",
  currency_required: "Currency is required.",
  debit_amount_required: "Debit amount is required.",
  duplicate_credit_record: "Duplicate credit record.",
  master_comm_rate_required: "Master commission rate is required.",
  master_id_required: "Master ID is required.",
  member_id_required: "Member ID is required.",
  member_wallet_not_exists: "Member wallet not exists.",
  permission_denied: "Permission denied.",
  rate_required: "Rate is required.",
  result_exists: "Result already exists.",
  room_id_required: "Room ID is required.",
  s_senior_comm_rate_required: "Super Senior commission rate is required.",
  s_senior_id_required: "Super Senior ID is required.",
  senior_comm_rate_required: "Senior commission rate is required.",
  senior_id_required: "Senior ID is required.",
  shareholder_comm_rate_required: "Shareholder commission rate is required.",
  shareholder_id_required: "Shareholder ID is required.",
  transfer_id_required: "Transfer ID is required.",
  minBetAmount: "Minimum bet amount error occured.",
  tournamentDisabled: "The tournament is disabled, please check contact your upline.",
  marketTypeRequired: "Market Type is required.",
  invalidMarketType: "Invalid market type provided.",
  marketTypeChanged: "Bet Slip has been removed due to market type changed.<br />Please select again from the market.",
  internalServerError: "Internal Server Error!",
  refundTest: "Refund Testing!",
  matchChangeTime: "Room already closed.",
  changeStakeMin: "Input stake amount been changed due to MIN limit.",
  changeStakeMax: "Input stake amount been changed due to MAX limit.",
};
