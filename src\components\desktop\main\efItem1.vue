<template lang="pug">
.esports-gamelist
  .esports-row(:class="source.marketId == 3 ? 'game-top-line' : ''")
    .esports-cell.flex-grow-1.justify-content-start.w-130.pl-2(v-if="source.marketId == 3")
      .live-now
        img(src="/v1/images/esports/live-icon.svg")
        span.text-uppercase live
        i.fas.fa-circle
    .esports-cell.w-130(v-else)
      efTime(:source="source")
    .esports-cell.w-140.justify-content-start.pl-3.font-weight-normal {{ $t("m.BT_HDP") }}
    .esports-cell.w-160.bg-hero01-inner.flex
      .e-home-team {{ source.homeTeam }}
      img(:src="getImage(source.sportsId, source.matchBody[22])")
    .esports-cell.bg-01.w-65.ef-bet.ef-home
      template(v-if="details['hdp'] != null && details['hdp'][0] && details['hdp'][0][9] != 0 && details['hdp'][0][10] != 0 && details['hdp'][0][9] != '' && details['hdp'][0][10] != ''")
        oddsItem(:odds="details['hdp'][0]" :idx="details['hdp'][0][7] == 1 ? '10' : '9'" :typ="oddsType" dataType="1")
      template(v-else)
        i.fad.fa-lock-alt.text-muted
    .esports-cell.w-70.border-01.vs-small
      img(src="/v1/images/esports/vs.svg")
    .esports-cell.bg-01.w-65.ef-bet.ef-away
      template(v-if="details['hdp'] != null && details['hdp'][0] && details['hdp'][0][9] != 0 && details['hdp'][0][10] != 0 && details['hdp'][0][9] != '' && details['hdp'][0][10] != ''")
        oddsItem(:odds="details['hdp'][0]" :idx="details['hdp'][0][7] == 1 ? '9' : '10'" :typ="oddsType" dataType="1")
      template(v-else)
        i.fad.fa-lock-alt.text-muted
    .esports-cell.w-160.bg-hero02-inner.flex
      img(:src="getImage(source.sportsId, source.matchBody[23])")
      .e-away-team {{ source.awayTeam }}
    .esports-cell.bg-01.flex-fill
      .e-more(
        v-if="(details['ou'] != null && details['ou'][0] != null) || (details['oe'] != null && details['oe'][0] != null)"
        data-toggle="collapse",
        :data-target="'#ef-' + source.matchId"
        )
        i.fad.fa-chevron-circle-up
  .collapse.show(:id="'ef-' + source.matchId")
    .esports-table
      .esports-row(v-if="details['ou'] != null && details['ou'][0] != null")
        .esports-cell.w-130 &nbsp;
        .esports-cell.w-270.justify-content-start.pl-3.font-weight-normal
          span {{ $t("m.BT_OU") }}
          span.text-muted &nbsp;(
          span.text-white {{ details['ou'][0][8] }}
          span.text-muted )
        .esports-cell.text-uppercase.text-center.w-30.text-color01 o
        .esports-cell.bg-01.w-65.esports-pointer.ef-bet.ef-home.ef-away
          template(v-if="details['ou'] != null && details['ou'][0] != null && details['ou'][0][11] != 0 && details['ou'][0][12] != 0 && details['ou'][0][11] != '' && details['ou'][0][12] != ''")
            oddsItem(:odds="details['ou'][0]" idx=12 :typ="oddsType" dataType="1")
          template(v-else)
            i.fad.fa-lock-alt.text-muted
        .esports-cell.w-70.border-01.esports-pointer &nbsp;
        .esports-cell.bg-01.w-65.esports-pointer.ef-bet.ef-home.ef-away
          template(v-if="details['ou'] != null && details['ou'][0] != null && details['ou'][0][11] != 0 && details['ou'][0][12] != 0 && details['ou'][0][11] != '' && details['ou'][0][12] != ''")
            oddsItem(:odds="details['ou'][0]" idx=11 :typ="oddsType" dataType="1")
          template(v-else)
            i.fad.fa-lock-alt.text-muted
        .esports-cell.text-uppercase.text-center.w-30.text-color01 u
        .esports-cell.flex-fill &nbsp;
      .esports-row(v-if="details['oe'] != null && details['oe'][0] != null")
        .esports-cell.w-130 &nbsp;
        .esports-cell.w-270.justify-content-start.pl-3.font-weight-normal {{ $t("ui.ko") }}
        .esports-cell.text-uppercase.text-center.w-30.text-color01 o
        .esports-cell.bg-01.w-65.esports-pointer.ef-bet.ef-home.ef-away
          template(v-if="details['oe'] != null && details['oe'][0] != null && details['oe'][0][5] != 0 && details['oe'][0][7] != 0 && details['oe'][0][5] != '' && details['oe'][0][7] != ''")
            oddsItem(v-if="details['oe'][0][5] != 0" :odds="details['oe'][0]" cls="" idx=5 :typ="oddsType" dataType="2")
          template(v-else)
            i.fad.fa-lock-alt.text-muted
        .esports-cell.w-70.border-01.esports-pointer &nbsp;
        .esports-cell.bg-01.w-65.esports-pointer.ef-bet.ef-home.ef-away
          template(v-if="details['oe'] != null && details['oe'][0] != null && details['oe'][0][5] != 0 && details['oe'][0][7] != 0 && details['oe'][0][5] != '' && details['oe'][0][7] != ''")
            oddsItem(v-if="details['oe'][0][7] != 0" :odds="details['oe'][0]" cls="" idx=7 :typ="oddsType" dataType="2")
          template(v-else)
            i.fad.fa-lock-alt.text-muted
        .esports-cell.text-uppercase.text-center.w-30.text-color01 e
        .esports-cell.flex-fill &nbsp;
</template>

<script>
import config from "@/config";
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

export default {
  components: {
    efTime: () => import("@/components/desktop/main/efTime"),
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem"),
  },
  mixins: [mixinHDPOUOdds],
  props: {
    source: {
      type: Object,
    },
    index: {
      type: Number,
    },
  },
  computed: {
    newFeatures() {
      return config.newFeatures;
    },
    efsrc() {
      return config.efsrc;
    }
  },
  methods: {
    getImage(s, img) {
      // return config.resourceUrl + "/ef/" + s + "/" + img + ".png";
      return config.resourceUrl + "/ef/" + this.efsrc + "/" + s + "/" + img + ".png";
    },
  },
};
</script>
