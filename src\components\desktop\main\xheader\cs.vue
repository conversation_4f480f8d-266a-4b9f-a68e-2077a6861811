<template lang="pug">
  .hx-table.hx-top-rounded.hx-mt-2(:class="source.marketId == 3 ? 'live' : 'non-live'")
    .hx-cell.flex-fill
      .hx-row.h-100
        .d-flex.flex-row.align-items-center.h-100
          .pl-1
            img(:src="'/v1/images/icon-sport-svg/' + getImage(source.sportsId)" width="22")
          .d-flex.flex-row.align-items-baseline.align-item-margin
            .hx-header-title(:class="{ 'live' : source.marketId == 3 }") {{ source.marketType }}
            .hx-header-subtitle {{ source.sportsType }}
    template(v-if="pageType != '4'")
      .hx-cell.w-624
        .hx-row
          .hx-col.b-0.w-624
            .hx.text-center {{ $t("ui.full_time") }}
        .hx-row
          .hx-col.w-39(v-for="item in cs")
            .hx.text-center {{ item }}
    template(v-if="pageType == '4'")
      .hx-cell.w-429
        .hx-row
          .hx-col.b-0.w-429
            .hx.text-center {{ $t("ui.half_time") }}
        .hx-row
          .hx-col.w-39(v-for="item in csh")
            .hx.text-center {{ item }}
</template>

<script>
import config from "@/config";
export default {
  props: {
    source: {
      type: Object
    },
  },
  data() {
    return {
      cs: ['1-0', '2-0', '2-1', '3-0', '3-1', '3-2', '4-0', '4-1', '4-2', '4-3', '0-0', '1-1', '2-2', '3-3', '4-4', 'AOS'],
      csh: ['1-0', '2-0', '2-1', '3-0', '3-1', '3-2', '0-0', '1-1', '2-2', '3-3', 'AOS'],
    }
  },
  computed: {
    pageType() {
      return this.$store.getters.pageDisplay.pageType;
    }
  },
  methods: {
    getImage(e) {
      return config.getSportsImage(e);
    }
  }
}
</script>