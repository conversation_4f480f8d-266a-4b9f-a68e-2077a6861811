<template lang="pug">
#landing.landing2(v-if="!whiteLabel")
  #effect-rain1
  #effect-rain2
  .header2
    .container.d-flex.justify-content-between.align-items-center
      .logo
        img.img-fluid(:src="logoPath")
      login2
  section.slider-wrap
    .container 
      .swiper.lp-banner
        .swiper-wrapper
          .swiper-slide
            .banner-text
              h1 Sports Betting
              p With a comprehensive selection of over 90 sports and thousands of live matches to bet on, the adrenalin rush never ends.
            img.banner-desktop(src="v1/img2/landing2/banner/banner-sport.png")
            img.banner-mobile(src="v1/img2/landing2/banner/banner-sport-m.png")
          .swiper-slide
            .banner-text
              h1 Live Casino
              p Rake in winnings, all while enjoying a rich and immersive casino gaming experience at any of our luxurious suites.
            img.banner-desktop(src="v1/img2/landing2/banner/banner-casino.png")
            img.banner-mobile(src="v1/img2/landing2/banner/banner-casino-m.png")
          .swiper-slide
            .banner-text
              h1 Slot Games
              p An extensive selection of games, as far as the eye can see. Get a firsthand taste of it now.
            img.banner-desktop(src="v1/img2/landing2/banner/banner-slot.png")
            img.banner-mobile(src="v1/img2/landing2/banner/banner-slot-m.png")
          .swiper-slide
            .banner-text
              h1 E-Fighting
              p Collect your favorite characters, throw down in competitive combat, enter the battlefield and feel the fight!
            img.banner-desktop(src="v1/img2/landing2/banner/banner-efight.png")
            img.banner-mobile(src="v1/img2/landing2/banner/banner-efight-m.png")
          .swiper-slide
            .banner-text
              h1 Tournament
              p Get ready for the ultimate display for skills, strategy, and passion as teams from all over the city battle it out in the annual football tournament.
            img.banner-desktop(src="v1/img2/landing2/banner/banner-tournament.png")
            img.banner-mobile(src="v1/img2/landing2/banner/banner-tournament-m.png")
        .swiper-button-next
        .swiper-button-prev
        .swiper-pagination
  //- section.event-slider-wrap
  //-   .container
  //-     iframe.event-banner-iframe(src="https://banner.wbmarketingtools.com/en/", allowtransparency="true")
  //-     img.img-fluid(src="v1/img2/landing2/transparent-slider-height.png")
  section.widget-wrap
    .container
      .widget-content-wrapper
        .left
          .widget-box
            .widget-box-header {{ $t('ui.highlight') }}
            .widget-content
              iframe.hlighlight-iframe(:src="'https://wbetwidget.com/highlight?theme=6&lang=' + lang", allowtransparency="true")
        .right
          .widget-box
            .widget-box-header {{ $t('ui.upcoming') }}
            .widget-content
              iframe.hotmatch-iframe(:src="'https://wbetwidget.com/upcomingmatch?theme=6&lang=' + lang", allowtransparency="true")
          .widget-box.mt-4
            .widget-box-header {{ $t('ui.live_streaming') }}
            .widget-content
              iframe.livestream-iframe(:src="'https://wbetwidget.com/livestream?theme=6&lang=' + lang", allowtransparency="true")
  footer.footer
    .container.footer-wrapper
      .tnc
        .d-flex.align-items-center
          h4
            img(src="v1/img2/landing2/18plus.png")
            | Underage Gambling
        .d-flex
          p(style="color: #ffffff88;")
            | Gambling is illegal for kids under the age of 18. Every state prohibits gambling by minors.&nbsp;
            | IBCBET will carry out age verification randomly and winnings will be forfeited if the gambler is below 18 years old.&nbsp;
            | IBCBET is An Internationally Registered Trademark (Licensed Sports Bookmaker).&nbsp;
            | © Copyright 2024 IBCBET. All Rights Reserved.
      .row.pt-4.pb-4
        .col-8.license
          h4 License
          .d-flex.align-items-center.license-logo
            .image-wrap2
              img(src="v1/img2/landing2/license/01.png")
            .image-wrap2
              img(src="v1/img2/landing2/license/02.png")
            .image-wrap2
              img(src="v1/img2/landing2/license/03.png")
            .image-wrap2
              img(src="v1/img2/landing2/license/04.png")
            .image-wrap2
              img(src="v1/img2/landing2/license/05.png")
            .image-wrap2
              img(src="v1/img2/landing2/license/06.png")
            .image-wrap2
              img(src="v1/img2/landing2/license/07.png")
            .image-wrap2
              img(src="v1/img2/landing2/license/08.png")
        .col-4
          h4 Suggest Browser
          .d-flex.align-items-center.browser-logo
            .image-wrap2
              img(src="img2/landing2/browser/chrome.png")
            .image-wrap2
              img(src="img2/landing2/browser/edge.png")
            .image-wrap2
              img(src="img2/landing2/browser/firefox.png")
            .image-wrap2
              img(src="img2/landing2/browser/safari.png")
    .copyright
      .container © Copyright 2024 IBCBET Sportsbook
</template>

<script>
import config from "@/config";
import errors from "@/errors";

export default {
  components: {
    logo: () => import("@/components/desktop/logo"),
    login2: () => import("@/components/desktop/login2"),
  },
  data() {
    return {};
  },
  computed: {
    logoPath() {
      return config.logoPath;
    },
    brand() {
      return config.brand;
    },
    whiteLabel() {
      return config.whiteLabel;
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
    lang() {
      return this.$store.getters.language;
    },
  },
  destroyed() {
  },
  mounted() {
    this.$nextTick(() => {
      var swiper = new Swiper(".lp-banner", {
        speed: 400,
        loop: true,
        autoplay: true,
        draggable: true,

        navigation: {
          nextEl: ".lp-banner .swiper-button-next",
          prevEl: ".lp-banner .swiper-button-prev",
        },
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
      });
    });
  },
  methods: {
    getImage(fn) {
      return config.getLandingUrl(fn);
    },
    hardRefresh() {
      window.location.reload(true);
    },
    desktop() {
      this.$router.push("/desktop").catch((err) => {
        console.trace(err);
      });
    },
  },
};
</script>
