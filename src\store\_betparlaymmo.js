import Vue from "vue";

export default {
  namespaced: true,
  state: {
    combo: {},
    data: {}
  },
  mutations: {
    updateData(state, payload) {
      var matchid = payload.parent != undefined && typeof payload.parent != "object" ? payload.parent : payload.matchId;
      Vue.set(state.data, matchid, payload);
    },
    deleteData(state, payload) {
      // console.log("deleteDataMMO", state.data, payload);
      Vue.delete(state.data, payload);
    },
    purgeData(state, payload) {
      // console.log("purgeDataMMO", state.data, payload);
      for (var n in state.data) {
        // console.log("purgeDataMMO", n);
        Vue.delete(state.data, n);
      }
      // console.log("purgeDataMMO", state.data, payload);
    },
    updateSingleData(state, payload) {
      if (payload.property != undefined) {
        state.data[payload.matchid][payload.property] = payload.value;
      }
    }
  },
  actions: {
    setData(context, payload) {
      context.commit("updateData", payload);
    },
    removeData(context, payload) {
      context.commit("deleteData", payload);
    },
    clearData(context, payload) {
      context.commit("purgeData");
    },
    setSingleData(context, payload) {
      context.commit("updateSingleData", payload);
    }
  }
};
