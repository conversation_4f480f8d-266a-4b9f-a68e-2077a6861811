# Vue 2 + Pinia Setup Example

## Step-by-Step Implementation

### Step 1: Install Pinia for Vue 2

```bash
# Install Pinia with Vue 2 support
yarn add pinia@^2.1.0 pinia-plugin-persistedstate
```

### Step 2: Setup Pinia Alongside Vuex

```javascript
// src/main.js - Modified to include Pinia
import Vue from "vue";
import VueMeta from "vue-meta";
Vue.use(VueMeta);

// Existing imports
import router from "@/router";
import store from "@/store"; // Keep existing Vuex store
import i18n from "@/i18n";
import App from "@/app.vue";

// Add Pinia imports
import { PiniaVuePlugin, createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

// Setup Pinia
Vue.use(PiniaVuePlugin)
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// ... other Vue.use() calls remain the same

// Initialize app with both stores
export const app = new Vue({
  router,
  store,    // Keep existing Vuex store
  pinia,    // Add Pinia
  i18n,
  mounted() {
    this.$store.dispatch("layout/setLanguage", this.$store.getters.language);
  },
  render: (h) => h(App),
}).$mount("#app");
```

### Step 3: Create First Pinia Store (User Store)

```javascript
// src/stores/user.js - New Pinia store
import { defineStore } from 'pinia'
import errors from "@/errors"
import service from "@/library/_xhr-user"

export const useUserStore = defineStore('user', {
  state: () => ({
    balance: 0,
    account: {},
    rememberMe: null,
    isPublic: false
  }),

  getters: {
    // Convert Vuex getters to Pinia getters
    isLoggedIn: (state) => {
      return state.account?.player_info?.session_token ? true : false
    },
    
    playerInfo: (state) => {
      return state.account?.player_info || {}
    },
    
    playerWallet: (state) => {
      return state.account?.player_wallet || {}
    },
    
    playerBetLimit: (state) => {
      return state.account?.player_bet_limit || {}
    },
    
    currencyCode: (state) => {
      return state.account?.player_wallet?.currency_code || null
    },
    
    sessionToken: (state) => {
      return state.account?.player_info?.session_token || null
    },
    
    accountId: (state) => {
      return state.account?.player_info?.account_id || null
    },
    
    nickName: (state) => {
      return state.account?.player_info?.nickname || null
    },
    
    hasSecondaryAccount: (state) => {
      return state.account?.player_info?.has_secondary_account || null
    },
    
    mmoMode: (state) => {
      const currencyCode = state.account?.player_wallet?.currency_code
      return currencyCode ? ["MMK", "MMO"].includes(currencyCode) : false
    }
  },

  actions: {
    // Convert Vuex mutations to Pinia actions
    updateAccount(payload) {
      this.account = payload
      if (this.account.player_wallet) {
        this.balance = this.account.player_wallet.available_balance
      }
    },

    deleteAccount() {
      this.account = {}
      this.balance = 0
    },

    updateBalance(payload) {
      this.balance = payload.balance
      if (this.account?.player_wallet) {
        this.account.player_wallet.available_balance = payload.balance
        this.account.player_wallet.cash_balance = payload.account_balance
        this.account.player_wallet.frozen_balance = payload.outstanding_balance
      }
    },

    updateNickname(payload) {
      if (this.account?.player_info) {
        this.account.player_info.nickname = payload
        this.account.player_info.has_secondary_account = true
      }
    },

    updateRememberMe(payload) {
      this.rememberMe = payload
    },

    // Convert Vuex actions to Pinia actions
    async doLaunch(user) {
      return await service.doLaunch(this, user)
    },

    async doSwitch(user) {
      return await service.doSwitch(this, user)
    },

    async doLogin(user) {
      return await service.doLogin(this, user)
    },

    async doLogout() {
      const feedback = {
        success: true,
        status: errors.logout.succeed
      }
      this.deleteAccount()
      return feedback
    },

    clear() {
      this.deleteAccount()
    },

    async reLogin() {
      return await service.reLogin(this)
    },

    async getBalance() {
      return await service.getBalance(this)
    },

    async setNickname(payload) {
      this.updateNickname(payload)
      return {
        success: true,
        status: errors.request.succeed
      }
    },

    async setSettings(payload) {
      return await service.setSettings(this, payload)
    },

    setRememberMe(payload) {
      this.updateRememberMe(payload)
    }
  },

  // Configure persistence
  persist: {
    key: 'user-storage-pinia',
    storage: localStorage,
    paths: ['account', 'rememberMe'], // Only persist specific state
  }
})
```

### Step 4: Create Store Directory Structure

```
src/
├── stores/
│   ├── index.js          # Optional: export all stores
│   ├── user.js          # User store (migrated)
│   └── layout.js        # Layout store (to be migrated)
├── store/               # Keep existing Vuex store
│   ├── index.js         # Existing Vuex store
│   ├── _user.js         # Will be deprecated after migration
│   └── ... other stores
```

### Step 5: Optional Store Index File

```javascript
// src/stores/index.js - Optional centralized exports
export { useUserStore } from './user'
// export { useLayoutStore } from './layout'  // When migrated
```

### Step 6: Update a Component to Use Pinia

```vue
<!-- Example: Update a component to use Pinia instead of Vuex -->
<template>
  <div>
    <p>Balance: {{ userStore.balance }}</p>
    <p>User: {{ userStore.nickName }}</p>
    <p>Logged in: {{ userStore.isLoggedIn }}</p>
    <button @click="logout" :disabled="isLoading">
      {{ isLoading ? 'Logging out...' : 'Logout' }}
    </button>
  </div>
</template>

<script>
import { useUserStore } from '@/stores/user'

export default {
  name: 'UserProfile',
  data() {
    return {
      isLoading: false
    }
  },
  computed: {
    userStore() {
      return useUserStore()
    }
  },
  methods: {
    async logout() {
      this.isLoading = true
      try {
        await this.userStore.doLogout()
        this.$router.push('/')
      } catch (error) {
        console.error('Logout failed:', error)
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>
```

### Step 7: Gradual Migration Strategy

```javascript
// src/components/mixins/userMixin.js
// Create a mixin to ease transition
export const userMixin = {
  computed: {
    // Provide both Vuex and Pinia access during transition
    userStore() {
      return useUserStore()
    },
    
    // Proxy Vuex getters to Pinia for backward compatibility
    isLoggedIn() {
      return this.userStore.isLoggedIn
    },
    
    balance() {
      return this.userStore.balance
    },
    
    playerInfo() {
      return this.userStore.playerInfo
    }
  },
  
  methods: {
    // Proxy Vuex actions to Pinia
    async doLogout() {
      return await this.userStore.doLogout()
    },
    
    async getBalance() {
      return await this.userStore.getBalance()
    }
  }
}
```

### Step 8: Testing Both Stores

```javascript
// tests/unit/stores/user.spec.js
import { setActivePinia, createPinia } from 'pinia'
import { useUserStore } from '@/stores/user'

describe('User Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('initializes with correct default state', () => {
    const userStore = useUserStore()
    expect(userStore.balance).toBe(0)
    expect(userStore.account).toEqual({})
    expect(userStore.isLoggedIn).toBe(false)
  })

  it('updates account correctly', () => {
    const userStore = useUserStore()
    const mockAccount = {
      player_info: { session_token: 'abc123' },
      player_wallet: { available_balance: 1000 }
    }
    
    userStore.updateAccount(mockAccount)
    
    expect(userStore.account).toEqual(mockAccount)
    expect(userStore.balance).toBe(1000)
    expect(userStore.isLoggedIn).toBe(true)
  })

  it('clears account on logout', async () => {
    const userStore = useUserStore()
    userStore.updateAccount({
      player_info: { session_token: 'abc123' },
      player_wallet: { available_balance: 1000 }
    })
    
    await userStore.doLogout()
    
    expect(userStore.account).toEqual({})
    expect(userStore.balance).toBe(0)
    expect(userStore.isLoggedIn).toBe(false)
  })
})
```

### Step 9: Update Service Layer

```javascript
// src/library/_xhr-user.js - Update to work with both Vuex and Pinia
import config from "@/config"

export default {
  async doLogin(context, user) {
    // context could be Vuex context or Pinia store
    try {
      const response = await this.makeRequest('/api/login', {
        method: 'POST',
        data: user
      })
      
      // Update account in store (works with both Vuex and Pinia)
      if (context.commit) {
        // Vuex context
        context.commit('updateAccount', response.data)
      } else {
        // Pinia store
        context.updateAccount(response.data)
      }
      
      return {
        success: true,
        data: response.data
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  },

  async getBalance(context) {
    try {
      const response = await this.makeRequest('/api/balance')
      
      // Update balance in store
      if (context.commit) {
        // Vuex context
        context.commit('updateBalance', response.data)
      } else {
        // Pinia store
        context.updateBalance(response.data)
      }
      
      return {
        success: true,
        data: response.data
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  },

  async makeRequest(url, options = {}) {
    // Implement your HTTP request logic
    // This is a placeholder
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            balance: 1000,
            account_balance: 1000,
            outstanding_balance: 0
          }
        })
      }, 1000)
    })
  }
}
```

### Step 10: Migration Checklist

**Phase 1 - Setup (Complete)**
- [x] Install Pinia and plugins
- [x] Set up Pinia alongside Vuex
- [x] Create stores directory structure
- [x] Create first Pinia store (user)

**Phase 2 - Testing**
- [ ] Test Pinia store independently
- [ ] Test component with Pinia store
- [ ] Verify persistence works
- [ ] Test service layer compatibility

**Phase 3 - Gradual Migration**
- [ ] Update 1-2 components to use Pinia
- [ ] Create migration mixin for backward compatibility
- [ ] Update tests for Pinia store
- [ ] Document migration patterns

**Phase 4 - Complete Migration**
- [ ] Update all components using user store
- [ ] Remove Vuex user store
- [ ] Update service layer to use Pinia only
- [ ] Remove migration mixins

### Benefits of This Approach

1. **Zero Downtime**: Both stores work simultaneously
2. **Gradual Migration**: Migrate components one by one
3. **Easy Rollback**: Can revert individual components
4. **Testing**: Test new store without affecting existing functionality
5. **Team Learning**: Developers can learn Pinia gradually

### Common Gotchas

1. **State Synchronization**: Keep both stores in sync during transition
2. **Persistence Keys**: Use different keys for Vuex and Pinia persistence
3. **Service Layer**: Update services to work with both store types
4. **Testing**: Mock both stores in tests during transition

---

*This example shows how to safely introduce Pinia into your Vue 2 application while maintaining full backward compatibility.* 