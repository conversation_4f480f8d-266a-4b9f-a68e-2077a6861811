<template lang="pug">
#left-bar.left(:class="{ active: !sidebar, extend1: whiteLabel }")
  .x-side.d-flex.flex-column
    #user-info.user-info-wrapper(v-if="whiteLabel")
      .player.d-flex.flex-row.align-items-center.justify-content-center
        .user.flex-fill.text-left.p-2.d-flex.align-items-center
          i.fad.fa-user.mr-1
          span {{ account_id }}
      .details.d-flex
        #user-profile.balance-text.collapsed(data-toggle="collapse" data-target="#balance-collapse" aria-expanded="false" aria-controls="balance-collapse")
          span.pr-2 {{ $t("ui.balance") }}
          .arrow-up.active-none
            i.far.fa-chevron-up
        .balance
          .caption {{ currency_code }}
          .value {{ available_balance }}
      #balance-collapse.multi-collapse.profile.collapse(style="")
        .dropdown-panel.rounded-0(v-if="operatorType == 1")
          .dropdown-li.rounded-0
            .caption {{ $t('ui.cash_balance') }}
            .value.d-flex.flex-row.align-items-start.justify-content-end
              .amount {{ cash_balance }}
          .dropdown-li
            .caption {{ $t('ui.outstanding') }}
            .value.d-flex.flex-row.align-items-start.justify-content-end
              .amount {{ frozen_balance }}
          .dropdown-li
            .caption {{ $t('ui.bet_credit') }}
            .value.d-flex.flex-row.align-items-start.justify-content-end
              .amount {{ available_balance }}
          .dropdown-li
            .caption {{ $t('ui.last_login') }}
            .value.d-flex.flex-row.align-items-start.justify-content-end
              .unit {{ last_login_date }}
              .amount {{ last_login_time }}
        .dropdown-panel.rounded-0(v-else)
          .dropdown-li.rounded-0
            .caption {{ $t('ui.cash_balance') }}
            .value.d-flex.flex-row.align-items-start.justify-content-end
              .amount {{ cash_balance }}
          .dropdown-li
            .caption {{ $t('ui.outstanding') }}
            .value.d-flex.flex-row.align-items-start.justify-content-end
              .amount {{ frozen_balance }}
          .dropdown-li
            .caption {{ $t('ui.last_login') }}
            .value.d-flex.flex-row.align-items-start.justify-content-end
              .unit {{ last_login_date }}
              .amount {{ last_login_time }}
    #accordion-sports.x-accordion.accordion
      .bg
        .side-column.tb.xb
          .nav-header.nav-left(:title="$t('ui.bet_slip')")
            #heading-betslip(data-toggle="collapse" data-target="#collapse-betslip" aria-expanded="false" aria-controls="collapse-betslip")
              .d-flex.justify-content-center.align-items-center.flex-row
                .live-icon
                  i.fad.fa-cart-plus
                .flex-fill.active-none.text-left.text-ellipsis
                  span {{ $t("ui.bet_slip") }}
                .live-icon.active-none.arrow-up
                  i.far.fa-chevron-up
          .nav-header.nav-right(:title="$t('ui.my_bet')")
            #heading-mybet.collapsed(data-toggle="collapse" data-target="#collapse-mybet" aria-expanded="false" aria-controls="collapse-mybet")
              .d-flex.justify-content-center.align-items-center.flex-row
                .live-icon
                  i.fad.fa-receipt
                .flex-fill.active-none.text-left.text-ellipsis
                  span {{ $t("ui.my_bet") }}
                .live-icon.active-none.arrow-up
                  i.far.fa-chevron-up
        #collapse-betslip.collapse.show(aria-labelledby="heading-betslip" data-parent="#accordion-sports")
          betSlip
        #collapse-mybet.collapse(aria-labelledby="heading-mybet" data-parent="#accordion-sports")
          betList
      //--------------------------------
      //- CATEGORY MENU
      //- COPA AMERICA
      menu-special-event(
        v-if="SPECIAL_EVENTS[0]"
        startDate="2025-01-01T00:00:00"
        :title="$t('ui.event_copa')"
        :icon="'img/copa24.png'"
        :menuId="'1'"
        :menu0="menu0"
        :menu1="menu1"
        :menu3="menu3"
        :menuY="menuY"
        @setMenu0="setMenu0"
      )
      //- EURO CUP
      menu-special-event(
        v-if="SPECIAL_EVENTS[1]"
        startDate="2025-01-01T00:00:00"
        :title="$t('ui.event_euro')"
        :icon="'img/euro24.png'"
        :menuId="'2'"
        :menu0="menu0"
        :menu1="menu1"
        :menu3="menu3"
        :menuY="menuY"
        @setMenu0="setMenu0"
      )
      //- WORLD CUP
      menu-special-event(
        v-if="SPECIAL_EVENTS[2]"
        startDate="2025-01-01T00:00:00"
        :title="$t('ui.event_worldcup')"
        :icon="'img/worldcup.svg'"
        :menuId="'3'"
        :menu0="menu0"
        :menu1="menu1"
        :menu3="menu3"
        :menuY="menuY"
        @setMenu0="setMenu0"
      )
      //- OLYMPIC
      menu-special-event(
        v-if="SPECIAL_EVENTS[3]"
        startDate="2025-01-01T00:00:00"
        :title="$t('ui.event_olympic')"
        :icon="'img/olympic2024.png'"
        :menuId="'4'"
        :menu0="menu0"
        :menu1="menu1"
        :menu3="menu3"
        :menuY="menuY"
        @setMenu0="setMenu0"
      )
      //- CLUB WORLD CUP
      menu-special-event(
        v-if="SPECIAL_EVENTS[4]"
        startDate="2025-06-14T12:00:00"
        :title="$t('ui.event_club_worldcup')"
        :icon="'img/club-world-cup.svg'"
        :menuId="'5'"
        :menu0="menu0"
        :menu1="menu1"
        :menu3="menu3"
        :menuY="menuY"
        @setMenu0="setMenu0"
      )
      //- WOMEN'S EURO
      menu-special-event(
        v-if="SPECIAL_EVENTS[5]"
        startDate="2025-07-02T12:00:00"
        :title="$t('ui.event_women_euro')"
        :icon="'img/women-euro.svg'"
        :menuId="'6'"
        :menu0="menu0"
        :menu1="menu1"
        :menu3="menu3"
        :menuY="menuY"
        @setMenu0="setMenu0"
      )
      //- NATION LEAGUE
      menu-special-event(
        v-if="SPECIAL_EVENTS[6]"
        startDate="2025-06-04T12:00:00"
        :title="$t('ui.event_nation_league')"
        :icon="'img/nation-league.svg'"
        :menuId="'7'"
        :menu0="menu0"
        :menu1="menu1"
        :menu3="menu3"
        :menuY="menuY"
        @setMenu0="setMenu0"
      )
      //- FIFA CLUB
      menu-special-event(
        v-if="SPECIAL_EVENTS[7]"
        startDate="2025-06-14T00:00:00"
        :title="$t('ui.event_fifa_club')"
        :icon="'img/fifa-club.svg'"
        :menuId="'8'"
        :menu0="menu0"
        :menu1="menu1"
        :menu3="menu3"
        :menuY="menuY"
        @setMenu0="setMenu0"
      )

      //- VGAMES MENU
      menu-v-games(
        :menu0="menu0"
        :menu1="menu1"
        :menu2="menu2"
        :menuY="menuY"
        :menuList="menuList"
        @setMenu2="setMenu2"
      )
      //- LIVE MENU
      .bg
        .side-row.xb
          #heading-live.heading-collapse(
            data-toggle="collapse"
            data-target="#collapse-live"
            aria-expanded="false"
            aria-controls="collapse-live"
            @click="setMenu0('live')"
            )
            .group(:title="$t('ui.inplay')" :class="{ 'selected' : menu0 == 'live'}")
              .d-flex.justify-content-center.align-items-center
                .live-icon
                  i.fad.fa-running
                .flex-fill.active-none
                  span {{ $t("ui.live") }}
                .number.active-none(v-if="menuList.live_count > 0") {{ menuList.live_count }}
        #collapse-live.collapse.xb(aria-labelledby="heading-live" data-parent="#accordion-sports")
          optionList(
            ref="livePanel"
            :order="order"
            :storage="liveOptions"
            :items="liveMenu"
            :menu="menuList.menu"
            :sports="menuList.sports"
            @save="saveLive")

      //- MMO PARLAY MENU
      .bg.bg-mmo(v-if="mmoMode && menuList.parlay_count > 0")
        .side-row.xb
          #heading-mmo-parlay.heading-collapse.collapsed(
            data-toggle="collapse"
            data-target="#collapse-mmo-parlay"
            aria-expanded="false"
            aria-controls="collapse-mmo-parlay"
            )
            .group(:title="$t('ui.mmo_parlay')" :class="{ 'selected' : menuX }")
              .d-flex.justify-content-center.align-items-center
                .live-icon(styl)
                  img(src="/v1/images/lang/my.svg?v=muQbdTxYGU")
                .flex-fill.active-none
                  span {{ $t("ui.mm_odds") }}
                .arrow-up.active-none
                  i.far.fa-chevron-up
        #collapse-mmo-parlay.collapse.xb(aria-labelledby="heading-mmo-parlay" data-parent="#accordion-sports")
          .group.sub.group-mmo
            ul.subgroup
              li.small
                a(
                  href="javascript:void(0);"
                  @click="setMenu0('early', true, null, null, null, true)"
                )
                  .d-flex(:class="{ 'active' : menuX && menu1 == 'early' }")
                    .subtitle.flex-fill
                      span {{ $t("ui.early") }}
                    .game-number
              li.small
                a(
                  href="javascript:void(0);"
                  @click="setMenu0('today', true, null, null, null, true)"
                )
                  .d-flex(:class="{ 'active' : menuX && menu1 == 'today' }")
                    .subtitle.flex-fill
                      span {{ $t("ui.today") }}
                    .game-number
              li.small
                a(
                  href="javascript:void(0);"
                  @click="setMenu0('parlay', true, null, null, null, true)"
                )
                  .d-flex(:class="{ 'active' : menuX && menu1 == 'parlay' }")
                    .subtitle.flex-fill
                      span {{ $t("m.BT_PARLAY") }}
                    .game-number

      //- ALL SPORTS MENU
      .bg
        .side-row.xb
          #heading-allsports.heading-collapse
            .group(
              :title="$t('ui.all_sports')"
              data-toggle="collapse"
              data-target="#collapse-allsports"
              aria-expanded="true"
              aria-controls="collapse-allsports"
              :class="{ selected : menu0 == 'all'}"
              )
              .d-flex.justify-content-center.align-items-center
                .live-icon
                  img(src="/v1/images/icon-sport-svg/0.svg")
                .flex-fill.active-none
                  span {{ $t("ui.all_sports") }}
                .arrow-up.active-none
                  i.far.fa-chevron-up
        #collapse-allsports.xb.collapse(ref="allsports" aria-labelledby="heading-allsports" data-parent="#accordion-sports")
          #accordion-allsports
            #market-group.group
              .market-icon(:title="$t('ui.' + menu1)")
                i.fad.fa-chevron-double-right(v-if="!['today','early','parlay'].includes(menu1)")
                i.fad.fa-clock(v-if="menu1 == 'today'")
                i.fad.fa-calendar-alt(v-if="menu1 == 'early'")
                i.fad.fa-tasks(v-if="menu1 == 'parlay'")
              ul.nav.market-menu(role="tablist")
                li.nav-item.sport-type(:title="$t('ui.early')" :class="{ active: (!menuX && menu0 =='all' && menu1 =='early')}" @click="setMenu1('early')")
                  a#market-menu-e.nav-link.text-ellipsis(
                    data-toggle="pill"
                    href="#market-tab-e"
                    role="tab"
                    aria-controls="market-tab-e"
                    aria-selected="true"
                    :title="$t('ui.early')"
                    )
                    i.fad.fa-calendar-alt
                    span {{ $t("ui.early") }}
                li.nav-item.sport-type(:title="$t('ui.today')" :class="{ active: (!menuX && menu0 =='all' && menu1 =='today')}" @click="setMenu1('today')")
                  a#market-menu-t.nav-link.text-ellipsis(
                    data-toggle="pill"
                    href="#market-tab-t"
                    role="tab"
                    aria-controls="market-tab-t"
                    aria-selected="true"
                    :title="$t('ui.today')"
                    )
                    i.fad.fa-clock
                    span {{ $t("ui.today") }}
                li.nav-item.sport-type(:title="$t('ui.parlay')" :class="{ active: (!menuX && menu0 =='parlay' && menu1 =='parlay')}" @click="setMenu1('parlay')")
                  a#market-menu-p.nav-link.text-ellipsis(
                    data-toggle="pill"
                    href="#market-tab-p"
                    role="tab"
                    aria-controls="market-tab-p"
                    aria-selected="true"
                    :title="$t('ui.parlay')"
                    @click="handleParlay"
                    )
                    i.fad.fa-tasks
                    span {{ $t("ui.parlay") }}
            #market-tab.tab-content(:class="{'higher': !header}")
              #market-tab-e.tab-pane.magicY.show(role="tabpanel" aria-labelledby="market-menu-e" :class="{ active: (!menuX && menu1 =='early'), mmo: mmoMode}")
                template(v-for="(n, index) in order")
                  listItem(
                    :uid="'1-' + n"
                    :menu0="menu0"
                    :menu1="menu1"
                    :menu2="menu2"
                    :menu3="menu3"
                    :index="index"
                    :item="earlyMenu[n]"
                    :name="sportsName[n]"
                    :isLive="getLive(n) > 0"
                    @setMenu2="setMenu2"
                    @setMenu3="setMenu3"
                    :sportsType="n"
                    )
              #market-tab-t.tab-pane.magicY(role="tabpanel" aria-labelledby="market-menu-t" :class="{ active: (!menuX && menu1 =='today'), mmo: mmoMode}")
                template(v-for="(n, index) in order")
                  template(v-if="!gameList.includes(n.toString())")
                    listItem(
                      :uid="'2-' + n"
                      :menu0="menu0"
                      :menu1="menu1"
                      :menu2="menu2"
                      :menu3="menu3"
                      :index="index"
                      :item="todayMenu[n]"
                      :name="sportsName[n]"
                      :isLive="getLive(n) > 0"
                      @setMenu2="setMenu2"
                      @setMenu3="setMenu3"
                      :sportsType="n"
                      )
              #market-tab-p.tab-pane.magicY(role="tabpanel" aria-labelledby="market-menu-p" :class="{ active: (!menuX && menu1 =='parlay'), mmo: mmoMode}")
                #collapse-parlay.h-100
                  optionList(
                    cls="active-none"
                    ref="parlayPanel"
                    :order="order"
                    :storage="parlayOptions"
                    :items="parlayMenu"
                    :menu="menuList.menu"
                    :sports="menuList.sports"
                    @save="saveParlay")
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";
import naming from "@/library/_name.js";
import config from "@/config";
import xhr from "@/library/_xhr-menu";
import sync from "@/library/_sync-menu";
import mixinTypes from "@/library/mixinTypes";
import betSlip from "@/components/desktop/left/betSlip";
import betList from "@/components/desktop/left/betList";
import listItem from "@/components/desktop/left/listItem";
import optionList from "@/components/desktop/left/optionList";
import menuVGames from "@/components/desktop/left/menuVGames";
import menuSpecialEvent from "@/components/desktop/left/menuSpecialEvent";

export default {
  components: {
    betSlip,
    betList,
    listItem,
    optionList,
    menuVGames,
    menuSpecialEvent,
  },
  mixins: [mixinTypes],
  data() {
    return {
      loading: false,
      menuList: {},
      firstLoad: true,
      SPECIAL_EVENTS: [0, 0, 0, 0, 1, 1, 0, 0]
    };
  },
  computed: {
    gameList() {
      return config.gameList;
    },
    params() {
      return config.params;
    },
    newFeatures() {
      return config.newFeatures;
    },
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
    menu() {
      if (!this.menuList) {
        return {};
      }

      if (!this.menuList.menu) {
        return {};
      }

      switch (this.menu1) {
      case "early":
        if (this.menuList.menu["1"]) return this.menuList.menu["1"];
        else return {};
      case "today":
        if (this.menuList.menu["2"]) return this.menuList.menu["2"];
        else return {};
      case "live":
        if (this.menuList.menu["3"]) return this.menuList.menu["3"];
        else return {};
      case "parlay":
        if (this.menuList.menu["4"]) return this.menuList.menu["4"];
        else return {};
      default:
        return {};
      }
    },
    getLatest() {
      if (this.menuList.menu && this.menuList.menu.hasOwnProperty("2")) {
        var m = Object.keys(this.menuList.menu["2"]);
        var n = this.gameList.filter((value) => m.includes(value));
        n.sort((a, b) => {
          return a - b;
        });
        return n[n.length - 1];
      } else {
        return null;
      }
    },
    getGameList() {
      if (this.menuList.menu && this.menuList.menu.hasOwnProperty("2")) {
        var m = Object.keys(this.menuList.menu["2"]);
        // console.log(this.menuList.menu, m);
        var n = this.gameList.filter((value) => m.includes(value));
        return n;
      } else {
        return [];
      }
    },
    sportsName() {
      if (!this.menuList) {
        return {};
      }

      if (!this.menuList.sports) {
        return {};
      }

      return this.menuList.sports;
    },
    earlyMenu() {
      if (!this.menuList) {
        return {};
      }

      if (!this.menuList.menu) {
        return {};
      }

      if (this.menuList.menu["1"]) return this.menuList.menu["1"];
      else return {};
    },
    todayMenu() {
      if (!this.menuList) {
        return {};
      }

      if (!this.menuList.menu) {
        return {};
      }

      if (this.menuList.menu["2"]) return this.menuList.menu["2"];
      else return {};
    },
    liveMenu() {
      if (!this.menuList) {
        return {};
      }

      if (!this.menuList.menu) {
        return {};
      }

      var m = this.menuList.menu["3"];
      if (m) {
        var n = Object.keys(m);
        var p = {};
        for (var i = 0; i < n.length; i++) {
          if (!this.gameList.includes(String(n[i]))) {
            p[n[i]] = m[n[i]];
          }
        }
        return p;
      } else {
        return {};
      }
    },
    parlayMenu() {
      if (!this.menuList) {
        return {};
      }

      if (!this.menuList.menu) {
        return {};
      }

      var m = this.menuList.menu["4"];
      if (m) {
        var n = Object.keys(m);
        var p = {};
        for (var i = 0; i < n.length; i++) {
          if (!this.gameList.includes(String(n[i]))) {
            p[n[i]] = m[n[i]];
          }
        }
        return p;
      } else {
        return {};
      }
    },
    displayName() {
      if (this.nickname) return this.nickname;
      else return this.account_id;
    },
    account_id() {
      return this.$store.getters.accountId;
    },
    nickname() {
      return this.$store.getters.nickName;
    },
    last_login_date() {
      if (this.$store.getters.playerInfo && this.$store.getters.playerInfo.last_login_time) return this.$dayjs(this.$store.getters.playerInfo.last_login_time).format("MM/DD/YYYY");
      else return "";
    },
    last_login_time() {
      if (this.$store.getters.playerInfo && this.$store.getters.playerInfo.last_login_time) return this.$dayjs(this.$store.getters.playerInfo.last_login_time).format("h:mm:ss A");
      else return "-";
    },
    wallet() {
      if (this.$store.getters.playerWallet) return this.$store.getters.playerWallet;
      return null;
    },
    bet_limit() {
      if (this.$store.getters.playerBetLimit) return this.$store.getters.playerBetLimit;
      return null;
    },
    balance() {
      return this.$store.getters.balance;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
    available_balance() {
      if (this.wallet) {
        return this.$numeral(this.balance).format("0,0.00");
      }
      return "-";
    },
    cash_balance() {
      if (this.wallet) {
        return this.$numeral(this.wallet.cash_balance).format("0,0.00");
      }
      return "-";
    },
    frozen_balance() {
      if (this.wallet) {
        return this.$numeral(this.wallet.frozen_balance).format("0,0.00");
      }
      return "-";
    },
    given_credit() {
      if (this.wallet) {
        return this.$numeral(this.wallet.credit_limit).format("0,0.00");
      }
      return "-";
    },
  },
  watch: {
    language(newVal) {
      this.populateMenu();
    },
    sidebar() {},
  },
  destroyed() {
    clearTimeout(this.countDown);
    clearInterval(this.populateMenu);
  },
  mounted() {
    this.populateMenu();
    setInterval(this.populateMenu, 15000);
    setTimeout(() => {
      this.autoOpenMenu();
      this.countDown();
      //- set market tab height according to the number of special events
      //- 40px for each special event
      const specialEvents = this.SPECIAL_EVENTS.filter(Boolean);
      const marketTabHeight = (40 * specialEvents.length) + 41;
      // console.log(marketTabHeight);
      document.documentElement.style.setProperty('--market-tab-height', `${marketTabHeight}px`);
    }, 500);

    EventBus.setMenu0 = this.setMenu0;
    EventBus.setGame = this.setGame;
  },
  methods: {
    getImage(e) {
      return config.getSportsImage(e);
    },
    handleParlay() {
      this.$refs.parlayPanel.initial();
    },
    autoOpenMenu() {
      this.$nextTick(() => {
        // open level 1 menu
        switch (this.menu0) {
        case "live":
          $("#collapse-live").collapse("show");
          break;
        default:
          if (config.vg1.includes(this.menu2)) {
            $("#collapse-vgames").collapse("show");
          } else {
            $("#collapse-allsports").collapse("show");
          }
          break;
        }
      });
    },
    getMenu(e) {
      if (!this.menuList) {
        return {};
      }

      if (!this.menuList.menu) {
        return {};
      }

      switch (e) {
      case "early":
        if (this.menuList.menu["1"]) return this.menuList.menu["1"];
        else return {};
      case "today":
        if (this.menuList.menu["2"]) return this.menuList.menu["2"];
        else return {};
      case "live":
        if (this.menuList.menu["3"]) return this.menuList.menu["3"];
        else return {};
      case "parlay":
        if (this.menuList.menu["4"]) return this.menuList.menu["4"];
        else return {};
      default:
        return {};
      }
    },
    logout() {
      this.$helpers.logout();
    },
    toggleSidebar() {
      this.$store.dispatch("layout/setMinimizer", {
        property: "sidebar",
        value: !this.sidebar,
      });
    },
    setDays(e) {
      if (config.day1.includes(e)) {
        this.$store.dispatch("layout/setSelectedDays", "1");
      } else {
        this.$store.dispatch("layout/setSelectedDays", "0");
      }
    },
    getEventDate(e) {
      var d1 = new Date();
      var d2 = new Date(e);
      return d1 >= d2 ? "today" : "early";
    },
    getEventDate2(e) {
      var d1 = new Date();
      var d2 = new Date(e);
      return d1 >= d2 ? null : "orz";
    },
    /**
     * Sets menu state based on provided parameters
     * @param {string} e - Menu type (live, parlay, early, favorite, event)
     * @param {boolean} x - Toggle for menuX state
     * @param {string} y - MenuY identifier
     * @param {string} z - Menu1 value (defaults to 'today' for event type)
     * @param {string} bt - Menu3 value (defaults to 'hdpou' for event type)
     * @param {boolean} fb - Toggle for menu2 state
     */
    setMenu0(e, x, y, z, bt, fb) {
      var mi5 = {};
      if (x) {
        mi5["menuX"] = true;
      } else {
        mi5["menuX"] = false;
      }

      if (y) {
        mi5["menuY"] = y.toString();
      } else {
        mi5["menuY"] = "0";
      }

      if (fb) {
        mi5["menu2"] = 1;
      } else {
        mi5["menu2"] = this.menu2;
      }

      switch (e) {
      case "live":
        mi5["menu0"] = "live";
        mi5["menu1"] = "live";
        mi5["menu3"] = "live";
        break;
      case "parlay":
        mi5["menu0"] = "parlay";
        mi5["menu1"] = "parlay";
        mi5["menu3"] = "parlay";
        break;
      case "early":
        mi5["menu0"] = "all";
        mi5["menu1"] = "early";
        mi5["menu3"] = "hdpou";
        break;
      case "favorite":
        mi5["menu0"] = "favorite";
        mi5["menu1"] = "today";
        mi5["menu3"] = "hdpou";
        break;
      case "event":
        mi5["menu0"] = "event";
        mi5["menu1"] = z == null ? "today" : z;
        mi5["menu3"] = bt == null ? "hdpou" : bt;
        break;
      default:
        mi5["menu0"] = "all";
        mi5["menu1"] = "today";
        mi5["menu3"] = "hdpou";
        break;
      }

      var changed = false;
      if (mi5["menuX"] != this.menuX) {
        changed = true;
      }
      if (mi5["menuY"] != this.menuY) {
        changed = true;
      }
      if (mi5["menu0"] != this.menu0) {
        changed = true;
      }
      if (mi5["menu0"] != this.menu0) {
        changed = true;
      }
      if (mi5["menu1"] != this.menu1) {
        changed = true;
      }
      if (mi5["menu2"] != this.menu2) {
        changed = true;
      }
      if (mi5["menu3"] != this.menu3) {
        changed = true;
      }

      if (changed) {
        this.setDays(parseInt(mi5["menu2"]));
        this.resetSelectLeague();
        this.clearSearch();
        mi5["src"] = "setMenu0";
        this.$store.dispatch("layout/setMenuItems", mi5);
        this.$refs.livePanel.initial();
        EventBus.$emit("INVALIDATE");
      }
    },
    setMenu1(e) {
      var mi5 = {};
      mi5["menuX"] = false;
      mi5["menuY"] = "0";
      mi5["menu1"] = e;
      mi5["menu2"] = this.menu2;
      switch (e) {
      case "live":
        mi5["menu0"] = "live";
        mi5["menu3"] = "live";
        break;
      case "parlay":
        mi5["menu0"] = "parlay";
        mi5["menu3"] = "parlay";
        break;
      default:
        var menu = this.getMenu(e);
        var sports = Object.keys(menu);
        const defaultSport = 1;
        var choosenSport = defaultSport;
        if (this.menu2 != null) {
          choosenSport = this.menu2;
        }
        if (!sports.includes(choosenSport.toString())) {
          if (sports != null && sports.length > 0) {
            choosenSport = sports[0];
          } else {
            choosenSport = defaultSport;
          }
        }
        var choosenBetType = naming.chooseBetType(choosenSport, menu);
        mi5["menu0"] = "all";
        mi5["menu2"] = parseInt(choosenSport);
        mi5["menu3"] = choosenBetType;
        break;
      }

      var changed = false;
      if (mi5["menuX"] != this.menuX) {
        changed = true;
      }
      if (mi5["menuY"] != this.menuY) {
        changed = true;
      }
      if (mi5["menu0"] != this.menu0) {
        changed = true;
      }
      if (mi5["menu1"] != this.menu1) {
        changed = true;
      }
      if (mi5["menu2"] != this.menu2) {
        changed = true;
      }
      if (mi5["menu3"] != this.menu3) {
        changed = true;
      }

      if (changed) {
        this.setDays(parseInt(mi5["menu2"]));
        this.resetSelectLeague();
        this.clearSearch();
        mi5["src"] = "setMenu1";
        this.$store.dispatch("layout/setMenuItems", mi5);
        EventBus.$emit("INVALIDATE");
      }
    },
    setGame() {
      var el = $(".vgames-link");
      if (el[0]) {
        setTimeout(() => {
          el[0].click();
        }, 100);
      }
    },
    setMenu2(e, f) {
      var choosenBetType = naming.chooseBetType(e, this.menu);
      var mi5 = {};
      mi5["menuX"] = false;
      mi5["menuY"] = "0";
      mi5["menu1"] = f ? f : this.menu1;
      mi5["menu0"] = "all";
      mi5["menu2"] = e;
      mi5["menu3"] = choosenBetType;

      var changed = false;
      if (mi5["menuX"] != this.menuX) {
        changed = true;
      }
      if (mi5["menuY"] != this.menuY) {
        changed = true;
      }
      if (mi5["menu0"] != this.menu0) {
        changed = true;
      }
      if (mi5["menu1"] != this.menu1) {
        changed = true;
      }
      if (mi5["menu2"] != this.menu2) {
        changed = true;
      }
      if (mi5["menu3"] != this.menu3) {
        changed = true;
      }

      if (changed) {
        this.setDays(parseInt(mi5["menu2"]));
        this.resetSelectLeague();
        this.clearSearch();
        mi5["src"] = "setMenu2";
        this.$store.dispatch("layout/setMenuItems", mi5);
        $(".market-menu").removeClass("active");
        setTimeout(() => {
          EventBus.$emit("INVALIDATE");
        }, 100);
      }
    },
    setMenu3(e, f) {
      var mi5 = {};
      mi5["menuX"] = false;
      mi5["menuY"] = "0";
      mi5["menu0"] = "all";
      mi5["menu1"] = this.menu1;
      mi5["menu2"] = f;
      mi5["menu3"] = e;

      var changed = false;
      if (mi5["menuX"] != this.menuX) {
        changed = true;
      }
      if (mi5["menuY"] != this.menuY) {
        changed = true;
      }
      if (mi5["menu0"] != this.menu0) {
        changed = true;
      }
      if (mi5["menu1"] != this.menu1) {
        changed = true;
      }
      if (mi5["menu2"] != this.menu2) {
        changed = true;
      }
      if (mi5["menu3"] != this.menu3) {
        changed = true;
      }

      if (changed) {
        this.resetSelectLeague();
        this.clearSearch();
        mi5["src"] = "setMenu3";
        this.$store.dispatch("layout/setMenuItems", mi5);
        $(".market-menu").removeClass("active");
        EventBus.$emit("INVALIDATE");
      } else {
        EventBus.$emit("RESET_SINGLE");
      }
    },
    clearSearch() {
      this.$store.dispatch("layout/setSearch", null);
    },
    resetSelectLeague() {
      this.$store.dispatch("layout/resetSelectLeague");
    },
    populateMenu(callback) {
      if (this.loading == true) return;
      if (this.isLoggedIn) {
        this.loading = true;

        var args = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          arguments: [this.$store.getters.language],
        };
        xhr.getMenu(args).then(
          (res) => {
            this.loading = false;
            if (res.success) {
              if (res.data) {
                var m = sync.decode(res.data);
                this.$store.dispatch("layout/setSports", {
                  items: m["sports"],
                  currency_code: this.$store.getters.currencyCode,
                });
                this.menuList = m;
                if (callback) {
                  callback();
                }
              }
            } else {
              if (this.$helpers.handleFeedback(res.status)) {
                // if (callback) callback();
              }
            }
            if (this.firstLoad) {
              const d = {
                early: 1,
                today: 2,
                live: 3,
                parlay: 4,
              };

              var sports_id = this.currency_code == "BDT" ? 12 : 1;

              var mm = m.menu[d[this.menu1]];
              if (mm) {
                var mmm = Object.keys(mm);
                if (!mmm.includes(sports_id.toString())) {
                  sports_id = 1;
                }
              } else {
                sports_id = 1;
              }

              this.$store
                .dispatch("layout/setMenuItems", {
                  menu2: sports_id,
                })
                .then(() => {
                  setTimeout(() => {
                    EventBus.$emit("PREPARE");
                  }, 500);
                })
                .catch(() => {
                  setTimeout(() => {
                    EventBus.$emit("PREPARE");
                  }, 500);
                });
            }
            this.firstLoad = false;
          },
          (err) => {
            this.loading = false;
            if (this.$helpers.handleFeedback(err.status)) {
              // if (callback) callback();
            }
            this.firstLoad = false;
          }
        );
      }
    },
    getLive(e) {
      if (!this.menuList) {
        return 0;
      }

      if (!this.menuList.menu) {
        return 0;
      }

      if (!this.menuList.menu["3"]) {
        return 0;
      }

      if (!this.menuList.menu["3"][e]) {
        return 0;
      }

      return this.menuList.menu["3"][e]["tn"];
    },
    isExist(e) {
      if (!this.menuList) {
        return false;
      }

      if (!this.menuList.menu) {
        return false;
      }

      if (this.menuList.menu[e]) return true;
      else return false;
    },
    saveLive(e) {
      for (var i = 0; i < this.gameList.length; i++) {
        e[this.gameList[i]] = false;
      }
      this.$store.dispatch("layout/setOptions", { property: "live", value: e });
    },
    saveParlay(e) {
      for (var i = 0; i < this.gameList.length; i++) {
        e[this.gameList[i]] = false;
      }
      this.$store.dispatch("layout/setOptions", { property: "parlay", value: e });
    },
    getTimer(elem, e) {
      if (this.params.hasOwnProperty(e) == false) {
        elem.addClass("danger");
        return "ERROR";
      }
      const dt = new Date(new Date().getTime());
      let n = dt.getMinutes();
      let f1 = Math.floor(n / this.params[e].duration);
      let f2 = Math.floor(n % this.params[e].duration);
      let f3 = f1 * this.params[e].duration + this.params[e].delay;
      let f4 = f3;
      if (f3 <= n) {
        f4 = f3 + this.params[e].duration;
      }
      let s = dt.getSeconds();
      let m = s == 0 ? f4 - n : f4 - n - 1;
      if (s != 0) {
        s = 60 - s;
      }

      var mmm = this.params[e].duration - this.params[e].live;
      if (m >= mmm) {
        elem.addClass("danger");
        return this.$t("ui.live");
      } else {
        if (m == 0 && s <= 30) {
          elem.addClass("danger");
          return this.$t("ui.live");
        } else {
          elem.removeClass("danger");
          return `${m}:${s < 10 ? "0" + s : s}`;
        }
      }
    },
    countDown() {
      for (var m = 0; m < this.gameList.length; m++) {
        var elem = $(".vgames-" + this.gameList[m]);
        elem.html(this.getTimer(elem, this.gameList[m]));
      }
      setTimeout(this.countDown, 1000);
    },
  },
};
</script>
