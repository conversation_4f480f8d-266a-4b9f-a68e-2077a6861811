/* arabic */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxtL2QoJDuBh5RMFcDg&skey=9d22fae1754942f6&v=v11) format('woff2');
  unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
}
/* armenian */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxtL2QoJDuBh5RMAMDg&skey=9d22fae1754942f6&v=v11) format('woff2');
  unicode-range: U+0530-058F, U+FB13-FB17;
}
/* cyrillic-ext */
@font-face {
  font-family: '<PERSON>hom<PERSON>';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxtL2QoJDuBh5RMHcDg&skey=9d22fae1754942f6&v=v11) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxtL2QoJDuBh5RMFMDg&skey=9d22fae1754942f6&v=v11) format('woff2');
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxtL2QoJDuBh5RMHMDg&skey=9d22fae1754942f6&v=v11) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxtL2QoJDuBh5RME8Dg&skey=9d22fae1754942f6&v=v11) format('woff2');
  unicode-range: U+0370-03FF;
}
/* hebrew */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxtL2QoJDuBh5RMEsDg&skey=9d22fae1754942f6&v=v11) format('woff2');
  unicode-range: U+0590-05FF, U+20AA, U+25CC, U+FB1D-FB4F;
}
/* thai */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxtL2QoJDuBh5RMBMDg&skey=9d22fae1754942f6&v=v11) format('woff2');
  unicode-range: U+0E01-0E5B, U+200C-200D, U+25CC;
}
/* vietnamese */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxtL2QoJDuBh5RMH8Dg&skey=9d22fae1754942f6&v=v11) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxtL2QoJDuBh5RMHsDg&skey=9d22fae1754942f6&v=v11) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxtL2QoJDuBh5RMEMDg&skey=9d22fae1754942f6&v=v11) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* arabic */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxuL2QoJDuBh5REq-X1jqbU&skey=325a0ea84e3a99&v=v11) format('woff2');
  unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
}
/* armenian */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxuL2QoJDuBh5REq-X1m6bU&skey=325a0ea84e3a99&v=v11) format('woff2');
  unicode-range: U+0530-058F, U+FB13-FB17;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxuL2QoJDuBh5REq-X1hqbU&skey=325a0ea84e3a99&v=v11) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxuL2QoJDuBh5REq-X1j6bU&skey=325a0ea84e3a99&v=v11) format('woff2');
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxuL2QoJDuBh5REq-X1h6bU&skey=325a0ea84e3a99&v=v11) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxuL2QoJDuBh5REq-X1iKbU&skey=325a0ea84e3a99&v=v11) format('woff2');
  unicode-range: U+0370-03FF;
}
/* hebrew */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxuL2QoJDuBh5REq-X1iabU&skey=325a0ea84e3a99&v=v11) format('woff2');
  unicode-range: U+0590-05FF, U+20AA, U+25CC, U+FB1D-FB4F;
}
/* thai */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxuL2QoJDuBh5REq-X1n6bU&skey=325a0ea84e3a99&v=v11) format('woff2');
  unicode-range: U+0E01-0E5B, U+200C-200D, U+25CC;
}
/* vietnamese */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxuL2QoJDuBh5REq-X1hKbU&skey=325a0ea84e3a99&v=v11) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxuL2QoJDuBh5REq-X1habU&skey=325a0ea84e3a99&v=v11) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Tahoma';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/l/font?kit=HTxuL2QoJDuBh5REq-X1i6bU&skey=325a0ea84e3a99&v=v11) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
