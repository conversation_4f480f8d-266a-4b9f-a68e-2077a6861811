.content .main .header-wrap {
	z-index: 2000;
	position: -webkit-sticky;
	position: sticky;
	top: 110px;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	border-collapse: collapse;
}
.content .main .filter-area {
	line-height: 14px;
	padding: 0;
	color: #fff;
	border-radius: 3px;
	background: #C0CED9;
	font-size: 12px;
	font-weight: 400;
	font-family: "Roboto", "Tahoma", sans-serif;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	border-collapse: collapse;
	width: 100%;
}
.container.active .content .main .header-wrap {
	top: 43px;
}
.content .main .filter-area .filter-item {
	border-right: 1px solid #5078af88;
	padding: 0 8px;
	width: auto;
	min-width: 120px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;

	background: #FFFFFF;			
	border: 1px solid #88ABC8;
	border-radius: 3px;
	height: 30px;
	color: #014273;
	margin: 0 1px;
	font-weight: 500;
}
.content .main .filter-area .filter-item:first-child {
	/* margin-left: 0; */
}
.content .main .filter-area .filter-item:last-child {
	margin-right: 0;
}
.content .main .filter-area .filter-item.refresh i {
	color: #014273;
}
.content .main .filter-area .filter-item.refresh.live,.content .main .filter-area .filter-item.refresh.live i,.content .main .filter-area .filter-item.refresh.live .timer {
	color: #CB5A34;
}
.content .main .filter-area .filter-item:last-child {
}
.content .main .filter-area .filter-item .text-filter {
	text-transform: capitalize;
	margin: 0 5px;
	cursor: pointer;
	white-space: nowrap;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
}
.content .main .filter-area .filter-item .text-filter.active {
	color: #f9d040;
}
.content .main .filter-area .filter-item .switch-wrap .switch {
	position: relative;
	display: inline-block;
	width: 32px;
	height: 16px;
	margin-bottom: 0;
	top: 7px;
}
.content .main .filter-area .filter-item .switch-wrap .switch input {
	opacity: 0;
	width: 0;
	height: 0;
}
.content .main .filter-area .filter-item .switch-wrap .switch input:checked + .slider {
	background-color: #c3e5ff;
}
.content .main .filter-area .filter-item .switch-wrap .switch input:checked + .slider:before {
	-webkit-transform: translateX(14px);
	-ms-transform: translateX(14px);
	transform: translateX(14px);
}
.content .main .filter-area .filter-item .switch-wrap .switch input:focus + .slider {
	-webkit-box-shadow: 0 0 1px #c3e5ff;
	box-shadow: 0 0 1px #c3e5ff;
}
.content .main .filter-area .filter-item .switch-wrap .switch .slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #a4a4a4;
	-webkit-transition: 0.4s;
	-o-transition: 0.4s;
	transition: 0.4s;
}
.content .main .filter-area .filter-item .switch-wrap .switch .slider:before {
	position: absolute;
	content: "";
	height: 14px;
	width: 14px;
	left: 2px;
	bottom: 1px;
	background-color: #123c64;
	-webkit-transition: 0.4s;
	-o-transition: 0.4s;
	transition: 0.4s;
}
.content .main .filter-area .filter-item .switch-wrap .switch .slider.round {
	border-radius: 10px 10px 10px 10px;
}
.content .main .filter-area .filter-item .switch-wrap .switch .slider.round:before {
	border-radius: 50%;
}
.content .main .filter-area .filter-item .filter-icon {
	cursor: pointer;
}
.content .main .filter-area .filter-item .circle {
	border-radius: 50%;
	border: 1px solid #c8eaff;
	background: #0c3764;
	width: 20px;
	height: 20px;
	line-height: 18px;
	text-align: center;
	color: #c8eaff;
	font-size: 10px;
	margin-top: 5px;
	cursor: pointer;
}
.content .main .filter-area .filter-item .rectangle {
	text-transform: uppercase;
	font-size: 9px;
	color: #c8eaff;
	border: 1px solid #c8eaff;
	border-radius: 2px 2px 2px 2px;
	padding: 0 3px;
	line-height: 12px;
	height: 14px;
	margin-top: 8px;
	min-width: 25px;
	text-align: center;
}
.content .main .filter-area .filter-item .filter-down {
	font-size: 12px;
	padding: 0;
}
.content .main .filter-area .filter-item .dropdown {
	cursor: pointer;
	width: 100%;
	height: 100%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
}
.content .main .filter-area .filter-item .dropdown .dropdown-menu {
	position: absolute !important;
	-webkit-transform: none !important;
	-ms-transform: none !important;
	transform: none !important;
	top: 27px !important;
	left: -8px !important;
	padding: 0;
	border-radius: 0 0 3px 3px;
	border-top: 0;
	min-width: calc(100% + 17px);
}
.content .main .filter-area .filter-item .dropdown .dropdown-menu a.dropdown-item {
	text-transform: capitalize;
	font-size: 12px;
	line-height: 30px;
	color: #0f4f8c;
	padding: 0.25rem 0.8rem;
}
.content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item .rectangle {
	color: #0f4f8c;
	border: 1px solid #0f4f8c;
	margin-right: 5px;
}
.content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item .filter-icon {
	color: #0f4f8c;
	margin-right: 5px;
}
.content .main .filter-block {
}
.content .main .filter-block .filter-single {
	margin-right: 4px;
}
.content .main .filter-block .filter-single label {
	margin-bottom: 0;
}
.content .main .filter-block .filter-single .filter-inside {
	background: #0d3a64;
	padding: 0 10px;
	border: 1px solid #c9d1dc;
	font-size: 13px;
	border-radius: 5px 5px 5px 5px;
	cursor: pointer;
}
.content .main .filter-block .filter-single .filter-inside .checkbox-wrap input[type="checkbox"] {
	width: 16px;
	height: 16px;
	margin: 5px 5px 0 0;
}
.content .main .filter-block .filter-single .filter-inside .filter-sportpic {
	margin-right: 6px;
}
.content .main .filter-block .filter-single .filter-inside .filter-sportpic img {
	width: 15px;
	position: relative;
	top: 2px;
}
.content .main .filter-block .filter-single .filter-inside .filter-sporttype {
	line-height: 24px;
	color: #fff;
}
.content .main .filter-block .filter-single .filter-inside .filter-sporttype .text {
	text-transform: capitalize;
}
.content .main .filter-block .filter-single .filter-date {
	padding: 0;
	border: 1px solid #88ABC8;
	font-size: 11px;
	cursor: pointer;
	line-height: 1;
	border-radius: 3px;
	min-width: 50px;
	text-align: center;
	color: #fff;
	font-family: "Roboto";
}
.content .main .filter-block .filter-single .filter-date .filter-date-title {
	border: 0;
	font-size: 10px;
	padding: 2px 4px;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
	background-color: #276FA8;
}
.content .main .filter-block .filter-single .filter-date .filter-date-body {
	border: 0;
	background: #fff;
	padding: 4px 4px 3px 4px;
	font-weight: bold;
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
	color: #014273;
}
.content .main .filter-block .filter-single .filter-date.active .filter-date-body {
	border: 0;
	background: #ffffffdd;
	padding: 4px 4px 3px 4px;
	font-weight: bold;
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
}
.content .main .filter-block .filter-single .filter-date:hover {
	background: #a4a4a488;
	color: #fff;
}
.content .main .filter-block .filter-single .filter-date.active {
	border: 1px solid #FDB338;
	background: #FFF0C9;
	color: #014273;
}
.content .main .filter-block .filter-single .filter-date.active .filter-date-title {
	background-color: #F6C344;
}
.content .main .filter-block .filter-single .filter-date.active .filter-date-body {
	background-color: #FFF0C9;
}
.content .main .sport .wrapper .card {
	border: none;
	border-radius: 0 0 0 0;
	font-weight: 700;
	background-color: #cccfd9;
}
.content .main .sport .wrapper .card .card-header {
	padding: 0;
	line-height: 26px;
	border-radius: 0 0 0 0;
	border-bottom: none;
	font-weight: 400;
}
.content .main .sport .wrapper .card .card-header.collapsed .fa-chevron-circle-down {
	-webkit-transform: rotate(-90deg);
	-ms-transform: rotate(-90deg);
	transform: rotate(-90deg);
}
.content .main .sport .wrapper .card .card-header.border-l {
	border-left: 1px solid #acacac;
}
.content .main .sport .wrapper .card .card-header.parent {
	background: #0d3a64;
	color: #fff;
}
.content .main .sport .wrapper .card.card-live .card-header.parent {
	background: #85360b;
}
.content .main .sport .wrapper .card .card-body .card .card-header.child {
	background: #b7c8e2;
	color: #000;
	padding: 1px 4px;
	cursor: pointer;
	font-weight: 600;
}
.content .main .sport .wrapper .card.card-live .card-body .card .card-header.child {
	background: #ffaf96;
	color: #000;
}
.content .main .sport .wrapper .card .card-body .card .card-header.child[aria-expanded="false"] {
	border-bottom: 1px solid #1649784d;
}
.content .main .sport .wrapper .card .card-body .card .card-header.child .fa-chevron-circle-down {
	font-size: 16px;
	vertical-align: -4px;
	cursor: pointer;
}
.content .main .sport .wrapper .card .card-body .card .card-header.child .bg-btn {
	background: #4f6d97;
	color: #fff;
	height: 22px;
	width: 22px;
	position: relative;
	border-radius: 0;
	border: 1px solid rgba(255, 255, 255, 0.8);
	display: inline-block;
	text-align: center;
	line-height: 20px;
	cursor: pointer;
	margin: 1px;
	padding: 0;
	text-shadow: 0 0 1px rgba(0, 0, 0, 0.3), 0 0 2px rgba(0, 0, 0, 0.3);
	vertical-align: 1px;
	font-size: 12px;
}
.content .main .sport .wrapper .card.card-live .card-body .card .card-header.child .bg-btn {
	background: #ae5e39;
}
.content .main .sport .wrapper .card .card-body .card .card-header.child .bg-btn .fa-star.selected {
	color: #ffc107;
	text-shadow: inset 0 0 0 rgba(0, 0, 0, 0.8), 0 0 0 rgba(255, 255, 255, 0.8);
}
.content .main .sport .wrapper .card .card-body .card .card-header.child .type-icon img {
	width: 22px;
	position: relative;
	top: -2px;
}
.content .main .sport .wrapper .card .card-body .card .card-header.child .league-name {
	padding-left: 16px;
	font-weight: 700;
}
.content .main .sport .wrapper .card .card-body .card .card-header.child .fa-chevron-down {
	-webkit-transition: 0.3s -webkit-transform ease-in-out;
	transition: 0.3s -webkit-transform ease-in-out;
	-o-transition: 0.3s transform ease-in-out;
	transition: 0.3s transform ease-in-out;
	transition: 0.3s transform ease-in-out, 0.3s -webkit-transform ease-in-out;
}
.content .main .sport .wrapper .card .card-body .card .card-header.child.collapsed .fa-chevron-down {
	-webkit-transform: rotate(-90deg);
	-ms-transform: rotate(-90deg);
	transform: rotate(-90deg);
}
.content .main .sport .wrapper .card .icon-wrap {
	text-align: right;
	width: auto;
	font-size: 13px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	    -ms-flex-direction: row;
	        flex-direction: row;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: end;
	    -ms-flex-pack: end;
	        justify-content: flex-end;
}
.content .main .sport .wrapper .card .icon {
	width: 23px;
	height: 16px;
	line-height: 16px;
	cursor: pointer;
	opacity: 1;
	display: inline-block;
	opacity: 0.8;
	text-align: center;
	font-size: 0.9rem;
	color: rgba(0, 0, 0, 0.6);
	margin-top: 1px;
}
.content .main .sport .wrapper .card .icon .fa-star.selected {
	color: #ffc107;
	text-shadow: inset 0 0 0 rgba(0, 0, 0, 1), 0 0 0 rgba(255, 255, 255, 1);
}
.content .main .sport .wrapper .card .icon img {
	vertical-align: top;
}
.content .main .sport .wrapper .card .icon:hover {
	opacity: 1;
}
.content .main .filter-area .filter-item .dropdown .dropdown-menu a.dropdown-item:hover,
.content .main .filter-area .filter-item .dropdown .dropdown-menu a.dropdown-item:focus,
.content .main .filter-area .filter-item .dropdown .dropdown-menu a.dropdown-item.active {
	color: #000;
	background: #DCE6EF;
}
.content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item:hover .rectangle,
.content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item:focus .rectangle,
.content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item.active .rectangle {
	color: #fff;
	border: 1px solid #fff;
}
.content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item:hover .filter-icon,
.content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item:focus .filter-icon,
.content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item.active .filter-icon {
	color: #000;
}
.content .quick-bet .btn-process {
	padding: 0 10px;
	height: inherit;
	line-height: 26px;
	background: #0d3a64;
	font-size: 13px;
	border: none;
	text-transform: capitalize;
}
.content .quick-bet .btn-process:hover {
	background: #345684;
}
.content .quick-bet .btn-cancel {
	padding: 0 10px;
	height: inherit;
	line-height: 26px;
	background: #a4a4a4;
	font-size: 13px;
	border: none;
	color: #000;
	text-transform: capitalize;
}
.content .main .title-block {
	background: #0d3a64dd;
	margin-top: 5px;
	padding: 0;
	border-radius: 6px 6px 0 0;
	border-top: 1px solid rgba(255, 255, 255, 0.1);
	border-left: 1px solid rgba(255, 255, 255, 0.1);
	border-right: 1px solid rgba(255, 255, 255, 0.1);
	line-height: 16px;
	color: #fff;
	font-size: 14px;
	font-weight: 500;
}
.content .main .title-icon {
	padding: 6px;
	background: rgba(255, 255, 255, 0.1);
	border-top-left-radius: 6px;
}
.content .main .title-content {
	padding: 6px;
}
.content .main .title-icon .fa-star {
	color: #ffe105;
	text-shadow: 0 0 1px rgba(0, 0, 0, 0.6), 0 0 3px rgba(0, 0, 0, 0.6);
}
.content .main .filter-area .filter-item.highlight-button {
	width: inherit;
	font-size: 20px;
	cursor: pointer;
}
.content .main .filter-area .filter-item.highlight-button.active {
	color: #ffe105;
}
.content .main .filter-area .filter-item.signaling {
	width: inherit;
	font-size: 18px;
	color: #b8b8b8;
}
.content .main .filter-area .filter-item.signaling.fast {
	color: #05ff1a;
}
.content .main .filter-area .filter-item.signaling.good {
	color: #05b0ff;
}
.content .main .filter-area .filter-item.signaling.average {
	color: #ff8615;
}
.content .main .filter-area .filter-item.signaling.slow {
	color: #ffe105;
}
.content .main .filter-area .filter-item.signaling.failed {
	color: #ff3c7d;
}
.content .main .filter-area .filter-item.signaling.unknown {
	color: #b8b8b8;
}
.page-wrap {
	position: relative;
	min-height: 400px;
}
#dropdown-quickbet .dropdown-menu,#dropdown-quickbet .form-control {
	font-size: 13px;
}
.parlay-button {
	color: #d18f7a;
}
.booster-arrow {
	background: #28a745 !important;
	-webkit-box-shadow: 3px 2px #afafaf;
	        box-shadow: 3px 2px #afafaf;
	margin-right: 2px;
}
.booster-arrow-quick {
	color: #28a745;
}
.btn-booster {
	color: #000000 !important;
	background-color: #f9d040 !important;
}
.btn-booster-remove {
	color: #000000 !important;
	background-color: #d18f7a !important;
	border-color: #d18f7a !important;
}
@keyframes blink {
	50% {
		opacity: 0;
	}
}
@-webkit-keyframes blink {
	50% {
		opacity: 0;
	}
}
.blink {
	animation: blink 1s step-start 0s infinite;
	-webkit-animation: blink 1s step-start 0s infinite;
}
.tooltip.show {
	opacity: 1;
}
.tooltip > .tooltip-inner {
	background-color: #0d3a64;
	color: #fff;
	border: 1px solid #0d3a64;
	font-size: 10px;
	width: 150px;
}
.tooltip.bottom > .tooltip-arrow {
	border-top: 5px solid #0d3a64;
}
.tooltip.top > .tooltip-arrow {
	border-top: 5px solid #0d3a64;
}

.content .main .filter-block .filter-single.filter-mmo .filter-date {
	background: #7fa54dbb;
	border: 1px solid #557432bb;
	color: #ffffffbb;
}

.content .main .filter-block .filter-single.filter-mmo .filter-date:hover {
	background: #7fa54d;
	color: #fff;
}
.content .main .filter-block .filter-single.filter-mmo .filter-date.active {
	border: 1px solid #6e5a0f66;
	background: #f9d040;
	color: #0d3a64;
	-webkit-box-shadow: 0 0 3px rgba(255, 255, 255, 0.5), 0 0 1px rgba(255, 255, 255, 0.5);
	        box-shadow: 0 0 3px rgba(255, 255, 255, 0.5), 0 0 1px rgba(255, 255, 255, 0.5);
}

.main-top {
	margin-bottom: 4px;
}
.main-top ul {
	padding: 0;
	margin: 0;
	display: flex;
}
.main-top ul li {
	list-style: none;
	margin-right: 5px;
}
.main-top ul li:last-child {
	margin-right: 0;
}
.main-top ul li a img {
	border-radius: 5px;
}