import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

const router = new Router({
  mode: "history",
  base: process.env.BASE_URL,
  routes: [
    {
      path: "/desktop",
      component: () => import("@/views/desktop/index.vue"),
      children: [
        {
          path: "/desktop",
          component: () => import("@/views/desktop/_home.vue"),
          meta: { requiredLoggedIn: true },
        },
      ],
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/tournament2",
      component: () => import("@/tournament2/views/index.vue"),
      meta: { requiredLoggedIn: true },
      children: [
        {
          path: "/tournament2",
          redirect: "/tournament2/home",
          meta: { requiredLoggedIn: true },
        },
        {
          path: "/tournament2/home",
          component: () => import("@/tournament2/views/_home.vue"),
          meta: { requiredLoggedIn: true },
        },
      ],
    },
    {
      path: "/highlight",
      component: () => import("@/views/highlight/index.vue"),
      meta: { requiredLoggedIn: true },
      children: [
        {
          path: "/highlight",
          redirect: "/highlight/home",
          meta: { requiredLoggedIn: true },
        },
        {
          path: "/highlight/home",
          component: () => import("@/views/highlight/_home.vue"),
          meta: { requiredLoggedIn: true },
        },
      ],
    },
    {
      path: "/info",
      component: () => import("@/views/desktop/info/index.vue"),
      children: [
        {
          path: "/info",
          redirect: "/info/settings",
          meta: { requiredLoggedIn: true },
        },
        {
          path: "/info/sysparlay",
          component: () => import("@/views/desktop/info/_sysparlay.vue"),
          meta: { requiredLoggedIn: true },
        },
        {
          path: "/info/settings",
          component: () => import("@/views/desktop/info/_settings.vue"),
          meta: { requiredLoggedIn: true },
        },
        {
          path: "/info/betlist",
          component: () => import("@/views/desktop/info/_betList.vue"),
          meta: { requiredLoggedIn: true },
        },
        {
          path: "/info/statement",
          component: () => import("@/views/desktop/info/_statement.vue"),
          meta: { requiredLoggedIn: true },
        },
        {
          path: "/info/result",
          component: () => import("@/views/desktop/info/_result.vue"),
          meta: { requiredLoggedIn: true },
        },
        {
          path: "/info/message",
          component: () => import("@/views/desktop/info/_message.vue"),
          meta: { requiredLoggedIn: true },
        },
        {
          path: "/info/rules",
          component: () => import("@/views/desktop/info/rules/rules.vue"),
          meta: { requiredLoggedIn: true },
        },
      ],
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/event",
      component: () => import("@/views/desktop/event/index.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/ngresult",
      component: () => import("@/views/desktop/ext/ngresult.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/efresult",
      component: () => import("@/views/desktop/ext/efresult.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/minigame",
      component: () => import("@/views/game/minigame.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots",
      component: () => import("@/views/game/slots.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots2",
      component: () => import("@/views/game/slots2.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots3",
      component: () => import("@/views/game/slots3.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots4",
      component: () => import("@/views/game/slots4.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots5",
      component: () => import("@/views/game/slots5.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots6",
      component: () => import("@/views/game/slots6.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots7",
      component: () => import("@/views/game/slots7.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots8",
      component: () => import("@/views/game/slots8.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots9",
      component: () => import("@/views/game/slots9.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/others",
      component: () => import("@/views/game/others.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/others1",
      component: () => import("@/views/game/others1.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots11",
      component: () => import("@/views/game/slots11.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots12",
      component: () => import("@/views/game/slots12.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots13",
      component: () => import("@/views/game/slots13.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/slots14",
      component: () => import("@/views/game/slots14.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/casino100",
      component: () => import("@/views/game/casino100.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/esports2",
      component: () => import("@/views/game/esports2.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/fishing",
      component: () => import("@/views/game/fishing.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/w4d",
      component: () => import("@/views/game/w4d.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/casino",
      component: () => import("@/views/game/casino.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "/livetv",
      component: () => import("@/views/game/livetv.vue"),
      meta: { requiredLoggedIn: true },
    },
    {
      path: "",
      component: () => import("@/views/d/index.vue"),
      children: [
        {
          path: "",
          component: () => import("@/views/d/_home.vue"),
        },
      ],
    },
    {
      path: "/maintenance",
      component: () => import("@/views/common/_maintenance.vue"),
    },
    {
      path: "*",
      component: () => import("@/views/common/_notFound.vue"),
    },
  ],
});

export default router;
