body.color2 {
	background: #E3E3E3;
}
.color2 header,.color2 header .topbar {
	background: #2789af;
}
.color2 header .toolbar {
	background-color: #108BAD;
	border-top: 0;
	border-bottom: 8px solid #E3E3E3;
	top: 0;
}
.color2 header .toolbar.active.white-label .new-timezone {
	min-width: 256px;
}
.color2 header .toolbar.active.white-label .menu {
	min-width: 256px;
}
.color2 header .toolbar.active .container .logo {
	padding-top: 1px;
	display: none !important;
}
.color2 .content .right .z-side .card .card-header {
	color: #fff;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	background: url(/images/color2/tab-bg.png) no-repeat;
    background-size: cover;
}
.color2 header .toolbar .menu .nav {
    justify-content: flex-end;
}
.color2 .content .right .text-icon.selected {
}
.color2 .frame-header {
	background: #10708C;
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
	border-left: 1px solid rgba(255, 255, 255, 0.2);
	border-right: 1px solid rgba(255, 255, 255, 0.2);
}
.color2 .mini-bg1 {
    background: #fff;
}
.color2 .right .text-icon.selected {
	color: #fff;
}
.color2 .right .text-icon.selected i, .color2 .right .text-icon.selected svg {
	color: #fff;
}
.color2 .frame-wrapper {
	background: #fff;
}
.color2 .mini-game-container .carousel-control-prev-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%2310708C' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
}
.color2 .mini-game-container .carousel-control-next-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%2310708C' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
}
.color2 .mini-game-container .carousel-indicators .active {
	background-color: #10708C;
}
.color2 .luckybox .btn-1 {
	background-color: rgba(0, 0, 0, 0.3);
	color: #fff;
}
.color2 .luckybox .btn-2 {
	background: transparent linear-gradient(90deg, #10708C 0%, #0E89AB 100%) 0% 0% no-repeat padding-box;
}
.color2 .luckybox .switch-wrap .switch .slider {
	background-color: #AEAEAE;
}
.color2 .luckyslider-label {
    color: #000;
}
.color2 .luckybox {
    color: #000;
}
.color2 header .toolbar .show-user {
	display: none !important;
}
.color2 header .toolbar .menu ul.nav li.nav-item a.nav-link {
	height: 32px;
    border-radius: 3px;
    margin: 1px;
	background-color: #10708C;
	border: 1px solid #164c61ee;
	-webkit-box-shadow: 0 1px 0 #2789afcc inset, 1px 1px 0 #2789af44 inset;
	        box-shadow: 0 1px 0 #2789afcc inset, 1px 1px 0 #2789af44 inset;
}
.color2 .dropdown-panel {
	background: #fff;
    box-shadow: none;
    border-radius: 0 0 3px 3px !important;
}
.color2 .dropdown-li {
	border-left: 0;
	border-right: 0;
}
.color2 .dropdown-li:last-child {
	border-bottom: 0;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0;
}
.color2 .dropdown-li .caption {
	font-size: 10px;
	color: #545454;
}
.color2 .dropdown-li .value .unit {
	font-size: 10px;
	color: #545454;
}
.color2 .dropdown-li .value {
	font-size: 12px;
}
.color2 .user-info-wrapper {
	border-radius: 3px;
	margin-bottom: 4px;
	font-size: 11px;
	font-family: "Lato", sans-serif;
}
.color2 .profile .dropdown-li {
	padding: 2px 2px;
	border-top: 1px solid rgba(0, 0, 0, 0.05);
	border-bottom: 0;
}
.color2 .user-info-wrapper {
	background-color: #065B74;
	border: 1px solid rgba(255, 255, 255, 0.2);
	color: #fff;
}
.color2 .user-info-wrapper .user {
	color: #fff;
}
.color2 .user-info-wrapper .balance-drop {
	border-top: 1px solid rgba(255, 255, 255, 0.2);
	background-color: #eeeff4;
	color: #0f4f8c;
}
.color2 .user-info-wrapper .details {
	background-color: #10708C;
	color: #fff;
	border-top: 1px solid rgba(255, 255, 255, .1);
}
.color2 .user-info-wrapper .details .balance-text {
	cursor: pointer;
	padding: 0.5rem;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	    -ms-flex-pack: justify;
	        justify-content: space-between;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	text-align: left;
	position: relative;
}
.color2 .user-info-wrapper .details .balance-text::after {
    content: "";
    height: 19px;
    border-right: 1px solid rgba(255, 255, 255, .1);
    padding-right: 16px;
}
.color2 .user-info-wrapper .details .balance .caption {
	font-size: 9px;
	color: #fff;
}
.color2 .user-info-wrapper .details .balance {
	padding: 0.5rem;
	-webkit-box-flex: 1;
	    -ms-flex-positive: 1;
	        flex-grow: 1;
	text-align: right;
	background-color: #10708C;
	color: #F6C344;
	font-weight: bold;
}
.color2 .content .main .header-wrap {
}
.color2 .content .main .filter-area {
}
.color2 .content .main .filter-area .filter-item {
	color: #505050;
}
.color2 .content .main .filter-area .filter-item .filter-icon {
	color: #505050;
}
.color2 .content .main .filter-area .filter-item .filter-icon.page-button:hover {
	background-color: #2789af;
}
.color2 .page-button {
	border: 1px solid rgba(255, 255, 255, 0.2);
}
.color2 #new-searchbar.searchbar .input-group .input-group-prepend .input-group-text {
	color: #505050;
}
.color2 #new-searchbar.searchbar .input-group .form-control {
	color: #505050;
}
.color2 #new-searchbar.filter-item.searchbar .input-group .form-control::-webkit-input-placeholder {
	color: #505050 !important;
}
.color2 #new-searchbar.filter-item.searchbar .input-group .form-control:-ms-input-placeholder {
	color: #505050 !important;
}
.color2 #new-searchbar.filter-item.searchbar .input-group .form-control:-moz-placeholder {
	color: #505050 !important;
}
.color2 .x-accordion.accordion .bg {
	background: #10708C;
}
.color2 .language-selector .dropdown-menu {
	background: #206f8e !important;
	border: 1px solid rgba(0, 0, 0, 0.3) !important;
	-webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset !important;
	        box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset !important;
}
.color2 .x-btn-default,.color2 .x-btn {
	border: 1px solid rgba(0, 0, 0, 0.5);
	-webkit-box-shadow: 0 1px 0 #ffffff3d inset, 1px 1px 0 #ffffff2e inset;
	        box-shadow: 0 1px 0 #ffffff3d inset, 1px 1px 0 #ffffff2e inset;
}
.color2 .x-btn-default:hover {
	-webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 1px 1px 0 rgba(255, 255, 255, 0.2) inset, -1px -1px 1px rgba(0, 0, 0, 0.2) inset, 0 0 3px rgba(0, 0, 0, 0.2);
	        box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 1px 1px 0 rgba(255, 255, 255, 0.2) inset, -1px -1px 1px rgba(0, 0, 0, 0.2) inset, 0 0 3px rgba(0, 0, 0, 0.2);
}
.color2 .content .left .nav-header {
	color: #206f8e;
}
.color2 .content .left.active .nav-header.nav-right {
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.color2 .content .left .nav-header .collapsed {
	background: #206f8e;
}
.color2 .content .left .nav-header [aria-expanded="true"] {
	background: #065B74;
}
.color2 .content .left .nav-header [aria-expanded="false"] {
	background: #10708C;
}
.color2 .content .left .content-bet .nav li.nav-item a.nav-link.active {
    background: #f9d040;
}
.color2 .content .left .content-bet .nav li.nav-item a.nav-link {
    background: #eeeff4;
    color: #333;
}
.color2 .content .left .group.selected {
	background: url(/images/color2/tab-bg.png) no-repeat;
    background-size: auto 100%;
	color: #fff;
}
.color2 .content .left .side-row {
    background: url(/images/color2/tab-bg.png) no-repeat;
    background-size: cover;
}
.color2 .content .left.active .collapse.show .group {
	background: #206f8e;
}
.color2 .content .left.active #collapse-allsports.collapse.show #market-group {
	border-left: 1px solid #5991C1;
	border-right: 1px solid #5991C1;
}
.color2 .content .left.active .group.changed.selected {
	background: #164c61 !important;
}
.color2 .content .left.active .heading-collapse[aria-expanded="false"] .group.changed:hover {
	background: #164c61 !important;
}
.color2 .content .left .group {
	color: #fff;
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.color2 .content .left .tb {
	border-top: 1px solid #5991C1;
	border-radius: 3px 3px 0 0;
}
.color2 .content .left .xb {
	border-left: 1px solid #5991C1;
	border-right: 1px solid #5991C1;
	border-radius: 5px;
}
.color2 .content .left .xb a:hover {
	text-decoration: none;
}
.color2 .content .left .xb.tb {
	border-radius: 5px;
}
.color2 .content .left ul.subgroup li.small {
	border-bottom: 1px solid #257da0;
}
.color2 .content .left ul.subgroup li.small:last-child {
	border-bottom: 0;
}
.color2 .content .left .group .sport-type {
	background: #267b9c;
}
.color2 .content .left .group .sport-type a {
	color: #e8f9ff;
}
.color2 .content .left .group.selected,.color2 .content .left .group .sport-type.active {
}
.color2 .content .left .group.changed.selected, .color2 .content .left .group .sport-type.active {
    background: #065B74;
}
.color2 .content .main .filter-block {
}
.color2 .content .main .filter-block .filter-single .filter-date {
    border: 1px solid rgba(0, 0, 0, 0.1);
}
.color2 .content .main .filter-block .filter-single .filter-date .filter-date-title {
    background-color: #10708C;
}
.color2 .content .main .filter-block .filter-single .filter-date.active {
	color: #000;
}
.color2 .content .main .filter-block .filter-single .filter-date.active .filter-date-body {
	color: #000;
}
.color2 .content .main .filter-block .filter-single .filter-date.active .filter-date-title {
    background-color: #F6C344;
}
.color2 .content .main .filter-block .filter-single .filter-date .filter-date-body {
	color: #3E3E3E;
}
.color2 .round-alert {
	background: #00000044;
	border: 1px solid #164c61;
}
.color2 .text-info {
	color: #134558 !important;
}
.color2 .content .main .filter-area .filter-item .dropdown .dropdown-menu a.dropdown-item,.color2 .content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item .filter-icon {
	color: #164c61;
}
.color2 .content .main .filter-area .filter-item .dropdown .dropdown-menu a:hover.dropdown-item {
	background-color: #2789af;
	color: #fff;
}
.color2 .x-accordion.accordion .bg:last-child {
	border-bottom: 1px solid #5991C1;
}
.color2 .modal .modal-dialog .modal-content .modal-header {
	background: #2789af !important;
}
.color2 .modal .modal-dialog .modal-content .modal-body #select-league .card .card-header {
	background: #deeef5;
	color: #227190;
}
.color2 .modal .modal-dialog .modal-content .modal-footer .btn-primary {
}
.color2 .table-info tr th,.color2 .table-betresult tr th {
	background: #164c61;
}
.color2 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link.active {
	background: #206f8e;
}
.color2 header .topbar .info-content .nav-info a:hover,.color2 header .topbar .nav-info a.active {
	background-color: #164c61;
	border-left: 1px #206f8e solid;
	border-right: 1px #206f8e solid;
	text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
}
.color2 .info-wrapper .info-tablewrap .nav-tabs {
	border-bottom: 1px solid #2789af;
}
.color2 .info-wrapper .info-tablewrap .nav-tabs li.nav-item {
	background: rgba(0, 0, 0, 0.05);
	border-top: 1px solid #2789af;
	border-left: 1px solid #2789af;
	border-right: 1px solid #2789af;
}
.color2 .result-selection .form-control.datepicker {
	background: #227190;
}
.color2 .accordion-rules .card .card-header {
	padding: 8px;
	background: #206f8e;
	color: #efdd00;
	border-radius: 0 0 0 0;
}
.color2 .info-tablewrap .setting-right .btn-result.active {
	color: #fff;
	border: 1px solid #ffffff;
	border-radius: 0 0 0 0;
}
.color2 .modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap:hover input ~ .checkmark {
	background-color: #164c61;
	border: 1px solid #206f8e;
}
.color2 .hx-league {
	background: #ECECEC;
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.color2 .hx-league.live {
	/* background: #c9e4ef; */
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.color2 .hx-table {
	background: url(/images/color2/market-head-bg.png);
    background-size: auto 100%;
	border-bottom: 1px solid #c4c4c4;
}
.color2 .hx-cell {
	border-left: 1px solid #3497be;
}
.color2 .hx-row {
	border-top: 1px solid #3497be;
}
.color2 .hx-col {
	border-left: 1px solid #3497be;
}
.color2 .hx-more {
	background: #0E89AB;
}
.color2 .morebet-wrapper .body-bet .nav-tabs li.nav-item a.nav-link.active {
	background: #3497be;
	border-bottom: 2px solid #0E89AB;
}
.color2 .morebet-wrapper .body-bet .tab-content .tab-pane .card .card-header {
	background: #3497be82;
}
.color2 .morebet-wrapper .body-bet .nav-tabs.live-tab li.nav-item a.nav-link.active {
	background: #85360b;
	border-bottom: 2px solid #85360b;
}
.color2 .morebet-wrapper .body-bet .tab-content .tab-pane .card .card-header.live {
	background: #85360b;
}
.color2 .profile .dropdown-li a {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	padding: 6px;
	margin: 2px;
	color: #a5cae5;
	border: 1px solid #0e3342;
	-webkit-box-shadow: 0 1px 0 #2789af88 inset, 1px 1px 0 #2789af44 inset;
	        box-shadow: 0 1px 0 #2789af88 inset, 1px 1px 0 #2789af44 inset;
	border-radius: 3px;
	text-align: center;
	text-decoration: none;
	line-height: 1;
}
.color2 .profile .dropdown-li a i {
	margin-right: 4px;
	font-size: 12px;
}
.color2 .profile .dropdown-li a:hover {
	color: #ffc107;
}
.color2 .content .left ul.subgroup input[type="checkbox"] {
	position: relative;
	width: 15px !important;
	height: 15px !important;
	color: #0e3342;
	border: 1px solid #fff;
	border-radius: 3px;
	-webkit-appearance: none;
	   -moz-appearance: none;
	        appearance: none;
	outline: 0;
	cursor: pointer;
	-webkit-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	-o-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
}
.color2 .content .left ul.subgroup input[type="checkbox"]::before {
	position: absolute;
	content: "";
	display: block;
	top: 0;
	left: 4px;
	width: 5px !important;
	height: 10px !important;
	border-style: solid;
	border-color: #fff;
	border-width: 0 2px 2px 0;
	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	        transform: rotate(45deg);
	opacity: 0;
}
.color2 .content .left ul.subgroup input[type="checkbox"]:checked {
	color: #fff;
	border-color: #0e3342;
	background: #0e3342;
}
.color2 .content .left ul.subgroup input[type="checkbox"]:checked::before {
	opacity: 1;
}
.color2 .content .left ul.subgroup input[type="checkbox"]:checked ~ label::before {
	-webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
	        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}
.color2 .hx-table.hx-match.live .morebet-wrapper {
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	border-bottom: 1px solid #b5d0db;
	border-top: 1px solid #b5d0db;
	border-collapse: collapse;
}
.color2 .content .main .single-match {
	background: #206f8e;
	color: #ffffffcc;
	border: 1px solid rgba(255, 255, 255, 0.2);
}
.color2 .content .main .single-match .action-block {
	border-right: 1px solid rgba(255, 255, 255, 0.2);
}
.color2 .content .main .single-match .content img {
	-webkit-filter: hue-rotate(350deg);
	        filter: hue-rotate(350deg);
}
.color2 .hx-table.alternate.hx-match {
	background: #F6F6F6;
}
.color2 .morebet-wrapper .body-bet .tab-content .tab-pane .card .card-header.live {
    background: #F4D1C5;
}
.color2 .morebet-wrapper .body-bet .nav-tabs.live-tab li.nav-item a.nav-link.active {
    background: #C85F3F;
    border-bottom: 2px solid #C85F3F;
}
.color2 .content .left.active .nav-header .collapsed, .content .left.active .nav-header [aria-expanded="true"], 
.color2 .content .left.active .nav-header [aria-expanded="false"] {
    background: transparent;
}
.color2 .content .left.active .nav-header.nav-left, .color2 .content .left.active .nav-header.nav-right {
	background: #10708C;
}
.color2 .content .left ul.subgroup li.hot { 
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.color2 .europeview .content .main .single-match {
	background: #10708C;
    border: 1px solid #10708C;
}
.color2 .europeview .content .main .single-match .action-block {
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}
.color2 .europeview .hx-table.hx-match .bet-value {
	background-color: #ECECEC;
}
.color2 .x-side {
	background: #E3E3E3;
}
.color2 .x-side::before {
	border-left: 12px solid #E3E3E3;
}
.color2 .x-side::after {
	border-right: 8px solid #E3E3E3;
}
.color2 .luckybox .btn-1:hover {
	background: #10708C;
}
.color2 .luckydelete {
	background: rgba(0, 0, 0, 0.3);
}
.color2 .luckydelete:hover {
	background: #10708C;
}
.color2 .luckyodds:last-child {
	color: #0A4782;
}
.color2 .luckyeven {
	background: rgba(0,0,0,.05);
}
.color2 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link {
	color: #000;
}
.color2 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link.active {
    color: #F6C344;
}
.color2 .info-tablewrap .setting-right .btn-result.active {
	background: #065B74 !important;
}
.color2 .result-selection .btn-result {
	border: 1px solid #2789af;
}
.color2 .result-selection .btn-result:hover, 
.color2 .result-selection .btn-result.active {
	background: #065B74 !important;
	border: 1px solid #2789af;
}
.color2 .info-tablewrap .pagination .page-item .page-link {
	border: 1px solid #2789af;
	color: #2789af;
}
.color2 .info-tablewrap .pagination .page-item .page-link.active {
	background: #065B74;
	color: #fff;
}
.color2 .info-tablewrap .setting-right .custom-control-input:checked ~ .custom-control-label::before {
    background: #065B74;
    border: 1px solid #2789af;
}
.color2 .modal .modal-dialog .modal-content .modal-footer .btn-primary {
    background: #065B74;
}
.color2 .result-selection .dropdown-menu .dropdown-item:hover, 
.color2 .result-selection .dropdown-menu .dropdown-item:focus, 
.color2 .result-selection .dropdown-menu .dropdown-item.active {
	background: #9f9f9f;
}
.color2 .tournament-top, 
.color2 .tournament-top-plus {
	background: #098AAD;
}
.color2 .tournament-btn-search {
	background-color: #098AAD;
}
.color2 .tournament-btn-search:hover, 
.color2 .tournament-btn-search:focus {
	background-color: #098AAD;
}
.color2 .tournament-user {
	background: transparent linear-gradient(90deg, #10708C 0%, #0E89AB 100%) 0% 0% no-repeat padding-box;
}
.color2 .tournament-pagination ul li .page-link.disable,
.color2 .tournament-pagination ul li .page-link,
.color2 .tournament-pagination ul li .page-link:hover {
	border: 1px solid #2789af;
}
.color2 .tournament-pagination ul li .page-link i,
.color2 .tournament-pagination ul li .page-link {
	color: #2789af;
}
.color2 .tournament-pagination ul li .page-link.active {
	background-color: #098AAD;
	border: 1px solid #2789af;
	color: #fff;
}
.color2 .tournament-pagination ul li .page-link:hover {
	color: #fff;
}
.color2 .tournament-btn {
	background: url('../../img/tn/bg-match-color2.png') top center no-repeat;
}
.color2 .tournament-pool-fee {
	background: rgba(0,0,0,0.5);
	border: 1px rgba(255,255,255,0.2) solid;
}
.color2 .tournament-pool-result {
    background-color: #098AAD;	
}
.color2 .tournament-page-wrapper ul li.nav-item .nav-link {
	color: #000;
}
.color2 .select-day-top {
	color: #000;
}
.color2 .select-day-bottom {
	color: #025167;
}
.color2 .select-day.today .select-day-bottom {
	color: #F6C344;
}
.color2 .select-league-title {
	color: #000;
}
.color2 .select-league-teams {
	color: #000;
}
.color2 .select-league-time {
	color: #000;
}
.color2 .select-league .date-check input:checked ~ .checkmark-date {
	background-color: #098AAD;
	border: 1px solid #098AAD;
}
.color2 .room-bottom {
	color: #000;
}
.color2 .room-rate-select select option, 
.color2 .room-limit-select select option,
.color2 .tournament-search select option {
	background-color: #098AAD;
}
.color2 .room-rate-select select,
.color2 .room-limit-select select {
	color: #000;
}
.color2 .tournament-point {
	background-color: #098AAD;
}
.color2 .tournament-menu ul li.active {
	background-color: #F6C344;
}
.color2 .tournament-menu ul li.active a {
	color: #000;
}
.color2 .bet-info .bet-type.blue {
	color: #025167;
}
.color2 .tournament-mybet-inner {
	background: #D1D9E1;
}
.color2 .tournament-details-icon {
	filter: brightness(0%);
    opacity: 0.5;
}
.color2 .player-prize-icon {
	filter: brightness(0%);
    opacity: 0.5;
}
.color2 .tournament-details-bottom {
	color: #025167;
}
.color2 .tournament-betslip-room {
	background: #065B74;
}
.color2 .tournament-betslip {
    background: #D1D9E1;
}
.color2 .alert-tournament {
	background-color: #fff;
	color: #000;
}
.color2 .tournament-odds {
	background-color: #fff;
}
.color2 .tournament-odds::before {
	filter: brightness(0) invert(1);
}
.color2 .tournament-odds::after {
	filter: brightness(0) invert(1);
}
.color2 .tournament-betslip-matches {
	color: #000;
}
.color2 .tournament-odds-text {
	color: #000;
}
.color2 .tournament-betslip-matches .tn-team {
	color: #000;
}
.color2 .tournament-betslip-matches .tn-vs {
	color: #333;
}
.color2 .tournament-betslip-header-title {
	background: #10708C;
}
.color2 .player-betslip-top {
	background-color: #098AAD;
}
.color2 .player-prize-bottom {
	color: #025167;
}
.color2 .player-betslip-room {
    color: #000;
}
.color2 .player-betslip-content .tournament-mybet-single .tournament-mybet-date {
    color: #025167;
}
.color2 .player-betslip-content .tournament-mybet-single .tournament-mybet-small span {
	color: #025167;
}
.color2 .tournament-mybet-status {
	color: #50AE1B;
}
.color2 .tournament-betslip-wrapper ul li.nav-item .nav-link {
	background-color: #065B74;
}
.color2 .hl-select-date {
	background-color: #206f8e;
}
.color2 .hl-filter-bar .dropdown-menu {
	background: #2789af;
}
.color2 .hl-filter-bar .dropdown-menu .dropdown-item:hover {
	background: #206f8e;
}
.color2 .hl-filter-bar .dropdown-menu .dropdown-item.active,
.color2 .hl-filter-bar .dropdown-menu .dropdown-item:active,
.color2 .hl-filter-bar .dropdown-menu .dropdown-item.active:hover {
	background-color: #065B74;
}
.color2 .hl-league-titlebar {
	background: #164c61;
}
.color2 .tournament-betslip-matches .text-center {
	color: #000 !important;
}
.color2 .tournament-table-entry .table-tournament {
	color: #000;
}
.color2 .tournament-stake-field .input-group-text {
	background: #098AAD;
}
.color2 .swal2-confirm {
	background: #206f8e !important;
    border: 1px solid #2789af !important;
}

.color2 .tournament-betslip-league span {
	color: #000;
}
.color2 .tournament-odds2 .tournament-odds-text {
	color: #fff;
}