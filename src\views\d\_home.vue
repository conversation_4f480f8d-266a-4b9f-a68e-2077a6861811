<template lang="pug">
newbet(v-if="brand == 'WBET'")
ubett(v-else-if="brand == 'BT'")
ibcbet(v-else-if="brand == 'IBCBET'")
sportsbook(v-else)
</template>

<script>
import config from "@/config";
import errors from "@/errors";

export default {
  components: {
    wbet: () => import("@/views/d/wbet"),
    sportsbook: () => import("@/views/d/sportsbook"),
    ubett: () => import("@/views/d/ubett"),
    newbet: () => import("@/views/d/newbet"),
    ibcbet: () => import("@/views/d/ibcbet"),
  },
  data() {
    return {
    };
  },
  computed: {
    brand() {
      return config.brand.toUpperCase();
    },
    whiteLabel() {
      return config.whiteLabel;
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    }
  },
  destroyed() {
  },
  mounted() {
  },
  methods: {
  }
};
</script>
