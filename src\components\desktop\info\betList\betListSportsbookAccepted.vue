<template lang="pug">
.tab-pane.active(role='tabpanel')
  template(v-if="feedback.loading")
    .row
      .col-12.d-flex.align-items-center.justify-content-center.p-4
        h5.fad.fa-spinner.fa-spin.text-white
  template(v-else)
    betListItem(:betList="betList" status="accept" :pageIndex="page.value" :pageSize="pageSize")
    .mt-2
      v-pagination(
        :value="page.value"
        @input="changePage($event)"
        :page-count="page.count"
        :classes="bootstrapPaginationClasses"
        :labels="paginationAnchorTexts"
      )
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import xhr from "@/library/_xhr-betlist";
import betListItem from "@/components/desktop/info/betList/betListItem";
import vPagination from "vue-plain-pagination";

export default {
  components: {
    betListItem,
    vPagination,
  },
  data() {
    return {
      page: {
        value: 1,
        count: 1,
        total: 0,
      },
      bootstrapPaginationClasses: {
        ul: "pagination justify-content-center",
        li: "page-item",
        liActive: "active",
        liDisable: "disabled",
        button: "page-link",
        buttonActive: "active",
        buttonDisable: "disable",
      },
      paginationAnchorTexts: {
        first: "<i class='fas fa-angle-double-left'></i>",
        prev: "<i class='fas fa-angle-left'></i>",
        next: "<i class='fas fa-angle-right'></i>",
        last: "<i class='fas fa-angle-double-right'></i>",
      },
      betList: [],
      feedback: {
        loading: false,
        success: false,
        status: errors.session.invalidSession,
        source: "",
      },
    };
  },
  computed: {
    pageSize() {
      // return this.$store.getters.pageSize;
      return 100;
    },
  },
  destroyed() {
    EventBus.$off("BETLIST_REFRESH", this.getList);
  },
  mounted() {
    EventBus.$on("BETLIST_REFRESH", this.getList);
    this.getList();
  },
  methods: {
    changePage(e) {
      this.page.value = e;
      this.getList();
    },
    getList() {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        page_number: this.page.value,
        page_size: this.pageSize,
      };

      this.feedback.loading = true;
      this.$emit("loading", true);
      this.betList = [];

      xhr.getBetAcceptFullList(json).then(
        (result) => {
          this.feedback.loading = false;
          this.$emit("loading", false);
          if (result) {
            this.feedback.status = result.status;
            this.feedback.source = result.source;

            if (result.success == true) {
              this.betList = result.data;
              if (this.betList.length > 0) {
                this.page.total = this.betList[0].totalrows;
                if (this.page.total % this.pageSize != 0)
                  this.page.count = Math.ceil(
                    parseFloat(this.page.total) / parseFloat(this.pageSize)
                  );
                else
                  this.page.count =
                    parseFloat(this.page.total) / parseFloat(this.pageSize);
              }
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.feedback.loading = false;
          this.$emit("loading", false);
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
  },
};
</script>
