<template lang="pug">
.esports-gamelist(v-if="source.items != null && source.items[0] != null && source.items.length == 7")
  //- small {{ source.items[6] }}
  .esports-row.game-bottom-line(:class="source.items[0].marketId == 3 ? 'game-top-line' : ''")
    .esports-cell.esports-cell-sm.flex-grow-1.justify-content-start.w-100(v-if="source.items[0].marketId == 3")
      .live-now(style="margin-left: 16px;")
        img(src="/v1/images/esports/live-icon.svg")
        span.text-uppercase live
        i.fas.fa-circle
      .d-flex.flex-fill.justify-content-end.align-items-center(v-if="racingList.includes(source.items[0].sportsId) && source.items[0].matchTime")
        span(style="color: #fff; margin-right: 16px;") No. {{ $dayjs(this.source.items[0].matchTime).format("MMDDhhmm") }}
    .esports-cell.esports-cell-sm(v-else)
      efTime(:source="source.items[0]" :horiz="true")
  .game-group
    .game-table.pb-0
      .game-table-top
        .game-row
          .game-cell.w-40
          .game-cell.flex-fill
          .game-cell.game-state.w-152 {{ $t("m.GH_OVER") }}
          .game-cell.game-state.w-152 {{ $t("m.GH_UNDER") }}
          .game-cell.game-state.w-152 {{ $t("m.GH_ODD") }}
          .game-cell.game-state.w-152 {{ $t("m.GH_EVEN") }}
        efItemChild3a(:source="source.items[6]" :player="7")
        .game-line
    .game-table
      .game-table-left
        .game-row
          .game-cell.w-40
          .game-cell.flex-fill
          .game-cell.game-state.w-60 {{ $t("m.GH_1X20") }}
          .game-cell.game-state.w-60 {{ $t("m.GH_1X2HDP2") }}
          .game-cell.game-state.w-60 {{ $t("m.GH_1X2HDP4") }}
        efItemChild3(:source="source.items[0]" :player="1")
        .game-line
        efItemChild3(:source="source.items[1]" :player="2")
        .game-line
        efItemChild3(:source="source.items[2]" :player="3")
      .game-table-right
        .game-row
          .game-cell.w-40
          .game-cell.flex-fill
          .game-cell.game-state.w-60 {{ $t("m.GH_1X20") }}
          .game-cell.game-state.w-60 {{ $t("m.GH_1X2HDP2") }}
          .game-cell.game-state.w-60 {{ $t("m.GH_1X2HDP4") }}
        efItemChild3(:source="source.items[3]" :player="4")
        .game-line
        efItemChild3(:source="source.items[4]" :player="5")
        .game-line
        efItemChild3(:source="source.items[5]" :player="6")
</template>

<script>
import config from "@/config";
export default {
  components: {
    efTime: () => import("@/components/desktop/main/efTime"),
    efItemChild3: () => import("@/components/desktop/main/efItemChild3"),
    efItemChild3a: () => import("@/components/desktop/main/efItemChild3a"),
  },
  props: {
    source: {
      type: Object,
    },
    index: {
      type: Number,
    },
  },
  computed: {
    racingList() {
      return config.racingList;
    },
  },
  methods: {},
};
</script>
