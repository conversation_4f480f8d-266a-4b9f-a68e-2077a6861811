/**
 * Performance monitoring utility for Vue.js components
 * Tracks computed property execution times and identifies bottlenecks
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.thresholds = {
      computed: 16, // 16ms threshold for computed properties
      watcher: 5,   // 5ms threshold for watchers
      method: 10,   // 10ms threshold for methods
    };
    this.enabled = process.env.NODE_ENV === 'development';
  }

  /**
   * Start timing a computation
   * @param {string} name - Name of the computation
   * @param {string} type - Type (computed, watcher, method)
   * @returns {Function} End timing function
   */
  startTiming(name, type = 'computed') {
    if (!this.enabled || typeof performance === 'undefined') return () => {};
    
    const startTime = performance.now();
    
    return () => {
      try {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        this.recordMetric(name, type, duration);
        
        if (duration > this.thresholds[type]) {
          console.warn(`⚠️ Slow ${type}: ${name} took ${duration.toFixed(2)}ms`);
        }
      } catch (error) {
        // Silently fail if there's an issue with timing
      }
    };
  }

  /**
   * Record a performance metric
   * @param {string} name - Name of the computation
   * @param {string} type - Type of computation
   * @param {number} duration - Duration in milliseconds
   */
  recordMetric(name, type, duration) {
    const key = `${type}:${name}`;
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, {
        name,
        type,
        count: 0,
        totalTime: 0,
        avgTime: 0,
        maxTime: 0,
        minTime: Infinity,
      });
    }
    
    const metric = this.metrics.get(key);
    metric.count++;
    metric.totalTime += duration;
    metric.avgTime = metric.totalTime / metric.count;
    metric.maxTime = Math.max(metric.maxTime, duration);
    metric.minTime = Math.min(metric.minTime, duration);
  }

  /**
   * Get performance report
   * @returns {Array} Array of performance metrics
   */
  getReport() {
    const report = Array.from(this.metrics.values())
      .sort((a, b) => b.avgTime - a.avgTime);
    
    return report;
  }

  /**
   * Print performance report to console
   */
  printReport() {
    if (!this.enabled) return;
    
    const report = this.getReport();
    
    console.group('🚀 Performance Report');
    console.table(report.map(metric => ({
      Name: metric.name,
      Type: metric.type,
      Count: metric.count,
      'Avg Time (ms)': metric.avgTime.toFixed(2),
      'Max Time (ms)': metric.maxTime.toFixed(2),
      'Total Time (ms)': metric.totalTime.toFixed(2),
    })));
    console.groupEnd();
  }

  /**
   * Clear all metrics
   */
  clear() {
    this.metrics.clear();
  }

  /**
   * Create a performance-wrapped computed property
   * @param {string} name - Name of the computed property
   * @param {Function} computedFn - Computed function
   * @returns {Function} Wrapped computed function
   */
  wrapComputed(name, computedFn) {
    if (!this.enabled) return computedFn;
    
    return function(...args) {
      // Check if component is properly initialized
      if (!this || !this.$options) {
        return computedFn.apply(this, args);
      }
      
      const endTiming = this.startTiming(name, 'computed');
      try {
        const result = computedFn.apply(this, args);
        return result;
      } catch (error) {
        // If there's an error (like $store not available), just run the original function
        return computedFn.apply(this, args);
      } finally {
        endTiming();
      }
    }.bind(this);
  }

  /**
   * Create a performance-wrapped watcher
   * @param {string} name - Name of the watcher
   * @param {Function} watcherFn - Watcher function
   * @returns {Function} Wrapped watcher function
   */
  wrapWatcher(name, watcherFn) {
    if (!this.enabled) return watcherFn;
    
    return function(...args) {
      const endTiming = this.startTiming(name, 'watcher');
      try {
        return watcherFn.apply(this, args);
      } finally {
        endTiming();
      }
    }.bind(this);
  }

  /**
   * Create a performance-wrapped method
   * @param {string} name - Name of the method
   * @param {Function} methodFn - Method function
   * @returns {Function} Wrapped method function
   */
  wrapMethod(name, methodFn) {
    if (!this.enabled) return methodFn;
    
    return function(...args) {
      const endTiming = this.startTiming(name, 'method');
      try {
        return methodFn.apply(this, args);
      } finally {
        endTiming();
      }
    }.bind(this);
  }
}

// Global instance
const performanceMonitor = new PerformanceMonitor();

// Vue mixin for automatic performance monitoring
export const performanceMonitorMixin = {
  beforeCreate() {
    if (!performanceMonitor.enabled) return;
    
    try {
      // Wrap computed properties
      if (this.$options.computed) {
        Object.keys(this.$options.computed).forEach(key => {
          const computed = this.$options.computed[key];
          if (typeof computed === 'function') {
            this.$options.computed[key] = performanceMonitor.wrapComputed(
              `${this.$options.name || 'Component'}.${key}`,
              computed
            );
          } else if (computed && computed.get) {
            computed.get = performanceMonitor.wrapComputed(
              `${this.$options.name || 'Component'}.${key}`,
              computed.get
            );
          }
        });
      }
      
      // Wrap watchers
      if (this.$options.watch) {
        Object.keys(this.$options.watch).forEach(key => {
          const watcher = this.$options.watch[key];
          if (typeof watcher === 'function') {
            this.$options.watch[key] = performanceMonitor.wrapWatcher(
              `${this.$options.name || 'Component'}.${key}`,
              watcher
            );
          }
        });
      }
    } catch (error) {
      // If there's any error during setup, just continue without performance monitoring
      console.warn('Performance monitoring setup failed:', error);
    }
  },
};

export default performanceMonitor; 