<template lang="pug">
.hx-col.d-block.h-100.w-40
  .hx-action
    template(v-if="sportsradar")
      .hx-icon(v-if="source.radarId", @click="xSetLink('radar')")
        court
    template(v-else)
      .hx-icon(v-if="source.stats", target="_blank", @click="xLinkStats($event)")
        i.fal.fa-chart-bar
    .hx-icon(@click="xSetFav($event)")
      i(:class="xIsFav() ? 'fas fa-star selected' : 'fal fa-star'")
  .hx-action(v-if="source && source.betTypeId != 'parlay'")
    .hx-icon(v-if="sportsradar && source.stats", target="_blank", @click="xLinkStats($event)")
      i.fal.fa-chart-bar
    template(v-if="source.marketId == 3")
      .hx-icon(v-if="source.channelId", @click="xSetLink('channel')")
        i.fal.fa-tv
      .hx-icon(v-else)
    template(v-else)
      .hx-icon(v-if="source.channelId >= 1", style="cursor: initial;")
        i.fal.fa-tv
      .hx-icon(v-else)

</template>

<script>
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";
import court from "@/components/desktop/court";
export default {
  components: {
    court
  },
  props: {
    source: {
      type: Object
    }
  },
  data() {
    return {
      countDown: 0,
      ticker: null
    };
  },
  computed: {
    newFeatures() {
      return config.newFeatures;
    },
    sportsradar() {
      return config.sportsradar;
    },
    favorite() {
      return this.$store.getters.favorite;
    },
    menu0() {
      return this.$store.getters.menu0;
    }
  },
  destroyed() {
    if (this.ticker) {
      clearTimeout(this.ticker);
    }
  },
  mounted() {
  },
  methods: {
    isEFight(e) {
      if (e && e.sportsId) return config.vg1.includes(e.sportsId);
      else return false;
    },
    xLinkStats(e) {
      if (this.source && this.source.stats) {
        var url = config.statscenterUrl() + this.source.stats + "&u=" + this.$store.getters.accountId + "&t=" + this.$store.getters.sessionToken + "&l=" + this.$store.getters.language;
        window.open(url, "stats", "top=10,height=540,width=900,status=no,toolbar=no,menubar=no,location=no");
      }
      return false;
    },
    xSetLink(e) {
      if (this.source) {
        const data = {
          action: e,
          radar_id: this.source.radarId,
          channel: this.source.channelId,
          match_id: this.source.matchId,
          league_id: this.source.leagueId,
          sports_type: this.source.sportsId,
          market_type: this.source.marketId,
          home_team: this.source.homeTeam,
          away_team: this.source.awayTeam,
          working_date: this.source.workingTime,
          match_time: this.source.matchTime
        };
        EventBus.$emit("LIVE_CENTER", data);
      }
    },
    xIsFav() {
      if (this.favorite) {
        if (this.favorite.indexOf(this.source.matchId) != -1) {
          return true;
        }
      }
      return false;
    },
    xSetFav(evt) {
      var favList = [];
      if (favList.indexOf(this.source.matchId) == -1) {
        favList.push(this.source.matchId);
      }
      if (this.xIsFav(this.source.matchId)) {
        this.$store.dispatch("layout/delFavorite", favList);
      } else {
        this.$store.dispatch("layout/setFavorite", favList);
      }
      if (this.menu0 == "favorite") {
        setTimeout(() => {
          EventBus.$emit("GET_MARKET");
        }, 500);
      }
    }
  }
};
</script>
