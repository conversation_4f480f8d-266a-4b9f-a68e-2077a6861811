<template lang="pug">
  .hx-main.htftOdds
    //- small.text-white {{ details }}
    .hx-table.hx-match.hx-compact(:class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }")
      .hx-cell.w-62
        .hx-row.h-100.hx-rows
          timePanel(:source="source")
      .hx-cell.flex-fill
        .hx-row.h-100.hx-rows
          xTeam(:source="source" isDraw=false cls="w-168")
          xFavorite(:source="source")
      template(v-for="item in cols")
        .hx-cell.w-549
          .hx-row.hx-rows.h-100
            .hx-col.hx-cols.h-100.w-61(v-for="(i, index) in htft")
              htftItem(:details="details" :oddsType="oddsType" :i="i" :item="item")
      .hx-cell.w-40
        .hx-row.h-100.hx-rows
          .hx-col.hx-cols.w-100.d-flex.align-items-center.justify-content-center
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

// import timePanel from "@/components/desktop/main/xtable/timePanel";
// import xTeam from "@/components/desktop/main/xtable/xitem/xTeam";
// import xFavorite from "@/components/desktop/main/xtable/xitem/xFavorite";
// import htftItem from "@/components/desktop/main/xtable/xitem/htftItem";

export default {
  components: {
    // timePanel,
    // xTeam,
    // xFavorite,
    // htftItem

    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    xTeam: () => import("@/components/desktop/main/xtable/xitem/xTeam"),
    xFavorite: () => import("@/components/desktop/main/xtable/xitem/xFavorite"),
    htftItem: () => import("@/components/desktop/main/xtable/xitem/htftItem")
  },
  mixins: [mixinHDPOUOdds],
  data() {
    return {
      cols: ['htfto'],
      htft: ['HH', 'HD', 'HA', 'DH', 'DD', 'DA', 'AH', 'AD', 'AA']
    };
  }
};
</script>