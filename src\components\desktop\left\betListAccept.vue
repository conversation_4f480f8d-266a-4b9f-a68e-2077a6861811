<template lang="pug">
#pills-bets.tab-pane.show.active(role="tabpanel" aria-labelledby="pills-bets-tab")
  .empty.text-center(v-if="items == undefined || items.length <= 0")  {{ $t("message.no_bets") }}
  #bet-accept-accordion.magicY.bet-list-scroll(v-if="items != undefined && items.length > 0")
    template(v-for="(item, index) in items")
      betListItem(
        :item="item"
        :key="item.bet_id"
        :nopad="index == items.length - 1"
        status="running"
        @selected="handleSelected"
        :detail="selectedId == item.bet_id"
        :loading="loading"
        prefix="accept")
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";
import xhr from "@/library/_xhr-betlist.js";
import betListItem from "@/components/desktop/left/betListItem";

export default {
  components: {
    betListItem
  },
  data() {
    return {
      loading: false,
      items: [],
      selectedId: null
    };
  },
  computed: {
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    }
  },
  destroyed() {
  },
  mounted() {
    this.populateList();
    EventBus.betListAccept = this.runner;
  },
  methods: {
    runner() {
      this.populateList();
      $("#collapse-mybet").collapse("show");
      $("#pills-bets-tab").tab("show");
    },
    handleSelected(e) {
      this.selectedId = e;
    },
    populateList(callback) {
      if (this.loading == true) return;
      if (this.isLoggedIn) {
        this.loading = true;
        var args = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          page_size: 10
        };
        xhr.getBetAcceptList(args).then(
          res => {
            this.loading = false;
            if (res.success) {
              if (res.data) {
                // console.log(res.data);
                this.items = res.data;
                $(".bet-list-scroll").scrollTop(0);
                if (callback) callback();
              }
            } else {
              if (this.$helpers.handleFeedback(res.status)) {
                if (callback) callback();
              }
            }
          },
          err => {
            this.loading = false;
            if (this.$helpers.handleFeedback(err.status)) {
              if (callback) callback();
            }
          }
        );
      }
    }
  }
};
</script>
