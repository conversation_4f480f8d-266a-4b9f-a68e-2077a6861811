<template lang="pug">
  div(style="position: absolute; top: 200px; right: 0; font-size: 10px;" v-if="debugMode")
    table.table-x
      thead
        tr
          th.text-left Name
          th.text-right Value
          th Action
      tbody

        tr
          td.text-left MENU0
          td.text-right {{ menu0 }}
          td(rowspan="11" style="vertical-align: bottom;")
            button(@click="showConfig") CONFIG
            button(@click="showCache") CACHE
            button(@click="showOrder") ORDER
            button(@click="showSingleBets") SINGLE
            button(@click="showParlayBets") PARLAY
        tr
          td.text-left MENU1
          td.text-right {{ menu1 }}
        tr
          td.text-left MENU2
          td.text-right {{ menu2 }}
        tr
          td.text-left MENU3
          td.text-right {{ menu3 }}
        tr
          td.text-left MENUX
          td.text-right {{ menuX }}
        tr
          td.text-left MENUY
          td.text-right {{ menuY }}
        tr
          td.text-left MATCH
          td.text-right {{ selectedMatch }}
        tr
          td.text-left DAY
          td.text-right {{ selectedDays }}
        tr
          td.text-left WL
          td.text-right {{ whiteLabel }}
        tr
          td.text-left S
          td.text-right {{ search }}
</template>

<script>
import config from "@/config";
import mixinTypes from "@/library/mixinTypes";
import naming from "@/library/_name.js";
import { EventBus } from "@/library/_event-bus.js";

export default {
  mixins: [mixinTypes],
  data() {
    return {
      fps: 0,
      totalJSHeapSize: 0,
      usedJSHeapSize: 0,
      jsHeapSizeLimit: 0,
      times: [],
      fpsr: []
    };
  },
  mounted() {
    // console.log("DEBUG: " + this.debugMode);
  },
  methods: {
    showConfig() {
      console.log(config);
    },
    showCache() {
      console.log(this.$store.getters.data, this.$store.state.layout.sports);
    },
    showOrder() {
      console.log(this.$store.state.layout.order.sports, this.$store.state.layout.order.default.sports, this.$store.state.layout.sports);
    },
    showSingleBets() {
      console.log(this.$store.state.betsingle);
    },
    showParlayBets() {
      console.log(this.$store.state.betparlay);
    }
  }
};
</script>

<style lang="scss" scoped>
.table-x {
  margin: 2px;
  font-size: 10px;
  color: white;
  table-layout: fixed;
  background: #000000;

  td,
  th {
    border: 1px solid #ffffff;
    text-align: center;
    min-width: 32px;
    padding: 4px 4px;
  }
  td:first-child {
    color: #888888;
  }
  button {
    display: block;
    width: 100%;
    padding: 4px 8px;
    margin-bottom: 4px;
    border: 1px solid #ffffff;
    background: #e6e6e6;
    border-radius: 2px;
    color: #000000;
    font-weight: 600;
    font-size: 8px;
  }
  thead {
    background: #444444;
    th {
      color: #ffffff;
      font-size: 10px;
      font-weight: normal;
    }
  }
}
</style>
