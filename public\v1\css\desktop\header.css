header {
	max-width: 100vw !important;
	margin: 0 auto !important;
	padding: 0;
	min-width: 1340px !important;
	width: 100%;
	background: #0f4f8c;
	position: fixed;
	top: 0;
	z-index: 1030;
}
header .topbar .container,header .toolbar .container {
	padding: 0;
}
header .toolbar > .container {
	/* padding-left: 10px; */
}
header .toolbar .news .show-news {
	padding-top: 10px;
	color: #fff;
	font-size: 14px;
	line-height: 18px;
	display: block;
	width: 100%;
	-webkit-mask-image: -webkit-linear-gradient(right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 10%, rgba(0, 0, 0, 1) 90%, rgba(0, 0, 0, 0) 100%);
	mask-image: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 10%, rgba(0, 0, 0, 1) 90%, rgba(0, 0, 0, 0) 100%);
}
header .toolbar.active .news .show-news {
	padding-top: 0;
}
header .toolbar.active.white-label .news .show-news {
	padding-top: 10px;
}
header .toolbar .news .show-time {
	font-weight: 400;
	display: none;
	-webkit-box-align: start;
	    -ms-flex-align: start;
	        align-items: start;
	-webkit-box-pack: start;
	    -ms-flex-pack: start;
	        justify-content: start;
	font-size: 11px;
	padding: 2px 0 0 0;
}
header .toolbar.active .news .show-time {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	line-height: 1;
}
header .toolbar.active.white-label .news .show-time {
	display: none;
	line-height: 1;
}
header .toolbar .menu .nav {
	-ms-flex-wrap: nowrap;
	    flex-wrap: nowrap;
}
header .toolbar .menu .nav .nav-item:first-child {
	padding-left: 0;
}
header .toolbar .menu .nav .nav-item:last-child {
	padding-right: 0;
}
header .toolbar.active .new-timezone {
	min-width: 220px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}
header .toolbar.active.white-label .new-timezone {
	min-width: 220px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}
header .toolbar .new-timezone .show-time {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	font-weight: 400;
	-webkit-box-align: start;
	    -ms-flex-align: start;
	        align-items: start;
	-webkit-box-pack: start;
	    -ms-flex-pack: start;
	        justify-content: start;
	line-height: 18px;
	padding: 0 2px;
	font-family: "Oswald", sans-serif;
	color: #ffffff;
	font-size: 14px;
	width: 100%;
	white-space: nowrap;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	   text-overflow: ellipsis;
}
header .toolbar.active .new-timezone .show-time {
	display: none;
}
header .toolbar.active.white-label .new-timezone .show-time {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}
header .toolbar .new-timezone .show-time .text-info {
	color: #00EDFF !important;
}
header .toolbar .show-user {
	z-index: 10;
	cursor: pointer;
	display: none;
	font-family: "Lato", sans-serif;
	color: #ffffff;
	font-size: 12px;
	font-weight: 400;
	line-height: 1;
	border-radius: 3px;
	border: 1px solid #051c33;
	-webkit-box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset;
	        box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset;
	text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
	width: 120px;
	height: 28px;
	color: #ffc107;
	overflow: hidden;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
}
header .toolbar .show-user:hover {
	color: #f9d040;
	-webkit-box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset, 0 0 3px rgba(0, 0, 0, 0.75);
	        box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset, 0 0 3px rgba(0, 0, 0, 0.75);
	background-color: rgba(255, 255, 255, 0.1);
	text-shadow: 0 0 3px rgba(255, 255, 255, 0.5);
}
header .toolbar.active .show-user {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}
header .topbar .show-user {
	margin-top: 5px;
	z-index: 10;
	cursor: pointer;
	display: none;
	font-family: "Lato", sans-serif;
	color: #ffffff;
	font-size: 12px;
	font-weight: 400;
	line-height: 1;
	padding: 0 8px;
	border-radius: 3px;
	border: 1px solid #051c33;
	-webkit-box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset;
	        box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset;
	text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
	width: 120px;
	height: 28px;
	color: #ffc107;
	overflow: hidden;
}
header .topbar .show-user:hover {
	color: #f9d040;
	-webkit-box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset, 0 0 3px rgba(0, 0, 0, 0.75);
	        box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset, 0 0 3px rgba(0, 0, 0, 0.75);
	background-color: rgba(255, 255, 255, 0.1);
	text-shadow: 0 0 3px rgba(255, 255, 255, 0.5);
}
header .topbar.active .show-user {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
}
header .toolbar #logout-btn {
	display: none;
}
header .toolbar.active #logout-btn {
	display: block;
}
header .toolbar.active.white-label #logout-btn {
	display: none;
}
header .topbar {
	background: url(/images/top-bg.png) top center no-repeat;
	background-size: cover;
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 2;
	height: 66px;
	max-height: 66px;
}
header .topbar .container .main-nav ul.nav li.nav-item a.nav-link {
	font-size: 14px;
	text-transform: uppercase;
	color: #fff;
	letter-spacing: 1px;
	font-family: "Roboto", sans-serif;
	padding: 0 10px;
	border: 1px solid transparent;
	text-align: center;
	height: 100%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
}
header .topbar .container .main-nav ul.nav li.nav-item a.nav-link i {
	margin-left: 4px;
	margin-right: -4px;
}
header .topbar .container .login {
	padding-top: 5px;
	width: 400px;
}
header .topbar .container .login form .form-group {
	margin-bottom: 4px;
}
header .topbar .container .login form .form-group .text-muted {
	color: #f9d040 !important;
}
header .topbar .container .login form .form-group .form-control {
	padding: 0 10px;
	line-height: 24px;
	height: 24px;
	border-radius: 0 0 0 0;
	font-size: 13px;
	background: #ddd;
	border-color: #ddd;
	color: #0f4f8c;
}
header .topbar .container .login form .form-group .form-control::-webkit-input-placeholder {
	color: #0f4f8c !important;
}
header .topbar .container .login form .form-group .form-control::-moz-placeholder {
	color: #0f4f8c !important;
}
header .topbar .container .login form .form-group .form-control:-ms-input-placeholder {
	color: #0f4f8c !important;
}
header .topbar .container .login form .form-group .form-control:-moz-placeholder {
	color: #0f4f8c !important;
}
header .topbar .container .login form .form-group .dropdown .dropdown-menu[aria-labelledby="language"] {
	position: absolute !important;
	-webkit-transform: none !important;
	-ms-transform: none !important;
	transform: none !important;
	top: inherit !important;
	left: 0 !important;
}
header .topbar .container .login form .form-group .btn-login {
	padding: 0;
	line-height: 20px;
	height: 24px;
	border-radius: 0 0 0 0;
	font-size: 13px;
	text-transform: capitalize;
	background: #a4a4a4;
	border-color: #a4a4a4;
	color: #fff;
	width: 100%;
	top: -2px;
	position: relative;
}
header .topbar .container .loggedin .player {
	color: #d1dfe8;
}
header .topbar .container .loggedin .player .fa-user {
	margin-top: 4px;
}
header .topbar .container .loggedin .player .user {
	white-space: nowrap;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
}
header .topbar .container .loggedin .details {
	text-align: center;
	font-size: 12px;
}
header .topbar .container .loggedin .details .credit {
	text-transform: capitalize;
	background: #0d3a64;
	color: #fff;
	padding: 0 6px !important;
}
header .topbar .container .loggedin .details .btn-logout {
	text-transform: uppercase;
	background: #a4a4a4;
	color: #fff;
	cursor: pointer;
	padding: 0 6px !important;
}
header .topbar .container .loggedin .dropdown .btn-secondary {
	width: 140px;
}
header .topbar .container .loggedin .dropdown .dropdown-menu {
	position: absolute !important;
	-webkit-transform: none !important;
	-ms-transform: none !important;
	transform: none !important;
	top: inherit !important;
	left: inherit !important;
}
header .topbar .container .loggedin .timezone {
	font-size: 12px;
	color: #fff;
	text-align: right;
	line-height: 28px;
}
header .topbar.active {
	display: none;
}
header .topbar .container .logo img {
	padding-top: 2px;
	height: 56px;
	/* width: inherit; */
}
header .toolbar .container .logo {
	display: none !important;
}
header .toolbar.active .container .logo {
	padding-top: 2px;
	min-width: 100px;
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
	-webkit-box-align: start;
	    -ms-flex-align: start;
	        align-items: start;
	-webkit-box-pack: start;
	    -ms-flex-pack: start;
	        justify-content: start;
	position: relative;
	padding-left: 0 !important;
	text-align: left !important;
}
header .toolbar.active .logo img {
	width: 94px !important;
	height: inherit !important;
	margin-top: -3px;
}
header .toolbar {
	background: #1C62A0;
	width: 100%;
	border-top: 1px #5991C1 solid;
	border-bottom: 8px #C0CED9 solid;
	position: absolute;
	top: 66px;
	left: 0;
	z-index: 1;
}
header .toolbar.active {
	top: 0;
}
header .toolbar.active .container {
	height: 35px;
}
header .toolbar .searchbar .input-group .input-group-prepend .input-group-text {
	background-color: transparent;
	border: 1px solid transparent;
	color: #fff;
}
header .toolbar .searchbar .input-group .form-control {
	background-color: transparent;
	border: 1px solid transparent;
	color: #fff;
	font-size: 15px;
}
header .toolbar .searchbar .input-group .form-control::-webkit-input-placeholder {
	color: #fff !important;
}
header .toolbar .searchbar .input-group .form-control::-moz-placeholder {
	color: #fff !important;
}
header .toolbar .searchbar .input-group .form-control:-ms-input-placeholder {
	color: #fff !important;
}
header .toolbar .searchbar .input-group .form-control:-moz-placeholder {
	color: #fff !important;
}
header .toolbar .menu ul.nav li.nav-item a.nav-link {
	color: #fff;
	text-transform: capitalize;
	font-size: 14px;
}
header .toolbar .menu ul.nav li.nav-item {
	height: 35px;
	font-family: "Oswald", sans-serif;
	font-size: 10px;
	color: #a5cae5;
	line-height: 35px;
	padding: 0 2px;
}
header .toolbar .menu ul.nav li.nav-item a.nav-link {
	color: #FFFFFF;
	white-space: nowrap;
	margin-top: 2px;
	margin-left: 0;
	margin-right: 0;
	margin-bottom: 0;
	padding: 0;
	border-radius: 3px;
	border: 1px solid #5991C1;
	cursor: pointer;
	height: 30px;
	min-width: 32px;
	font-size: 16px;
	line-height: 1;
	text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	background-color: #2379C4;
}
header .toolbar .menu ul.nav li.nav-item a.nav-link.icon01,
header .toolbar .menu ul.nav li:last-child.nav-item a.nav-link {
	background-color: #145693;
	border: 0;
	border-left: 1px solid #5991C1;
	border-right: 1px solid #5991C1;
	border-radius: 0;
	margin: 0 1px;
	height: 35px;
}
header .toolbar .menu ul.nav li.nav-item a.nav-link i {
	margin: 0 6px;
}
header .toolbar .menu ul.nav li.nav-item a.nav-link .flash {
	font-size: 10px;
	margin-left: -3px;
	margin-right: 6px;
	display: none;
}
header .toolbar .menu ul.nav li.nav-item a.nav-link:hover .flash {
	display: block;
}
header .toolbar .menu ul.nav li.nav-item a.nav-link:active {
	-webkit-box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.75);
	        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.75);
}
.w-28px {
	width: 28px !important;
}
header .toolbar .menu ul.nav li.nav-item a.nav-link:hover {
	color: #f9d040;
	-webkit-box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset, 0 0 3px rgba(0, 0, 0, 0.75);
	        box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset, 0 0 3px rgba(0, 0, 0, 0.75);
	background-color: rgba(255, 255, 255, 0.1);
	text-shadow: 0 0 3px rgba(255, 255, 255, 0.5);
}
header .toolbar .menu ul.nav li.nav-item a.nav-link.thinner {
}
header .toolbar .icon-wrapper {
	padding: 0;
	height: 100%;
}
header .toolbar .icon-wrapper .icon {
	margin: 0;
	width: 32px;
	height: 100%;
	line-height: 1;
	text-align: center;
	font-size: 14px;
	cursor: pointer;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
}
header .toolbar .icon-wrapper .icon.icon01-up,header .toolbar .icon-wrapper .icon.icon01-down {
	color: #fff;
	margin: 0;
	position: relative;
	height: 100%;
	top: 0;
	right: 0;
	border-left: 1px #0f4f8c solid;
	border-right: 1px #0f4f8c solid;
	background: #092c4c;
}
header .toolbar .icon-wrapper .icon.icon01-up:hover,header .toolbar .icon-wrapper .icon.icon01-down:hover {
	color: #f9d040;
}
header .toolbar .icon-wrapper .icon.icon01-down {
	display: none;
}
header .toolbar .icon-wrapper .icon.icon02 {
	color: #fff;
	opacity: 0.8;
}
header .toolbar .icon-wrapper .icon.icon02:hover {
	-webkit-filter: grayscale(0%);
	filter: grayscale(0%);
	opacity: 1;
}
header .toolbar .icon-wrapper .icon.icon03 {
	color: #37516c;
	opacity: 0.8;
	font-size: 20px;
	width: 50px;
	margin-right: -5px;
}
header .toolbar .icon-wrapper .icon.icon03:hover {
	-webkit-filter: grayscale(0%);
	filter: grayscale(0%);
	opacity: 1;
}
header .toolbar .icon-wrapper .icon.icon04 {
	display: none;
	color: #a5cae5ff;
	border-left: 1px #0f4f8c solid;
}
header .toolbar .icon-wrapper .icon.icon04:hover {
	color: #f9d040;
}
header .toolbar.active .searchbar {
	padding-left: 200px;
}
header .topbar .container .main-nav ul.nav li.nav-item {
	font-weight: 700;
	margin: 0 1px;
	font-family: "Lato", sans-serif;
	height: 100%;
	padding: 0;
}
header .topbar .container .main-nav ul.nav li.nav-item:first-child {
	margin-left: 0;
}
header .topbar .container .main-nav ul.nav li.nav-item a.nav-link.active,header .topbar .container .main-nav ul.nav li.nav-item a.nav-link:hover {
	margin: 0;
	width: inherit;
	color: #0A4782;
}
header .topbar .container .login form .form-group .dropdown .btn-secondary,header .topbar .container .loggedin .dropdown .btn-secondary {
	padding: 0 10px;
	line-height: 20px;
	height: 24px;
	border-radius: 0 0 0 0;
	font-size: 13px;
	text-transform: capitalize;
	background: #ddd;
	border-color: #ddd;
	color: #0f4f8c;
	width: 100%;
	position: relative;
	top: -2px;
}
header .topbar .container .login form .form-group .dropdown .dropdown-menu,header .topbar .container .loggedin .dropdown .dropdown-menu {
	border-radius: 0 0 0 0;
	background: #ddd;
	border-color: #ddd;
	padding: 0;
	min-width: 0;
	width: auto;
}
header .topbar .container .login form .form-group .dropdown .dropdown-menu .dropdown-item,header .topbar .container .loggedin .dropdown .dropdown-menu .dropdown-item {
	padding: 0.25rem 10px;
	font-size: 13px;
	color: #0f4f8c;
	cursor: pointer;
}
header .topbar .container .login form .form-group .dropdown .dropdown-menu .dropdown-item.active,header .topbar .container .login form .form-group .dropdown .dropdown-menu .dropdown-item:active {
	color: #fff;
	background: #0f4f8c;
}
header .topbar .container .loggedin .dropdown .dropdown-menu .dropdown-item.active,header .topbar .container .loggedin .dropdown .dropdown-menu .dropdown-item:active {
	color: #fff;
	background: #0f4f8c;
}
header .topbar .container .login form .form-group .dropdown .dropdown-toggle::after,header .topbar .container .loggedin .dropdown .dropdown-toggle::after {
	position: relative;
	float: right;
	top: 8px;
}
header .topbar .container .loggedin .player .fa-sync,header .topbar .container .loggedin .player .fa-chevron-circle-down {
	cursor: pointer;
}
header .topbar .container .loggedin .details .credit,header .topbar .container .loggedin .details .amount,header .topbar .container .loggedin .details .btn-logout {
	white-space: nowrap;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
}
header .toolbar .menu ul.nav li.nav-item a.nav-link.active,header .toolbar .menu ul.nav li.nav-item a.nav-link:hover {
	color: #f9d040;
}
header .topbar.active .main-nav,header .topbar.active .player,header .topbar.active .details,header .topbar.active .dropdown #language,header .topbar.active .timezone,header .toolbar.active .icon-wrapper .icon.icon01-up,header .topbar.active #user-profile,header .topbar.active .loggedin {
	display: none !important;
}
header .toolbar.active .icon-wrapper .icon.icon01-down,header .toolbar.active .icon-wrapper .icon.icon04 {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}
.yellow {
	color: #f9d040;
	padding: 0 !important;
}
header .topbar .container .loggedin {
	margin-top: 4px;
	padding: 6px;
	width: 100%;
	border-radius: 6px;
	position: relative;
	/* min-width: 300px; */
}
header .topbar .container .loggedin #user-info {
	overflow: hidden;
	margin-right: 6px;
}
header .topbar .container .loggedin #user-header {
	color: #d1dfe8ee;
	background-color: #d1dfe811;
	font-family: "Lato", sans-serif;
	font-family: 16px;
	font-weight: 600;
	height: 100%;
	padding: 0 8px;
	border-top-left-radius: 6px;
	border-bottom-left-radius: 6px;
	line-height: 44px;
}
header .topbar .container .loggedin .player .user {
	color: #FFFFFF;
	font-size: 12px;
	font-family: "Lato", sans-serif;
	font-weight: 500;
	line-height: 14px;
	margin-bottom: 2px;
}
header .topbar .container .loggedin .player .message-box {
	color: #d1dfe8;
	font-size: 16px;
	font-family: "Lato", sans-serif;
	line-height: 1;
	margin-bottom: 2px;
}
header .topbar .container .loggedin .details .amount {
	font-size: 14px;
	text-transform: uppercase;
	text-align: left;
	color: #FFFFFF;
	font-weight: 600;
	font-family: "Lato", sans-serif;
	line-height: 1;
}
header .topbar .container .loggedin .details .unit {
	font-size: 10px;
	text-transform: uppercase;
	text-align: left;
	color: #C4E4FF;
	font-weight: bold;
	font-family: "Lato", sans-serif;
	line-height: 1;
	margin-top: 1px;
	margin-right: 3px;
}
.icon-header {
	margin-left: 4px;
	padding: 8px;
	border-radius: 3px;
	cursor: pointer;
	height: 34px;
	width: 34px;
	text-align: center;
	font-size: 16px;
	line-height: 1;
	text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
	background-color: #145693;
	border: 1px solid #5991C1;
	color: #FFFFFF;
}
.icon-header:hover {
	color: #014273;
	background-color: #F6C344;
}
.col-width {
	max-width: 34px;
}
.email-counter {
	background: #b53f39;
	border-radius: 50%;
	color: #ffffff;
	font-size: 8px;
	line-height: 1;
	text-align: center;
	position: absolute;
	right: -3px;
	top: -3px;
	width: 16px;
	height: 16px;
	z-index: 30;
	font-family: "Lato", sans-serif;
	display: block;
	margin: 0;
	padding: 3px 0 0 0;
	border: 1px solid #7c2c28;
	-webkit-box-shadow: 0 0 3px rgba(255, 255, 255, 0.75);
	        box-shadow: 0 0 3px rgba(255, 255, 255, 0.75);
}
.language-selector {
	margin: 0;
	padding: 0;
	margin-left: 4px;
}
.language-selector > .dropdown-toggle {
	display: inline-block;
	border-radius: 3px;
	padding: 0 6px;
	background-clip: padding-box;
	width: auto;
	height: 34px;
	border: 1px solid #051c33;
	background-color: #145693;
	border: 1px solid #5991C1;
	color: #FFFFFF;
}
.language-selector > .dropdown-toggle img {
	display: inline-block;
	line-height: 1;
	width: 20px;
}
.language-selector.open > .dropdown-toggle {
	background: #f5f5f6;
}
header .topbar .container .loggedin .dropdown.language-selector .dropdown-toggle::after {
	color: #fff;
	position: relative;
	float: right;
	top: 0;
}
header .topbar .container .loggedin .dropdown.language-selector .dropdown-toggle:hover {
	color: #014273;
	background-color: #F6C344;
}
.language-selector .dropdown-menu {
	background: #145693 !important;
	border: none;
	margin: 0;
	padding: 0;
	overflow: hidden;
	-webkit-border-radius: 0 3px 3px 3px;
	-webkit-background-clip: padding-box;
	-moz-border-radius: 0 3px 3px 3px;
	-moz-background-clip: padding;
	border-radius: 0 3px 3px 3px;
	background-clip: padding-box;
	border-radius: 4px 4px 4px 4px !important;
	border: 1px solid #5991C1 !important;
	-webkit-box-shadow: 0 1px 0 #2d6fb2 inset !important;
	        box-shadow: 0 1px 0 #2d6fb2 inset !important;
	margin-top: 5px;
}
.dropdown.language-selector .dropdown-menu {
	top: 53px !important;
}
.language-selector .dropdown-menu.pull-right {
	-webkit-border-radius: 3px 0 3px 3px;
	-webkit-background-clip: padding-box;
	-moz-border-radius: 3px 0 3px 3px;
	-moz-background-clip: padding;
	border-radius: 3px 0 3px 3px;
	background-clip: padding-box;
}
.language-selector .dropdown-menu > li {
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}
.language-selector .dropdown-menu > li a {
	margin: 0;
	display: block;
	-webkit-border-radius: 0;
	-webkit-background-clip: padding-box;
	-moz-border-radius: 0;
	-moz-background-clip: padding;
	border-radius: 0;
	background-clip: padding-box;
	padding: 7px;
	color: #8d929a;
	-webkit-transition: all 300ms ease-in-out;
	-o-transition: all 300ms ease-in-out;
	transition: all 300ms ease-in-out;
}
.language-selector .dropdown-menu > li a img {
	width: 32px;
	height: 32px;
}
.language-selector .dropdown-menu > li a:hover {
	background: rgba(235, 235, 235, 0.2);
}
.language-selector .dropdown-menu > li:last-child {
	border-bottom: 0;
}
.language-selector .dropdown-menu > li.active a {
	background: #003c79;
	color: #737881;
}
.language-selector.open > .dropdown-toggle {
	-webkit-border-radius: 3px 3px 0 0;
	-webkit-background-clip: padding-box;
	-moz-border-radius: 3px 3px 0 0;
	-moz-background-clip: padding;
	border-radius: 3px 3px 0 0;
	background-clip: padding-box;
}
header .topbar .logo,header .toolbar .new-timezone {
	min-width: 220px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	font-family: "Oswald", sans-serif;
}
header .topbar .main-nav,header .toolbar .news {
	margin: 0 7px;
	width: 860px;
	min-width: 860px;
	max-width: 920px;
	/* width: 1190px;
    min-width: 1190px;
    max-width: 1190px; */
}
header .topbar .menu,header .toolbar .menu {
	width: 320px;
	white-space: nowrap;
	padding: 0 1px;
	min-width: 256px;
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
}
header .toolbar .menu {
	overflow: hidden;
	justify-content: flex-start !important;
}
header .toolbar .news,header .toolbar .new-timezone {
	color: #fff;
	font-size: 11px;
	font-family: "Oswald", sans-serif;
}
header .topbar .main-nav {
	overflow: hidden;
	padding-left: 3px;
}
@media (max-width: 1280px) {
	header .topbar .main-nav,header .toolbar .news {
		margin: 0 7px;
		width: 720px;
		min-width: 720px;
		max-width: 920px;
		/* width: 1190px;
		min-width: 1190px;
		max-width: 1190px; */
	}
	header .topbar .menu {
		margin-right: 140px;
	}
}
@media (min-width: 1440px) {
	header .topbar .main-nav,header .toolbar .news {
		margin: 0 7px;
		width: 860px;
		min-width: 860px;
		max-width: 920px;
		/* width: 1190px;
		min-width: 1190px;
		max-width: 1190px;	 */
	}
	header .topbar .menu,header .toolbar .menu {
		margin-right: 0;
	}
	header .topbar .menu {
		border-left: 1px dashed #9CC2E3;
		margin: 7px 0;
		justify-content: space-between !important;
	}
}
@media (min-width: 1440px) {
	header .topbar .container .loggedin {
		width: 100%;
	}
}
@media (min-width: 1920px) {
	header .topbar .main-nav,header .toolbar .news {
		width: 1190px;
		min-width: 1190px;
		max-width: 1190px;	
	}
}
@media (min-width: 2048px) {
	header .topbar .main-nav,header .toolbar .news {
		width: 1438px;
		min-width: 1438px;
		max-width: 1438px;	
	}
}

/* new */
header .topbar .container .main-nav ul.nav li.nav-item a.nav-link:hover {
	transform: skewX(-15deg);
	position: relative;
}
header .topbar .container .main-nav ul.nav li.nav-item a.nav-link:hover img {
	transform: skewX(15deg);
}
header .topbar .container .main-nav ul.nav li.nav-item a.nav-link:hover:before {
	content: "";
    position: absolute;
    top: 16px;
    left: 0;
    right: 0;
    background-color: #F6C344;
    height: 30px;
    z-index: -1;
	box-shadow: 0px 0px 6px #0000005C;
}
header .topbar .container .main-nav ul.nav li.nav-item a.nav-link .icon-menu,
header .topbar .container .main-nav ul.nav li.nav-item a.nav-link .icon-menu-hover {
	margin-right: 6px;
	width: 16px;
}
header .topbar .container .main-nav ul.nav li.nav-item a.nav-link .icon-menu-hover {
	display: none;
}
header .topbar .container .main-nav ul.nav li.nav-item a.nav-link:hover .icon-menu-hover {
	display: block;
}
header .topbar .container .main-nav ul.nav li.nav-item a.nav-link:hover .icon-menu {
	display: none;
}