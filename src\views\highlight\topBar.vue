<template lang="pug">
header.short
  .topbar.info-top
    .hl-container.d-flex.align-items-center.justify-content-center
      template(v-if="whiteLabel.mode")
        router-link(to="/desktop")
          i.fas.fa-chevron-left
          span {{ $t("ui.back")  }}
      .d-flex.info-content.align-items-center.hl-mod1.flex-fill
        template(v-if="!whiteLabel.mode")
          .logo(@click="hardRefresh()")
            logo
        .nav-info.nav-title
          | {{ $t('ui.highlight') }}
</template>

<script>
import config from "@/config";

export default {
  components: {
    logo: () => import("@/components/desktop/logo")
  },
  data() {
    return {
      page: location.pathname.split("/").pop()
    };
  },
  computed: {
    whiteLabel() {
      return this.$store.getters.whiteLabel;
    },
    pageName() {
      return this.page;
    },
    language() {
      return this.$store.getters.language;
    }
  },
  methods: {
    hardRefresh() {
      window.location.reload(true);
    },
    go(tab) {
      // var routeData = this.$router.resolve('/' + tab);
      // this.$helpers.info(routeData.href);
      if (tab.split("/").pop() === "statement" && this.pageName === "statement") window.location.reload();
      if (this.page != tab.split("/").pop()) {
        this.$router.push(tab);
        this.page = tab.split("/").pop();
      }
    }
  }
};
</script>
