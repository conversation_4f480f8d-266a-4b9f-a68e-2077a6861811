<template lang="pug">
.right-wrapper
  .d-flex.align-items-center.mr-2
    .btn-switch-device.cursor-pointer(@click="switchToMobile")
      i.far.fa-mobile-android-alt.switch-device-mobile
      //- i.fas.fa-desktop-alt.switch-device-desktop
    .line
    .lang-wrap
      .lang-select
        .btn-select.dropdown-language.dropdown(data-toggle="dropdown" data-close-others="true" aria-expanded="false")
          img(:src="'/v1/images/lang/' + language + '.svg?v=muQbdTxYGU'")

        ul.dropdown-menu.pull-left.lang-dropdown-wrap-blue#lang-dropdown-list
          li(v-for="(item, key) in availableLanguage" @click="changeLanguage(item.id)")
            a(href="javascript:void(0)" :title="item.lang")
              img(:src="'/v1/images/lang/' + item.id + '.svg?v=muQbdTxYGU'")
              span {{ item.lang }}
  .login-group
    .login-field.ml-0
      .input-group
        .input-group-prepend
          .input-group-text
            i.far.fa-user
        input#username.form-control(v-model="username" type="text" :placeholder="$t('ui.account_id')" autofocus maxlength="20")
        .invalid-feedback(v-if="feedback.username") {{ feedback.username }}
    .login-field
      .invalid-feedback Password is invalid!
      .input-group
        .input-group-prepend
          .input-group-text
            i.far.fa-unlock-alt
        input#password.form-control(v-model="password" type="password" :placeholder="$t('ui.password')" maxlength="20" @keyup.enter="login")
        .invalid-feedback(v-if="feedback.password") {{ feedback.password }}
    button.login-btn2(@click="login")
      span {{ $t('ui.login') }}
</template>

<script>
import { required, alphaNum } from "vuelidate/lib/validators";
import config from "@/config";
import errors from "@/errors";
import SpinButton from "@/components/ui/SpinButton";
import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {
    SpinButton,
  },
  data() {
    return {
      languageList: config.languageAvailable,
      username: "",
      password: "",
      feedback: {
        username: "",
        password: "",
        loading: false,
        timeout: null,
      },
      result: "",
      availableLanguage: config.languageAvailable,
    };
  },
  computed: {
    language() {
      return this.$store.getters.language;
    },
    version() {
      return config.appVersion;
    },
    title() {
      return config.appTitle;
    },
    currency_code() {
      return this.$store.getters.currencyCode;
    },
  },
  watch: {
    result(newVal) {
      if (newVal) {
        setTimeout(() => {
          this.result = "";
        }, 5000);
      }
    },
  },
  validations: {
    username: {
      required,
    },
    password: {
      required,
    },
  },
  mounted() {
    $(".btn-select").click(function () {
      $(".lang-dropdown-wrap").toggle();
    });

    $("#login-modal").on("shown.bs.modal", function () {
      $("#username").trigger("focus");
    });
  },
  methods: {
    switchToMobile() {
      var url = config.getMobileUrl();
      window.location = url;
    },
    changeLanguage(lang) {
      this.$store.dispatch("layout/setLanguage", lang);
      EventBus.$emit("INVALIDATE");
    },
    setLanguage(lang) {
      this.$store.dispatch("layout/setLanguage", lang);
      $(".lang-dropdown-wrap").toggle();
    },
    reset() {
      this.feedback.username = "";
      this.feedback.password = "";
    },
    login() {
      // must invoke touch to validate the errors
      this.$v.$touch();
      clearTimeout(this.feedback.timeout);
      if (!this.$v.$invalid) {
        this.feedback.loading = true;
        this.reset();
        this.$store.dispatch("layout/reset");
        this.$store.dispatch("user/doLogin", { username: this.username, password: this.password }).then(
          (res) => {
            this.feedback.loading = false;
            if (res.success) {
              this.feedback.loading = true;
              this.$store.dispatch("user/getBalance").then(
                (res) => {
                  this.feedback.loading = false;
                  if (res.success) {
                    $("#login-modal").modal("hide");
                    this.$store.dispatch("layout/setRadarId", null);
                    this.$store.dispatch("layout/setSingleBetting", { property: "quickBet", value: false });

                    EventBus.$emit("INVALIDATE");
                    this.$router.push("/desktop").catch((err) => {
                      console.trace(err);
                    });
                  } else {
                    this.result = res.status;
                    if (this.$helpers.handleFeedback(res.status)) {
                    }
                  }
                },
                (err) => {
                  this.feedback.loading = false;
                  this.result = res.status;
                  if (this.$helpers.handleFeedback(err.status)) {
                  }
                }
              );
            } else {
              this.result = res.status;
              this.$helpers.handleFeedback(res.status);
            }
          },
          (err) => {
            this.feedback.loading = false;
            this.result = err.status;
            this.$helpers.handleFeedback(err.status);
          }
        );
      } else {
        this.feedback.loading = false;
        this.reset();
        if (!this.$v.username.required) {
          this.feedback.username = this.$t("error.usernameRequired");
        }

        if (!this.$v.password.required) {
          this.feedback.password = this.$t("error.passwordRequired");
        }
      }

      // reset the error message after 10 s
      this.feedback.timeout = setTimeout(() => {
        this.$v.$reset();
        this.reset();
      }, 10000);
    },
  },
};
</script>
