<template lang="pug">
.content-bet
  ul#pills-tab.nav(role="tablist")
    li.nav-item.col-4.px-0.nav-mybet
      a#pills-bets-tab.nav-link.active.text-ellipsis(
        data-toggle="pill"
        href="#pills-bets"
        role="tab"
        aria-controls="pills-bets"
        aria-selected="true"
        @click="handleAccept"
        :title="$t('ui.bets')")
        | {{ $t("ui.bets") }}
    li.nav-item.col-4.px-0.nav-mybet
      a#pills-waiting-tab.nav-link.text-ellipsis(
        data-toggle="pill"
        href="#pills-waiting"
        role="tab"
        aria-controls="pills-waiting"
        aria-selected="false"
        @click="handlePending"
        :title="$t('ui.waiting')") {{ $t("ui.waiting") }}
    li.nav-item.col-4.px-0.nav-mybet
      a#pills-void-tab.nav-link.text-ellipsis(
        data-toggle="pill"
        href="#pills-void"
        role="tab"
        aria-controls="pills-void"
        aria-selected="false"
        @click="handleReject"
        :title="$t('ui.void') + '/' + $t('ui.reject')")
        | {{ $t("ui.void") }}/{{ $t("ui.reject") }}
  #pills-tabContent.tab-content
    betListAccept(ref="accept")
    betListPending(ref="pending")
    betListReject(ref="reject")
</template>

<script>
import betListAccept from "@/components/desktop/left/betListAccept";
import betListPending from "@/components/desktop/left/betListPending";
import betListReject from "@/components/desktop/left/betListReject";
import { EventBus } from "@/library/_event-bus.js";
import mixinDelay from "@/library/mixinDelay";

export default {
  components: {
    betListAccept,
    betListPending,
    betListReject
  },
  mixins: [mixinDelay],
  data() {
    return {
      debounceAccept: null,
      debouncePending: null,
      debounceReject: null
    };
  },
  mounted() {
    this.debounceAccept = this.debounce(this.$refs.accept.populateList, 500);
    this.debouncePending = this.debounce(this.$refs.pending.populateList, 500);
    this.debounceReject = this.debounce(this.$refs.reject.populateList, 500);
  },
  methods: {
    handleAccept() {
      this.debounceAccept();
    },
    handlePending() {
      this.debouncePending();
    },
    handleReject() {
      this.debounceReject();
    }
  }
};
</script>
