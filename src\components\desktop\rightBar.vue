<template lang="pug">
#right-bar.right(:class="{ active: !asidebar }")
  .z-side.d-flex.flex-column.magicY

    //- Live Center
    .card.hb-5
      #heading-live-center.card-header
        .tools.d-flex.flex-row
          .icon
            .pointable(data-toggle="collapse", data-target="#collapse-live-center", aria-expanded="false", aria-controls="collapse-live-center")
              i.far.fa-chevron-down
          .text-actions.flex-fill.d-flex.align-items-center
            .text-icon(@click="setMode(0)", :class="{ selected: mode == 0, 'full-w': !sportsradar }")
              i.fal.fa-tv.xx-1
              span.text-ellipsis.xx-1 {{ $t('ui.live_tv') }}
              b.xx-1(v-if="this.liveTV.count") {{ this.liveTV.count }}
            .text-icon(v-if="sportsradar", @click="setMode(1)", :class="{ selected: mode == 1 }")
              court
              span.text-ellipsis {{ $t('ui.live_match') }}
              b(v-if="this.liveMatch.count") {{ this.liveMatch.count }}
          .icon
            .pointable(@click="handleToggle")
              #icon-expand
                i.far.fa-expand-alt
              #icon-compress.d-none
                i.far.fa-compress-alt
      #collapse-live-center.collapse.show(aria-labelledby="heading-live-center", data-parent=".z-side")
        .card-body.frame-parent
          .frame-header.tools.d-flex.flex-row.flex-fill
            .icon
              .pointable(v-if="event.match_id", @click="findEvent(event)")
                i.fal.fa-search
            .frame-dropdown.d-flex.flex-row
              #tracker-header.d-flex.flex-row(data-toggle="dropdown", aria-haspopup="true", aria-expanded="false", @click="getSchedule()")
                .tracker-sport
                  img(v-if="event.sports_type", :src="'/v1/images/icon-sport-svg/' + event.sports_type + '.svg'")
                .tracker-team
                  .text-ellipsis(v-if="event.sports_type") {{ event.home_team }} vs {{ event.away_team }}
                #drop-tv.tracker-menu.pointable
                  i.fal.fa-chevron-down
              .tracker-dropdown.dropdown-menu.dropdown-menu-right.magicY(aria-labelledby="tracker-header")
                template(v-if="mode == 0")
                  template(v-for="group in liveTV.schedule")
                    template(v-for="item in group")
                      .dropdown-item.pointable.d-flex.flex-row.align-items-center(href="javascript:void(0);", @click="chooseMatch(item)", :class="{ active: item.match_id == event.match_id }")
                        img(:src="'/v1/images/icon-sport-svg/' + item.sports_type + '.svg'")
                        .pl-1 {{ item.home_name_en }} vs {{ item.away_name_en }}
                template(v-else)
                  template(v-for="group in liveMatch.schedule")
                    template(v-for="item in group")
                      .dropdown-item.pointable.d-flex.flex-row.align-items-center(href="javascript:void(0);", @click="chooseMatch(item)", :class="{ active: item.match_id == event.match_id }")
                        img(:src="'/v1/images/icon-sport-svg/' + item.sports_type + '.svg'")
                        .pl-1 {{ item.home_name_en }} vs {{ item.away_name_en }}

            .icon.b-0
              .pointable(v-if="!event.radar_id || event.channel", @click="closeWidget()")
                i.fal.fa-times
            .icon
              a.pointable(
                v-if="event.channel && event.match_id",
                href="javascript:void(0);"
                @click="popup1(event)"
                )
                i.fal.fa-external-link-square
          //- widget
          #tracker-frame.frame-wrapper
            .side-frame
              #widget

    //- Parlay Generator
    .card
      #heading-luckypick.card-header
        .tools.d-flex.flex-row
          .icon
            .pointable(data-toggle="collapse", data-target="#collapse-luckypick", aria-expanded="false", aria-controls="collapse-luckypick")
              i.far.fa-chevron-down
          .text-actions.flex-fill.d-flex.align-items-center
            .text.flex-fill
              span {{ $t('ui.lucky_pick') }}
      #collapse-luckypick.collapse.show(aria-labelledby="heading-luckypick")
        .card-body
          .frame-wrapper.p-1
            luckypick

    //- Banners
    //- .card.some-border
    //-   .card-body
    //-     a.highlight-btn.pointable.hover-dark(:href="euroPage", target="_blank")
    //-       img(src="/v1/images/euro24_news.gif")

    //- .card.some-border(v-if="whiteLabel" :class="{ 'mb-5': whiteLabel }")
    //-   .card-body
    //-     router-link.highlight-btn.pointable.hover-dark(to="/highlight", target="_blank", onclick="window.open(this.href,'highlight','top=10,height=700,width=1366,status=no,toolbar=no,menubar=no,location=no');return false;")
    //-       img(src="/v1/images/highlight_banner.png")


    //- Slots Games
    .card.hb-5(v-if="!whiteLabel && ['MYR', 'USD', 'THB', 'MMK', 'CNY', 'IDR', 'SGD'].includes(currency_code)")
      #heading-games.card-header
        .tools.d-flex.flex-row
          .icon
            .pointable(data-toggle="collapse", data-target="#collapse-games", aria-expanded="false", aria-controls="collapse-games")
              i.far.fa-chevron-down
          .text-actions.flex-fill.d-flex.align-items-center
            .text.flex-fill
              span {{ $t('ui.mini_games') }}
          .icon(style="border-right: 0;")
            .pointable(v-if="play1", @click="playGame1('0')")
              i.fal.fa-home
          .icon
            .pointable(v-if="play1", @click="closeGame1")
              i.fal.fa-times
      #collapse-games.collapse.show(aria-labelledby="heading-games")
        .card-body
          #game-frame1.frame-wrapper
            .side-frame.h-100.mini-bg1
              iframe(v-if="play1", :src="slotsUrl" loading="lazy")
              //- Table Game
              //- 821031 Tables Mini Roulette
              //- 821038 Tables Mini Sic Bo
              //- Laughing Buddha - 5402
              //- God of Wealth - 5500
              //- Queen Femida - 5700
              //- Highway Kings - 5800
              //- Golden Monkey - 8400
              //- Fong Shen - 8900
              //- Dragon FaFaFa - 9200
              //- Burning Wheel - 10800
              //- Dragon Blaze - 11300
              //- Golden Lion - 12400
              //- Champion Star - 10401
              //- Fifa Fiesta - 15001
              //- Penalty Mania - 14801
              //- Dragon's Treasure - 16900
              //- Illuvium Master - 861004
              //- Mobox Olympia - 17300
              //- Goal Rush - 30034

              .productx.mini-game-container(v-else)
                #carou1.carousel.slide(data-ride='carousel')
                  ol.carousel-indicators
                    li(data-target='#carou1' data-slide-to='0').active
                    //- li(data-target='#carou1' data-slide-to='1')
                  .carousel-inner
                    .carousel-item.active
                      .d-flex.flex-row.justify-content-between.align-items-center.mini-game
                        .d-block.mini-game-thumb(@click="playGame1('30033')")
                          img(:src='getSlotImage("hades_thumb.png")')
                        .d-block.mini-game-thumb(@click="playGame1('30034')")
                          img(:src='getSlotImage("goal_rush_thumb.png")')
                        .d-block.mini-game-thumb(@click="playGame1('30020')")
                          img(:src='getSlotImage("apes_thumb.png")')
                    //- .carousel-item
                    //-   .d-flex.justify-content-between.align-items-center
                    //-     .d-block.mini-game-thumb(@click="playGame1('16900')")
                    //-       img(:src='getSlotImage("16900_L.png")')
                    //-     .d-block.mini-game-thumb(@click="playGame1('861004')")
                    //-       img(:src='getSlotImage("861004_L.png")')
                    //-     .d-block.mini-game-thumb(@click="playGame1('15001')")
                    //-       img(:src='getSlotImage("17300_L.png")')
                  a.carousel-control-prev(href='#carou1' role='button' data-slide='prev')
                    span.carousel-control-prev-icon(aria-hidden='true')
                  a.carousel-control-next(href='#carou1' role='button' data-slide='next')
                    span.carousel-control-next-icon(aria-hidden='true')


    //- Mini Arcade
    //- .card.hb-5(v-if="!whiteLabel && !mmoMode")
    //-   #heading-arcade.card-header
    //-     .tools.d-flex.flex-row
    //-       .icon
    //-         .pointable(data-toggle="collapse", data-target="#collapse-arcade", aria-expanded="false", aria-controls="collapse-arcade")
    //-           i.far.fa-chevron-down
    //-       .text-actions.flex-fill.d-flex.align-items-center
    //-         .text.flex-fill
    //-           span {{ $t('ui.mini_arcade') }}
    //-       .icon(style="border-right: 0;")
    //-         .pointable(v-if="play2", @click="playGame2('0')")
    //-           i.fal.fa-home
    //-       .icon
    //-         .pointable(v-if="play2", @click="closeGame2")
    //-           i.fal.fa-times
    //-   #collapse-arcade.collapse.show(aria-labelledby="heading-arcade")
    //-     .card-body
    //-       #game-frame2.frame-wrapper
    //-         .side-frame.h-100
    //-           iframe(v-if="play2", :src="miniUrl" loading="lazy")
    //-           .productx.mini-game-container.pointable.hover-dark(@click="playGame2('0')")
    //-             img.mini-game-bg(src='/v1/images/minigame_v2.jpg', width="100%", height="100%")

</template>

<script>
import config from "@/config";
import errors from "@/errors";
import xhr from "@/library/_xhr-game.js";
import xhrUtils from "@/library/_xhr-utils.js";
import mixinExt from "@/library/mixinExt.js";
import { EventBus } from "@/library/_event-bus.js";
// import court from "@/components/desktop/court";
import service from "@/library/_xhr-ext";

export default {
  components: {
    court: () => import("@/components/desktop/court"),
    luckypick: () => import("@/components/desktop/luckypick"),
  },
  mixins: [mixinExt],
  data() {
    return {
      countdown: 10,
      mode: 0,
      play1: false,
      play2: false,
      vertical1: false,
      vertical2: false,
      vertical3: false,
      event: {
        action: null,
        radar_id: null,
        channel: null,
        match_id: null,
        sports_type: null,
        market_type: null,
        home_team: null,
        away_team: null,
        working_date: null,
        match_time: null,
        next: null,
        prev: null,
      },
      error: null,
      url: "",
      embedded: false,
      liveTV: {
        schedule: [],
        loading: false,
        count: 0,
        matches: {},
      },
      liveMatch: {
        schedule: [],
        loading: false,
        count: 0,
        matches: {},
      },
      timer: null,
      slotsUrl: "/slots",
      miniUrl: "/minigame",
      win: [],
      intervals: [],
      timeouts: [],
    };
  },
  computed: {
    vg1() {
      return config.vg1;
    },
    newFeatures() {
      return config.newFeatures;
    },
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
    sportsradar() {
      return config.sportsradar;
    },
    whiteLabel() {
      return config.whiteLabel;
    },
    asidebar() {
      return this.$store.getters.minimizer.asidebar;
    },
    radarId() {
      return this.$store.state.layout.radarId;
    },
    channel() {
      return this.$store.state.layout.channel;
    },
    euroPage() {
      return "https://wbeteuro.com?referer=" + this.getDomain(window.location.hostname, true);
    },
    currency_code() {
      return this.$store.getters.currencyCode;
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    EventBus.$off("LIVE_CENTER", this.handleLiveCenter);
    this.intervals.forEach(clearInterval);
    this.intervals = [];
    this.timeouts.forEach(clearTimeout);
    this.timeouts = [];
  },
  mounted() {
    window.addEventListener("resize", this.handleResize);
    this.setTrackedTimeout(() => {
      this.handleResize();
    }, 1000);
    this.closeWidget();
    EventBus.$on("LIVE_CENTER", this.handleLiveCenter);
    const scheduleInterval = setInterval(this.getSchedule, 60000);
    this.intervals.push(scheduleInterval);
  },
  methods: {
    getSlotImage(e) {
      return config.resourceUrl + "/thumbnail/slotgame/" + e;
    },
    popup1(e) {
      var canPlay = false;
      if (this.win.length < 4) {
        canPlay = true;
      } else {
        for (var n = 0; n < this.win.length; n++) {
          if (this.win[n].closed == true || this.win[n].name == e.match_id) {
            this.win.splice(n, 1);
            canPlay = true;
          }
        }
      }

      if (canPlay) {
        var w = 640; //screen.availWidth / 2;
        var h = 360; //screen.availHeight / 2;
        if (e.channel) {
          var url = "/livetv?channel=" + e.channel + "&match=" + e.match_id + "&sports=" + e.sports_type;
          var opts = "height=" + h + ",width=" + w + ",status=no,toolbar=no,menubar=no,location=no,resizable=yes";
          var wx = null;
          if (this.whiteLabel) {
            location.href = url;
          } else {
            wx = window.open(url, e.match_id, opts);
          }
          var canAdd = true;
          for (var n = 0; n < this.win.length; n++) {
            if (this.win[n] == wx) {
              canAdd = false;
              break;
            }
          }
          if (canAdd) {
            this.win.push(w);
            // console.log(this.win);
          }
        }
      } else {
        alert("You have exceeded your video streaming limit. Please close one if you wish to open another new session.");
      }
    },
    findEvent(e) {
      EventBus.$emit("FIND_EVENT", e);
    },
    setTrackedTimeout(fn, delay) {
      const id = setTimeout(fn, delay);
      this.timeouts.push(id);
      return id;
    },
    firstPlayTV(e) {
      this.timer = this.setTrackedTimeout(() => {
        if (this.mode == 0 && this.event.match_id == null) {
          if (this.countdown > 0) {
            this.countdown = this.countdown - 1;
            this.clearWidget("<div>" + this.countdown + "</div>");
            this.firstPlayTV(e);
          } else {
            var keys = Object.keys(this.liveTV.schedule);
            if (keys.length > 0) {
              // if (keys.includes("41")) {
              //   this.chooseMatchAction(this.liveTV.schedule["41"][0], "channel");
              // } else {
              var obj = this.liveTV.schedule[keys[0]];
              var m = obj[0];
              for (var i = 0; i < obj.length; i++) {
                if (parseInt(obj[i].channel) > 1) {
                  m = obj[i];
                  break;
                }
                // }
                this.chooseMatchAction(m, "channel");
              }
            } else {
              this.clearWidget(this.$t("message.match_not_started"));
            }
          }
        }
      }, 1000);
    },
    firstPlayMatch(e) {
      var keys = Object.keys(this.liveMatch.schedule);
      if (keys.length > 0) {
        this.chooseMatchAction(this.liveMatch.schedule[keys[0]][0], "radar");
      }
    },
    setMode(e) {
      if (e != this.mode) {
        var match_id = this.event.match_id;
        this.closeWidget();
        this.mode = e;

        var m = null;

        if (e == 0) {
          m = this.liveTV.matches[match_id];
          if (m) {
            this.chooseMatch(m);
          } else {
            this.firstPlayTV();
          }
        } else {
          m = this.liveMatch.matches[match_id];
          if (m) {
            this.chooseMatch(m);
          } else {
            this.firstPlayMatch();
          }
        }
      }
    },
    getSchedule(callback) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
      };

      if (this.liveTV.loading != true) {
        this.liveTV.loading = true;
        service.getLiveList(config.liveTvListUrl(), json).then(
          (result) => {
            this.liveTV.loading = false;
            if (result) {
              this.feedback.status = result.status;
              if (result.success == true) {
                // var data = result.data;
                var data = [];
                for (var n = 0; n < result.data.length; n++) {
                  // data.push(result.data[n]);
                  if (!this.vg1.includes(result.data[n].sports_type)) {
                    data.push(result.data[n]);
                  }
                }

                this.liveTV.count = data.length;
                var group = data.reduce((r, a) => {
                  r[a.sports_type] = [...(r[a.sports_type] || []), a];
                  return r;
                }, {});

                this.liveTV.schedule = group;
                this.liveTV.matches = {};
                for (var i = 0; i < data.length; i++) {
                  this.liveTV.matches[data[i].match_id] = data[i];
                }
                if (callback) {
                  callback(data);
                }
              } else {
                this.$helpers.handleFeedback(this.feedback.status);
              }
            }
          },
          (err) => {
            this.liveTV.loading = false;
            this.feedback.success = false;
            this.feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);
          }
        );
      }
    },
    chooseMatch(e) {
      clearTimeout(this.timer);
      var action = "channel";
      if (this.mode == 1) {
        action = "radar";
      }
      EventBus.$emit("LIVE_CENTER", {
        action: action,
        radar_id: e.radar_id,
        channel: e.channel,
        match_id: e.match_id,
        league_id: e.league_id,
        sports_type: e.sports_type,
        market_type: e.market_type,
        home_team: e.home_name_en,
        away_team: e.away_name_en,
        working_date: e.working_date,
        match_time: e.match_time,
      });
    },
    chooseMatchAction(e, action) {
      clearTimeout(this.timer);
      EventBus.$emit("LIVE_CENTER", {
        action: action,
        radar_id: e.radar_id,
        channel: e.channel,
        match_id: e.match_id,
        league_id: e.league_id,
        sports_type: e.sports_type,
        market_type: e.market_type,
        home_team: e.home_name_en,
        away_team: e.away_name_en,
        working_date: e.working_date,
        match_time: e.match_time,
      });
    },
    getSportsImage(e) {
      return config.getSportsImage(e);
    },
    handleExpanded() {
      $("#collapse-live").removeClass("show");
      $("#collapse-parlay").removeClass("show");
    },
    handleToggle() {
      if ($(".content .right").hasClass("expanded")) {
        $(".content .left").removeClass("active");
        $(".content .right").removeClass("expanded");
        $("#icon-expand").removeClass("d-none");
        $("#icon-compress").removeClass("d-block");
        $("#icon-compress").addClass("d-none");
        this.handleResize();
        $("body").off("mouseup", this.handleExpanded);
      } else {
        $("body").on("mouseup", this.handleExpanded);
        $(".content .left").addClass("active");
        $(".content .right").addClass("expanded");
        $("#icon-expand").addClass("d-none");
        $("#icon-compress").removeClass("d-none");
        $("#icon-compress").addClass("d-block");
        this.handleResize();
      }
    },
    clearWidget(e) {
      $(".side-frame").removeClass("no-bg");
      $("#widget").html('<div id="tv-widget"><div class="wrapper-frame">' + e + "</div></div>");
    },
    setWidget(e) {
      $("#widget").html('<div id="tv-widget"><div class="wrapper-frame">' + e + "</div></div>");
    },
    setOverlay(e, t) {
      $("#overlay-title").html(t);
      $("#overlay-widget").html('<div id="tv-widget"><div class="wrapper-frame">' + e + "</div></div>");
      $("#overlay").show();
    },
    closeWidget(isLoading) {
      clearTimeout(this.timer);
      if (isLoading) {
        this.clearWidget('<div class="fa fa-spinner fa-spin"></div>');
      } else {
        this.clearWidget(this.$t("message.match_not_started"));
      }
      this.vertical3 = false;
      this.handleResize();
      this.event = {
        action: null,
        radar_id: null,
        channel: null,
        match_id: null,
        sports_type: null,
        market_type: null,
        home_team: null,
        away_team: null,
        working_date: null,
        match_time: null,
        next: null,
        prev: null,
      };
      this.error = null;
      this.url = "";
      this.embedded = false;
    },
    handleLiveCenter(e) {
      this.event = e;
      this.error = null;
      this.embedded = false;
      this.clearWidget('<div class="fa fa-spinner fa-spin"></div>');
      clearTimeout(this.timer);
      switch (e.action) {
      case "radar":
        this.mode = 1;
        this.setRadar();
        break;
      case "channel":
        this.mode = 0;
        this.setChannel();
        break;
      }
      if (this.error) {
        this.clearWidget(this.error);
      }
    },
    getDomain(url, subdomain) {
      subdomain = subdomain || false;

      url = url.replace(/(https?:\/\/)?(www.)?/i, "");

      if (!subdomain) {
        url = url.split(".");

        url = url.slice(url.length - 2).join(".");
      }

      if (url.indexOf("/") !== -1) {
        return url.split("/")[0];
      }

      return url;
    },
    tvLink(e) {
      if (config.vg1.includes(e)) {
        return config.player10Url();
      } else {
        return config.bintuUrl();
      }
    },
    launchLiveTV(ch, mc, sp, ip) {
      var args = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        channel: ch,
        match: mc,
        sports: sp,
        ip: ip,
        brand: config.brand,
      };
      xhrUtils.launchLiveTV(args).then(
        (res) => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              this.embedded = this.url.includes("<iframe");
              if (!this.embedded) {
                this.embedded = this.url.includes("<script");
              }

              if (!this.embedded) {
                // not a iframe link, can be glive or self-hosted live streaming
                //
                if (ch != 1) {
                  var link = this.url + "&compact=1";
                  var html = '<iframe src="' + link + '" frameborder="0" allowfullscreen="true" scrolling="no" loading="lazy">';
                  this.setWidget(html);
                } else {
                  var link = this.tvLink(sp) + this.url + "&u=" + args.account_id + "&t=" + args.session_token + "&product=" + sp;
                  var html = '<iframe src="' + link + '" frameborder="0" allowfullscreen="true" scrolling="no" loading="lazy">';
                  this.setWidget(html);
                }
              } else {
                // is a iframe link, can be twitch, youtube or any embeded video
                //
                // var domain = this.getDomain(window.location.hostname, false);
                var kns = this.url.replace("www.example.com", window.location.hostname);
                this.setWidget(kns);
              }
            } else {
              this.error = this.$t("error." + res.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.error = this.$t("error." + err.status);
        }
      );
    },
    getVersion(q) {
      const feedback = {
        success: false,
        status: errors.login.failed,
      };
      return new Promise((resolve, reject) => {
        this.$http.get(config.ipify1).then(
          (res) => {
            resolve(res.bodyText);
          },
          (err) => {
            reject(err);
          }
        );
      });
    },
    setChannel() {
      this.vertical3 = false;
      this.handleResize();
      var q = this.event;
      if (q.channel == null) {
        this.error = "Please select a channel to watch...";
      } else if (q.channel == "1") {
        this.expanded = true;
        // console.log(q);
        this.launchLiveTV(q.channel, q.match_id, q.sports_type, "*******");
      } else {
        this.expanded = true;
        this.getVersion(q).then(
          (res) => {
            var ip = res;
            // console.log(q);
            this.launchLiveTV(q.channel, q.match_id, q.sports_type, ip);
          },
          (err) => {
            this.error = err;
          }
        );
      }
    },
    setRadar() {
      this.vertical3 = true;
      this.handleResize();
      if (this.event.radar_id) {
        $(".side-frame").addClass("no-bg");
        var url = config.radarUrl() + this.event.radar_id + "&u=" + this.$store.getters.accountId + "&t=" + this.$store.getters.sessionToken;
        var html = '<iframe src="' + url + '" frameborder="0" allowfullscreen="true" scrolling="no" loading="lazy">';
        $("#widget").html(html);
      }
    },
    logout() {
      this.$helpers.logout();
    },
    toggleASidebar() {
      this.$store.dispatch("layout/setMinimizer", {
        property: "asidebar",
        value: !this.asidebar,
      });
    },
    handleResize() {
      var gw1 = $(".z-side").width();
      // var gw2 = $(".z-side").width();
      var gw3 = $(".z-side").width();
      var gh1 = gw1 * (254 / 436);
      if (this.vertical1) {
        gh1 = gw1 * (436 / 254);
      }
      // var gh2 = gw2 * (156 / 436);
      // if (this.vertical2) {
      //   gh2 = gw2 * (398 / 436);
      // }
      var gh3 = gw3 * (244 / 436);
      if (this.vertical3) {
        gh3 = gw3 * (270 / 436);
        gh3 += 65;
      }
      $("#game-frame1").height(gh1);
      // $("#game-frame2").height(gh2);
      $("#tracker-frame").height(gh3);
    },
    playGame1(e) {
      if (e) {
        this.slotsUrl = "/slots?game=" + e;
      } else {
        this.slotsUrl = "/slots";
      }
      this.play1 = false;
      this.$nextTick(() => {
        this.play1 = true;
      });
    },
    closeGame1() {
      this.play1 = false;
    },
    reloadGame1() {
      this.play1 = false;

      this.$nextTick(() => {
        this.play1 = true;
      });
    },
    // playGame2(e) {
    //   this.play2 = false;
    //   this.$nextTick(() => {
    //     this.play2 = true;
    //     this.vertical2 = true;
    //     this.handleResize();
    //   });
    // },
    // closeGame2() {
    //   this.play2 = false;
    //   this.vertical2 = false;
    //   this.handleResize();
    // },
    // toggleScreen2() {
    //   this.vertical2 = !this.vertical2;
    //   if (this.vertical2) {
    //     $("#collapse-live-center").collapse("hide");
    //     $("#collapse-games").collapse("hide");
    //     $("#collapse-casino").collapse("show");
    //   } else {
    //     $("#collapse-live-center").collapse("show");
    //     $("#collapse-games").collapse("show");
    //     $("#collapse-casino").collapse("show");
    //   }
    //   this.handleResize();
    // }
  },
};
</script>
