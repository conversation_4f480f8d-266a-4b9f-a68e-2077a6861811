import calc from "@/library/_calculation";
import store from "@/store";

export default {
  data: {},
  decode(r, t, c, ot, parlay) {
    const oddsIndex = 0;
    const mmoIndex = 1;
    var m = {};
    m["odds"] = r[oddsIndex];
    m["mmo"] = r[mmoIndex];
    calc.collate(c, ot, m["odds"], parlay);
    if (store.getters.mmoMode) {
      if (m["mmo"] != null && Object.keys(m["mmo"]).length > 0) {
        calc.collate_mmo(c, ot, m["mmo"], parlay);
      }
    }

    return m;
  },
  decode2(r, t, c, ot, parlay) {
    const oddsIndex = 0;
    var m = {};
    m["odds"] = r[oddsIndex];
    calc.collate(c, ot, m["odds"], parlay);
    return m;
  }
};
