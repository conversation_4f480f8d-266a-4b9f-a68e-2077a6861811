export const GAME_PROVIDERS_CONFIG = {
  // Player ID/Password Pattern - uses res.data.status check
  playerIdPassword: {
    gs: {
      // GamingSoft
      urlMethod: "launchGSUrl",
      loadingKey: "gamingsoft",
      responseType: "status",
      responseDataPath: "url",
      requiredArgs: ["player_id", "password"],
    },
    l22: {
      // Live22
      urlMethod: "launchL22Url",
      loadingKey: "live22",
      responseType: "status",
      responseDataPath: "url",
      requiredArgs: ["player_id", "password"],
    },
    gp: {
      // Gameplay
      urlMethod: "launchGPUrl",
      loadingKey: "gameplay",
      responseType: "status",
      responseDataPath: "url",
      requiredArgs: ["player_id", "password"],
    },
  },

  // Username/Session ID Pattern - uses res.data.errorId check
  usernameSessionId: {
    sa: {
      // SA Gaming
      urlMethod: "launchSAUrl",
      loadingKey: "sagaming",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    ct: {
      // CT Gaming
      urlMethod: "launchCTUrl",
      loadingKey: "ctgaming",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    pg: {
      // Pretty Gaming
      urlMethod: "launchPGUrl",
      loadingKey: "pretty",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    evo: {
      // Evolution
      urlMethod: "launchEvoUrl",
      loadingKey: "evolution",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    yb: {
      // Yee Bet
      urlMethod: "launchYbUrl",
      loadingKey: "yeebet",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    pgs: {
      // PG Soft
      urlMethod: "launchPgsUrl",
      loadingKey: "pgsoft",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    e2: {
      // Esports2
      urlMethod: "launchEsports2Url",
      loadingKey: "e2",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    pragmatic: {
      // Pragmatic Play
      urlMethod: "launchPragmaticUrl",
      loadingKey: "pragmatic",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
      specialCase: "conditionalUrl", // Has conditional URL logic
    },
    minigame: {
      // Minigame
      urlMethod: "launchMagUrl",
      loadingKey: "minigame",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    w4d: {
      // W4D
      urlMethod: "launchW4DUrl",
      loadingKey: "w4d",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    joker: {
      // Joker Gaming
      urlMethod: "launchJokerUrl",
      loadingKey: "joker",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    spp: {
      // Simple Play
      urlMethod: "launchSimplePlayUrl",
      loadingKey: "spp",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    jili: {
      // JILI
      urlMethod: "launchJiliUrl",
      loadingKey: "jili",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    uusl: {
      // UUSL
      urlMethod: "launchUuslUrl",
      loadingKey: "uusl",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    next: {
      // NEXTSPIN
      urlMethod: "launchNextSpinUrl",
      loadingKey: "next",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    dbhash: {
      // DBHASH
      urlMethod: "launchDbHashUrl",
      loadingKey: "next",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    dbpoker: {
      // DBPOKER
      urlMethod: "launchDbPokerUrl",
      loadingKey: "next",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    wow: {
      // WOW Gaming
      urlMethod: "launchWowGamingUrl",
      loadingKey: "next",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    ai: {
      // AI Gaming
      urlMethod: "launchAICasinoUrl",
      loadingKey: "yeebet",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    live22: {
      // Live22
      urlMethod: "launchLive22Url",
      loadingKey: "live22",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    wf: {
      // WF Gaming
      urlMethod: "launchWfUrl",
      loadingKey: "wf",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    epw: {
      // EpicWin
      urlMethod: "launchEpwUrl",
      loadingKey: "epw",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    awc: {
      // Sexy Bacarrat
      urlMethod: "launchAwcUrl",
      loadingKey: "awc",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
    aio: {
      // AIO (Binary)
      urlMethod: "launchAioUrl",
      loadingKey: "aio",
      responseType: "errorId",
      responseDataPath: "client_url",
      requiredArgs: ["username", "sessionid"],
    },
  },

  // Game List Pattern - uses res.body check
  gameList: {
    pragmatic: {
      // Pragmatic Play
      urlMethod: "getPragmaticGameListUrl",
      loadingKey: "pragmaticGameList",
      responseType: "body",
      requiredArgs: ["username", "sessionid"],
    },
    joker: {
      // Joker Gaming
      urlMethod: "getJokerGameListUrl",
      loadingKey: "jokerGameList",
      responseType: "body",
      requiredArgs: ["username", "sessionid"],
    },
    jili: {
      // JILI
      urlMethod: "getJiliGameListUrl",
      loadingKey: "jiliGameList",
      responseType: "body",
      requiredArgs: ["username", "sessionid"],
    },
    uusl: {
      // UUSL
      urlMethod: "getUuslGameListUrl",
      loadingKey: "uuslGameList",
      responseType: "body",
      requiredArgs: ["username", "sessionid"],
    },
    live22: {
      // Live22
      urlMethod: "getLive22GameListUrl",
      loadingKey: "live22GameList",
      responseType: "body",
      requiredArgs: ["username", "sessionid"],
    },
    wf: {
      // WF Gaming
      urlMethod: "getWfGameListUrl",
      loadingKey: "wfGameList",
      responseType: "body",
      requiredArgs: ["username", "sessionid"],
    },
    epw: {
      // EpicWin
      urlMethod: "getEpwGameListUrl",
      loadingKey: "epwGameList",
      responseType: "body",
      requiredArgs: ["username", "sessionid"],
    },
    awc: {
      // Sexy Bacarrat
      urlMethod: "getAwcGameListUrl",
      loadingKey: "awcGameList",
      responseType: "body",
      requiredArgs: [], // Special case: no validation
    },
  },
};
