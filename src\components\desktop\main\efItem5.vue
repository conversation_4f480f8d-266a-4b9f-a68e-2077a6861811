<template lang="pug">
.esports-gamelist(v-if="source.items != null && source.items[0] != null && source.items.length == 16")
  //- small {{ source.items[6] }}
  .esports-row.game-bottom-line(:class="source.items[0].marketId == 3 ? 'game-top-line' : ''")
    .esports-cell.esports-cell-sm.flex-grow-1.justify-content-start.w-100(v-if="source.items[0].marketId == 3")
      .live-now(style="margin-left: 16px;")
        img(src="/v1/images/esports/live-icon.svg")
        span.text-uppercase live
        i.fas.fa-circle
      .d-flex.flex-fill.justify-content-end.align-items-center(v-if="racingList.includes(source.items[0].sportsId) && source.items[0].matchTime")
        span(style="color: #fff; margin-right: 16px;") No. {{ $dayjs(this.source.items[0].matchTime).format("MMDDhhmm") }}
    .esports-cell.esports-cell-sm(v-else)
      efTime(:source="source.items[0]" :horiz="true")
  .game-group
    .game-table.pb-0
      .game-table-top
        .game-row
          .game-cell.w-40
          .game-cell.flex-fill
          .game-cell.game-state.w-152 {{ $t("m.GX_OVER") }}
          .game-cell.game-state.w-152 {{ $t("m.GX_UNDER") }}
          .game-cell.game-state.w-152 {{ $t("m.GX_ODD") }}
          .game-cell.game-state.w-152 {{ $t("m.GX_EVEN") }}
        efItemChild3a(:source="source.items[15]" :player="16")
        .game-line
    .game-table
      .game-table-column
        efItemChild5(:source="source.items[12]" :player="13" :isTeam="true")
        .game-line
        efItemChild5(:source="source.items[13]" :player="14" :isTeam="true")
        .game-line
        efItemChild5(:source="source.items[14]" :player="15" :isTeam="true")
      .game-table-column
        efItemChild5(:source="source.items[0]" :player="1")
        .game-line
        efItemChild5(:source="source.items[1]" :player="2")
        .game-line
        efItemChild5(:source="source.items[2]" :player="3")
      .game-table-column
        efItemChild5(:source="source.items[3]" :player="4")
        .game-line
        efItemChild5(:source="source.items[4]" :player="5")
        .game-line
        efItemChild5(:source="source.items[5]" :player="6")
      .game-table-column
        efItemChild5(:source="source.items[6]" :player="7")
        .game-line
        efItemChild5(:source="source.items[7]" :player="8")
        .game-line
        efItemChild5(:source="source.items[8]" :player="9")
      .game-table-column
        efItemChild5(:source="source.items[9]" :player="10")
        .game-line
        efItemChild5(:source="source.items[10]" :player="11")
        .game-line
        efItemChild5(:source="source.items[11]" :player="12")


</template>

<script>
import config from "@/config";
export default {
  components: {
    efTime: () => import("@/components/desktop/main/efTime"),
    efItemChild5: () => import("@/components/desktop/main/efItemChild5"),
    efItemChild3a: () => import("@/components/desktop/main/efItemChild3a"),
  },
  props: {
    source: {
      type: Object,
    },
    index: {
      type: Number,
    },
  },
  computed: {
    racingList() {
      return config.racingList;
    },
  },
  methods: {},
};
</script>
