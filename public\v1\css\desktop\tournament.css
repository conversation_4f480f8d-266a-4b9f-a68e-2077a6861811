@import url("https://fonts.googleapis.com/css?family=Lato:300,400,700,900|Oswald:300,400,500,600,700|Roboto+Condensed:400,700|Roboto:300,400,500,700,900|Open+Sans:300,400,500,600,700&display=swap");

.tournament-main {
  font-family: "Open Sans", sans-serif;
  background: #C0CED9 url('../../img/tn/bg.jpg') top center no-repeat;
  color: #fff;
  font-size: 16px;
  font-weight: 400;
  overflow: hidden;
}

.tournament-main a,
.tournament-main a:hover {
  text-decoration: none;
}

/* .tournament-header .back-link {
  color: #ffd452cc;
  font-weight: 600;
  text-transform: uppercase;
  font-family: "Lato";
  padding-right: 20px;
  padding-left: 4px;
  margin-right: 20px;
  border-right: 1px solid #ffffff44;
}

.tournament-header .back-link:hover {
  color: #ffd452;
}

.tournament-header .back-link i {
  margin-right: 4px;
}

.tournament-header .back-link a {
  color: #ffd452cc;
  font-weight: 600;
  text-transform: uppercase;
}

.tournament-header .back-link a:hover {
  color: #ffd452;
} */

.tournament-color-green {
  color: #08ff00;
}

.tournament-color-white {
  color: #fff;
}

.tournament-color-red {
  color: #DC5862;
}

/* blinking animation */
@keyframes blink {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.tournament-blink {
  animation: blink 1s linear infinite;
}

.tournament-fw-bold {
  font-weight: 700;
  color: #fff;
}

.tournament-fc-light {
  color: #ffd452;
}

.tournament-wrapper {}

.tournament-container {
  width: 1366px;
  min-width: 1366px;
  max-width: 1366px;
  margin: 0 auto;
  padding: 0 8px;
}

.tournament-top {
  background-color: #0f4f8c;
  background: url(/images/top-bg.png) no-repeat;
  background-size: cover;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 99;
}

.tournament-top-plus {
  background-color: #0f798c;
}

.tournament-header {
  display: flex;
  align-items: center;
  height: 45px;
}

.tournament-header-left {
  margin-right: 0;
  min-width: 120px;
  width: 120px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.tournament-header-left img {
  width: auto;
  height: 36px;
}

.tournament-header-right {
  margin: 0 1px;
  margin-top: 0;
  font-family: "Lato";
  color: #fff;
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 600;
}

.tournament-wrapper {
  display: flex;
  justify-content: space-between;
}

.tournament-content {
  padding-top: 58px;
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
  height: calc(100vh - 0);
  overflow: hidden;
}

.tournament-content-left {
  width: 1000px;
  min-width: 1000px;
  max-width: 1000px;
  margin-right: 0;
  height: calc(100vh - 58px);
  overflow: hidden scroll;
  padding-right: 20px;
}

.tournament-content-right {
  width: calc(100% - 1000px);
  height: calc(100vh - 58px);
  overflow: hidden scroll;
  padding: 0 16px;
}

.tournament-back {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 16px;
  cursor: pointer;
  color: #ffffffcc;
}

.tournament-back i {
  margin-right: 8px;
}

.tournament-back span {}

.tournament-back:hover {
  color: #ffd452;
}

.tournament-room-list,
.tournament-room-join,
.tournament-room-enter {
  width: 100%;
  height: auto;
  margin-bottom: 48px;
}

.tournament-room-join iframe {
  overflow-x: hidden !important;
  overflow-y: overlay !important;
  border: 1px solid #ffffff88;
  padding: 4px;
  margin: 0;
  width: 100%;
  height: 400px;
  border-radius: 5px;
  box-shadow: 0 0 5px #000000, inset 0 0 3px #ffffff44;
}

.tournament-room {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;
}

.tournament-search {}

.tournament-search .form-control {
  height: 35px;
  font-size: 14px;
  background-color: #fff;
  -webkit-border-radius: 3px 0 0 3px;
  border-radius: 3px 0 0 3px;
}

.tournament-search input::placeholder {
  color: #9b9b9b;
}

.tournament-btn-search {
  height: 35px;
  background-color: #1869b7;
  -webkit-border-radius: 0 5px 5px 0;
  border-radius: 0 5px 5px 0;
  color: #fff;
  border: 0;
  box-shadow: none;
}

.tournament-btn-search:hover,
.tournament-btn-search:focus {
  box-shadow: none;
  background-color: #1869b7;
  color: #fff;
}

.tournament-create-room {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background-color: #409120;
  height: 40px;
  line-height: 1;
  padding: 0 12px;
  min-width: 165px;
  color: #fff;
}

.room-bottom .tournament-create-room {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  border: 1px solid #00000000;
  border-radius: 5px;
  height: 35px;
}

.tournament-text {
  font-weight: 600;
}

.tournament-icon {
  margin-left: 6px;
}

.tournament-pool-single {
  margin-bottom: 16px;
  position: relative;
  max-width: 976px;
}

.tournament-free-room-shadow {
  filter: drop-shadow(1px 3px 6px rgba(0, 0, 0, .5));
  padding: 8px;
  position: absolute;
  top: -20px;
  left: 0;
  z-index: 1;
  line-height: 24px;
}

.tournament-free-room {
  width: 120px;
  height: 24px;
  line-height: 24px;
  background: linear-gradient(to right, #f5240dcc 0%, #b80c0c 100%);
  clip-path: polygon(5% 0%, 100% 0%, 95% 100%, 0% 100%);
}

.tournament-free-text {
  font-size: 12px;
  font-weight: 800;
  text-transform: uppercase;
  font-style: italic;
  text-align: center;
}

.tournament-countdown {
  font-size: 11px;
  font-weight: 600;
  background: #ffffff22;
  color: #ffffffcc;
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 2px 16px;
  border-top-left-radius: 5px;
}

.tournament-exclusive {
  font-size: 11px;
  font-weight: 600;
  background: -moz-linear-gradient(left, rgba(32, 124, 160, 1) 0%, rgba(83, 180, 193, 1) 62%, rgba(88, 185, 196, 0.7) 68%, rgba(115, 214, 214, 0) 100%);
  background: -webkit-linear-gradient(left, rgba(32, 124, 160, 1) 0%, rgba(83, 180, 193, 1) 62%, rgba(88, 185, 196, 0.7) 68%, rgba(115, 214, 214, 0) 100%);
  background: linear-gradient(to right, rgba(32, 124, 160, 1) 0%, rgba(83, 180, 193, 1) 62%, rgba(88, 185, 196, 0.7) 68%, rgba(115, 214, 214, 0) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#207ca0', endColorstr='#0073d6d6', GradientType=1);
  position: absolute;
  left: 0;
  bottom: 0;
  padding: 2px 16px;
  border-bottom-left-radius: 5px;
  color: #ffffff;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  text-shadow: 1px 1px 5px #00000088;
}

@keyframes blinker {
  50% {
    opacity: 0;
  }
}

.tournament-pool-single:last-child {
  margin-bottom: 0;
}

.tournament-pool {
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  /* border: 1px solid #3d5164; */
  overflow: hidden;
  box-shadow: 0 3px 6px rgba(0, 0, 0, .15);
}

.tournament-pool-body {
  display: block;
  position: relative;
  padding: 0;
  border: 0;
  margin: 0;
  background-color: #fff;
}

.tournament-pool-body .row {
  margin: 0;
}

.tournament-pool-body-wrapper {
  display: block;
  position: relative;
  padding: 14px;
}

.tournament-pool-body-wrapper .text-center span {
  color: #014273;
}

.tournament-pool-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  background: url('../../img/tn/bg-pool.png') top center no-repeat;
  padding: 14px 0;
  height: 88px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, .15);
}

.tournament-pool-amount {
  padding: 0 16px;
  color: #ffffffaa;
  z-index: 1;
}

.tournament-pool-amount .tournament-color-green {
  font-size: 20px;
  font-weight: 700;
  text-shadow: 0 0 5px black;
}

.tournament-pool-amount .tournament-color-white {
  font-size: 16px;
  font-weight: 700;
  text-shadow: 0 0 5px black;
}

.tournament-pool-details {
  display: flex;
  align-items: center;
  z-index: 1;
}

.tournament-pool-type {
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  min-width: 80px;
  height: 30px;
  font-size: 14px;
  font-weight: 400;
  padding: 0 8px;
}

.tournament-pool-type.private {
  background-color: #ae1b1b;
}

.tournament-pool-type.public {
  background-color: #409120;
}

.tournament-pool-type.exclusive {
  background-color: #ffd452;
  color: #000;
}

.tournament-pool-number {
  margin-top: 6px;
}

.tournament-pool-amount .tournament-text,
.tournament-pool-amount .tournament-pool-number {
  margin-top: 0;
  line-height: 1.1;
}


.tournament-pool-status {
  -webkit-border-radius: 30px 30px 30px 30px;
  border-radius: 30px 30px 30px 30px;
  font-size: 14px;
  font-weight: 600;
  min-width: 100px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  text-transform: capitalize;
  font-family: "Open Sans", sans-serif;
  color: #fff;
  overflow: hidden;
  border: 0;
  padding: 0 8px;
}

.tournament-pool-status.active {
  background-color: #50ae1b;
}

.tournament-pool-status.running {
  background-color: #db8b00;
}

.tournament-pool-status.ended {
  background-color: #2c547e;
}

.tournament-pool-result {
  font-size: 14px;
  width: auto;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  background-color: #0f4f8c;
  margin: 0 16px 0 8px;
  font-size: 16px;
  overflow: hidden;
  border: 1px solid #ffffff00;
  padding: 0 4px;
  cursor: pointer;
  min-width: 32px;
}

.tournament-pool-result.show-rank {
  background-color: #db8b00;
}

.tournament-pool-result i {
  font-size: 16px;
  width: auto;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tournament-pool-result span {
  height: 30px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tournament-body-inner {
  display: flex;
}

.tournament-league {}

.tournament-background {
  width: 100%;
  height: 100%;
  z-index: 0;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
}

.tournament-background img {
  width: 100%;
}

.tournament-pool-fee {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  /* border: 1px solid #1C62A0; */
  border: 1px solid #ffffff99;
  padding: 8px 16px;
  width: 400px;
  height: 60px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  /* background: rgba(9, 45, 80, .8); */
  background: #000000bf;
}

.tournament-pool-fee a {
  color: #ffffffcc;
}

.tournament-pool-people {
  font-size: 14px;
  font-weight: 600;
  color: #92d2ff;
  text-align: center;
}

.tournament-pool-entry {
  color: #ffffffcc;
  font-weight: 600;
}

.tournament-league-details {
  padding: 8px 16px;
  min-width: 200px;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.tournament-league-name {
  font-size: 14px;
  font-weight: 600;
}

.tournament-league-date {
  font-size: 14px;
  color: #92d2ff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 6px;
  margin-bottom: 12px;
}

.tournament-league-team {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
}

.tournament-league-team:last-child {
  margin-bottom: 0;
}

.tournament-league-team-logo {
  margin-right: 16px;
}

.tournament-league-team-name {
  color: #000;
}

.tournament-home {
  color: #912817;
}

.tournament-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('../../img/tn/bg-match.png') top center no-repeat;
  height: 27px;
  cursor: pointer;
}

.tournament-btn .tournament-text {
  font-size: 14px;
  color: #fff;
  font-weight: 400;
}

.tournament-arrow .fa-chevron-up {
  -webkit-transition: 0.3s -webkit-transform ease-in-out;
  transition: 0.3s -webkit-transform ease-in-out;
  -o-transition: 0.3s transform ease-in-out;
  transition: 0.3s transform ease-in-out;
  transition: 0.3s transform ease-in-out, 0.3s -webkit-transform ease-in-out;
  font-size: 13px;
}

.tournament-btn.collapsed .fa-chevron-up,
.tournament-league-list.collapsed .fa-chevron-up {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.tournament-collapse {}

.tournament-collapse .collapse {
  background-color: #fff;
  -webkit-border-radius: 0 0 5px 5px;
  border-radius: 0 0 5px 5px;
}

.tournament-league-single {
  margin-bottom: 1px;
}

.tournament-league-single:last-child {
  margin-bottom: 0;
}

.tournament-league-list {
  border-top: 0;
  cursor: pointer;
}

.tournament-league-header {
  background-color: #DCE6EF;
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 35px;
}

.tournament-league-club,
.tournament-league-team-logo {
  width: 24px;
  height: 24px;
  overflow: hidden;
  margin-right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tournament-league-club img,
.tournament-league-team-logo img {
  width: 20px;
  max-height: 20px;
}

.tournament-league-type {
  color: #000;
}

.tournament-arrow {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.5);
}

.tournament-league-match {
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 35px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  color: #000;
}

.tournament-league-match:last-child {
  border-bottom: 0;
}

.tournament-match-date {
  font-size: 14px;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  padding-right: 16px;
}

.tournament-match-teams {
  font-size: 14px;
  padding-left: 16px;
  font-weight: 500;
}

.tournament-winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #0f4f8c;
  border-radius: 5px;
  height: 35px;
  padding: 4px 16px;
  font-weight: 600;
  margin-bottom: 4px;
  box-shadow: 0 0 5px #00000088;
}

.tournament-winner i {
  margin-right: 4px;
}

.tournament-winner span {}

.tournament-winner-name {
  margin: 0 12px;
  padding: 0 12px;
  border-left: 1px solid #ffffff22;
  border-right: 1px solid #ffffff22;
  color: #70a7db;
}

.tournament-winner-point span {}

.tournament-winner-point small {
  color: #ffffff66;
}

.tournament-winner.winner-1 i,
.tournament-winner.winner-1 span {
  color: #faca0c;
}

.tournament-winner.winner-2 i,
.tournament-winner.winner-2 span {
  color: #c9c9c9;
}

.tournament-winner.winner-3 i,
.tournament-winner.winner-3 span {
  color: #f18556;
}

.tournament-user {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: transparent linear-gradient(90deg, #0F4F8C 0%, #1869B7 100%) 0% 0% no-repeat padding-box;
  -webkit-border-radius: 10px 10px 10px 10px;
  border-radius: 10px 10px 10px 10px;
  height: 50px;
  padding: 0 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.tournament-username {
  margin-right: 16px;
  border-right: 1px solid rgba(255, 255, 255, 0.15);
}

.tournament-user-balance {}

.tournament-betslip-wrapper {
  margin-bottom: 24px;
}

.tournament-betslip-wrapper ul {
  margin: 0 16px;
}

.tournament-betslip-wrapper ul li.nav-item {
  cursor: pointer;
  width: 50%;
}

.tournament-betslip-wrapper ul li.nav-item .nav-link {
  text-transform: capitalize;
  background-color: #0d4275;
  height: 37px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tournament-betslip-wrapper ul li:first-child.nav-item .nav-link {
  -webkit-border-radius: 10px 0 0 0;
  border-radius: 10px 0 0 0;
}

.tournament-betslip-wrapper ul li:last-child.nav-item .nav-link {
  -webkit-border-radius: 0 10px 10px 0;
  border-radius: 0 10px 0 0;
}

.tournament-betslip-wrapper ul li.nav-item .nav-link.active {
  font-weight: 600;
  background-color: #409120;
}

.tournament-tab-content {
  background-color: #0f4f8c;
  -webkit-border-radius: 10px 10px 10px 10px;
  border-radius: 10px 10px 10px 10px;
  padding: 6px 0;
  background: #409120;
}

.tournament-betslip {
  background: #0f4f8c;
}

.tournament-betslip-title {
  font-size: 16px;
  font-weight: 700;
  padding: 5px 12px 10px 12px;
}

.tournament-betslip-room {
  font-size: 14px;
  font-weight: 600;
  padding: 10px 12px;
  background: #0d4275;
}

.tournament-betslip-single {}

.tournament-betslip-header {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
}

.tournament-betslip-body {
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tournament-betslip-text {
  font-size: 12px;
  font-weight: 600;
  padding: 0 12px;
}

.tournament-betslip-header-title {
  font-size: 11px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  color: #ffffffcc;
  background: transparent linear-gradient(90deg, #1869b7 0%, #1a94ff 100%) 0% 0% no-repeat padding-box;
  padding: 2px 12px;
  text-transform: uppercase;
}

.tournament-close {
  margin-right: 4px;
}

.tournament-close a {
  width: 23px;
  height: 23px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #1869b7;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
}

.tournament-betslip-matches {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tournament-betslip-matches .tn-team {
  font-weight: 400;
  font-size: 11px;
  color: #ffffffcc;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 318px;
  padding: 0 4px;
}

.tournament-betslip-matches .tn-vs {
  font-weight: 400;
  font-size: 10px;
  color: #ffffff66;
  padding: 0 4px;
  line-height: 1;
}

.tournament-betslip-matches .tn-red {
  color: #dc5862cc;
}

.tournament-betslip-league {
  font-weight: 400;
  font-size: 12px;
  color: #ffffffcc;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 318px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px 4px 4px;
  color: #92d2ff;
}

.tournament-betslip-league span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 318px;
}

.tournament-vs {
  color: #dc5862;
  margin: 0 6px;
}

.tournament-odds {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0d4275;
  height: 30px;
  font-size: 12px;
  font-weight: 600;
  margin: 0 20px;
  padding: 0 8px;
  position: relative;
}

.tournament-odds::before {
  background: url('../../img/tn/slip.png') top center no-repeat;
  width: 12px;
  height: 30px;
  content: "";
  position: absolute;
  left: -8px;
  top: 0;
}

.tournament-odds::after {
  background: url('../../img/tn/slip.png') top center no-repeat;
  width: 12px;
  height: 30px;
  content: "";
  position: absolute;
  right: -8px;
  top: 0;
  -moz-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.tournament-odds2 {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0d4275;
  height: 24px;
  font-size: 12px;
  font-weight: 600;
  margin: 0 20px;
  padding: 0 8px;
  position: relative;
}

.tournament-odds2::before {
  background: url('../../img/tn/slip.png') top center no-repeat;
  width: 12px;
  height: 24px;
  content: "";
  position: absolute;
  left: -8px;
  top: 0;
}

.tournament-odds2::after {
  background: url('../../img/tn/slip.png') top center no-repeat;
  width: 12px;
  height: 24px;
  content: "";
  position: absolute;
  right: -8px;
  top: 0;
  -moz-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}


.tournament-betslip-footer {
  padding: 12px;
}

.tournament-betslip-single.tournament-betslip-highlight {
  animation: blinker 1s linear infinite;
}

.tournament-better-odds {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.tournament-checkbox input {
  width: 12px;
  height: 12px;
  -webkit-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
  margin-right: 6px;
}

.tournament-checkbox-text {
  font-size: 12px;
}

.tournament-stake-field {
  margin-bottom: 8px;
}

.tournament-stake-field .input-group-text {
  background: #0d4275;
  color: #fff;
  -webkit-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
  border: 0;
  height: 46px;
  font-weight: 700;
}

.tournament-stake-field .form-control {
  height: 46px;
  font-weight: 700;
  color: #000000;
  text-align: right;
  font-size: 18px;
  font-weight: 700;
}

.tournament-stake-field .currency-code {
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tournament-table-entry {
  margin-bottom: 8px;
}

.table-tournament {
  color: #92d2ff;
  font-size: 12px;
}

.tournament-betslip-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.tournament-btn-refresh,
.tournament-btn-cancel,
.tournament-btn-bet {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(50% - 8px);
  height: 38px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  font-size: 14px;
  font-weight: 600;
  border: 0;
  margin: 0 4px;
  border: 1px solid #ffffff00;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.tournament-btn-refresh:focus,
.tournament-btn-cancel:focus,
.tournament-btn-bet:focus {
  border: 0;
  outline: none;
}

.tournament-btn-refresh {
  background-color: #409120;
  color: #fff;
}

.tournament-btn-refresh:hover {
  background: #50ae1b;
  box-shadow: 0 0 5px 1px #ffd452;
  border: 1px solid #ffffffcc;
}

.tournament-btn-cancel {
  background-color: #a1a1a1;
  color: #fff;
}

.tournament-btn-cancel:hover {
  background: #c4c4c4;
  box-shadow: 0 0 5px 1px #ffd452;
  border: 1px solid #ffffffcc;
}

.tournament-btn-bet {
  color: #090909;
  background-color: #ffc400;
}

.tournament-btn-bet:hover {
  background-color: #ffd452;
  box-shadow: 0 0 5px 1px #ffd452;
  border: 1px solid #ffffffcc;
}

.tournament-btn-refresh:disabled,
.tournament-btn-cancel:disabled,
.tournament-btn-bet:disabled {
  color: #ffffff88;
  background: #00000088;
  box-shadow: none;
  border: 1px solid #ffffff00;
  cursor: not-allowed;
}

.tournament-mybet {}

.tournament-mybet ul {
  margin: 6px 16px 2px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tournament-mybet ul li.nav-item {
  cursor: pointer;
  width: 32%;
}

.tournament-mybet ul li.nav-item .nav-link {
  text-transform: capitalize;
  background-color: #0d4275;
  height: 24px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  font-size: 12px;
  font-weight: 600;
}

.tournament-mybet ul li:first-child.nav-item .nav-link {
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
}

.tournament-mybet ul li:last-child.nav-item .nav-link {
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
}

.tournament-mybet ul li.nav-item .nav-link.active {
  background-color: #ffc400;
  color: #000000;
  font-weight: 600;
}

.tournament-mybet-inner {
  background: #0a2f52;
  padding: 16px 0;
}

.tournament-mybet-single {
  padding: 10px 12px;
  background-color: #F5F5F5;
  height: 160px;
  margin: 0 20px;
  position: relative;
  margin-bottom: 8px;
}

.tournament-mybet-single .empty {
  color: #014273;
}

.tournament-mybet-single:last-child {
  margin-bottom: 0;
}

.tournament-mybet-single::before {
  background: url('../../img/tn/slip2.png') top center repeat-y;
  width: 12px;
  height: 160px;
  content: "";
  position: absolute;
  left: -8px;
  top: 0;
}

.tournament-mybet-single::after {
  background: url('../../img/tn/slip2.png') top center repeat-y;
  width: 12px;
  height: 160px;
  content: "";
  position: absolute;
  right: -8px;
  top: 0;
  -moz-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.tournament-mybet-single.tournament-mybet-compact {
  height: 112px;
}

.tournament-mybet-single.tournament-mybet-compact::before {
  height: 112px;
}

.tournament-mybet-single.tournament-mybet-compact::after {
  height: 112px;
}

.tournament-mybet-text {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tournament-mybet-title {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #5574a7;
  text-transform: uppercase;
}

.text-glass {
  color: #ffffff88;
}

.tournament-mybet-odds {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 600;
  position: relative;
  margin-bottom: 6px;
  color: #000;
}

.tournament-mybet-odds-left {
  display: flex;
  width: 60%;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.tournament-mybet-odds-right {}

.tournament-mybet-text {
  color: #014273;
}

.tournament-mybet-odds-left .tournament-mybet-text {
  color: #50ae1b;
}

.tournament-mybet-odds-right .tournament-mybet-text {
  color: #000;
}

.tournament-mybet-text:nth-child(3) {}

.tournament-mybet-small {
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  font-weight: 600;
}

.tournament-mybet-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 28px;
  height: 28px;
}

.tournament-mybet-matches {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.tournament-mybet-matches .tn-team {
  font-weight: 400;
  font-size: 10px;
  color: #000;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.tournament-mybet-tiny {
  color: #000;
  font-size: 10px;
}

.tournament-mybet-status {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: #777;
}

.tournament-pool-status.room_0 {
  background-color: #50ae1b;
}

.tournament-pool-status.room_1 {
  background-color: #db8b00;
}

.tournament-pool-status.room_2 {
  background-color: #dc5862;
}

.tournament-pool-status.room_3 {
  background-color: #2c547e;
}

.tournament-pool-status.room_4 {
  background-color: #0f4f8c;
}

.tournament-container .loader-wrapper {
  display: flex;
  position: relative;
  width: 100%;
}

.tournament-pagination {
  margin-bottom: 0;
}

.tournament-pagination ul {
  display: flex;
  margin: 0;
  padding: 0;
}

.tournament-pagination ul li {
  list-style: none;
}

.tournament-pagination ul li .page-link {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #0F4F8C;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  padding: 0;
  width: 28px;
  height: 28px;
  margin-right: 3px;
  color: #0F4F8C;
  font-size: 12px;
  background: none;
  font-family: "Open Sans", Arial, "Helvetica Neue", Helvetica, sans-serif;
}

.tournament-pagination ul li .page-link i {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #0F4F8C;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  color: #0F4F8C;
  border: 1px solid rgba(0, 0, 0, 0.1);
  width: 100%;
  height: 100%;
  font-weight: normal;
}

.tournament-pagination ul li .page-link.active {
  background-color: #0f4f8c;
  border: 1px solid #0F4F8C;
}

.tournament-pagination ul li .page-link.disable {
  background-color: #00000000;
  border: 1px solid #0F4F8C;
}

.tournament-pagination ul li .page-link:hover {
  color: #0F4F8C;
  border: 1px solid #0F4F8C;
}

/* room */
.room-wrapper {}

.room-row {
  display: flex;
  align-items: center;
}

.room-col {
  display: flex;
}

.room-col.d-flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.room-text {
  font-size: 14px;
  text-align: center;
  color: #014273;
  font-weight: 500;
}

.room-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin: 12px 0;
  color: #000;
}

.room-date-date {}

.room-teams {
  padding-left: 8px;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.room-odds-col {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.room-odds {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.room-odds:last-child {
  margin-bottom: 0;
}

.room-bet-value-io {
  background: #E1E7F1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  cursor: pointer;
  border-radius: 3px;
  padding: 0;
  margin: 0;
  padding-right: 13px;
  width: 60px;
  height: 30px;
  font-size: 14px;
  font-weight: 700;
  color: #000;
}

.room-ball-value {
  font-size: 12px;
  padding-right: 8px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #777777;
}

.room-bet-value {
  background: none;
  border-radius: 3px;
  width: 60px;
  height: 30px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ffffff00;
  position: relative;
  overflow: hidden;
}

.room-bet-value:hover {
  box-shadow: 0 0 5px 1px #ffd452;
  border: 1px solid #ffffffcc;
}

.room-value-lock {
  background: #d4d4d4 0% 0% no-repeat padding-box;
  color: #acacac;
  align-items: center;
  justify-content: center;
  cursor: not-allowed;
  padding-right: 0;
}

.room-value-lock:hover {
  box-shadow: none;
  border: 1px solid #ffffff00;
}

.room-value-negative {
  color: #ff8e8e;
}

.room-value-highlight {
  background: transparent linear-gradient(295deg, #1897b7 0%, #0f7d8c 100%) 0% 0% no-repeat padding-box;
  color: #fff;
}

.room-value-selected {
  background: #FFF0C9 0% 0% no-repeat padding-box;
  color: #000;
}

.room-bet-value .room-value-up::after {
  content: "\f0de";
  font-family: "Font Awesome 5 Duotone";
  margin-left: 1px;
  color: #7edb13;
  position: absolute;
  right: 1px;
  top: 0;
}

.room-bet-value .room-value-down::after {
  content: "\f0dd";
  font-family: "Font Awesome 5 Duotone";
  margin-left: 1px;
  color: #ff8e8e;
  position: absolute;
  right: 1px;
  top: 0;
}

.room-bet-value-io.room-value-no-bet {
  cursor: not-allowed !important;
}

.room-br {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.room-bb {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.room-bb:last-child {
  border-bottom: 0;
}

.room-h33 {
  height: 33px;
  min-height: 33px;
  max-height: 33px;
}

.room-h70 {
  height: 70px;
  min-height: 70px;
  max-height: 70px;
}

.room-h95 {
  height: 95px;
  min-height: 95px;
  max-height: 95px;
}

.room-w40 {
  width: 40px;
  min-width: 40px;
  max-width: 40px;
}

.room-w40r {
  width: calc(100% - 40px);
}

.room-w100 {
  width: 100px;
  min-width: 100px;
  max-width: 100px;
}

.room-w110 {
  width: 110px;
  min-width: 110px;
  max-width: 110px;
}

.room-w350 {
  width: 350px;
  min-width: 350px;
  max-width: 350px;
}

.room-w500 {
  width: 500px;
  min-width: 500px;
  max-width: 500px;
}

.room-w80 {
  width: 80px;
  min-width: 80px;
  max-width: 80px;
}

.room-w320 {
  width: 320px;
  min-width: 320px;
  max-width: 320px;
}

.room-w640r {
  width: calc(100% - 640px);
  min-width: calc(100% - 640px);
  max-width: calc(100% - 640px);
}

.room-w-odds {
  width: 240px;
  min-width: 240px;
  max-width: 240px;
}

.room-w-teams {
  width: calc(100% - 480px);
  min-width: calc(100% - 480px);
  max-width: calc(100% - 480px);
}

/* modal */
.modal-title-left {
  font-weight: 700;
  color: #fff;
}

.modal-title-right {}

.modal-room .modal-dialog .modal-content {
  background-color: #fff !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  -webkit-border-radius: 10px 10px 10px 10px !important;
  border-radius: 10px 10px 10px 10px !important;
  font-family: "Open Sans", sans-serif !important;
}

.modal-room .modal-dialog.modal-xl {
  width: 1220px;
  min-width: 1220px;
  max-width: 1220px;
}

.modal-room .modal-dialog .modal-content .modal-header {
  background: url(/images/market-head-bg.png)no-repeat !important;
  background-size: cover !important;
  border-bottom: 0 !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -ms-flex-align: start !important;
  align-items: flex-start !important;
  -ms-flex-pack: justify !important;
  justify-content: space-between !important;
  padding: 1rem 1rem !important;
  border-top-left-radius: calc(0.3rem - 1px) !important;
  border-top-right-radius: calc(0.3rem - 1px) !important;
}

.modal-room .modal-dialog .modal-content .modal-body {
  background: transparent !important;
  padding: 0 !important;
}

.modal-room .modal-dialog .modal-content .modal-footer {
  background: transparent !important;
  border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
  justify-content: flex-start !important;
  position: relative !important;
  height: 90px !important;
  padding: 0 16px !important;
}

.modal-room .modal-dialog .modal-content .modal-header .modal-title {
  font-size: 18px !important;
  font-weight: 400 !important;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.modal-title-right {
  font-size: 16px;
  font-weight: 400;
}

.modal-match-no {
  color: #F6C344;
  margin: 0 6px;
  font-weight: 700;
}

.date-wrapper {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 12px 0;
  height: 85px;
  width: 100%;
  overflow-y: hidden;
  overflow-x: auto;
  white-space: nowrap;
}

.date-block {
  display: inline-block;
  width: 140px;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  font-weight: 500;
  position: relative;
  text-align: center;
  cursor: pointer;
  color: #014273;
}

.date-block.today {
  color: #50ae1b;
  font-weight: 500;
}

.magicX {
  overflow-x: overlay !important;
  overflow-y: hidden !important;
}

.magicX::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.magicX::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.magicX::-webkit-scrollbar-thumb {
  background: #5078af;
}

.magicX:hover::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.select-date-wrapper {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 12px 0;
  height: 85px;
  background: #fff;
}

.select-date {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #014273;
}

.select-day {
  display: flex;
  align-items: center;
  width: 140px;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  flex-direction: column;
  font-weight: 500;
  position: relative;
}

.select-day-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  padding: 0;
  border-radius: 4px;
  background: #ae1b1b;
  color: white;
  font-size: 10px;
  height: 16px;
  width: 16px;
  text-align: center;
  line-height: 16px;
  font-weight: 400;
}

.select-day.today {
  color: #50ae1b;
  font-weight: 500;
}

.select-day-top {}

.select-day-bottom {}

.select-league-wrapper {
  overflow-x: hidden;
  overflow-y: auto;
  height: 480px;
}

.select-league-header {
  display: flex;
  align-items: center;
  height: 46px;
  padding: 0 24px;
}

.select-league {}

.select-league-title {
  display: flex;
  align-items: center;
  height: 46px;
  padding: 0 24px;
  border-bottom: 1px solid #00000022;
  cursor: pointer;
  background: #DCE6EF;
  color: #014273;
}

.select-league .checkmark-date {
  background-color: #C0CED9;
  border: 1px solid #C0CED9;
}

.select-league .date-check input:checked~.checkmark-date {
  background-color: #276FA8;
  border: 1px solid #276FA8;
}

.select-league-title .tournament-arrow i {
  color: #061f36;
}

.select-league-header .tournament-arrow {
  height: 28px;
  align-items: center;
  justify-content: flex-end;
  display: flex;
  cursor: pointer;
  width: 28px;
}

.select-league-body {
  background: #ffffffaf;
  color: #061f36;
}

.select-league-body .home-vs-away {
  color: #061f3688;
}

.select-league-row {
  display: flex;
  align-items: center;
  height: 50px;
  border-bottom: 1px solid #00000022;
  padding: 0 24px;
  cursor: pointer;
}

.select-league-row:last-child {}

.check-content {
  display: flex;
  align-items: center;
  height: 35px;
  padding: 0;
  cursor: pointer;
}

.date-check {
  display: block;
  position: relative;
  padding-left: 40px;
  margin-bottom: 22px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.check-content .date-check {
  padding-left: 36px;
  margin-bottom: 24px;
}

/* Hide the browser's default checkbox */
.date-check input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark-date {
  position: absolute;
  top: 0;
  left: 0;
  height: 24px;
  width: 24px;
  background-color: #fff;
  border: 1px solid #014273;
}

/* On mouse-over, add a grey background color */
.date-check:hover input~.checkmark-date {
  background-color: #ccc;
}

/* When the checkbox is checked, add a green background */
.date-check input:checked~.checkmark-date {
  background-color: #50ae1b;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark-date:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.date-check input:checked~.checkmark-date:after {
  display: block;
}

/* Style the checkmark/indicator */
.date-check .checkmark-date:after {
  left: 8px;
  top: 2px;
  width: 7px;
  height: 14px;
  border: solid white;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.select-league-time {
  border-right: 1px solid #00000033;
  width: 150px;
  margin-right: 30px;
  color: #014273;
  font-weight: 500;
}

.select-league-teams {
  color: #014273;
  font-weight: 500;
}

.room-bottom {
  display: flex;
  align-items: center;
  color: #014273;
}

.room-rate {
  display: flex;
  align-items: center;
  margin-right: 32px;
}

.room-rate-text {
  margin-right: 12px;
}

.room-rate-input {}

.room-rate-input .form-control {
  border: 1px solid #0C4B86;
  width: 110px;
  height: 35px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  background-color: #fff;
  color: #014273;
}

.room-limit {
  display: flex;
  align-items: center;
}

.room-limit-text {
  margin-right: 12px;
}

.room-limit-select select {
  border: 1px solid #0C4B86;
  width: 80px;
  height: 35px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  background-color: #fff;
  color: #014273;
  outline: none;
  padding: 0 8px;
}

.room-rate-select select {
  border: 1px solid #0C4B86;
  width: 200px;
  height: 35px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  background-color: #fff;
  color: #014273;
  outline: none;
  padding: 0 8px;
}

.room-rate-select select option, .room-limit-select select option {
  background-color: #145693;
  color: #fff;
}

.tournament-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  background-color: #409120;
  height: 35px;
  line-height: 1;
  padding: 0 8px;
  min-width: 165px;
  color: #fff;
  cursor: pointer;
}

.tournament-button.tournament-button-primary {
  background-color: #409120;
}

.tournament-button.tournament-button-secondary {
  background-color: #0d4275;
}

.tournament-button.tournament-button-warning {
  background-color: #db8b00;
}

.tournament-button.tournament-button-danger {
  background-color: #dc5862;
}

.tournament-button.tournament-button-info {
  background-color: #2c547e;
}

.tournament-button-hover:hover {
  box-shadow: 0 0 5px 1px #ffd452;
  border: 1px solid #ffffffcc;
}

.tournament-button-hover:disabled,
.tournament-button-hover.disabled {
  box-shadow: none !important;
  border: 1px solid #ffffff00;
  background-color: #00000088;
}

.tournament-search select {
  border: 1px solid #88ABC8;
  min-width: 100px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  background-color: #fff;
  color: #014273;
  outline: none;
  padding: 0 8px;
  height: 35px;
}

.tournament-search select option {
  background: #145693;
}

.tournament-main .round-alert {
  background: #092a4bcc;
  color: #a5cae5;
  text-align: center;
  font-size: 14px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  line-height: 35px;
  border-collapse: collapse;
  border-radius: 5px;
  border-top: 1px solid #4f7eabcc;
  border-left: 1px solid #4f7eabcc;
  border-right: 1px solid #2f669bcc;
  border-bottom: 1px solid #2f669bcc;
  -webkit-box-shadow: 0 0 3px #00000088;
  box-shadow: 0 0 3px #00000088;
  padding: 8px;
}

.home-vs-away {
  margin: 0 0.5rem;
  color: #777777;
}

.tournament-outline-button {
  pointer-events: none;
  cursor: auto;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  padding: 0 8px;
  height: 28px;
  margin-left: 0;
  margin-right: 3px;
  color: #ffffffcc;
  background: none;
  border: 1px solid #ffffff44;
  font-size: 12px;
  font-family: "Open Sans", Arial, "Helvetica Neue", Helvetica, sans-serif;
}

.tournament-space {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 0 24px;
}

.tournament-search option {
  color: #ffffff88;
}

.tournament-main .table-info,
.tournament-main .table-total {
  font-family: "Open Sans", Arial, "Helvetica Neue", Helvetica, sans-serif;
  font-size: 12px;
}

.tournament-main .table-total {
  font-weight: 600;
}

.tournament-main .table-total td {
  padding: 6px 6px;
  border-right: 1px solid #aaaaaa;
}

.tournament-main .table-total td:last-child {
  border-right: 0;
}

.tournament-page-wrapper {
  font-family: "Open Sans", Arial, "Helvetica Neue", Helvetica, sans-serif;
  font-size: 14px;
}

.tournament-page-wrapper ul {
  margin: 0;
}

.tournament-page-wrapper ul li.nav-item {
  cursor: pointer;
}

.tournament-page-wrapper ul li.nav-item .nav-link {
  font-family: inherit;
  text-transform: capitalize;
  background-color: #fff;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
  padding: 0 16px;
  border-right: 1px solid rgba(0, 0, 0, 0.15);
  color: #014273;
}

.tournament-page-wrapper ul li:first-child.nav-item .nav-link {
  border-radius: 3px 0 0 3px;
}

.tournament-page-wrapper ul li:last-child.nav-item .nav-link {
  border-radius: 0 3px 3px 0;
  border-right: none;
}

.tournament-page-wrapper ul li.nav-item .nav-link.active {
  background-color: #409120;
  color: #fff;
}

.tournament-page-wrapper i {
  margin-right: 4px;
}

.tournament-jackpot {
  font-size: 12px;
  padding: 1px 8px;
  border-radius: 2px;
  border: 1px solid #ffffffcc;
  margin: 4px 0;
  background: rgb(145, 232, 66);
  background: -moz-linear-gradient(top, rgba(145, 232, 66, 1) 0%, rgba(210, 255, 82, 1) 100%);
  background: -webkit-linear-gradient(top, rgba(145, 232, 66, 1) 0%, rgba(210, 255, 82, 1) 100%);
  background: linear-gradient(to bottom, rgba(145, 232, 66, 1) 0%, rgba(210, 255, 82, 1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#91e842', endColorstr='#d2ff52', GradientType=0);
  color: #00000088;
  box-shadow: 0 0 5px #00000088;
  width: fit-content;
}

.tournament-jackpot i {
  margin-right: 4px;
}

.tournament-jackpot span {
  color: #000000;
  font-weight: 700;
  text-transform: uppercase;
}

.alert-tournament {
  color: #92d2ff;
  background-color: #0a2f52;
  border: 0;
  position: relative;
  padding: 4px 12px;
  border-radius: 5px;
  font-size: 11px;
}

.select-league-box {
  display: flex;
  align-items: center;
  justify-content: center;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.tournament-countdown {
  font-size: 11px;
  font-weight: 600;
  background: #ffffff22;
  color: #ffffffcc;
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 2px 16px;
  border-top-left-radius: 5px;
}

/* additional */
.tournament-trophy {
  position: absolute;
  top: 14px;
  right: 16px;
}

.tournament-trophy img {
  width: 30px;
}

.tournament-point {
  text-align: center;
  padding: 10px 16px;
  background: #1C62A0 url('../../img/tn/bg-points.png') top center no-repeat;
}

.tournament-point-top {
  color: #ffffff99;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
}

.tournament-point-bottom {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
}

.tournament-menu {
  margin-top: 16px;
}

.tournament-menu ul {
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tournament-menu ul li {
  list-style: none;
  min-width: 250px;
  background-color: #DCE4EC;
  margin: 0 4px;
  border-radius: 5px;
}

.tournament-menu ul li.active {
  background-color: #50ae1b;
}

.tournament-menu ul li.active a {
  color: #fff;
}

.tournament-menu ul li a {
  font-size: 14px;
  text-transform: uppercase;
  color: #000;
  display: block;
  padding: 5px 0px;
  font-weight: 600;
}

.tournament-details {
  background-color: #fff;
}

.tournament-details-row {
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  padding: 8px 0;
}

.tournament-details-row:last-child {
  border-bottom: 0;
}

.tournament-details-content,
.tournament-details-content a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 770px;
  margin: 0 auto;
  color: #000;
}

.tournament-details-content a {
  color: #000;
}

.tournament-details-content a:hover,
.tournament-details-content a:hover .icon-player {
  color: #ffc400;
}

.tournament-details-left {
  display: flex;
  align-items: center;
  flex-direction: row;
}

.tournament-details-left .icon-trophy {
  margin-right: 16px;
}

.tournament-details-left .icon-player {
  font-size: 20px;
  color: #0C4B86;
  margin-right: 8px;
}

.tournament-details-left .icon-player span {
  font-size: 14px;
}

.tournament-details-right img {
  width: 22px;
  height: 22px;
  margin-right: 8px;
}

.tournament-details-left .icon-trophy img {
  width: 22px;
}

.tournament-details-left .tournament-position {
  font-size: 16px;
  color: #000 !important;
}

.tournament-details-right {
  font-size: 16px;
}

.tournament-details-row.winning-row {
  padding: 24px 0;
}

.tournament-details-single {
  display: flex;
  align-items: center;
  flex-direction: row;
  position: relative;
  border-right: 1px solid rgba(0, 0, 0, 0.3);
  width: 25%;
}

.tournament-details-single:last-child {
  border-right: 0;
}

.tournament-details-row.winning-row .tournament-details-single {
  justify-content: center;
}

.tournament-details-row.winning-row .tournament-details-single:first-child {
  justify-content: flex-start;
}

.tournament-details-row.winning-row .tournament-details-single:last-child {
  justify-content: flex-end;
}

.tournament-details-icon {
  font-size: 26px;
  margin-right: 16px;
  color: #014273;
}

.tournament-details-icon img {
  width: 28px;
}

.tournament-details-text {
  text-align: center;
  line-height: 1;
}

.tournament-details-top {
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.tournament-details-bottom {
  font-size: 16px;
  font-weight: 700;
  color: #014273;
}

#player-betslip.modal-room .modal-dialog .modal-content {
  border: 0 !important;
}

#player-betslip .modal-lg {
  max-width: 710px;
}

.player-betslip-wrapper {
  background: #fff;
  border-radius: 5px;
  padding: 14px;
  padding-top: 28px;
}

.player-betslip-top {
  background: #276FA8 url('../../img/tn/bg-points.png') top center no-repeat;
  background-size: contain;
  border-radius: 5px;
  text-align: center;
  padding-bottom: 16px;
}

.player-betslip-icon {
  background-color: #0C4B86;
  width: 50px;
  height: 50px;
  margin: 0 auto;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 30px;
  margin-bottom: 6px;
  position: relative;
  top: -16px;
}

.player-betslip-holder {
  margin-top: -16px;
  margin-bottom: 8px;
}

.player-betslip-prize {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.player-prize-single {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 5px;
  min-width: 200px;
  padding: 10px 0;
  margin-right: 16px;
}

.player-prize-single:last-child {
  margin-right: 0;
}

.player-prize-icon {
  font-size: 24px;
  color: #014273;
  margin-right: 12px;
}

.player-prize-icon img {
  width: 28px;
}

.player-prize-detail {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  line-height: 1;
}

.player-prize-top {
  color: rgba(0, 0, 0, 0.5);
  font-weight: 600;
  margin-bottom: 6px;
}

.player-prize-bottom {
  font-weight: 700;
  color: #014273;
}

.player-betslip-room {
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
  font-weight: 600;
  padding: 10px 0;
  color: #014273;
}

.player-betslip-content {
  overflow-x: hidden;
  overflow-y: auto;
  height: calc(100vh - 300px);
  min-height: 156px;
}

.player-betslip-content .tournament-mybet-single {
  margin-left: 16px;
  margin-right: 16px;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-date {
  color: #014273;
  text-transform: uppercase;
  font-size: 12px;
  margin-bottom: 6px;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-text {
  font-weight: 400;
  font-size: 12px;
  color: #000;
  text-transform: uppercase;
  margin-bottom: 4px;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-odds .tournament-mybet-odds-left {
  font-size: 16px;
  font-weight: 600;
  border-right: 0;
}

.player-betslip-content .tournament-mybet-single {
  height: 168px;
}

.player-betslip-content .tournament-mybet-single:last-child {}

.player-betslip-content .tournament-mybet-single::before {
  height: 168px;
}

.player-betslip-content .tournament-mybet-single::after {
  height: 168px;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-border {
  /* border-top: 1px solid #ffffff15;
  border-bottom: 1px solid #ffffff15; */
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-border.tournament-mybet-league {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #92d2ff;
  font-size: 13px;
  line-height: 1;
  padding: 2px 4px;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-border.tournament-mybet-team {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #ffffffcc;
  font-size: 11px;
  line-height: 1;
  padding: 2px 4px;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-border.tournament-mybet-border-top {
  border-top: 1px solid #ffffff15;
  padding-top: 4px;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-border.tournament-mybet-border-bottom {
  border-bottom: 1px solid #ffffff15;
  padding-bottom: 4px;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-border.tournament-mybet-vs {
  font-weight: 400;
  font-size: 10px;
  color: #ffffffaa;
  line-height: 1;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-time {
  color: #92d2ff;
  padding-left: 4px;
  font-size: 11px;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-details {
  padding: 5px 0;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-odds .tournament-mybet-odds-right .tournament-mybet-text {
  font-size: 16px;
  font-weight: 600;
  color: #000;
}

.tournament-main .modal iframe {
  overflow-x: hidden !important;
  overflow-y: overlay !important;
  border: 1px solid #ffffff88;
  padding: 4px;
  margin: 0;
  width: 100%;
  height: 400px;
  border-radius: 5px;
  box-shadow: 0 0 5px #000000, inset 0 0 3px #ffffff44;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-small {
  /* border-top: 1px solid rgba(0, 0, 0, 0.15);
  border-bottom: 1px solid rgba(0, 0, 0, 0.15); */
  margin: 1px 0;
  padding: 8px 0;
  font-weight: 600;
  color: #040404;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-small span {
  color: #1C62A0;
  font-weight: 700;
  margin-right: 8px;
}

.player-betslip-content .tournament-mybet-single span {
  color: #014273;
}

.tournament-main .table-info {
  box-shadow: 0 3px 6px rgba(0, 0, 0, .15);
  border: 1px solid #014273;
  /* border-top: 0; */
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-border.tournament-mybet-team {
  color: #000;
}

.player-betslip-content .tournament-mybet-single .tournament-mybet-small:last-child {
  border-bottom: 0;
}

/* .side-tournament {
  background: url('../../img/tournament-tag-desktop.gif') !important;
} */

.content .left .side-row.side-tournament.side-tournament-plus {
  background: #C0CED9;
  border: 1px solid #C0CED9;
  border-radius: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
  height: 50px;
  background-image: url('../../img/tournament-tag-desktop.gif');
  background-size: 100%;
  background-repeat: no-repeat;
}

.content .left.active .side-row.side-tournament.side-tournament-plus {
  background-image: url('../../img/tournament-icon.png');
  background-size: 100%;
  background-repeat: no-repeat;
  height: 39px;
}
