import config from "@/config";

export default {
  computed: {
    selectedLeague() {
      return this.$store.getters.selectLeague;
    },
    language() {
      return this.$store.getters.language;
    },
    header() {
      return this.$store.getters.header;
    },
    leagueFiltered() {
      return this.$store.state.layout.leagueFiltered;
    },
    selectedDays() {
      return this.$store.getters.selectedDays;
    },
    search() {
      return this.$store.getters.search;
    },
    commType() {
      return this.$store.getters.commType;
    },
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
    eventSorting() {
      return this.$store.getters.pageDisplay.eventSorting;
    },
    displayMarketType() {
      return this.$store.getters.pageDisplay.marketType;
    },
    sidebar() {
      return this.$store.getters.minimizer.sidebar;
    },
    asidebar() {
      return this.$store.getters.minimizer.asidebar;
    },
    options() {
      return this.$store.state.layout.options;
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
    favorite() {
      return this.$store.getters.favorite;
    },
    menuY() {
      return this.$store.state.layout.menuY;
    },
    menuX() {
      return this.$store.getters.menuX;
    },
    menu0() {
      return this.$store.getters.menu0;
    },
    menu1() {
      return this.$store.getters.menu1;
    },
    menu2() {
      return this.$store.getters.menu2;
    },
    menu3() {
      return this.$store.getters.menu3;
    },
    marketType() {
      return this.menu1;
    },
    sportsType() {
      return this.menu2;
    },
    betType() {
      return this.menu3;
    },
    smenu2() {
      if (this.$store.getters.menu2 != null) return this.$store.getters.menu2.toString();
      else return null;
    },
    smenu3() {
      if (this.$store.getters.menu3 != null) return this.$store.getters.menu3.toString();
      else return null;
    },
    imenu0() {
      var result = "0";
      switch (this.menu0) {
        default:
        case "all":
          result = "1";
          break;
        case "live":
          result = "2";
          break;
        case "upcoming":
          result = "2";
          break;
        case "favorite":
          result = "3";
          break;
        case "parlay":
          result = "4";
          break;
        case "outright":
          result = "5";
          break;
        case "search":
          result = "6";
          break;
        case "event":
          result = "7";
          break;
      }
      return result;
    },
    imenu1() {
      var result = "0";
      switch (this.menu1) {
        case "early":
          result = "1";
          break;
        default:
        case "today":
          result = "2";
          break;
        case "live":
          result = "3";
          break;
        case "parlay":
          result = "4";
          break;
        case "outright":
          result = "5";
          break;
      }
      return result;
    },
    marketIds() {
      var result = "0";
      switch (this.menu1) {
        case "early":
          result = "1";
          break;
        default:
        case "today":
          result = "2|3";
          break;
        case "live":
          result = "3";
          break;
        case "parlay":
          result = "4";
          break;
        case "outright":
          result = "5";
          break;
      }
      return result;
    },
    liveOptions() {
      return this.$store.state.layout.options.live;
    },
    parlayOptions() {
      return this.$store.state.layout.options.parlay;
    },
    order() {
      return this.$store.state.layout.order.sports;
    },
    sports() {
      return this.$store.state.layout.sports;
    },
    selectedMatch() {
      return this.$store.getters.selectedMatch;
    },
    sSelectedMatch() {
      if (this.$store.getters.selectedMatch != null) return this.$store.getters.selectedMatch;
      else return null;
    },
    selectLeague() {
      return this.$store.getters.selectLeague;
    },
    debugMode() {
      return config.debugMode;
    },
    whiteLabel() {
      return config.whiteLabel;
    },
    operatorType() {
      return this.$store.getters.operatorType;
    },
    //- new added
    pageType() {
      return this.$store.getters.pageDisplay.pageType;
    }
  }
};
