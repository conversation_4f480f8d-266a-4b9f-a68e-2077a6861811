<template lang="pug">
div
  .tab-content
    #betlist.tab-pane.show.active(role='tabpanel', aria-labelledby='tab-betlist' )
      table.table-info(width='100%' id="statement-accordion")
        tbody(v-if="ploading")
          tr
            .empty.match-info.text-center.p-4
              i.fad.fa-spinner.fa-spin
        tbody(v-else)
          tr
            th.text-center(scope='col', width='5%') {{ $t("ui.no/") }}
            th.text-left(scope='col', width='20%') {{ $t("ui.trans_time") }}
            th.text-left(scope='col', width='42%') {{ $t("ui.event") }}
            th.text-right(scope='col', width='7%') {{ $t("ui.odds") }}
            th.text-right(scope='col', width='8%') {{ $t("ui.stake") }}
            th.text-right(scope='col', width='10%') {{ $t("ui.win") }} / {{ $t("ui.loss") }}
            th.text-left(scope='col', width='8%') {{ $t("ui.status") }}
          tr.grey(v-if="gameResultList.length == 0")
            td(colspan="7").text-center
              span {{ $t('message.no_information_available') }}
          tr(v-for="(item, index) in gameResultList" :class="{ grey: index % 2 === 0 }")
            td.text-center(valign='top') {{ ((currentPage - 1) * $store.getters.pageSize + index + 1) }}
            td.text-left(valign='top')
              div {{ $t("ui.ref_no") }}: {{ item.bet_id }}
              div {{ $dayjs(item.event_datetime).format("MM/DD/YYYY hh:mm:ss A") }}
            td.text-left(valign='top')
              .bet-info
                .bet-type.blue(v-if="item.is_combo == false && item.game_type_name") {{ item.game_type_name }}
                  span(v-if="item.game_market_name") &nbsp;({{ item.game_market_name }})
                .d-flex.justify-content-between.align-content-center(v-else :id="'heading-' + item.bet_id")
                  .bet-type.blue {{ $t("ui.mix_parlay") }}
                  .bg-blue.x-blue.collapsed(data-toggle="collapse" :data-target="'#collapse-' + item.bet_id" role="button" aria-expanded="false"
                    :aria-controls="'collapse-' + item.bet_id" @click="getParlayDetails(item.bet_id)" :ref="'collapse-' + item.bet_id")
                    i.far.fa-chevron-up
              .bet-list-scroll.match-info.collapse(v-if="item.is_combo == true" :id="'collapse-' + item.bet_id" :data-parent="'#statement-accordion'" :aria-labelledby="'heading-' + item.bet_id")
                .empty.match-info.white.text-center(v-if="loading")
                  i.fad.fa-spinner.fa-spin
                esports2Details(:items="parlayItems")
              .match-info.d-flex.flex-column.pl-2(v-if="item.is_combo == false")
                .name-home
                  span.red(style="text-transform: uppercase;") {{ item.bet_selection }}
                  span &nbsp;{{ item.bet_type_name }}
                  span &nbsp;@&nbsp;{{ item.member_odds }}
                .name-home(v-if="item.event_name") {{ item.event_name }}
                .name-league.font-weight-bold(v-if="item.competition_name") {{ item.competition_name }}
            td.text-right(valign='top')
              div(:class="{ red: parseFloat(item.member_odds) < 0 }") {{ item.member_odds }}
              div(v-if="item.member_odds_style" style="text-transform: uppercase;") {{ item.member_odds_style }}
            td.text-right(valign='top') {{ $numeral(item.bet_amount).format("0,0.00") }}
            td.text-right(valign='top')
              div
                span(:class="{ red: parseFloat(item.winlose) < 0 }") {{ $numeral(item.winlose).format("0,0.00") }}
              div {{  $numeral(item.comm).format("0,0.00") }}
            td.text-left(valign='top')
              div.font-weight-bold(style="text-transform: uppercase;") {{ item.result_status }}
              div(style="text-transform: uppercase;") {{ item.result }}
      table.table-total(width='100%' v-if="isTotal")
        tbody
          tr
            td.text-right(valign='top' width='81%') {{ $t("ui.subtotal") }} ({{ parseFloat(gameResultSummary.winlose) < 0 ? $t("ui.lost") : $t("ui.won") }})
            td.text-right(valign='top' width='10%')
              span(
                :class="{ red: parseFloat(gameResultSummary.winlose) < 0 }"
                ) {{ $numeral(gameResultSummary.winlose).format("0,0.00") }}
            td.text-right(valign='top' width='8%')  
          tr
            td.text-right(valign='top') {{ $t("ui.subtotal") }} ({{ $t("ui.commission") }})
            td.text-right(valign='top') {{ $numeral(gameResultSummary.comm).format("0,0.00") }}
            td  
          tr
            td.text-right(valign='top') {{ $t("ui.total") }}
            td.text-right(valign='top')
              span(
                :class="{ red: total < 0 }"
                ) {{ $numeral(total).format("0,0.00") }}
            td
      .mt-2
        v-pagination(
          v-model="currentPage"
          :page-count="gameResultTotalPages"
          :classes="bootstrapPaginationClasses"
          :labels="paginationAnchorTexts"
          @input="changedPage($event, 'game_result')"
          v-if="gameResultTotalPages"
        )

  resultModal(v-if="selectedMatch" :matchId="selectedMatch" :betType="selectedBetType")
</template>
<script>
import vPagination from "vue-plain-pagination";
import config from "@/config";
import naming from "@/library/_name";
import service from "@/library/_xhr-statement";
import calc from "@/library/_calculation.js";
import resultModal from "@/components/desktop/info/statement/resultModal.vue";
import betDetails from "@/components/desktop/info/betList/betDetails";
import esports2Details from "@/components/desktop/info/betList/esports2Details";

export default {
  components: { vPagination, resultModal, betDetails, esports2Details },
  props: {
    gameResultList: {
      type: Array,
      default: [],
    },
    gameResultSummary: {
      type: Object,
      default: {},
    },
    gameSummary: {
      type: Array,
      default: [],
    },
    gameResultTotalPages: {
      type: Number,
    },
    ploading: {
      type: Boolean,
      default: false,
    },
    cancelledBetList: {
      type: Array,
      default: [],
    },
    cancelledBetTotalPage: {
      type: Number,
    },
  },
  data() {
    return {
      loading: false,
      parlayItems: [],
      selectedBetType: "",
      selectedMatch: 0,
      currentPage: 1,
      currentPage2: 1,
      bootstrapPaginationClasses: {
        ul: "pagination justify-content-center",
        li: "page-item",
        liActive: "active",
        liDisable: "disabled",
        button: "page-link",
        buttonActive: "active",
        buttonDisable: "disable",
      },
      paginationAnchorTexts: {
        first: "<i class='fas fa-angle-double-left'></i>",
        prev: "<i class='fas fa-angle-left'></i>",
        next: "<i class='fas fa-angle-right'></i>",
        last: "<i class='fas fa-angle-double-right'></i>",
      },
    };
  },
  computed: {
    language() {
      return this.$store.getters.language;
    },
    sports() {
      return this.$store.state.layout.sports;
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
    odds() {
      return config.oddsTypeLocale;
    },
    isTotal() {
      if (this.gameResultSummary != null) {
        return Object.keys(this.gameResultSummary).length > 0;
      } else {
        return false;
      }
    },
    total() {
      if (this.gameResultSummary != null) {
        return (
          parseFloat(this.gameResultSummary.winlose) +
          parseFloat(this.gameResultSummary.comm)
        );
      } else {
        return 0;
      }
    },
    sortedGameResultList() {
      var result = new Array();
      this.gameResultList.forEach((x) => {
        var idx = result.findIndex(
          (y) =>
            y.bet_type === x.bet_type &&
            y.home_team_name === x.home_team_name &&
            y.away_team_name === x.away_team_name
        );
        if (idx < 0)
          result.push(
            new Object({
              bet_type: x.bet_type,
              home_team_name: x.home_team_name,
              away_team_name: x.away_team_name,
              bet_member: x.bet_member,
              winlose: x.winlose,
            })
          );
        else {
          result[idx].bet_member = result[idx].bet_member + x.bet_member;
          result[idx].winlose = result[idx].winlose + x.winlose;
        }
      });
      return result;
    },
  },
  mounted() {
    this.currentPage = this.currentGameResultPage;
    this.changedPage(1, "game_result");
  },
  methods: {
    // getBetTypeName(e) {
    //   return this.$t("m.BT_" + e);
    // },
    // getBetDetail(bet) {
    //   return naming.betDisplay(bet, this);
    // },
    // formatOddsDisplay(odds, bet_type) {
    //   if (bet_type == "cs") return calc.fmcs(odds);
    //   else return calc.fmt(odds);
    // },
    // getBall(bet) {
    //   if ((bet.team_g == bet.home_team_id && bet.bet_team_id == bet.home_team_id) || (bet.team_g == bet.away_team_id && bet.bet_team_id == bet.away_team_id)) return -bet.ball;
    //   else return bet.ball;
    // },
    // ballDisplay(e) {
    //   return naming.ballDisplay1(e, this);
    // },
    // ballDisplayMMO(e) {
    //   return naming.ballDisplayMMO1(e, this);
    // },
    getParlayDetails(result_id) {
      var detailPanel = this.$refs["collapse-" + result_id][0].className;
      // call API on expansion only..
      if (typeof detailPanel != "undefined" && detailPanel.indexOf("collapsed") > -1) {
        if (this.loading == true) return;

        if (this.isLoggedIn) {
          this.loading = true;
          this.parlayItems = [];

          var json = {
            account_id: this.$store.getters.accountId,
            session_token: this.$store.getters.sessionToken,
            bet_id: result_id,
          };
          service.getEsportsDetails(json).then(
            (res) => {
              this.loading = false;
              if (res) {
                if (res.success == true) {
                  // var g = res.data;
                  this.parlayItems = res.data;
                  // if (res.names) {
                  //   var mm = g.reduce((groups, item) => {
                  //     const val = item["match_id"];
                  //     groups[val] = groups[val] || [];
                  //     groups[val].push(item);
                  //     return groups;
                  //   }, {});

                  //   var ll = g.reduce((groups, item) => {
                  //     const val = item["league_id"];
                  //     groups[val] = groups[val] || [];
                  //     groups[val].push(item);
                  //     return groups;
                  //   }, {});

                  //   var m = res.names["Table"];
                  //   var l = res.names["Table1"];
                  //   if (m) {
                  //     for (var i = 0; i < m.length; i++) {
                  //       var hn = m[i]["home_name_" + this.language];
                  //       var an = m[i]["away_name_" + this.language];
                  //       var mid = m[i]["match_id"];

                  //       if (hn || an) {
                  //         for (var j = 0; j < mm[mid].length; j++) {
                  //           if (hn) mm[mid][j]["home_team_name"] = hn;
                  //           if (an) mm[mid][j]["away_team_name"] = an;
                  //         }
                  //       }
                  //     }
                  //   }

                  //   if (l) {
                  //     for (var i = 0; i < l.length; i++) {
                  //       var ln = l[i]["name_" + this.language];
                  //       var lid = l[i]["league_id"];

                  //       if (ln) {
                  //         for (var j = 0; j < ll[lid].length; j++) {
                  //           ll[lid][j]["league_name"] = ln;
                  //         }
                  //       }
                  //     }
                  //   }
                  // }
                } else {
                  this.$helpers.handleFeedback(res.status);
                }
              }
            },
            (err) => {
              this.loading = false;
              this.$helpers.handleFeedback(err.status);
            }
          );
        }
      }
    },
    // getSelectedMatch(item) {
    //   this.selectedBetType = item.bet_type;
    //   if (item.bet_type === "PARLAY") this.selectedMatch = item.result_id;
    //   else this.selectedMatch = item.match_id;
    // },
    changedPage(pageNo, type) {
      if (type == "game_result") this.currentPage = pageNo;
      else this.currentPage2 = pageNo;

      this.$emit("changedPage", pageNo, type);
    },
    // filterMatch(id, bet, sports) {
    //   $("#tab-betlist").click();
    //   this.currentPage = 1;
    //   this.$emit(
    //     "filterMatch",
    //     new Object({
    //       id: id,
    //       bet_type: bet,
    //       sports_category: sports
    //     })
    //   );
    // }
  },
};
</script>
