<template lang="pug">
table.table-info(width='100%' :id="'bet-' + status + '-accordion'")
  tbody
    tr
      th(scope='col', width='4%').text-center {{ $t("ui.no/") }}
      th(scope='col', width='20%').text-left {{ $t("ui.date") }}
      th(scope='col', width='52%').text-left {{ $t("ui.choice") }}
      th(scope='col', width='7%').text-right {{ $t("ui.odds") }}
      th(scope='col', width='7%').text-right {{ $t("ui.stake") }}
      th(scope='col', width='10%').text-center {{ $t("ui.status") }}
    tr.grey(v-if="betList == 'undefined' || betList.length <= 0")
      td(colspan="6").text-center
        span {{ $t('message.no_information_available') }}
    tr(v-for="(item, index) in betList" :class="{ grey: index % 2 === 0 }")
      td.text-center(valign='top') {{ getNo(index) }}
      td.text-left(valign='top')
        div {{ $t("ui.ref_no") }}: {{ item.bet_id }}
        div(v-if="item.created_on") {{ $dayjs(item.created_on).format("MM/DD/YYYY hh:mm:ss A") }}
        div(v-else) -
      td.text-left(valign='top')
        .bet-info
          template(v-if="racingList.includes(item.sports_type)")
            .bet-type.blue.x-normal {{ sports[item.sports_type] }}
          template(v-else)
            .bet-type.blue.x-parlay(v-if="item.bet_type != 'PARLAY'") {{ sports[item.sports_type] }} - {{ getBetTypeName(item.bet_type) }}
          .d-flex.justify-content-between.align-content-center(v-if="item.bet_type == 'PARLAY'" :id="'heading-' + item.bet_id")
            .bet-type.blue.x-mmo(v-if="item.mmo") {{ $t("ui.mmo_parlay") }}
            .bet-type.blue.x-normal(v-else) {{ getBetTypeName(item.bet_type) }}
            .bg-blue.x-blue.btn-inner.collapsed(
              data-toggle="collapse"
              :data-target="'#collapse-' + item.bet_id" role="button" aria-expanded="false"
              :aria-controls="'collapse-' + item.bet_id"
              @click="getParlayDetails(item.bet_id)"
              :ref="'collapse-' + item.bet_id"
              )
              i.far.fa-chevron-up
          .bet-detail.blue
            template(v-if="racingList.includes(item.sports_type)")
              template(v-if="racingList1.includes(item.sports_type)")
                .name(v-if="['OU','OE'].includes(item.bet_type)")
                  | {{ $t("m.GH_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                .name(v-else)
                  | {{ $t("m.GH_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}
              template(v-if="racingList2.includes(item.sports_type)")
                .name(v-if="['OU','OE'].includes(item.bet_type)")
                  | {{ $t("m.GC_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                template(v-else)
                  .name(v-if="['CS'].includes(item.bet_type)")
                    | {{ item.criteria1 }}
                  .name(v-else)
                    | {{ $t("m.GC_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}
              template(v-if="racingList3.includes(item.sports_type)")
                .name(v-if="['OU','OE'].includes(item.bet_type)")
                  | {{ $t("m.GX_" + item.bet_type + (item.home_away == undefined ? '' : item.home_away)) }}
                .name(v-else)
                  | {{ $t("m.GX_" + item.bet_type + (item.ball_home_3way == undefined ? '' : item.ball_home_3way)) }}

            template(v-else)
              .name {{ getBetDetail(item) }}
              .oddsdetail
                .d-flex.justify-content-start
                  template(v-if="isMMO(item)")
                    .selector-name(v-if="['HDP', 'HDPH', 'OU', 'OUH', '1X2HDP', '1X2HDPH'].includes(item.bet_type)")
                      | {{ ballDisplayMMO(item) }}({{ $numeral(item.criteria2).format("0") }})
                  template(v-else)
                    .selector-name(v-if="['HDP', 'HDPH', 'OU', 'OUH', '1X2HDP', '1X2HDPH'].includes(item.bet_type)") {{ getBallDetail(item) }}
                  span(v-if="item.market_type == 3 && item.home_running_score != null") [{{ item.home_running_score }}-{{ item.away_running_score }}]
        .bet-list-scroll.match-info.collapse.magicY(
          v-if="item.bet_type == 'PARLAY'"
          :id="'collapse-' + item.bet_id"
          :data-parent="'#bet-' + status + '-accordion'"
          :aria-labelledby="'heading-' + item.bet_id"
          )
          .empty.match-info.white.text-center(v-if="loading")
            i.fad.fa-spinner.fa-spin
          betDetails(:items="parlayItems")
        .match-info.d-flex.flex-column(v-if="item.bet_type != 'PARLAY'")
          template(v-if="racingList.includes(item.sports_type)")
            .d-flex
              template(v-if="racingList1.includes(item.sports_type)")
                .name-home {{ getHomeTeam(item) }} &nbsp;
              template(v-if="racingList2.includes(item.sports_type)")
                template(v-if="['ML','1X2'].includes(item.bet_type)")
                  .name-home(v-if="item.home_away == 1") {{ getHomeTeam(item) }} &nbsp;
                  .name-home(v-else) {{ getAwayTeam(item) }} &nbsp;
                template(v-if="['OU','OE'].includes(item.bet_type)")
                  .name-home {{ getHomeTeam(item) }} &nbsp;
              template(v-if="racingList3.includes(item.sports_type)")
                .name-home {{ getHomeTeam(item) }} &nbsp;
              .name-league(v-if="item.match_time") (No. {{ $dayjs(item.match_time).format("MMDDhhmm") }})
          template(v-else)
            .name-home(v-if="item.bet_type == 'OR'") {{ getHomeTeam(item) }}
            .name-home(v-else)
              span(:class="((item.bet_type == 'HDP' || item.bet_type == 'HDPH') && item.home_giving === true) ? 'red' : ''") {{ getHomeTeam(item) }}
              span &nbsp;vs&nbsp;
              span(:class="((item.bet_type == 'HDP' || item.bet_type == 'HDPH') && item.home_giving === false) ? 'red' : ''") {{ getAwayTeam(item) }}
            .name-league {{ getLeague(item) }}
      td.text-right(valign='top')
        div(:class="{ red: parseFloat(item.odds_display) < 0 }") {{ formatOddsDisplay(item.odds_display, item.bet_type) }}
        div(v-if="isMMO(item)|| item.mmo") {{ $t(odds[5]) }}
        div(v-else) {{ $t(odds[item.odds_type]) }}
      td.text-right(valign='top')
        span(v-if="item.bet_member") {{ $numeral(item.bet_member).format("0,0.00") }}
      td.text-center(valign='top')
        div(v-if="item.bet_status") {{ $t("ui." + item.bet_status.toLowerCase()) }}
    tr.table-total
      td.text-right(colspan="4") {{ $t("ui.total") }}
      td.text-right {{ $numeral(calculateTotalBet).format("0,0.00") }}
      td
</template>

<script>
import config from "@/config";
import naming from "@/library/_name";
import xhr from "@/library/_xhr-betlist.js";
import calc from "@/library/_calculation.js";
import betDetails from "@/components/desktop/info/betList/betDetails";

export default {
  components: {
    betDetails,
  },
  props: {
    betList: {
      type: Array,
      default: [],
    },
    status: {
      type: String,
      default: "accept",
    },
    pageIndex: {
      type: Number,
    },
    pageSize: {
      type: Number,
    },
  },
  data() {
    return {
      parlayItems: [],
      loading: false,
    };
  },
  computed: {
    racingList() {
      return config.racingList;
    },
    racingList1() {
      return config.racingList1;
    },
    racingList2() {
      return config.racingList2;
    },
    racingList3() {
      return config.racingList3;
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
    sports() {
      return this.$store.state.layout.sports;
    },
    odds() {
      return config.oddsTypeLocale;
    },
    calculateTotalBet() {
      var total = 0;

      for (var n in this.betList) {
        total += this.betList[n].bet_member;
      }

      return total.toFixed(3);
    },
    language() {
      return this.$store.getters.language;
    },
  },
  methods: {
    isMMO(e) {
      return (e['criteria2'] && ['HDP', 'HDPH', 'OU', 'OUH'].includes(e['bet_type'].toUpperCase()))
    },
    ballDisplayMMO(e) {
      return naming.ballDisplayMMO2(e, this);
    },
    getNo(e) {
      if (this.pageIndex == null) {
        return e + 1;
      } else {
        return (this.pageIndex - 1) * this.pageSize + (e + 1);
      }
    },
    getHomeTeam(e) {
      var r = e["home_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.home_team_name;
    },
    getAwayTeam(e) {
      var r = e["away_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.away_team_name;
    },
    getLeague(e) {
      var r = e["name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.league_name;
    },
    getBetTypeName(e) {
      return this.$t("m.BT_" + e);
    },
    getBetDetail(bet) {
      return naming.betDisplay(bet, this, this.language);
    },
    getBallDetail(bet) {
      return naming.ballDisplay(bet, this);
    },
    formatOddsDisplay(odds, bet_type) {
      if (bet_type == "CS") return calc.fmcs(odds);
      else return calc.format(odds.toFixed(3));
    },
    getParlayDetails(bet_id) {
      var detailPanel = this.$refs["collapse-" + bet_id][0].className;
      // call API on expansion only..
      if (typeof detailPanel != "undefined" && detailPanel.indexOf("collapsed") > -1) {
        if (this.loading == true) return;

        if (this.isLoggedIn) {
          this.loading = true;
          this.parlayItems = [];

          var json = {
            account_id: this.$store.getters.accountId,
            session_token: this.$store.getters.sessionToken,
            bet_id: bet_id,
          };

          switch (this.status) {
          case "accept":
            xhr.getParlayAcceptDetails(json).then(
              (res) => {
                this.loading = false;
                if (res) {
                  if (res.success == true) {
                    this.parlayItems = res.data;
                  } else {
                    this.$helpers.handleFeedback(res.status);
                  }
                }
              },
              (err) => {
                this.loading = false;
                this.$helpers.handleFeedback(err.status);
              }
            );
            break;
          case "pending":
            xhr.getParlayPendingDetails(json).then(
              (res) => {
                this.loading = false;
                if (res) {
                  if (res.success == true) {
                    this.parlayItems = res.data;
                  } else {
                    this.$helpers.handleFeedback(res.status);
                  }
                }
              },
              (err) => {
                this.loading = false;
                this.$helpers.handleFeedback(err.status);
              }
            );
            break;
          case "void":
            xhr.getParlayRejectDetails(json).then(
              (res) => {
                this.loading = false;
                if (res) {
                  if (res.success == true) {
                    this.parlayItems = res.data;
                  } else {
                    this.$helpers.handleFeedback(res.status);
                  }
                }
              },
              (err) => {
                this.loading = false;
                this.$helpers.handleFeedback(err.status);
              }
            );
            break;
          }
        }
      }
    },
  },
};
</script>
