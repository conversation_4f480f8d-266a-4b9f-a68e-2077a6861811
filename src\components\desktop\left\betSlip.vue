<template lang="pug">
.content-bet
  ul#pills-tab.nav(role="tablist")
    li.nav-item
      a#pills-single-tab.nav-link.active(data-toggle="pill", href="#pills-single", role="tab", aria-controls="pills-single", aria-selected="true")
        | {{ $t('ui.single') }}
    li.nav-item
      a#pills-parlay-tab.nav-link(data-toggle="pill", href="#pills-parlay", role="tab", aria-controls="pills-parlay", aria-selected="false")
        | {{ $t('ui.parlay') }}
        span.number-parlay(v-if="betparlay") {{ betparlay }}
        span.number-parlay(v-if="betparlaymmo") {{ betparlaymmo }}
  #pills-tabContent.tab-content
    #pills-single.tab-pane.tab-p1.show.active(role="tabpanel", aria-labelledby="pills-single-tab")
      .empty(v-if="!betsingle && !betsinglemmo") {{ $t('message.bet_empty') }}
      betSingle
      betSingleMMO
    #pills-parlay.tab-pane.tab-p1(role="tabpanel", aria-labelledby="pills-parlay-tab")
      betParlay
      betParlayMMO
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";
import xhrBet from "@/library/_xhr-bet.js";
import calc from "@/library/_calculation.js";
import config from "@/config";

export default {
  components: {
    betSingle: () => import("@/components/desktop/left/betSingle"),
    betParlay: () => import("@/components/desktop/left/betParlay"),
    betSingleMMO: () => import("@/components/desktop/left/betSingleMMO"),
    betParlayMMO: () => import("@/components/desktop/left/betParlayMMO"),
  },
  data() {
    return {};
  },
  computed: {
    betsingle() {
      return this.$store.state.betsingle.hasOwnProperty("val");
    },
    betsinglemmo() {
      return this.$store.state.betsinglemmo.hasOwnProperty("val");
    },
    betparlay() {
      return Object.keys(this.$store.state.betparlay.data).length;
    },
    betparlaymmo() {
      return Object.keys(this.$store.state.betparlaymmo.data).length;
    },
    debug() {
      return config.debugMode;
    },
  },
  mounted() {},
  methods: {},
};
</script>
