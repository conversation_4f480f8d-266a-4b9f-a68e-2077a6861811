<template lang="pug">
  .hx-col.hx-cols.justify-content-end.w-128
    .hx.hx-flex-c.w-100(v-if="details['orz'] != null && details['orz'][0] != null && details['orz'][0][5] != 0 && details['orz'][0][5] != ''")
      .hxs.w-100.px-2
        oddsItem(:odds="details['orz'][0]" idx=5 :typ="oddsType" dataType="4")
</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";

export default {
  components: {
    oddsItem
  },
  props: {
    details: {
      type: Object
    },
    oddsType: {
      type: String
    },
    i: {
      type: String
    },
    betType: {
      type: String
    }
  },
  // updated: function() {
  //   this.$nextTick(function() {
  //     console.log("odds1", new Date(), this.oddsType, this.i);
  //   });
  // }
};
</script>
