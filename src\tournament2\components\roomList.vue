<template lang="pug">
.tournament-room-list
  .tournament-pool-single(v-for="room in roomList")
    .tournament-free-room-shadow(v-if="room.room_rate <= 0")
      .tournament-free-room
        .tournament-free-text {{ $t("ui.free_room") }}
    .tournament-pool
      .tournament-pool-top
        .tournament-background(v-if="room.league_logo && room.room_type != 2")
          img(:src="getBackground(room.league_logo)")

        .tournament-pool-amount
          .tournament-text.d-flex.align-items-center.justify-content-start
            span {{ $t('ui.pool') }}:&nbsp;
            .tournament-color-green {{ $numeral(room.points).format("0,0") }}
            .tournament-jackpot.ml-2(v-if="room.jackpot > 0")
              i.fad.fa-chevron-double-up
              span {{ room.jackpot * 100 }}%
          .tournament-pool-number {{ $t('ui.room') }}:&nbsp;
            span.tournament-color-white {{ room.room_id }}

        //- NEW
        a.tournament-pool-fee.tournament-button-hover.cursor-pointer(v-if="room.room_status === 0 && room.room_join === 0 && room.room_count < room.room_limit"  @click="enterRoom(room)")
          .tournament-pool-people
            i.fad.fa-users
            span &nbsp;{{ $t('ui.players') }}:&nbsp;
            span &nbsp;{{ room.room_count }} / {{ room.room_limit }}&nbsp;
          .tournament-pool-entry(v-if="room.room_rate > 0")
            span {{ $t('ui.entry_fee') }}:&nbsp;
            span.tournament-color-green {{ $numeral(room.room_rate).format("0,0") }}&nbsp;&nbsp;
            small(style="color: #08ff00cc;") (&nbsp;{{ currency_code }}&nbsp;{{ $numeral(room.room_rate / room.rate).format("0,0.00")  }}&nbsp;)
          .tournament-trophy(v-if="room.room_status == 3 && (room.position != null) && ([1,2,3].includes(room.position))")
            img(:src="'img/tn/trophy' + room.position.toString() + '.svg'")

        //- JOINED or FULLED
        a.tournament-pool-fee.tournament-button-hover.cursor-pointer(v-else @click="enterRoom(room)")
          .tournament-pool-people(v-if="room.room_count < room.room_limit")
            i.fad.fa-users
            span &nbsp;{{ $t('ui.players') }}:&nbsp;
            span &nbsp;{{ room.room_count }} / {{ room.room_limit }}&nbsp;
          .tournament-pool-people(v-else) {{ $t('message.room_full') }}
          .tournament-pool-entry(v-if="(room.room_join > 0)")
            span.tournament-fc-light(v-if="room.room_host === 1") {{ $t('message.my_room') }}
            span.tournament-fc-light(v-else) {{ $t('message.room_joined') }}
          .tournament-trophy(v-if="room.room_status == 3 && (room.position != null) && ([1,2,3].includes(room.position))")
            img(:src="'img/tn/trophy' + room.position.toString() + '.svg'")

        .tournament-pool-details
          .tournament-pool-type.private(v-if="room.room_type == 2")
            i.fad.fa-lock.mr-2
            span {{ $t('ui.private') }}
          template(v-else)
            .tournament-pool-type.exclusive(v-if="room.system_id.toUpperCase() != 'MERCEDES'")
              i.fad.fa-crown.mr-2
              span {{ $t('ui.exclusive') }}
          .tournament-pool-type.public(v-if="room.room_type != 2 && room.room_host === 1")
            i.fad.fa-home.mr-2
            span {{ $t('ui.owner') }}
          .tournament-pool-status.mr-16px(:class="'room_' + room.room_status") {{ $t('m.ROOM_' + room.room_status) }}

          .tournament-countdown(v-if="(new Date(room.start_time)) > (new Date())")
            small(style="text-transform: uppercase;") {{ $t("ui.last_join_time") }}&nbsp;:&nbsp;
            span(:data-tick="ticker") {{ getCountDown($dayjs(room.start_time).subtract(15, 'minute').format("YYYY-MM-DDTHH:mm:ss")) }}

      .tournament-collapse(v-if="(room.total_match != null) && (room.total_match > 0)")
        .collapse(:id="'room-' + room.room_id")
          template(v-if="leagueList != null && leagueList.hasOwnProperty(room.room_id)")
            .tournament-league-single(v-for="ln in leagueList[room.room_id]")
              .tournament-league-list.collapsed(
                @click="populateMatchList(room.room_id, ln.league_id)"
                data-toggle="collapse"
                :data-target="'#room-' + room.room_id + '-league-' + ln.league_id"
                )
                .tournament-league-header
                  .tournament-league-club
                    img(v-if="ln.logo" :src="getLogo('league', ln.logo)")
                  .tournament-league-type.flex-grow-1 {{ getName('name_', ln) }}
                  .tournament-arrow
                    i.fas.fa-chevron-up.ml-2
              .collapse(:id="'room-' + room.room_id + '-league-' + ln.league_id")
                template(v-if="matchList.hasOwnProperty(room.room_id)")
                  template(v-if="matchList[room.room_id].hasOwnProperty(ln.league_id)")
                    .tournament-league-match(v-for="match in matchList[room.room_id][ln.league_id]")
                      .tournament-match-date
                        span(v-if="match.match_time") {{ getMatchTime(match.match_time) }}
                        span(v-else) --:--
                      .tournament-match-teams
                        | {{ getName('home_name_', match) }}
                        span.home-vs-away -vs-
                        | {{ getName('away_name_', match) }}
          template(v-else)
            .d-flex.align-items-center.justify-content-center.p-3
              .fad.fa-spinner.fa-spin(style="line-height: 1; font-size: 14px; color: #888;")
    .tournament-btn.collapsed(
      @click="getLeagueList(room.room_id, room)"
      v-if="(room.total_match != null) && (room.total_match > 0)"
      data-toggle="collapse"
      :data-target="'#room-' + room.room_id"
      )
      .tournament-text(v-if="room.match_count") {{ room.match_count }} / {{ room.total_match }} {{ $t('ui.matches') }}
      .tournament-text(v-else) {{ room.total_match }} {{ $t('ui.matches') }}
      .tournament-arrow
        i.fas.fa-chevron-up.ml-2
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import service from "@/tournament2/library/_xhr.js";
import vPagination from "vue-plain-pagination";

export default {
  components: {
    vPagination,
  },
  props: {
    roomList: {
      type: Array,
    },
  },
  data() {
    return {
      ticker: 0,
      leagueList: {},
      matchList: {},
      loading: {
        getLeagueList: false,
        getMatchList: false,
        getMemberBetList: false,
      },
      betList: [],
    };
  },
  computed: {
    language() {
      return this.$store.getters.language;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
  },
  destroyed() {
    clearInterval(this.ticking);
  },
  mounted() {
    setInterval(this.ticking, 60000);
  },
  methods: {
    getMatchTime(e) {
      var mdate = this.$dayjs(e).format("MM/DD, hh:mm A");
      switch (this.currency_code) {
      case "MMK":
      case "MMO":
        mdate = this.$dayjs(e).subtract(90, "minute").format("MM/DD, hh:mm A");
        break;
      case "NGN":
        mdate = this.$dayjs(e).subtract(420, "minute").format("MM/DD, hh:mm A");
        break;
      }
      return mdate;
    },
    ticking() {
      this.ticker += 1;
    },
    getCountDown(e) {
      var difference = this.$dayjs.utc(e + "+0800").diff(this.$dayjs.utc());
      var days = Math.floor(difference / (1000 * 60 * 60 * 24));
      var hours = Math.floor((difference / (1000 * 60 * 60)) % 24);
      var minutes = Math.floor((difference / 1000 / 60) % 60);
      var seconds = Math.floor((difference / 1000) % 60);
      if (days < 0 || hours < 0 || minutes < 0 || seconds < 0) {
        return this.$t("ui.match_started");
      } else {
        if (days === 0) {
          if (hours === 0) {
            if (minutes === 0) {
              return seconds + "s";
            } else {
              return minutes + "m";
            }
          } else {
            return hours + "h " + minutes + "m";
          }
        } else {
          return days + "d " + hours + "h " + minutes + "m";
        }
      }
    },
    refreshRoom(e) {
      EventBus.$emit("roomEnterById2", e);
    },
    enterRoom(room) {
      this.$emit("room-enter2", room);
    },
    joinRoom(room) {
      this.$emit("room-join2", room);
    },
    populateMatchList(e, f) {
      this.getMatchList(e, f).then((res) => {});
    },
    getMatchList(e, f) {
      return new Promise((resolve, reject) => {
        if ($("#room-" + e + "-league-" + f).hasClass("show") === true) {
          return;
        }

        var json = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          room_id: e,
          league_id: f,
          page_number: 1,
          page_size: 100,
        };

        var feedback = {
          success: false,
          status: errors.session.invalidSession,
        };

        this.loading.getMatchList = true;
        service.getMatchList(config.tournamentUrl().roommatchlist, json).then(
          (result) => {
            this.loading.getMatchList = false;
            if (result) {
              feedback.success = result.success;
              feedback.status = result.status;
              if (result.success == true) {
                if (!this.matchList.hasOwnProperty(e)) {
                  this.$set(this.matchList, e, {});
                }
                this.$set(this.matchList[e], f, result.data.value);
                resolve({ league_id: f, count: result.data.value.length });
              } else {
                this.$helpers.handleFeedback(feedback.status);
                resolve({ league_id: f, count: 0 });
              }
            } else {
              resolve({ league_id: f, count: 0 });
            }
          },
          (err) => {
            this.loading.getMatchList = false;
            feedback.success = false;
            feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);
            resolve({ league_id: f, count: 0 });
          }
        );
      });
    },
    getLeagueList(e, rn) {
      if ($("#room-" + e).hasClass("show") === true) {
        return;
      }

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        room_id: e,
        page_number: 1,
        page_size: 100,
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      this.loading.getLeagueList = true;
      service.getLeagueList(config.tournamentUrl().roomleaguelist, json).then(
        (result) => {
          this.loading.getLeagueList = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              var ln = result.data.value;
              var j = 0;
              var k = ln.length;
              var items = [];
              for (var i = 0; i < k; i++) {
                var lid = ln[i].league_id;
                var ret = false;
                this.getMatchList(e, lid).then((res) => {
                  items.push(res);
                  j = j + 1;
                  if (j == k) {
                    setTimeout(() => {
                      var deleted = [];
                      var inserted_array = [];
                      // var inserted_object = {};
                      // var inserted_ids = [];

                      var mc = 0;
                      for (var i = 0; i < items.length; i++) {
                        mc += items[i].count;
                        if (items[i].count <= 0) {
                          deleted.push(items[i].league_id);
                        }
                      }
                      // this.match_count = mc;
                      if (rn) rn["match_count"] = mc;
                      for (var i = 0; i < k; i++) {
                        if (!deleted.includes(ln[i].league_id)) {
                          // inserted_ids.push(ln[i].league_id);
                          inserted_array.push(ln[i]);
                          // inserted_object[ln[i].league_id] = ln[i];
                        }
                      }
                      this.$set(this.leagueList, e, inserted_array);
                    }, 200);
                  }
                });
              }
              // this.$set(this.leagueList, e, result.data.value);
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          this.loading.getLeagueList = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getName(p, e) {
      var name = e[p + this.language];
      if (name == null || name == "" || !name) {
        name = e[p + "en"];
      }
      return name;
    },
    getLogo(p, e) {
      return config.flagPath + p + "/" + e;
    },
    getBackground(e) {
      return config.tournamentBackgroundPath + e + "?v=********";
    },
    getMemberBetList(room) {
      if (![0, 1].includes(room.room_status)) {
        return;
      }

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        room_id: room.room_id,
        page_number: 1,
        page_size: 1000,
        target: "all",
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      this.betList = [];
      this.loading.getMemberBetList = true;
      service.memberBetList(config.tournamentUrl().memberbetlist, json).then(
        (result) => {
          this.loading.getMemberBetList = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              this.betList = result.data.value;
              EventBus.$emit("ROOM_BETLIST2", {
                room_id: room.room_id,
                data: this.betList,
              });
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          this.loading.getMemberBetList = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
  },
};
</script>
