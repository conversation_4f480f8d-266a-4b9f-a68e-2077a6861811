import config from "@/config";
import errors from "@/errors";
import Vue from "vue";

export default {
  loading: {
    getLiveTV: false,
    getAnnoucement: false,
    getPersonalMessage: false,
    getPersonalUnread: false,
    getMarqueeList: false
  },
  getAnnoucement(args) {
    const url = config.announceListUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getAnnoucement"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("datefrom" in args)) {
        feedback.status = errors.message.startDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("dateto" in args)) {
        feedback.status = errors.message.endDateRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!("an_type" in args)) {
        feedback.status = errors.message.anTypeRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.datefrom) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.dateto) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.an_type) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading.getNormalAnnouncement == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.getAnnoucement = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getAnnoucement = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getAnnoucement = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getPersonalMessage(args) {
    const url = config.personalListUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getPersonalMessage"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("datefrom" in args)) {
        feedback.status = errors.message.startDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("dateto" in args)) {
        feedback.status = errors.message.endDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.datefrom) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.dateto) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      // if (this.loading.getSpecialAnnouncement == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.getPersonalMessage = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getPersonalMessage = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getPersonalMessage = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getPersonalUnread(args) {
    const url = config.personalUnreadUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getPersonalUnread"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading.getPersonalMessage == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.getPersonalUnread = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getPersonalUnread = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getPersonalUnread = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getMarqueeList(args) {
    const url = config.marqueeListUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getMarqueeList"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading.getPersonalMessage == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.getMarqueeList = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getMarqueeList = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getMarqueeList = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  }
};
