<template lang="pug">
.new-betdetail(:class="{ changed: betslip.oddsChanged, 'grey': betslip.marketType != 3 }")
  template(v-if="racingList.includes(betslip.sportsType)")
    template(v-if="racingList1.includes(betslip.sportsType)")
      .team(v-if="['OU','OE'].includes(betslip.betType)")
        | {{ $t("m.GH_" + betslip.betDisplay.toUpperCase()) }}
      .team(v-else)
        | {{ $t("m.GH_" + betslip.betType + (betslip.ballDisplay == undefined ? '' : betslip.ballDisplay)) }}
      .new-oddsdetail
        span
          | &nbsp;@
        span.font-weight-bold.odds(:class="{ red: betslip.val < 0 }")
          | &nbsp;{{ betslip.val }}
    template(v-if="racingList2.includes(betslip.sportsType)")
      .team(v-if="['OU','OE'].includes(betslip.betType)")
          | {{ $t("m.GC_" + betslip.betDisplay.toUpperCase()) }}
      template(v-else)
        .team(v-if="['CS'].includes(betslip.betType)")
          | {{ betslip.betDisplay }}
        .team(v-else)
          | {{ $t("m.GC_" + betslip.betType + (betslip.ballDisplay == undefined ? '' : betslip.ballDisplay)) }}
        .new-oddsdetail
          span &nbsp;@
          span.font-weight-bold.odds(:class="{ red: betslip.val < 0 }") &nbsp;{{ betslip.val }}
    template(v-if="racingList3.includes(betslip.sportsType)")
      .team(v-if="['OU','OE'].includes(betslip.betType)")
        | {{ $t("m.GX_" + betslip.betDisplay.toUpperCase()) }}
      .team(v-else)
        | {{ $t("m.GX_" + betslip.betType + (betslip.ballDisplay == undefined ? '' : betslip.ballDisplay)) }}
      .new-oddsdetail
        span
          | &nbsp;@
        span.font-weight-bold.odds(:class="{ red: betslip.val < 0 }")
          | &nbsp;{{ betslip.val }}
  template(v-else)
    .team(
      :class="isBallDisplay == false ? '' : ((betslip.giving == 0 && betslip.homeAway == 2) ? 'red' : (betslip.giving == 1 && betslip.homeAway == 1) ? 'red' : '')")
      | {{ betslip.betDisplay }}
    .score.text-center.font-weight-bold(v-if="betslip.marketType == 3 && betslip.score != ''")
      | [{{ betslip.score }}]
    .new-oddsdetail
      span.font-weight-bold
        | &nbsp;{{ betslip.betType }}
      span.font-weight-bold
        | &nbsp;{{ ballDisplay }}
      span.font-weight-bold(v-if="['1X2OU', 'DCOU', 'OUOE'].includes(betslip.betType)")
        | {{ betslip.criteria2 }}
      span
        | &nbsp;@
      span.font-weight-bold.odds(:class="{ red: betslip.val < 0 }")
        | &nbsp;{{ betslip.val }}
</template>

<script>
import config from "@/config";
import naming from "@/library/_name";

export default {
  props: {
    betslip: {
      type: Object
    }
  },
  computed: {
    racingList() {
      return config.racingList;
    },
    racingList1() {
      return config.racingList1;
    },
    racingList2() {
      return config.racingList2;
    },
    racingList3() {
      return config.racingList3;
    },
    debug() {
      return config.debugMode;
    },
    betType() {
      return this.$t("m.BT_" + this.betslip.betType);
    },
    ballDisplay() {
      return naming.ballDisplay2(this.betslip.ballDisplay, this.betslip.giving, this.betslip.homeAway, this.betslip.betType, this);
    },
    isBallDisplay() {
      var result = this.ballDisplay;
      if (["HDP", "HDPH"].includes(this.betslip.betType)) {
        return result != null && result != "0";
      } else {
        return false;
      }
    }
  }
};
</script>