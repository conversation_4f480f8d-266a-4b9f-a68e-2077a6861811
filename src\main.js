import Vue from "vue";
import VueMeta from "vue-meta";
Vue.use(VueMeta);

import router from "@/router";
import store from "@/store";
import i18n from "@/i18n";
import App from "@/app.vue";

import Vuelidate from "vuelidate";
import VueResource from "vue-resource";
import VueSweetalert2 from "vue-sweetalert2";
import DateTimePicker from "vue-bootstrap-datetimepicker";
import numeral from "numeral";
import Snotify, { SnotifyPosition } from "vue-snotify";

import dayjs from "dayjs";
// import "dayjs/locale/zh-cn";
import utcPlugin from "dayjs/plugin/utc";
import timeZonePlugin from "dayjs/plugin/timezone";
import advancedFormat from "dayjs/plugin/advancedFormat";
// dayjs.locale("zh-cn"); // use locale globally

import helpers from "@/plugins/helpers";

// vue tour components
import VueTour from "vue-tour";
import "vue-tour/dist/vue-tour.css";
Vue.use(VueTour);

Vue.config.productionTip = process.env.NODE_ENV === "production" || false;
Vue.use(Vuelidate);
Vue.use(VueResource);
Vue.use(VueSweetalert2);
Vue.use(DateTimePicker);
Vue.use(Snotify, {
  toast: {
    position: SnotifyPosition.rightTop,
    showProgressBar: false,
  },
});

dayjs.extend(utcPlugin);
dayjs.extend(timeZonePlugin);
dayjs.extend(advancedFormat);
dayjs.tz.setDefault("Asia/Kuala_Lumpur");

// Vue.http.options.credentials = true;
Object.defineProperty(Vue.prototype, "$dayjs", { value: dayjs });
Object.defineProperty(Vue.prototype, "$numeral", { value: numeral });
// Vue.prototype.$log = console.log; // Removed for production

/* eslint-disable no-new */

router.beforeEach((to, from, next) => {
  var query = to.fullPath.match(/^\/$/) ? {} : { redirect: to.fullPath };
  var path = "/";
  if (
    to.matched.some((record) => {
      return record.meta.requiredLoggedIn;
    })
  ) {
    if (!store.getters.isLoggedIn) {
      if (next) {
        next({ path: path, query: query });
      }
      return;
    }
  }
  if (next) {
    try {
      next();
    } catch (err) {
      console.warn(err);
    }
  }
});

// store.dispatch("user/restore");
// Filtering Hours
store.dispatch("cache/setFilterMode", [0, 6].includes(new Date().getDay()) ? 1 : 0);

// Initialize Performance Monitoring
import performanceSetup from '@/utils/performanceSetup';
performanceSetup.init({
  enabled: process.env.NODE_ENV === 'development',
  autoReport: true,
  reportInterval: 30000, // Report every 30 seconds
  thresholds: {
    computed: 20,  // Warn if computed properties take >20ms
    watcher: 8,    // Warn if watchers take >8ms
    method: 15,    // Warn if methods take >15ms
  }
});
// Filtering mode logging removed for production

export const app = new Vue({
  router,
  store,
  i18n,
  mounted() {
    this.$store.dispatch("layout/setLanguage", this.$store.getters.language);
  },
  render: (h) => h(App),
}).$mount("#app");

// Vue.use(helpers, { $store: store, $router: router, $swal: Vue.swal, $i18n: i18n, $app: app });
// i18n.locale = process.env.VUE_APP_LANG;
Vue.use(helpers, { $i18n: i18n, $app: app });
