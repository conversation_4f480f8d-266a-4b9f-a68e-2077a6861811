<template lang="pug">
.main-item(v-if="show", :class="mainItemClass")
  small.d-none {{ source.matchId }}

  //- header
  template(v-if="source.ptype == 10")
    .hx-market(:id="source.id", @click="debouncedToggleGroup(source.id)")
      component(v-if="headerComponent", :is="headerComponent", :source="source")

  //- league
  league(v-if="shouldShowLeague", :source="source")

  //- odd layout
  template(v-if="source.ptype == 30")
    component(v-if="oddsComponent && hasDetails", :is="oddsComponent", :index="index", :source="source", :single="single")
</template>

<script>
import league from "@/components/desktop/main/xheader/league";

// HEADER
import hdpou from "@/components/desktop/main/xheader/hdpou";
import hdpoum from "@/components/desktop/main/xheader/hdpoum";
import hdpousl from "@/components/desktop/main/xheader/hdpousl";
import hdpouslm from "@/components/desktop/main/xheader/hdpouslm";
import hdpouoe from "@/components/desktop/main/xheader/hdpouoe";
import hdpouml from "@/components/desktop/main/xheader/hdpouml";
import hdpouft from "@/components/desktop/main/xheader/hdpouft";
import hdpouoeft from "@/components/desktop/main/xheader/hdpouoeft";
import hdpouoe1x2 from "@/components/desktop/main/xheader/hdpouoe1x2";
import hdpou1x2ft from "@/components/desktop/main/xheader/hdpou1x2ft";
import hdpouoeml from "@/components/desktop/main/xheader/hdpouoeml";
import hdpouoeml1x2 from "@/components/desktop/main/xheader/hdpouoeml1x2";
import hdpef from "@/components/desktop/main/xheader/hdpef";
import hdpml from "@/components/desktop/main/xheader/hdpml";
import hdpou4d from "@/components/desktop/main/xheader/hdpou4d";
import oxt from "@/components/desktop/main/xheader/oxt";
import cs from "@/components/desktop/main/xheader/cs";
import oe from "@/components/desktop/main/xheader/oe";
import dc from "@/components/desktop/main/xheader/dc";
import tg from "@/components/desktop/main/xheader/tg";
import htft from "@/components/desktop/main/xheader/htft";
import fglg from "@/components/desktop/main/xheader/fglg";
import orz from "@/components/desktop/main/xheader/orz";

// MMO layout
import hdpouMMO from "@/components/desktop/main/mmo/hdpouMMO";
import hdpouMMO2 from "@/components/desktop/main/mmo/hdpouMMO2";
import hdpouslMMO from "@/components/desktop/main/mmo/hdpouslMMO";

// NORMAL layout
import hdpouOdds from "@/components/desktop/main/xtable/hdpouOdds";
import hdpouslOdds from "@/components/desktop/main/xtable/hdpouslOdds";
import hdpouoeOdds from "@/components/desktop/main/xtable/hdpouoeOdds";
import hdpou1x2ftOdds from "@/components/desktop/main/xtable/hdpou1x2ftOdds";
import hdpouoe1x2Odds from "@/components/desktop/main/xtable/hdpouoe1x2Odds";
import hdpouftOdds from "@/components/desktop/main/xtable/hdpouftOdds";
import hdpouoeftOdds from "@/components/desktop/main/xtable/hdpouoeftOdds";
import hdpouoemlOdds from "@/components/desktop/main/xtable/hdpouoemlOdds";
import hdpouoemlOdds1 from "@/components/desktop/main/xtable/hdpouoemlOdds1";
import hdpouoemlOdds2 from "@/components/desktop/main/xtable/hdpouoemlOdds2";
import hdpouoemlOdds3 from "@/components/desktop/main/xtable/hdpouoemlOdds3";
import hdpouoeml1x2Odds from "@/components/desktop/main/xtable/hdpouoeml1x2Odds";
import hdpoumlOdds from "@/components/desktop/main/xtable/hdpoumlOdds";
import hdpmlOdds from "@/components/desktop/main/xtable/hdpmlOdds";
import hdp4dOdds from "@/components/desktop/main/xtable/hdp4dOdds";
import csOdds from "@/components/desktop/main/xtable/csOdds";
import oxtOdds from "@/components/desktop/main/xtable/oxtOdds";
import oeOdds from "@/components/desktop/main/xtable/oeOdds";
import dcOdds from "@/components/desktop/main/xtable/dcOdds";
import tgOdds from "@/components/desktop/main/xtable/tgOdds";
import htftOdds from "@/components/desktop/main/xtable/htftOdds";
import fglgOdds from "@/components/desktop/main/xtable/fglgOdds";
import orzOdds from "@/components/desktop/main/xtable/orzOdds";

export default {
  components: {
    league,
    hdpou,
    hdpoum,
    hdpousl,
    hdpouslm,
    hdpouoe,
    hdpouml,
    hdpouft,
    hdpouoeft,
    hdpouoe1x2,
    hdpou1x2ft,
    hdpouoeml,
    hdpouoeml1x2,
    hdpef,
    hdpml,
    hdpou4d,
    oxt,
    cs,
    oe,
    dc,
    tg,
    htft,
    fglg,
    orz,

    hdpouMMO,
    hdpouMMO2,
    hdpouslMMO,

    hdpouOdds,
    hdpoumlOdds,
    hdpouslOdds,
    hdpouoeOdds,
    hdpou1x2ftOdds,
    hdpouoe1x2Odds,
    hdpouftOdds,
    hdpouoeftOdds,
    hdpouoemlOdds,
    hdpouoemlOdds1,
    hdpouoemlOdds2,
    hdpouoemlOdds3,
    hdpouoeml1x2Odds,
    hdpmlOdds,
    hdp4dOdds,
    csOdds,
    oxtOdds,
    oeOdds,
    dcOdds,
    tgOdds,
    htftOdds,
    fglgOdds,
    orzOdds,
  },
  props: {
    index: {
      type: Number,
    },
    source: {
      type: Object,
      default() {
        return {};
      },
    },
    single: {
      type: Boolean,
      default: false,
    },
    lmc: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      timer: null,
      show: false,
      cachedGroupers: {},
      cachedElements: {},
    };
  },
  computed: {
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
    parlayMode() {
      return this.$store.state.cache.parlayMode;
    },
    menuX() {
      return this.$store.state.layout.menuX;
    },
    menu3() {
      return this.$store.state.layout.menu3;
    },
    pageType() {
      return this.$store.getters.pageDisplay.pageType;
    },
    mmoType() {
      return this.$store.getters.pageDisplay.mmoType;
    },
    selectLeague() {
      return this.$store.getters.selectLeague;
    },
    leagueFiltered() {
      return this.$store.state.layout.leagueFiltered;
    },
    
    // New computed properties for performance optimization
    mainItemClass() {
      if (this.source.ptype == 30) {
        return ['c-match', this.source.categoryId, this.source.groupId, 'collapse', 
                this.grouper(this.source.groupId) == false ? 'show' : ''];
      }
      return '';
    },
    
    shouldShowLeague() {
      return this.source.ptype == 20 && 
        !this.isSportInGroup('excludedSports', this.source.sportsId);
    },
    
    hasDetails() {
      return this.source.hasOwnProperty('details');
    },
    
    isHdpouBetType() {
      return this.source.betTypeId === 'hdpou';
    },
    
    headerComponent() {
      if (!this.source.betTypeId) return null;
      
      const { betTypeId, sportsId } = this.source;
      
      if (betTypeId === 'hdpou') {
        if (this.isSportInGroup('soccer', sportsId)) {
          if (this.mmoMode && this.menu3 != 'parlay') {
            if (this.pageType == 2) {
              return this.mmoType == 1 ? 'hdpou' : 'hdpoum';
            }
            return 'hdpouslm';
          }
          return this.pageType == 2 ? 'hdpou' : 'hdpousl';
        }
        
        if (this.isSportInGroup('basketball', sportsId)) return 'hdpouoe';
        if (this.isSportInGroup('tennis', sportsId)) return 'hdpouml';
        if (this.isSportInGroup('esports', sportsId)) return 'hdpouoeft';
        if (this.isSportInGroup('football', sportsId)) return 'hdpouoe1x2';
        if (this.isSportInGroup('volleyball', sportsId)) return 'hdpouft';
        if (this.isSportInGroup('baseball', sportsId)) return 'hdpml';
        if (this.isSportInGroup('lottery', sportsId)) return 'hdpou4d';
        if (this.isSportInGroup('otherSports', sportsId)) return 'hdpouoeml';
        if (this.isSportInGroup('hockey', sportsId)) return 'hdpouoeml1x2';
      }
      
      if (betTypeId === 'oxt') return 'oxt';
      if (betTypeId === 'cs') return 'cs';
      if (betTypeId === 'oe') return 'oe';
      if (betTypeId === 'dc') return 'dc';
      if (betTypeId === 'tg') return 'tg';
      if (betTypeId === 'htft') return 'htft';
      if (betTypeId === 'fglg') return 'fglg';
      if (betTypeId === 'orz') return 'orz';
      
      return null;
    },
    
    oddsComponent() {
      if (!this.source.betTypeId) return null;
      
      const { betTypeId, sportsId, marketId } = this.source;
      
      if (betTypeId === 'hdpou') {
        if (this.isSportInGroup('soccer', sportsId)) {
          if (this.mmoMode && this.menu3 != 'parlay') {
            if (this.pageType == 2) {
              return this.mmoType == 1 ? 'hdpouMMO' : 'hdpouMMO2';
            }
            return 'hdpouslMMO';
          }
          return this.pageType == 2 ? 'hdpouOdds' : 'hdpouslOdds';
        }
        
        if (this.isSportInGroup('basketball', sportsId)) return 'hdpouoeOdds';
        if (this.isSportInGroup('baseball', sportsId)) return 'hdpmlOdds';
        if (this.isSportInGroup('tennis', sportsId)) return 'hdpoumlOdds';
        if (this.isSportInGroup('volleyball', sportsId)) return 'hdpouftOdds';
        if (this.isSportInGroup('esports', sportsId)) return 'hdpouoeftOdds';
        if (this.isSportInGroup('football', sportsId)) return 'hdpouoe1x2Odds';
        if (this.isSportInGroup('otherSports', sportsId)) return 'hdpouoemlOdds';
        if (this.isSportInGroup('hockey', sportsId)) return 'hdpouoeml1x2Odds';
        if (marketId == 3 && this.isSportInGroup('cricket', sportsId)) return 'hdpouoemlOdds1';
        if (marketId != 3 && this.isSportInGroup('cricket', sportsId)) return 'hdpouoemlOdds2';
        if (this.isSportInGroup('lottery', sportsId)) return 'hdp4dOdds';
      }
      
      if (betTypeId === 'oxt') return 'oxtOdds';
      if (betTypeId === 'oe') return 'oeOdds';
      if (betTypeId === 'cs') return 'csOdds';
      if (betTypeId === 'dc') return 'dcOdds';
      if (betTypeId === 'tg') return 'tgOdds';
      if (betTypeId === 'htft') return 'htftOdds';
      if (betTypeId === 'fglg') return 'fglgOdds';
      if (betTypeId === 'orz') return 'orzOdds';
      
      return null;
    }
  },
  created() {
    // Pre-compute sports ID sets for faster lookups with more descriptive names
    this.sportIdSets = { 
      soccer: new Set([1]),                // For soccer (hdpou, hdpouMMO, etc.)
      basketball: new Set([2, 25]),        // For basketball (hdpouoe)
      baseball: new Set([6, 8, 13]),       // For baseball (hdpml)
      tennis: new Set([15, 19]),           // For tennis (hdpouml)
      esports: new Set([17, 18, 30]),      // For esports (hdpouoeft)
      football: new Set([23, 26, 27, 45, 46]), // For football (hdpouoe1x2)
      volleyball: new Set([29]),           // For volleyball (hdpouft)
      lottery: new Set([40]),              // For lottery (hdp4d)
      hockey: new Set([5]),                // For hockey (hdpouoeml1x2)
      cricket: new Set([20]),              // For cricket (special case with marketId)
      otherSports: new Set([3, 4, 7, 9, 10, 11, 12, 14, 16, 20, 21, 22, 24, 28, 31, 32, 33, 34, 35, 36, 37, 38, 39]), // For other sports
      excludedSports: new Set([41, 42, 43, 44, 47]) // For excluded sports
    };
    
    // Create debounced version of toggleGroup
    this.debouncedToggleGroup = this.debounce(this.toggleGroup, 100);
  },
  mounted() {
    if (this.source.marketId == 3 || this.menuX) {
      this.show = true;
    } else {
      if (this.$store.getters.menu1 == "today") {
        var timing = 500;
        if (this.$store.getters.menu2 == 1 && !["orz", "parlay"].includes(this.$store.getters.menu3)) {
          if (this.lmc > 10) {
            timing = Math.min(2500 + this.lmc * 5, 5000); // Cap at 5 seconds max
          }
        }
        
        // Use requestAnimationFrame for smoother rendering
        requestAnimationFrame(() => {
          this.timer = setTimeout(() => {
            this.show = true;
          }, timing);
        });
      } else {
        this.show = true;
      }
    }
  },
  beforeDestroy() {
    // Clean up timers to prevent memory leaks
    if (this.timer) {
      clearTimeout(this.timer);
    }
  },
  methods: {
    // Helper method for sports ID checks
    isSportInGroup(groupName, sportId) {
      return this.sportIdSets[groupName].has(sportId);
    },
    
    // Debounce function to prevent excessive calls
    debounce(func, wait) {
      let timeout;
      return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
      };
    },
    
    // Cache grouper results to avoid repeated DOM queries
    grouper(e) {
      if (!this.cachedGroupers) {
        this.cachedGroupers = {};
      }
      
      if (this.cachedGroupers[e] === undefined) {
        this.cachedGroupers[e] = $("#" + e).hasClass("collapsed");
      }
      return this.cachedGroupers[e];
    },
    
    toggleGroup(e) {
      // Cache DOM elements to avoid repeated queries
      if (!this.cachedElements) {
        this.cachedElements = {};
      }
      
      if (!this.cachedElements[e]) {
        this.cachedElements[e] = {
          elem: $("#" + e),
          ln: $(".c-league." + e),
          m: $(".c-match." + e)
        };
      }
      
      const { elem, ln, m } = this.cachedElements[e];
      
      if (elem.hasClass("collapsed")) {
        elem.removeClass("collapsed").attr("aria-expanded", "true");
        ln.removeClass("collapsed").attr("aria-expanded", "true");
        
        // Update cache
        this.cachedGroupers[e] = false;
        
        // Use requestAnimationFrame for smoother animations
        requestAnimationFrame(() => {
          setTimeout(() => {
            m.addClass("show");
          }, 100);
        });
      } else {
        elem.addClass("collapsed").attr("aria-expanded", "false");
        ln.addClass("collapsed").attr("aria-expanded", "false");
        
        // Update cache
        this.cachedGroupers[e] = true;
        
        // Use requestAnimationFrame for smoother animations
        requestAnimationFrame(() => {
          setTimeout(() => {
            m.removeClass("show");
          }, 100);
        });
      }
    }
  }
};
</script>
