.europeview .container {
    min-width: 1886px;
    max-width: 1886px !important;
}

@media (min-width: 1920px) {
    .europeview .container {
        min-width: 1874px;
        max-width: 1886px !important;
    }
}
  
@media (min-width: 2048px) {
    .europeview .container{
      min-width: 2078px;
      max-width: 2078px !important;
    }
}

.europeview .content .main {
    width: 1190px;
    min-width: 1190px;
    max-width: 1190px;
}
@media (min-width: 1920px) {
    .europeview .content .main {
      width: 1190px;
      min-width: 1190px;
      max-width: 1190px;
    }
}
  
@media (min-width: 2048px) {
    .europeview .content .main {
      width: 2000px;
      max-width: 2000px;
    }
}

.europeview header .topbar .main-nav, .europeview header .toolbar .news {
    width: 860px;
    min-width: 860px;
    max-width: 920px;
    /* width: 1190px;
    min-width: 1190px;
    max-width: 1190px; */
}
@media (min-width: 1920px) {
	.europeview header .topbar .main-nav, .europeview header .toolbar .news {
		width: 1190px;
		min-width: 1190px;
		max-width: 1190px;	
	}
}
@media (min-width: 2048px) {
	.europeview header .topbar .main-nav, .europeview header .toolbar .news {
		width: 1438px;
		min-width: 1438px;
		max-width: 1438px;	
	}
}
.europeview .hx-col-new {
    display: flex;
    align-items: center;
}
.europeview .hxs {
    margin: 0 auto;
    display: block;
}
.europeview .hx-table.hx-match .hx-rows {
    align-items: center;
}
.europeview .hx-table.hx-match .bet-value {
    height: 28px;
    line-height: 26px;
    border-radius: 3px;
    background-color: #E1E7F1;
    padding-right: 0;
    text-align: center;
    width: 58px;
}
.europeview .hx-table.hx-match.live .bet-value {
    background-color: #F6DFD8;
}
.europeview .hx-table.hx-match.live .bet-value.highlighted.up {
    background-color: #EDF2EE;
}
.europeview .hx-table .bet-value.lock {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #B9B9B9;
    color: #FFFFFF;
}
.europeview .hx-table.hx-match.live .bet-value.lock {
    background-color: #B9B9B9;
}
.w-60r {
    width: calc(100% - 60px);
}
.w-70r {
    width: calc(100% - 70px);
}
.w-80 {
    width: 80px;
    min-width: 80px;
    max-width: 80px;
}
.w-80r {
    width: calc(100% - 80px);
}
.w-165 {
    width: 165px;
    min-width: 165px;
    max-width: 165px;
}
.w-236 {
    width: 236px;
    min-width: 236px;
    max-width: 236px;
}
.w-250 {
    width: 250px;
    min-width: 250px;
    max-width: 250px;
}

.h-50 {
    height: 50px;
    min-height: 50px;
    max-height: 50px;
}
.europeview .dropdown-ou .dropdown-menu {
    padding: 0;
    border-radius: 3px 3px 0px 0px;
}
.europeview .hx-table.hx-match .dropdown-ou .bet-value.dropdown-toggle, .hx-table.hx-match .dropdown-ou .bet-value.dropdown-toggle:hover {
    background-color: transparent;
}
.europeview .dropdown-ou .dropdown-menu.short {
    min-width: 60px;
}
.europeview .dropdown-ou .dropdown-menu .dropdown-item {
    padding: 0 8px;
    line-height: 30px;
    font-size: 14px;
    color: #000;
}
.europeview .dropdown-ou .dropdown-menu .dropdown-item:focus, .dropdown-ou .dropdown-menu .dropdown-item:hover {
    background-color: #DCE6EF;
}
.europeview .hx-table.hx-match .hx-more-col .hx-cols {
    display: flex;
    align-items: center;
}
.europeview .bet-title {
    padding-left: 8px;
}
.europeview .bet-number {
    padding-left: 8px;
}
.europeview .content .main .single-match {
    background: #276FA8;
    border: 1px solid #276FA8;
}
.europeview .content .main .single-match .action-block {
    border-right: 1px solid #5991C1;
}