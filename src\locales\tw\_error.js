export default {
  OK: "យល់ព្រម",
  CLOSE: "គណនីត្រូវបានបិទ!",
  BLOCK: "គណនីត្រូវបានទប់ស្កាត់!",
  FREEZE: "គណនីត្រូវបានបាក់បែក!",
  SUSPEND: "គណនីរបស់អ្នកត្រូវបានផ្លាស់ប្តូរទៅជាមើលប៉ុណ្ណោះ។",
  passwordRequired: "ត្រូវការលេខសម្ងាត់!",
  usernameRequired: "ត្រូវការឈ្មោះអ្នកប្រើ!",
  incompletedRequest: "មានអ្វីមួយខុស...",
  unauthenticatedUser: "ការផ្ទៀងផ្ទាត់អ្នកប្រើមិនត្រឹមត្រូវទេ!",
  session_not_exists: "សម័យផុតកំណត់! សូមចូលឡើងវិញដើម្បីបន្ត...",
  session_expired: "សម័យផុតកំណត់! សូមចូលឡើងវិញដើម្បីបន្ត...",
  sessionExpired: "សម័យផុតកំណត់! សូមចូលឡើងវិញដើម្បីបន្ត...",
  invalidSession: "សម័យផុតកំណត់! សូមចូលឡើងវិញដើម្បីបន្ត...",
  accountIdRequired: "សម័យផុតកំណត់! សូមចូលឡើងវិញដើម្បីបន្ត...",
  NoUserAccessRight: "បានបដិសេធការអនុញ្ញាត! សូមទាក់ទងអ្នកជាន់ខ្ពស់របស់អ្នកដើម្បីបន្ត...",
  account_not_exists: "លេខសម្ងាត់មិនត្រឹមត្រូវ។",
  invalid_password: "លេខសម្ងាត់មិនត្រឹមត្រូវ។",
  secondary_account_exists: "ឈ្មោះហៅក្រៅរបស់អ្នកបានបង្កើតរួចហើយ។",
  systemError: "បញ្ហាក្នុងប្រព័ន្ធផ្ទៃក្នុង។",
  new_login_id_exists: "ឈ្មោះហៅក្រៅមិនត្រឹមត្រូវ សូមព្យាយាមបញ្ចូលឈ្មោះថ្មីផ្សេងទៀត។",
  loginLimit: "អ្នកបានចូលប្រើជាញឹកញាប់ពេក សូមព្យាយាមម្ដងទៀតក្រោយ 1 នាទី...",
  requestLimit: "អ្នកបានស្នើរសុំជាញឹកញាប់ពេក។",
  insufficient_balance: "ទឹកប្រាក់មិនគ្រប់គ្រាន់",
  insufficientBalance: "ទឹកប្រាក់មិនគ្រប់គ្រាន់",
  loginFailed: "ការចូលបរាជ័យ សូមព្យាយាមម្ដងទៀតនៅពេលក្រោយ...",
  requestFailed: "ការតភ្ជាប់របស់អ្នកមិនមានស្ថេរភាព។ សូមពិនិត្យបណ្ដាញរបស់អ្នកហើយសាកល្បងម្តងទៀត។",
  requestPending: "សំណើរនៅរងចាំ...",
  close: "គណនីត្រូវបានបិទ! សូមទាក់ទងអ្នកជាន់ខ្ពស់របស់អ្នកដើម្បីបន្ត...",
  suspend: "គណនីត្រូវបានផ្អាក! សូមទាក់ទងអ្នកជាន់ខ្ពស់របស់អ្នកដើម្បីបន្ត...",
  freeze: "គណនីត្រូវបានបាក់បែក! សូមទាក់ទងអ្នកជាន់ខ្ពស់របស់អ្នកដើម្បីបន្ត...",
  alphaNumOnly: "អនុញ្ញាតតែអក្សរនិងលេខប៉ុណ្ណោះ",
  isRequired: "សូមបញ្ចូល {fname}។",
  isMinValue: "{fname} ត្រូវមានតិចបំផុត {fvalue}។",
  isMaxValue: "{fname} មិនអាចលើស {fvalue} ទេ។",
  isMinLength: "{fname} ត្រូវមានយ៉ាងហោចណាស់ {fvalue} តួអក្សរ។",
  isMaxLength: "{fname} មិនអាចលើស {fvalue} តួអក្សរ។",
  isAlpha: "{fname} អនុញ្ញាតតែអក្សរប៉ុណ្ណោះ។",
  isAlphaNum: "{fname} អនុញ្ញាតតែអក្សរនិងលេខប៉ុណ្ណោះ។",
  isNumeric: "{fname} អនុញ្ញាតតែលេខប៉ុណ្ណោះ។",
  isEmail: "{fname} ត្រូវតែជាសារអ៊ីមែលត្រឹមត្រូវ។",
  isIpAddress: "{fname} ត្រូវតែជាអាសយដ្ឋាន IPv4 ត្រឹមត្រូវ។",
  isSameAs: "{fname} ត្រូវតែដូចគ្នានឹង {fname2}។",
  isUrl: "{fname} ត្រូវតែជាអាសយដ្ឋានបណ្តាញត្រឹមត្រូវ។",
  containAlphaNum: "{fname} ត្រូវតែមានអក្សរនិងលេខយ៉ាងហោចណាស់មួយ។",
  selectOption: "សូមជ្រើសរើស {fname}",
  notSameAs: "{fname} និង {fname2} មិនអាចដូចគ្នាបានទេ។",
  currPasswordRequired: "ត្រូវការលេខសម្ងាត់បច្ចុប្បន្ន!",
  newPasswordRequired: "ត្រូវការលេខសម្ងាត់ថ្មី!",
  confirmNewPasswordRequired: "ត្រូវការបញ្ជាក់លេខសម្ងាត់ថ្មី!",
  passwordsNotMatch: "លេខសម្ងាត់មិនដូចគ្នា!",
  nickNameRequired: "ត្រូវការឈ្មោះហៅក្រៅ!",
  startDateRequired: "ត្រូវការថ្ងៃចាប់ផ្ដើម!",
  endDateRequired: "ត្រូវការថ្ងៃបញ្ចប់!",
  pageSizeRequired: "ត្រូវការទំហំទំព័រ!",
  pageNumberRequired: "ត្រូវការលេខទំព័រ!",
  sportsTypeRequired: "ត្រូវការប្រភេទកីឡា!",
  workingDateRequired: "ត្រូវការថ្ងៃធ្វើការ!",
  leagueIdRequired: "ត្រូវការ League ID!",
  isOutrightRequired: "ត្រូវការព័ត៌មាន Is outright!",
  duplicate_debit_record: "សូមរងចាំខណៈពេលកំពុងធ្វើបច្ចុប្បន្នភាពអត្រាកីឡា...",
  invalidOddsBall: "សូមរងចាំខណៈពេលកំពុងធ្វើបច្ចុប្បន្នភាពអត្រាកីឡា...",
  oddsDisplayInvalid: "សូមរងចាំខណៈពេលកំពុងធ្វើបច្ចុប្បន្នភាពអត្រា...",
  oddsIsUpdating: "អត្រាកំពុងធ្វើបច្ចុប្បន្នភាព...",
  unableToGetBalanceAtTheMoment: "បញ្ហា Server ផ្ទៃក្នុង។",
  atLeastTwoMatchToBetParlay: "យ៉ាងហោចណាស់ត្រូវភ្ជាប់ 2 ប្រកួតដើម្បីភ្នាល់ parlay។",
  atLeastThreeMatchToBetParlay: "យ៉ាងហោចណាស់ត្រូវភ្ជាប់ 3 ប្រកួតដើម្បីភ្នាល់ parlay។",
  maxParlayTicket: "ឈានដល់ចំនួនសំបុត្រអតិបរមាសម្រាប់ parlay!",
  matchHide: "ការប្រកួតនេះមិនមានទេ សូមព្យាយាមការប្រកួតផ្សេងទៀត។ (318)",
  matchNotAllowBet: "ការប្រកួតនេះមិនអនុញ្ញាតអោយភ្នាល់ទេ។ សូមព្យាយាមការប្រកួតផ្សេងទៀត។ (311)",
  matchNotActive: "ការប្រកួតនេះមិនមានទេ សូមព្យាយាមការប្រកួតផ្សេងទៀត។ (308)",
  invalidBets: "ទីផ្សារនេះមិនមានទេ សូមព្យាយាមទីផ្សារផ្សេងទៀត។ (340)",
  invalidBet: "ទីផ្សារនេះមិនមានទេ សូមព្យាយាមទីផ្សារផ្សេងទៀត។ (340)",
  invalidBetType: "ទីផ្សារនេះមិនមានទេ សូមព្យាយាមទីផ្សារផ្សេងទៀត។ (314)",
  invalidBetTeam: "ទីផ្សារនេះមិនមានទេ សូមព្យាយាមទីផ្សារផ្សេងទៀត។ (328)",
  invalidOdds: "ទីផ្សារនេះមិនមានទេ សូមព្យាយាមទីផ្សារផ្សេងទៀត។ (313)",
  betOverMaxPerMatch: "ការភ្នាល់របស់អ្នកលើសពីកម្រិតអតិបរមាត្រឹមតែប្រកួតនេះ។",
  betOverMax: "ការភ្នាល់របស់អ្នកលើសពីកម្រិតអតិបរមា។",
  betLowerMin: "ការភ្នាល់របស់អ្នកទាបជាងអប្បបរមា។",
  betOverMaxPayout: "ការភ្នាល់របស់អ្នកលើសពីចំនួនទឹកប្រាក់ចេញអតិបរមា។",
  memberInactive: "គណនីនេះមិនសកម្មទេ សូមធ្វើការត្រួតពិនិត្យ និងទាក់ទងអ្នកខាងលើរបស់អ្នក",
  memberCommissionNotSetup: "ការបែងចែកកម្រៃជើងសារមិនបានកំណត់ទេ",
  betOverMaxBet: "ចំនួនភ្នាល់លើសដែនកំណត់ សូមពិនិត្យដែនកំណត់ភ្នាល់របស់អ្នក",
  invalidCurrency: "រូបិយប័ណ្ណមិនត្រឹមត្រូវសម្រាប់ភ្នាល់",
  betOverLimit: "ចំនួនភ្នាល់លើសដែនកំណត់",
  selectLeague: "សូមជ្រើសរើសយ៉ាងហោចណាស់ ១ ព្រឹត្តិការណ៍",
  game_maintenance: "ហ្គេមកំពុងធ្វើការថែទាំ",
  betTypeRequired: "ត្រូវការប្រភេទភ្នាល់",
  betAmountRequired: "ត្រូវការចំនួនភ្នាល់",
  invalidBetAmount: "ចំនួនភ្នាល់មិនត្រឹមត្រូវ",
  currencyNotSupported: "រូបិយប័ណ្ណមិនគាំទ្រ",
  forbiddenAccess: "មិនអាចចូលដំណើរការ",
  connectionFailed: "API របស់អ្នកប្រតិបត្តិមិនបានឆ្លើយតប",
  liveCasinoDisabled: "Live Casino ត្រូវបានបិទ",
  lotteryDisabled: "លទ្ធភាពឆ្នោតត្រូវបានបិទ",
  MiniGameDisabled: "ហ្គេមតូចត្រូវបានបិទ",
  EsportsDisabled: "ហ្គេម E-Sports ត្រូវបានបិទ",
  login_id_not_exists: "ព័ត៌មានចូលមិនត្រឹមត្រូវ សូមព្យាយាមឡើងវិញ",
  noMatchSelected: "សូមជ្រើសយ៉ាងហោចណាស់ ១ ព្រឹត្តិការណ៍",
  invalidRoomRate: "អត្រាបន្ទប់មិនត្រឹមត្រូវ",
  invalidRoomLimit: "ដែនកំណត់បន្ទប់មិនត្រឹមត្រូវ",
  invalidRoomPassword: "PIN មិនត្រឹមត្រូវ។ PIN ត្រូវមាន ៦ លេខ",
  InvalidRoomPassword: "PIN មិនត្រឹមត្រូវ។ PIN ត្រូវមាន ៦ លេខ",
  invalidPassword: "ពាក្យសម្ងាត់មិនត្រឹមត្រូវ",
  InvalidPassword: "ពាក្យសម្ងាត់មិនត្រឹមត្រូវ",
  roomPasswordRequired: "ត្រូវការពាក្យសម្ងាត់ (PIN)",
  minMatch: "ត្រូវជ្រើសយ៉ាងហោចណាស់ ៣ ប្រកួត",
  maxMatch: "ជ្រើសរើសបានច្រើនបំផុត {max} ប្រកួត",
  roomIdRequired: "ត្រូវការបញ្ជាក់លេខបន្ទប់",
  roomTypeRequired: "ត្រូវការបញ្ជាក់ប្រភេទបន្ទប់",
  sessionTokenRequired: "ត្រូវការបញ្ជាក់សេស្យុង",
  matchDateRequired: "ត្រូវការបញ្ជាក់កាលបរិច្ឆេទប្រកួត",
  matchRequired: "ត្រូវការជ្រើសប្រកួត",
  roomLimitRequired: "ត្រូវការកំណត់ចំនួនអ្នកលេង",
  roomRateRequired: "ត្រូវការបញ្ជាក់អត្រាចូលរួម",
  minRoomRate: "ថ្លៃចូលត្រូវមានយ៉ាងហោចណាស់ 10",
  maxRoomRate: "ថ្លៃចូលត្រូវមានយ៉ាងច្រើនបំផុត 1000",
  inRoomLimit: "ចំនួនអ្នកលេងត្រូវមានយ៉ាងហោចណាស់ 3",
  maxRoomLimit: "ចំនួនអ្នកលេងត្រូវមានយ៉ាងច្រើនបំផុត 30",
  invalidRoomType: "ប្រភេទបន្ទប់មិនត្រឹមត្រូវ",
  invalidMatch: "ប្រកួតមិនត្រឹមត្រូវ",
  dataError: "បញ្ហាទិន្នន័យ",
  MemberExists: "សមាជិកមានរួចហើយ",
  invalidMember: "សមាជិកមិនត្រឹមត្រូវ",
  invalidleague: "ព្រឹត្តិការណ៍មិនត្រឹមត្រូវ",
  "createRoomFailed-1": "បរាជ័យក្នុងការបង្កើតបន្ទប់ ១",
  "createRoomFailed-2": "បរាជ័យក្នុងការបង្កើតបន្ទប់ ២",
  "createRoomFailed-3": "បរាជ័យក្នុងការបង្កើតបន្ទប់ ៣",
  invalidOperator: "ប្រតិបត្តិមិនត្រឹមត្រូវ",
  roomFull: "បន្ទប់ពេញរួចហើយ",
  memberExists: "សមាជិកមានរួចហើយ",
  "joinFailed-1": "បរាជ័យក្នុងការចូលបន្ទប់ ១",
  "joinFailed-2": "បរាជ័យក្នុងការចូលបន្ទប់ ២",
  IPJoinedBefore: "IP នេះបានចូលរួចហើយ",
  memberJoinedBefore: "អ្នកបានចូលរួចហើយ សូមចងចាំថាបន្ទប់សាធារណៈឥតគិតថ្លៃអាចចូលបានម្តងតែប៉ុណ្ណោះ",
  roomNotExists: "បន្ទប់មិនមានទេ",
  roomIsPublicFree: "បន្ទប់នេះជាបន្ទប់សាធារណៈឥតគិតថ្លៃ",
  roomEndedOrCancelled: "បន្ទប់បានបញ្ចប់ឬបានលុបចោល",
  roomEnded: "បន្ទប់បានបញ្ចប់រួចហើយ",
  matchLowerMin: "មានបញ្ហាចំនួនប្រកួតក្រោមអប្បបរមា",
  matchOverMax: "មានបញ្ហាចំនួនប្រកួតលើសអតិបរមា",
  pointsOverLimit: "ពិន្ទុលើសដែនកំណត់",
  roomOverMax: "បន្ទប់លើសដែនកំណត់",
  invalidRoom: "បន្ទប់មិនត្រឹមត្រូវ",
  roomLimitOverMax: "ដែនកំណត់បន្ទប់លើសអតិបរមា",
  rateLowerMin: "អត្រាតិចជាងអប្បបរមា",
  rateOverMax: "អត្រាលើសអតិបរមា",
  liveMatchNotAllowed: "មិនអនុញ្ញាតឲ្យភ្នាល់ប្រកួតផ្ទាល់",
  roomLimitLowerMin: "ដែនកំណត់បន្ទប់ក្រោមអប្បបរមា",
  roomClosed: "បន្ទប់បានបិទ",
  tournamentLeagueExists: "ព្រឹត្តិការណ៍ទាំងនេះមានរួចហើយ",
  roomExists: "មិនអាចបង្កើតបន្ទប់ឯកជនច្រើនជាង១បានទេ",
  RoomExists: "មិនអាចបង្កើតបន្ទប់ឯកជនច្រើនជាង១បានទេ",
  betExists: "ការភ្នាល់មានរួចហើយ",
  betNotExists: "សូមដាក់ការភ្នាល់ត្រឹមត្រូវដើម្បីចូលរួមក្នុងការប្រកួត",
  roomClosedNotAllowed: "មិនអាចបិទបន្ទប់បានទេ",
  tournamentMatchExists: "ការប្រកួតនៅក្នុងព្រឹត្តិការណ៍មានរួចហើយ",
  account_id_required: "ត្រូវការបញ្ជាក់លេខគណនី",
  agent_comm_rate_required: "ត្រូវការបញ្ជាក់អត្រាកម្រៃជើងសារ",
  agent_id_required: "ត្រូវការបញ្ជាក់លេខអាជ្ញាធរ",
  bet_amount_required: "ត្រូវការបញ្ជាក់ចំនួនភ្នាល់",
  bet_id_required: "ត្រូវការបញ្ជាក់លេខការភ្នាល់",
  credit_amount_required: "ត្រូវការបញ្ជាក់ចំនួនប្រាក់បញ្ចូល",
  currency_required: "ត្រូវការបញ្ជាក់រូបិយប័ណ្ណ",
  debit_amount_required: "ត្រូវការបញ្ជាក់ចំនួនប្រាក់ដក",
  duplicate_credit_record: "កំណត់ត្រាបញ្ចូលប្រាក់ស្ទួន",
  master_comm_rate_required: "ត្រូវការអត្រាកម្រៃជើងសាររបស់Master",
  master_id_required: "ត្រូវការបញ្ជាក់លេខ Master",
  member_id_required: "ត្រូវការបញ្ជាក់លេខសមាជិក",
  member_wallet_not_exists: "កាបូបសមាជិកមិនមានទេ",
  permission_denied: "មិនមានសិទ្ធិចូលដំណើរការ",
  rate_required: "ត្រូវការបញ្ជាក់អត្រា",
  result_exists: "លទ្ធផលមានរួចហើយ",
  room_id_required: "ត្រូវការបញ្ជាក់លេខបន្ទប់",
  s_senior_comm_rate_required: "ត្រូវការអត្រាកម្រៃជើងសារ Super Senior",
  s_senior_id_required: "ត្រូវការបញ្ជាក់លេខ Super Senior",
  senior_comm_rate_required: "ត្រូវការអត្រាកម្រៃជើងសារ Senior",
  senior_id_required: "ត្រូវការបញ្ជាក់លេខ Senior",
  shareholder_comm_rate_required: "ត្រូវការអត្រាកម្រៃជើងសារ Shareholder",
  shareholder_id_required: "ត្រូវការបញ្ជាក់លេខ Shareholder",
  transfer_id_required: "ត្រូវការបញ្ជាក់លេខផ្ទេរ",
  minBetAmount: "បញ្ហាចំនួនភ្នាល់អប្បបរមា",
  tournamentDisabled: "ការប្រកួតត្រូវបានបិទ សូមទាក់ទងអ្នកខាងលើរបស់អ្នក",
  marketTypeRequired: "ត្រូវការបញ្ជាក់ប្រភេទទីផ្សារ",
  invalidMarketType: "ប្រភេទទីផ្សារមិនត្រឹមត្រូវ",
  marketTypeChanged: "បង្កាន់ដៃការភ្នាល់ត្រូវបានលុបចេញដោយសារប្រភេទទីផ្សារបានផ្លាស់ប្ដូរ។<br />សូមជ្រើសឡើងវិញពីទីផ្សារ",
  internalServerError: "បញ្ហាម៉ាស៊ីនមេខាងក្នុង!",
  refundTest: "ការធ្វើតេស្តការសងប្រាក់!",
  matchChangeTime: "បន្ទប់បានបិទរួចហើយ",
  changeStakeMin: "ចំនួនភ្នាល់ត្រូវបានផ្លាស់ប្ដូរតាមអប្បបរមា",
  changeStakeMax: "ចំនួនភ្នាល់ត្រូវបានផ្លាស់ប្ដូរតាមអតិបរមា",
};
