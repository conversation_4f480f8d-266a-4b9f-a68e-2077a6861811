import jwt from "jsonwebtoken";
import config from "@/config";
import errors from "@/errors";
import Vue from "vue";

export default {
  loading: {
    doLogin: false,
    reLogin: false,
    getBalance: false,
    setSettings: false
  },

  internalLogin(url, context, user, isWhiteLabel, secure) {
    const feedback = {
      success: false,
      status: errors.login.failed
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (isWhiteLabel) {
        if (user.length <= 0) {
          feedback.status = errors.login.usernameRequired;
          reject(feedback);
          canRequest = false;
        }
      } else {
        if (!("username" in user)) {
          feedback.status = errors.login.usernameRequired;
          reject(feedback);
          canRequest = false;
        }
        if (!("password" in user)) {
          feedback.status = errors.login.passwordRequired;
          reject(feedback);
          canRequest = false;
        }
        if (user.username.length <= 0) {
          feedback.status = errors.login.usernameRequired;
          reject(feedback);
          canRequest = false;
        }
        if (user.password.length <= 0) {
          feedback.status = errors.login.passwordRequired;
          reject(feedback);
          canRequest = false;
        }
      }

      if (this.loading.doLogin == true) {
        feedback.status = errors.request.processing;
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.doLogin = true;
        var body = {};
        if (isWhiteLabel) {
          body = {
            token: user
          };
        } else {
          body = {
            account_id: user.username,
            password: user.password
          };
        }
        var data = body;
        if (secure === true) {
          data = jwt.sign(body, config.whlmSkr6Ou);
        }
        Vue.http.post(url, data).then(
          res => {
            this.loading.doLogin = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                var account = null;
                try {
                  account = res.data.member_profile;
                  if (account) {
                    if (account.hasOwnProperty("player_info") && account.hasOwnProperty("player_wallet") && account.hasOwnProperty("player_bet_limit")) {
                      account.player_info = account.player_info[0];
                      account.player_wallet = account.player_wallet[0];
                      var b = {};
                      for (var n in account.player_bet_limit) {
                        b[account.player_bet_limit[n].a] = {
                          min_bet: account.player_bet_limit[n].b,
                          max_bet: account.player_bet_limit[n].c,
                          max_payout: account.player_bet_limit[n].d
                        };
                      }
                      account.player_bet_limit = null;
                      account.player_bet_limit = b;
                      context.commit("updateAccount", account);

                      resolve(feedback);
                    } else {
                      context.commit("deleteAccount");
                      feedback.success = false;
                      feedback.status = errors.login.failed;
                      reject(feedback);
                    }
                  } else {
                    context.commit("deleteAccount");
                    feedback.success = false;
                    feedback.status = errors.login.failed;
                    reject(feedback);
                  }
                } catch (error) {
                  // Failed to login
                  context.commit("deleteAccount");
                  feedback.success = false;
                  feedback.status = errors.login.failed;
                  reject(feedback);
                }
              } else {
                context.commit("deleteAccount");
                reject(feedback);
              }
            } else {
              // Response is empty...
              context.commit("deleteAccount");
              reject(feedback);
            }
          },
          err => {
            this.loading.doLogin = false;
            context.commit("deleteAccount");
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },

  doLaunch(context, user) {
    const url = config.authLoginUrl();
    return this.internalLogin(url, context, user, true, false);
  },

  doSwitch(context, user) {
    const url = config.switchLoginUrl();
    return this.internalLogin(url, context, user, false, false);
  },

  doLogin(context, user) {
    const url = config.loginUrl();
    return this.internalLogin(url, context, user, false, false);
    // const url = config.secureLoginUrl();
    // return this.internalLogin(url, context, user, false, true);
  },

  reLogin(context) {
    const url = config.getProfileUrl();
    const feedback = {
      success: false,
      status: errors.session.invalidSession
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!context.state.account) {
        reject(feedback);
        canRequest = false;
      }

      if (!context.state.account.player_info) {
        reject(feedback);
        canRequest = false;
      }

      if (!context.state.account.player_info) {
        reject(feedback);
        canRequest = false;
      }

      if (!context.state.account.player_info.account_id) {
        reject(feedback);
        canRequest = false;
      }

      if (!context.state.account.player_info.session_token) {
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        const body = {
          account_id: context.state.account.player_info.account_id,
          session_token: context.state.account.player_info.session_token
        };

        this.loading.reLogin = true;
        Vue.http.post(url, body).then(
          res => {
            this.loading.reLogin = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                var account = null;
                try {
                  account = res.data.member_profile;
                  if (account) {
                    if (account.hasOwnProperty("player_info") && account.hasOwnProperty("player_wallet") && account.hasOwnProperty("player_bet_limit")) {
                      account.player_info = account.player_info[0];
                      account.player_wallet = account.player_wallet[0];
                      var b = {};
                      for (var n in account.player_bet_limit) {
                        b[account.player_bet_limit[n].a] = {
                          min_bet: account.player_bet_limit[n].b,
                          max_bet: account.player_bet_limit[n].c,
                          max_payout: account.player_bet_limit[n].d
                        };
                      }
                      account.player_bet_limit = null;
                      account.player_bet_limit = b;
                      context.commit("updateAccount", account);
                      // console.log(account);
                      resolve(feedback);
                    } else {
                      context.commit("deleteAccount");
                      feedback.success = false;
                      feedback.status = errors.login.failed;
                      reject(feedback);
                    }
                  } else {
                    context.commit("deleteAccount");
                    feedback.success = false;
                    feedback.status = errors.login.failed;
                    reject(feedback);
                  }
                } catch (error) {
                  // Failed to login
                  context.commit("deleteAccount");
                  feedback.success = false;
                  feedback.status = errors.login.failed;
                  reject(feedback);
                }
              } else {
                context.commit("deleteAccount");
                reject(feedback);
              }
            } else {
              // Response is empty...
              context.commit("deleteAccount");
              reject(feedback);
            }
          },
          err => {
            this.loading.reLogin = false;
            context.commit("deleteAccount");

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },

  getBalance(context) {
    const operator_type = context.state.account.player_info.operator_type;
    const parent_id = context.state.account.player_info.parent_id;
    // console.log("operator_type", operator_type);
    // console.log("parent_id", parent_id);
    var url = config.getBalanceUrl();

    if (operator_type && operator_type == 2) {
      url = config.getVBalanceUrl();
    }
    const feedback = {
      success: false,
      status: errors.session.invalidSession
    };

    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!context.state.account) {
        reject(feedback);
        canRequest = false;
      }

      if (this.loading.getBalance == true) {
        feedback.status = errors.request.processing;
        canRequest = false;
      }

      if (canRequest == true) {
        const body = {
          account_id: context.state.account.player_info.account_id,
          session_token: context.state.account.player_info.session_token,
          parent_id: parent_id
        };

        this.loading.getBalance = true;
        Vue.http.post(url, body).then(
          res => {
            this.loading.getBalance = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                if (res.data != null) {
                  context.commit("updateBalance", res.data);
                }

                if (res.data.retrieve_profile) {
                  context.dispatch("reLogin").then(
                    res => {
                      if (!res.success) {
                        if (res.status != "no_changes") {
                          Vue.prototype.$helpers.handleFeedback(err.status);
                        }
                      }
                    },
                    err => {
                      if (!err.success) {
                        if (err.status != "no_changes") {
                          Vue.prototype.$helpers.handleFeedback(err.status);
                        }
                      }
                    }
                  );
                }
                // Successfully response
                resolve(feedback);
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getBalance = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },

  setSettings(context, payload) {
    const url = config.setSettingsUrl();
    const feedback = {
      success: false,
      status: errors.session.invalidSession
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!context.state.account) {
        reject(feedback);
        canRequest = false;
      }

      if (!context.state.account.player_info) {
        reject(feedback);
        canRequest = false;
      }

      if (!context.state.account.player_info) {
        reject(feedback);
        canRequest = false;
      }

      if (!context.state.account.player_info.account_id) {
        reject(feedback);
        canRequest = false;
      }

      if (!context.state.account.player_info.session_token) {
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        var body = payload;
        body.account_id = context.state.account.player_info.account_id;
        body.session_token = context.state.account.player_info.session_token;

        this.loading.setSettings = true;
        Vue.http.post(url, body).then(
          res => {
            this.loading.setSettings = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                resolve(feedback);
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.setSettings = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  }
};
