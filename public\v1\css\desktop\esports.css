@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap");

.esports-main {
  width: 100%;
  min-width: 860px;
  /* max-width: 920px; */
  display: inline-block;
  position: relative;
  font-family: "Open Sans", sans-serif;
}

/* @media (min-width: 1920px) {
  .esports-main {
    width: 1190px;
    min-width: 1190px;
    max-width: 1190px;
  }
}

@media (min-width: 2048px) {
  .esports-main {
    width: 2000px;
    max-width: 2000px;
  }
} */
.esports-page-wrap {
  position: relative;
  min-height: 400px;
  color: #000000;
}

.live-wrapper {
  width: 100%;
}

.esports-live {
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  background-color: #05283d;
  padding-left: 8px;
  display: flex;
  background-image: url(/images/esports/bg-line.png), url(/images/esports/bg-line.png);
  background-position: top center, bottom center;
  background-repeat: no-repeat, no-repeat;
}

.live-now {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 6px 10px;
  background-color: #C11A00 !important;
  border-radius: 30px !important;
  font-weight: 700;
  color: #fff;
}

.live-now img {
  width: 19px;
}

.live-now span {
  font-size: 13px;
  margin: 0 6px;
}

.live-now .fa-circle {
  font-size: 7px;
}

.live-game {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 700;
}

.game-dash {
  color: #9299b9;
}

.game-characters {
  display: flex;
  margin: 0 16px;
}

.characters-name {
  text-transform: capitalize;
}

.characters-vs {
  color: #dc5862;
  margin: 0 12px;
}

.live-watch {
  text-transform: capitalize;
  font-size: 12px;
  background-color: #dc5862;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  padding: 5px 8px;
  display: flex;
  align-content: center;
  cursor: pointer;
  font-weight: 600;
}

.live-watch .fa-play-circle,
.live-watch .fa-times-circle {
  font-size: 16px;
}

.icon-result {
  display: flex;
  justify-content: flex-end;
  padding: 7px 6px;
}

.icon-result img {
  border: 1px solid #FFFFFF26;
  padding: 5px 6px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  background-color: #145693;
  cursor: pointer;
}

.esports-wrapper {
  position: relative;
  font-size: 12px;
}

.esports {
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
}

.esports-game-thumb {
  padding: 3px;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  cursor: pointer;
}

.esports-game-thumb img {
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
}

.esports-heroes {
  height: 305px;
  position: relative;
  border-radius: 5px 5px 0 0;
  overflow: hidden;
}

.esports-heroes iframe {
  width: 540px;
  height: 100%;
  margin: 0 auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.esports-heroes.speedway {
  background: url(/images/esports/video-bg/video-bg-speedway.png) no-repeat;
}

.esports-heroes.greyhound {
  background: url(/images/esports/video-bg/video-bg-greyhound.png) no-repeat;
}

.esports-heroes.kof {
  background: url(/images/esports/video-bg/video-bg-kof.png) no-repeat;
}

.esports-heroes.ufc {
  background: url(/images/esports/video-bg/video-bg-ufc.png) no-repeat;
}

.esports-heroes.mk {
  background: url(/images/esports/video-bg/video-bg-mk.png) no-repeat;
}

.logo-esports {
  padding-top: 16px;
}

.logo-esports img {
  margin: 0 auto;
  width: 195px;
}

.opponents-pic.move-down {
  position: relative;
  top: 16px;
}

.vs-pic img {
  width: 59px;
}

.opponents {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 16px;
  padding-bottom: 36px;
}

.ufc .opponents {
  padding-top: 6px;
}

.mk .opponents {
  padding-top: 6px;
}

.hero-name {
  text-transform: uppercase;
  font-size: 16px;
  text-align: center;
  font-weight: 700;
  letter-spacing: 1px;
  width: 208px;
  height: 38px;
  line-height: 38px;
}

.hero-name {
  width: 208px;
  height: 38px;
  line-height: 38px;
  margin-top: -20px;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.9);
}

.ufc .hero-name {
  margin-top: -10px;
}

.mk .hero-name {
  margin-top: -10px;
}

.hero01 {
  background: url(/images/esports/bg-hero01.png) no-repeat;
}

.hero02 {
  background: url(/images/esports/bg-hero02.png) no-repeat;
}

.pulse {
  animation: pulse 0.5s infinite ease-in-out alternate;
}

@keyframes pulse {
  from {
    transform: scale(0.95);
  }

  to {
    transform: scale(1);
  }
}

#watch-now {
  display: none;
  width: 505px;
  margin: 0 auto;
}

#button-close {
  display: none;
}

.esports-item {
  width: 100%;
}

.esports-pointer {
  cursor: pointer;
}

.bg-01 {
  background-color: #E1E7F1;
}

.text-color01 {
  color: #000;
  font-weight: 600;
}

.border-01 {}

.e-win {
  color: greenyellow;
}

.e-more {
  color: #276FA8;
  width: 100%;
  text-align: center;
  line-height: 18px;
  display: block;
  border-radius: 0;
  cursor: pointer;
  border-radius: 2px;
  font-weight: 700;
}

.fa-chevron-circle-down,
.fa-chevron-circle-up {
  -webkit-transition: 0.3s -webkit-transform ease-in-out;
  transition: 0.3s -webkit-transform ease-in-out;
  -o-transition: 0.3s transform ease-in-out;
  transition: 0.3s transform ease-in-out;
  transition: 0.3s transform ease-in-out, 0.3s -webkit-transform ease-in-out;
  font-size: 13px;
}

.collapsed .fa-chevron-circle-down,
.collapsed .fa-chevron-circle-up {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.esports-head {
  font-size: 11px;
  font-weight: 600;
}

.esports-body {
  font-weight: 600;
}

.esports-row.game-bottom-line .e-event.horiz .e-date {
  color: #fff;
}

.esports-row.game-bottom-line .e-event.horiz .e-time {
  color: #F6C344;
}

.esports-row.game-bottom-line .e-event.horiz .e-no {
  color: #fff;
}

.esports-row {
  width: 100%;
  display: flex;
  color: #000000;
  -webkit-border-radius: 3px 3px 0 0;
  border-radius: 3px 3px 0 0;
  overflow: hidden;
}

.esports-body .esports-row.single {
  margin-top: 4px;
}

.esports-gamelist {
  margin-top: 4px;
  background-color: #fff;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  overflow: hidden;
}

.esports-gamelist.live {
  border: 1px solid #D5AB9E;
}

.esports-gamelist.live .game-top-line {
  background: url(/images/market-head-bg-live.png) no-repeat;
  background-size: cover;
}

.esports-gamelist:last-child {
  margin-bottom: 4px;
}

.esports-row.bottom-line {
  background: url(/images/market-head-bg.png) no-repeat;
  background-size: cover;
}

.esports-gamelist .collapse {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.esports-head .esports-row .esports-cell {
  min-height: 36px;
}

.esports-cell {
  min-height: 50px;
  align-items: center;
  display: flex;
  justify-content: center;
  margin: 2px 0;
}

.esports-cell .text-white {
  color: #000000 !important;
}

.e-time {
  color: #4F85B4;
}

.e-countdown {
  margin-left: 6px;
}

.fa-stopwatch {
  color: #dc5862;
  font-size: 14px;
}

.bg-hero01-inner {
  background: transparent linear-gradient(90deg, #155693 0%, #1A5F9E 100%) 0% 0% no-repeat padding-box;
  justify-content: flex-end;
}

.bg-hero02-inner {
  background: transparent linear-gradient(90deg, #F55453 0%, #B24342 100%) 0% 0% no-repeat padding-box;
  justify-content: flex-start;
}

.bg-hero01-inner,
.bg-hero02-inner {
  box-shadow: inset 0px 0px 10px #FFFFFF80;
  margin-bottom: 1px;
  text-transform: capitalize;
}

.bg-hero01-inner img,
.bg-hero02-inner img {
  width: 62px;
}

.vs-small img {
  width: 22px;
}

.w-30 {
  width: 30px;
  min-width: 30px;
  max-width: 30px;
}

.w-40 {
  width: 40px;
  min-width: 40px;
  max-width: 40px;
}

.w-50px {
  width: 50px;
  min-width: 50px;
  max-width: 50px;
}

.w-60 {
  width: 60px;
  min-width: 60px;
  max-width: 60px;
}

.w-65 {
  width: 65px;
  min-width: 65px;
  max-width: 65px;
}

.w-70 {
  width: 70px;
  min-width: 70px;
  max-width: 70px;
}

.w-80 {
  width: 80px;
  min-width: 80px;
  max-width: 80px;
}

.w-90 {
  width: 90px;
  min-width: 90px;
  max-width: 90px;
}

.w-120 {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
}

.w-130 {
  width: 130px;
  min-width: 130px;
  max-width: 130px;
}

.w-140 {
  width: 140px;
  min-width: 140px;
  max-width: 140px;
}

.w-152 {
  width: 152px;
  min-width: 152px;
  max-width: 152px;
}

.w-160 {
  width: 160px;
  min-width: 160px;
  max-width: 160px;
}

.w-190 {
  width: 190px;
  min-width: 190px;
  max-width: 190px;
}

.w-200 {
  width: 200px;
  min-width: 200px;
  max-width: 200px;
}

.w-210 {
  width: 210px;
  min-width: 210px;
  max-width: 210px;
}

.w-240 {
  width: 240px;
  min-width: 240px;
  max-width: 240px;
}

.w-280 {
  width: 280px;
  min-width: 280px;
  max-width: 280px;
}

.w-490 {
  width: 490px;
  min-width: 490px;
  max-width: 490px;
}

.slick-dotted.slick-slider {
  margin-bottom: 16px;
}

.slider-nav {
  margin-bottom: 2px;
}

.slick-dots {
  bottom: -18px;
}

.slick-dots li {
  width: 30px;
  height: 3px;
  margin: 0 4px;
}

.slick-dots li button:before {
  height: 3px;
  width: 30px;
  background-color: rgba(255, 255, 255, 0.3);
  opacity: 1;
}

.slick-dots li.slick-active button:before {
  background-color: #fff;
  opacity: 1;
}

.slick-slide.slick-current .esports-game-thumb img {
  -webkit-box-shadow: 0 0 3px 1px rgba(255, 193, 7, 0.7);
  box-shadow: 0 0 3px 1px rgba(255, 193, 7, 0.7);
}

/* greyhound and speedway */
.greyhound .logo-esports,
.speedway .logo-esports {
  padding-top: 32px;
}

.greyhound .logo-esports img,
.speedway .logo-esports img {
  width: inherit;
}

.esports-title {
  font-size: 27px;
  text-align: center;
  font-weight: 600;
}

.esports-title-small {
  font-size: 24px;
  color: #c8e1e0;
  font-weight: 400;
}

.game-icon {
  margin-left: 16px;
}

.game-icon img {
  width: 23px;
}

.game-title {
  margin: 0 8px;
}

.game-title-small {
  color: #9299b9;
}

.esports-row .game-icon {
  margin-left: 8px;
}

.game-table {
  padding: 0 10px 16px;
  display: flex;
  justify-content: space-between;
}

.game-table-left,
.game-table-right {
  width: 48.5%;
}

.game-row-fixed {
  height: 45px;
}

.game-table-column {
  width: 18.5%;
}

.game-table-top {
  width: 100%;
  padding-top: 0;
  padding-bottom: 0;
}

.game-row {
  width: 100%;
  display: flex;
}

.game-row:first-child .game-cell {
  min-height: 30px;
}

.game-cell {
  min-height: 50px;
  align-items: center;
  display: flex;
  justify-content: center;
  margin: 2px;
  font-weight: 600;
  border-radius: 3px;
}

.esports-gamelist.live .game-group .game-table .game-row .game-cell.game-state {
  color: #000000;
}

.esports-gamelist.live .game-group .game-table .game-row .game-cell.bg-01 {
  background-color: #B9B9B9;
}

.esports-gamelist.live .game-group .game-table .game-row .game-cell.bg-01 i {
  color: #fff !important;
}

.game-cell img {
  width: 35px;
}

.game-state {
  color: #014273;
  font-weight: 600;
  font-size: 11px;
  text-transform: uppercase;
}

.game-group {
  display: flex;
  flex-direction: column;
}

.esports-gamelist.live .game-group {
  background-color: #FFF0EA;
}

.game-title1 {
  width: 26%;
}

.game-group-top {
  margin-bottom: 4px;
}

.game-group-top-1 {
  margin-bottom: 0;
  font-size: 14px;
  font-weight: bold;
  text-shadow: 0 0 1px #00000088;
}

.game-group-bottom {
  display: flex;
  align-items: center;
}

.game-group-bottom-left {
  background-color: #9299b9;
  color: #042234;
  font-size: 9px;
  padding: 0px 4px;
  margin-right: 6px;
}

.game-group-bottom-right {
  font-size: 11px;
  color: #9299b9;
}

.game-line {
  background-color: rgba(0, 0, 0, 0.1);
  margin: 4px 0;
  height: 1px;
}

.game-bottom-line {
  background: url(/images/market-head-bg.png) no-repeat;
  background-size: cover;
}

.esports-gamelist.no-bottom-line {
  background: #05283d;
}

.esports-gamelist.no-bottom-line .collapse {
  background: #042234;
}

.game-bottom-line .game-title,
.game-bottom-line .game-title-small {
  font-weight: 600;
  font-size: 12px;
}

.clash-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bg-clash {
  display: flex;
  align-items: center;
  border-radius: 10px;
  padding: 12px;
  margin-top: 8px;
}

.bg-clash-yellow {
  background: linear-gradient(to right, #fbee1c 0%, #ffffff 100%);
}
.bg-clash-red {
  background: linear-gradient(to right, #fe0000 0%, #ffffff 100%);
}
.bg-clash-blue {
  background: linear-gradient(to right, #006fff 0%, #ffffff 100%);
}

.bg-clash img {
  height: 30px;
}

@media (max-width: 1919px) {
  .clash-wrapper {
    flex-direction: column;
  }
}