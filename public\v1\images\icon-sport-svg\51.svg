<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 50 50">
  <defs>
    <style>
      .cls-1 {
        fill: #5b5b5b;
      }

      .cls-2 {
        fill: url(#radial-gradient);
      }

      .cls-3 {
        fill: #ba0d0d;
      }
    </style>
    <radialGradient id="radial-gradient" cx=".6" cy="20.8" fx=".6" fy="20.8" r="13.3" gradientTransform="translate(18.9 6.9) rotate(-23.4)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ff0"/>
      <stop offset="1" stop-color="#ff9a02"/>
    </radialGradient>
  </defs>
  <!-- Generator: Adobe Illustrator 28.6.0, SVG Export Plug-In . SVG Version: 1.2.0 Build 709)  -->
  <g>
    <g id="Layer_1">
      <g>
        <path class="cls-3" d="M22.5,3.4s-11.2,2.5-12.9,11.3c0,0,3.6-8.2,18.1-6.8,14.4,1.4,16.2,13.6,16.2,13.6,0,0-2.5-10.9,1.9-9.1,0,0-10.5-11.6-23.2-9.1Z"/>
        <path class="cls-3" d="M26.7,42.5s-.3,0-.7,0c.4,0,.7,0,.7,0Z"/>
        <path class="cls-3" d="M34.2,47c-12.3,4.2-24.3-3.4-28.2-11.6-3.9-8.1-1.1-15.9-1-16.1,0,.1-1.2,5.8,2.4,5,2.3-.5,3.7-4.6,4.3-7.5-3.5,21.4,10.9,25,14.3,25.5-1.8-.2-6.4-.6-4.8,1.4,2,2.4,13,3.2,13,3.2Z"/>
        <path class="cls-3" d="M9.6,25.8s0,.7,0,1.6c0-.5,0-1.1,0-1.6Z"/>
        <path class="cls-3" d="M43.9,25.3s1.1,14.3-11,16.1c0,0,6.5-.8,7.4.6,1,1.4-2.5,3.5-2.5,3.5,0,0,9.2-3.1,6-20.2Z"/>
        <path class="cls-3" d="M45.7,15.1c1.5,4.7,3.9,15.2,0,20.7,0,0,4.5-4.2,3.3-11.2-.8-5.2-3.3-9.5-3.3-9.5Z"/>
        <path class="cls-3" d="M6.4,12.7c-.6-3.1-3.5-6.6-5.3-8.5,3.2,2.6,8.4,4,8.4,4-3.6,0-3.1,4.3-3.1,4.4Z"/>
      </g>
      <g>
        <circle class="cls-2" cx="27.7" cy="25.8" r="13.3"/>
        <path class="cls-1" d="M27.6,12.5c5.2,0,10.1,3,12.3,8.1,2.9,6.8-.2,14.6-7,17.5-1.7.7-3.5,1.1-5.3,1.1-5.2,0-10.1-3-12.3-8.1-2.9-6.8.2-14.6,7-17.5,1.7-.7,3.5-1.1,5.3-1.1M27.6,11h0c-2,0-4,.4-5.9,1.2-7.5,3.2-11,12-7.7,19.5,2.4,5.4,7.7,9,13.6,9s4-.4,5.9-1.2c7.5-3.2,11-12,7.7-19.5-2.4-5.4-7.7-9-13.6-9h0Z"/>
      </g>
    </g>
  </g>
</svg>