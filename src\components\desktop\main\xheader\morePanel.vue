<template lang="pug">
.morebet-wrapper.morePanel(v-if="!special.includes(sportsType)")
  .body-bet
    ul#nav-morebet.nav.nav-tabs(role="tablist", v-if="(layoutIndex == 3 && [1, 20].includes(sportsType)) || layoutIndex != 3", :class="{ 'live-tab': layoutIndex == 3 }")
      template(v-if="[20].includes(sportsType)")
        li.nav-item(v-if="isChild2", v-for="(val, key) in esportTab")
          a.nav-link(:class="{ active: emode == key }", href="javascript:void(0);", @click="goToTab(key)") {{ $t(val) }}
      template(v-else)
        li.nav-item(v-if="isDetailsTotal || (isChild2 && sportsType != 1)")
          a.nav-link(:class="{ active: mode == 0 }", href="javascript:void(0);", @click="goToTab(0)") {{ $t('ui.main') }}
        li.nav-item(v-if="isDetailsFull")
          a.nav-link(:class="{ active: mode == 1 }", href="javascript:void(0);", @click="goToTab(1)") {{ $t('ui.full_time') }}
        li.nav-item(v-if="isDetailsHalf")
          a.nav-link(:class="{ active: mode == 2 }", href="javascript:void(0);", @click="goToTab(2)") {{ $t('ui.half_time') }}
        li.nav-item(v-if="isChild1 && checkTabMode3")
          a.nav-link(:class="{ active: mode == 3 }", href="javascript:void(0);", @click="goToTab(3)") {{ $t('ui.corners') }}
        li.nav-item(v-if="isChild2 && sportsType == 1 && checkTabMode4")
          a.nav-link(:class="{ active: mode == 4 }", href="javascript:void(0);", @click="goToTab(4)") {{ $t('ui.specials') }}
    .tab-content(:id="'content-' + uid")
      .tab-pane.active(:id="'accordian-more-' + uid")
        template(v-if="pageType != 2")
          .row.mx-0
            template(v-if="details['oxt'] != null && checkOdds(details['oxt'], 0) == true && (mode == 0 || mode == 1)")
              oxtMoreMatch(
                :uid="uid",
                :details="details['oxt']",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )
            template(v-if="details['oxth'] != null && checkOdds(details['oxth'], 0) == true && (mode == 0 || mode == 2)")
              oxthMoreMatch(
                :uid="uid",
                :details="details['oxth']",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )

        .row.mx-0(v-if="details['cs'] != null && checkOdds(details['cs'], 0) == true && (mode == 0 || mode == 1)")
          csMoreMatch(
            :uid="uid",
            :details="details['cso']",
            :matchId="matchId",
            :leagueId="leagueId",
            :marketType="marketType",
            :sportsType="sportsType",
            :betType="betType",
            :layoutIndex="layoutIndex"
          )
        .row.mx-0(v-if="details['csh'] != null && checkOdds(details['csh'], 0) == true && (mode == 0 || mode == 2)")
          cshMoreMatch(
            :uid="uid",
            :details="details['csho']",
            :matchId="matchId",
            :leagueId="leagueId",
            :marketType="marketType",
            :sportsType="sportsType",
            :betType="betType",
            :layoutIndex="layoutIndex"
          )
        .row.mx-0(v-if="details['htft'] != null && checkOdds(details['htft'], 0) == true && (mode == 0 || mode == 1)")
          htftMoreMatch(
            :uid="uid",
            :details="details['htfto']",
            :matchId="matchId",
            :leagueId="leagueId",
            :marketType="marketType",
            :sportsType="sportsType",
            :betType="betType",
            :layoutIndex="layoutIndex"
          )
        .row.mx-0
          template(v-if="details['ml'] != null && checkOdds(details['ml'], 1) == true && (mode == 0 || mode == 1)")
            mlMoreMatch(
              :uid="uid",
              :details="details['ml']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['mlh'] != null && checkOdds(details['mlh'], 1) == true && (mode == 0 || mode == 2)")
            mlhMoreMatch(
              :uid="uid",
              :details="details['mlh']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
        .row.mx-0
          template(v-if="details['oe'] != null && checkOdds(details['oe'], 1) == true && (mode == 0 || mode == 1)")
            oeMoreMatch(
              :uid="uid",
              :details="details['oe']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['oeh'] != null && checkOdds(details['oeh'], 1) == true && (mode == 0 || mode == 2)")
            oehMoreMatch(
              :uid="uid",
              :details="details['oeh']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
        .row.mx-0
          template(v-if="details['tw'] != null && checkOdds(details['tw'], 2) == true && (mode == 0 || mode == 1)")
            twMoreMatch(
              :uid="uid",
              :details="details['tw']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['twh'] != null && checkOdds(details['twh'], 2) == true && (mode == 0 || mode == 2)")
            twhMoreMatch(
              :uid="uid",
              :details="details['twh']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
        .row.mx-0
          template(v-if="details['dc'] != null && checkOdds(details['dc'], 0) == true && (mode == 0 || mode == 1)")
            dcMoreMatch(
              :uid="uid",
              :details="details['dco']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['dch'] != null && checkOdds(details['dch'], 0) == true && (mode == 0 || mode == 2)")
            dchMoreMatch(
              :uid="uid",
              :details="details['dcho']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
        .row.mx-0
          template(v-if="details['tg'] != null && checkOdds(details['tg'], 0) == true && (mode == 0 || mode == 1)")
            tgMoreMatch(
              :uid="uid",
              :details="details['tgo']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['tgh'] != null && checkOdds(details['tgh'], 0) == true && (mode == 0 || mode == 2)")
            tghMoreMatch(
              :uid="uid",
              :details="details['tgho']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
        .row.mx-0
          template(v-if="details['fglg'] != null && checkOdds(details['fglg'], 0) == true && (mode == 0 || mode == 1)")
            fglgMoreMatch(
              :uid="uid",
              :details="details['fglgo']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['fglgh'] != null && checkOdds(details['fglgh'], 0) == true && (mode == 0 || mode == 2)")
            fglghMoreMatch(
              :uid="uid",
              :details="details['fglgho']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
        .row.mx-0
          template(v-if="details['cshtft'] != null && (mode == 0 || mode == 1)")
            cshtftMoreMatch(
              :uid="uid",
              :details="details['cshtfto']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
        .row.mx-0
          template(v-if="details['etghtft'] != null && (mode == 0 || mode == 1)")
            etghtftMoreMatch(
              :uid="uid",
              :details="details['etghtfto']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

          template(v-if="details['cl'] != null && checkOdds(details['cl'], 0) == true && (mode == 0 || mode == 1)")
            clMoreMatch(
              :uid="uid",
              :details="details['clo']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['bs'] != null && checkOdds(details['bs'], 0) == true && (mode == 0 || mode == 1)")
            bsMoreMatch(
              :uid="uid",
              :details="details['bso']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['hnb'] != null && checkOdds(details['hnb'], 0) == true && (mode == 0 || mode == 1)")
            hnbMoreMatch(
              :uid="uid",
              :details="details['hnbo']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['anb'] != null && checkOdds(details['anb'], 0) == true && (mode == 0 || mode == 1)")
            anbMoreMatch(
              :uid="uid",
              :details="details['anbo']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['dnb'] != null && checkOdds(details['dnb'], 0) == true && (mode == 0 || mode == 1)")
            dnbMoreMatch(
              :uid="uid",
              :details="details['dnbo']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['dnbh'] != null && checkOdds(details['dnbh'], 0) == true && (mode == 0 || mode == 2)")
            dnbhMoreMatch(
              :uid="uid",
              :details="details['dnbho']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['twtn'] != null && checkOdds(details['twtn'], 0) == true && (mode == 0 || mode == 1)")
            twtnMoreMatch(
              :uid="uid",
              :details="details['twtno']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['ouoe'] != null && checkOdds(details['ouoe'], 0) == true && (mode == 0 || mode == 1)")
            ouoeMoreMatch(
              :uid="uid",
              :details="details['ouoeo']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['htftoe'] != null && checkOdds(details['htftoe'], 0) == true && (mode == 0 || mode == 1)")
            htftoeMoreMatch(
              :uid="uid",
              :details="details['htftoeo']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

        .row.mx-0(v-if="details['oxtou'] != null && checkOdds(details['oxtou'], 0) == true && (mode == 0 || mode == 1)")
          oxtouMoreMatch(
            :uid="uid",
            :details="details['oxtouo']",
            :matchId="matchId",
            :leagueId="leagueId",
            :marketType="marketType",
            :sportsType="sportsType",
            :betType="betType",
            :layoutIndex="layoutIndex"
          )

        .row.mx-0(v-if="details['dcou'] != null && checkOdds(details['dcou'], 0) == true && (mode == 0 || mode == 1)")
          dcouMoreMatch(
            :uid="uid",
            :details="details['dcouo']",
            :matchId="matchId",
            :leagueId="leagueId",
            :marketType="marketType",
            :sportsType="sportsType",
            :betType="betType",
            :layoutIndex="layoutIndex"
          )

        .row.mx-0(v-if="details['wm'] != null && checkOdds(details['wm'], 0) == true && (mode == 0 || mode == 1)")
          wmMoreMatch(
            :uid="uid",
            :details="details['wmo']",
            :matchId="matchId",
            :leagueId="leagueId",
            :marketType="marketType",
            :sportsType="sportsType",
            :betType="betType",
            :layoutIndex="layoutIndex"
          )

        .row.mx-0
          template(v-if="details['etg'] != null && checkOdds(details['etg'], 0) == true && (mode == 0 || mode == 1)")
            etgMoreMatch(
              :uid="uid",
              :details="details['etgo']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['etgh'] != null && checkOdds(details['etgh'], 0) == true && (mode == 0 || mode == 2)")
            etghMoreMatch(
              :uid="uid",
              :details="details['etgho']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

        .row.mx-0
          template(v-if="details['ehtg'] != null && checkOdds(details['ehtg'], 0) == true && (mode == 0 || mode == 1)")
            ehtgMoreMatch(
              :uid="uid",
              :details="details['ehtgo']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['ehtgh'] != null && checkOdds(details['ehtgh'], 0) == true && (mode == 0 || mode == 2)")
            ehtghMoreMatch(
              :uid="uid",
              :details="details['ehtgho']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

        .row.mx-0
          template(v-if="details['eatg'] != null && checkOdds(details['eatg'], 0) == true && (mode == 0 || mode == 1)")
            eatgMoreMatch(
              :uid="uid",
              :details="details['eatgo']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['eatgh'] != null && checkOdds(details['eatgh'], 0) == true && (mode == 0 || mode == 2)")
            eatghMoreMatch(
              :uid="uid",
              :details="details['eatgho']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

        .row.mx-0
          template(v-if="details['oehm'] != null && checkOdds(details['oehm'], 1) == true && (mode == 0 || mode == 1)")
            oehmMoreMatch(
              :uid="uid",
              :details="details['oehm']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['oehmh'] != null && checkOdds(details['oehmh'], 1) == true && (mode == 0 || mode == 2)")
            oehmhMoreMatch(
              :uid="uid",
              :details="details['oehmh']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

        .row.mx-0
          template(v-if="details['oeaw'] != null && checkOdds(details['oeaw'], 1) == true && (mode == 0 || mode == 1)")
            oeawMoreMatch(
              :uid="uid",
              :details="details['oeaw']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
          template(v-if="details['oeawh'] != null && checkOdds(details['oeawh'], 1) == true && (mode == 0 || mode == 2)")
            oeawhMoreMatch(
              :uid="uid",
              :details="details['oeawh']",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

        //- Corners/Booking
        .row.mx-0(v-if="mode == 3 && [1].includes(sportsType)")
          template(v-for="(sitem, skey) in child1Ids", v-if="isDataEmptyModeMore(hdpou(sitem[0]))")
            hdpouMoreMatch(
              :uid="uid",
              :childId="sitem[0]",
              :details="hdpou(sitem[0])",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

        .row.mx-0(v-if="mode == 3 && ![1].includes(sportsType)")
          template(v-for="(sitem, skey) in child1Ids", v-if="isDataEmptyModeMore(hdpou(sitem[0]))")
            hdpmlMoreMatch(
              :uid="uid",
              :childId="sitem[0]",
              :details="hdpou(sitem[0])",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

        //- Special
        .row.mx-0(v-if="mode == 4 && [1].includes(sportsType)")
          template(v-for="(sitem, skey) in child2Ids", v-if="isDataEmptyModeMore(hdpou(sitem[0]))")
            template(v-if="sitem[2] == 4")
              ouSpecialMatch(
                :customTeam="false",
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              ) span {{ sitem[2] }}
            template(v-else)
              hdpouMoreMatch(
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )

        .row.mx-0(v-if="mode == 0 && ![1, 2, 5, 6, 8, 9, 12, 20].includes(sportsType)")
          template(v-for="(sitem, skey) in child2Ids")
            hdpmlMoreMatch(
              :uid="uid",
              :childId="sitem[0]",
              :details="hdpou(sitem[0])",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

        .row.mx-0(v-if="[20].includes(sportsType)")
          template(v-for="(sitem, skey) in child2Ids", v-if="emode == sitem[2] && isDataEmptyEsportMore(hdpou(sitem[0]))")
            hdpmlMoreMatch(
              :uid="uid",
              :childId="sitem[0]",
              :details="hdpou(sitem[0])",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

        .row.mx-0(v-if="mode == 0 && [2, 5, 6, 8, 9, 12].includes(sportsType)")
          // small {{ child2Ids }}
          template(v-for="(sitem, skey) in child2Ids")
            template(v-if="sitem[2] == 101")
              xsmlMoreMatch(
                :customTeam="true",
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )
            template(v-if="sitem[2] == 2")
              hdpmlMoreMatch(
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )
            template(v-if="sitem[2] == 3")
              hdpMoreMatch(
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )
            template(v-if="sitem[2] == 4")
              ouMoreMatch(
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )
            template(v-if="sitem[2] == 5")
              soeMoreMatch(
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )
            template(v-if="sitem[2] == 6")
              smlMoreMatch(
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )
            template(v-if="sitem[2] == 7")
              oeouMoreMatch(
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )
            template(v-if="sitem[2] == 8")
              mloeMoreMatch(
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )
            template(v-if="sitem[2] == 9")
              hdp1x2MoreMatch(
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )
            template(v-if="sitem[2] == 10")
              hdpou1x2MoreMatch(
                :uid="uid",
                :childId="sitem[0]",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )

.morebet-wrapper(v-else, :class="layoutIndex == 3 ? 'more-live' : 'more-non-live'")
  .body-bet(v-if="[40].includes(sportsType)")
    // 4D Special
    ul#nav-morebet.nav.nav-tabs.d-flex.flex-row(role="tablist", :class="{ 'live-tab': layoutIndex == 3 }")
      li.nav-item(v-if="isChild2", v-for="(val, key) in sport4dTab")
        a.nav-link(:class="{ active: emode == key }", href="javascript:void(0);", @click="goToTab(key)") {{ $t(val) }}
      li.flex-fill
      li.nav-item.nav-button
        a.nav-link(
          :href="'/ngresult?q=' + leagueId + '&n=' + encodeURI(league()[leagueId][4].replace('+', '$')) + '&u=' + encodeURI(league()[leagueId][6])",
          target="_blank",
          onclick="window.open(this.href,'_blank','top=10,height=448,width=900,status=no,toolbar=no,menubar=no,location=no');return false;"
        )
          i.fad.fa-chart-bar
      // template(v-else)
      //   li.nav-item(v-if="isChild2", v-for="(val, key) in sportSFVTab")
      //     a.nav-link(:class="{ active: emode == key }", href="javascript:void(0);", @click="goToTab(key)") {{ $t(val) }}
    .tab-content(:id="'content-' + uid")
      .tab-pane.active(:id="'accordian-more-' + uid")
        .row.mx-0
          template(v-for="(sitem, skey) in child2Ids", v-if="emode == sitem[2]")
            template(v-if="isDataEmptyEsportMore(hdpou(sitem[0]))")
              oeou4dMoreMatch(
                :childId="sitem[0]",
                :childLength="child2Ids.length",
                :childPos="skey",
                :details="hdpou(sitem[0])",
                :matchId="matchId",
                :leagueId="leagueId",
                :marketType="marketType",
                :sportsType="sportsType",
                :betType="betType",
                :layoutIndex="layoutIndex"
              )
            // template(v-if="[41, 42, 43, 44].includes(sportsType) && isDataEmptyEsportMore(hdpou(sitem[0]))")
            //   template(v-if="isDataEmptyHDPSFVMore(hdpou(sitem[0]))")
            //     hdpSFVMoreMatch(
            //       :childId="sitem[0]",
            //       :details="hdpou(sitem[0])",
            //       :matchId="matchId",
            //       :leagueId="leagueId",
            //       :marketType="marketType",
            //       :sportsType="sportsType",
            //       :betType="betType",
            //       :layoutIndex="layoutIndex"
            //     )
            //   template(v-if="isDataEmptyOUSFVMore(hdpou(sitem[0]))")
            //     ouSFVMoreMatch(
            //       :childId="sitem[0]",
            //       :details="hdpou(sitem[0])",
            //       :matchId="matchId",
            //       :leagueId="leagueId",
            //       :marketType="marketType",
            //       :sportsType="sportsType",
            //       :betType="betType",
            //       :layoutIndex="layoutIndex"
            //     )
</template>

<script>
import config from "@/config";

import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {
    csMoreMatch: () => import("@/components/desktop/main/more/csMoreMatch"),
    cshMoreMatch: () => import("@/components/desktop/main/more/cshMoreMatch"),
    htftMoreMatch: () => import("@/components/desktop/main/more/htftMoreMatch"),
    mlMoreMatch: () => import("@/components/desktop/main/more/mlMoreMatch"),
    mlhMoreMatch: () => import("@/components/desktop/main/more/mlhMoreMatch"),
    oeMoreMatch: () => import("@/components/desktop/main/more/oeMoreMatch"),
    oehMoreMatch: () => import("@/components/desktop/main/more/oehMoreMatch"),
    oehmMoreMatch: () => import("@/components/desktop/main/more/oehmMoreMatch"),
    oehmhMoreMatch: () => import("@/components/desktop/main/more/oehmhMoreMatch"),
    oeawMoreMatch: () => import("@/components/desktop/main/more/oeawMoreMatch"),
    oeawhMoreMatch: () => import("@/components/desktop/main/more/oeawhMoreMatch"),
    twMoreMatch: () => import("@/components/desktop/main/more/twMoreMatch"),
    twhMoreMatch: () => import("@/components/desktop/main/more/twhMoreMatch"),
    dcMoreMatch: () => import("@/components/desktop/main/more/dcMoreMatch"),
    dchMoreMatch: () => import("@/components/desktop/main/more/dchMoreMatch"),
    tgMoreMatch: () => import("@/components/desktop/main/more/tgMoreMatch"),
    tghMoreMatch: () => import("@/components/desktop/main/more/tghMoreMatch"),
    fglgMoreMatch: () => import("@/components/desktop/main/more/fglgMoreMatch"),
    fglghMoreMatch: () => import("@/components/desktop/main/more/fglghMoreMatch"),

    // Algo Bet Types
    clMoreMatch: () => import("@/components/desktop/main/more/clMoreMatch"),
    bsMoreMatch: () => import("@/components/desktop/main/more/bsMoreMatch"),
    hnbMoreMatch: () => import("@/components/desktop/main/more/hnbMoreMatch"),
    anbMoreMatch: () => import("@/components/desktop/main/more/anbMoreMatch"),
    dnbMoreMatch: () => import("@/components/desktop/main/more/dnbMoreMatch"),
    dnbhMoreMatch: () => import("@/components/desktop/main/more/dnbhMoreMatch"),
    twtnMoreMatch: () => import("@/components/desktop/main/more/twtnMoreMatch"),
    oxtMoreMatch: () => import("@/components/desktop/main/more/oxtMoreMatch"),
    oxthMoreMatch: () => import("@/components/desktop/main/more/oxthMoreMatch"),
    oxtouMoreMatch: () => import("@/components/desktop/main/more/oxtouMoreMatch"),
    dcouMoreMatch: () => import("@/components/desktop/main/more/dcouMoreMatch"),
    ouoeMoreMatch: () => import("@/components/desktop/main/more/ouoeMoreMatch"),
    htftoeMoreMatch: () => import("@/components/desktop/main/more/htftoeMoreMatch"),
    wmMoreMatch: () => import("@/components/desktop/main/more/wmMoreMatch"),
    etgMoreMatch: () => import("@/components/desktop/main/more/etgMoreMatch"),
    etghMoreMatch: () => import("@/components/desktop/main/more/etghMoreMatch"),
    ehtgMoreMatch: () => import("@/components/desktop/main/more/ehtgMoreMatch"),
    ehtghMoreMatch: () => import("@/components/desktop/main/more/ehtghMoreMatch"),
    eatgMoreMatch: () => import("@/components/desktop/main/more/eatgMoreMatch"),
    eatghMoreMatch: () => import("@/components/desktop/main/more/eatghMoreMatch"),
    cshtftMoreMatch: () => import("@/components/desktop/main/more/cshtftMoreMatch"),
    etghtftMoreMatch: () => import("@/components/desktop/main/more/etghtftMoreMatch"),

    // Special Bet Types
    hdpouMoreMatch: () => import("@/components/desktop/main/moremore/hdpouMoreMatch"),
    hdpmlMoreMatch: () => import("@/components/desktop/main/moremore/hdpmlMoreMatch"),
    oeouMoreMatch: () => import("@/components/desktop/main/moremore/oeouMoreMatch"),
    hdpMoreMatch: () => import("@/components/desktop/main/moremore/hdpMoreMatch"),
    ouMoreMatch: () => import("@/components/desktop/main/moremore/ouMoreMatch"),
    soeMoreMatch: () => import("@/components/desktop/main/moremore/soeMoreMatch"),
    smlMoreMatch: () => import("@/components/desktop/main/moremore/smlMoreMatch"),
    xsmlMoreMatch: () => import("@/components/desktop/main/moremore/xsmlMoreMatch"),
    hdp1x2MoreMatch: () => import("@/components/desktop/main/moremore/hdp1x2MoreMatch"),
    hdpou1x2MoreMatch: () => import("@/components/desktop/main/moremore/hdpou1x2MoreMatch"),
    mloeMoreMatch: () => import("@/components/desktop/main/moremore/mloeMoreMatch"),
    oeou4dMoreMatch: () => import("@/components/desktop/main/moremore/oeou4dMoreMatch"),
    hdpSFVMoreMatch: () => import("@/components/desktop/main/moremore/hdpSFVMoreMatch"),
    ouSFVMoreMatch: () => import("@/components/desktop/main/moremore/ouSFVMoreMatch"),
    ouSpecialMatch: () => import("@/components/desktop/main/moremore/ouSpecialMatch"),
  },
  props: {
    uid: {
      type: String,
    },
    child1Ids: {
      type: Array,
    },
    child2Ids: {
      type: Array,
    },
    details: {
      type: Object,
    },
    matchId: {
      type: Number,
    },
    leagueId: {
      type: Number,
    },
    marketType: {
      type: Number,
    },
    sportsType: {
      type: Number,
    },
    betType: {
      type: String,
    },
    layoutIndex: {
      type: Number,
    },
    mainOdds: {
      type: Object,
    },
  },
  data() {
    return {
      mode: 0,
      emode: 101,
      vHide: true,
    };
  },
  computed: {
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
    isDataExists() {
      if (this.details != null) {
        if ([40].includes(this.sportsType)) {
          if (this.details.hasOwnProperty("more")) {
            return this.details.more > 0 || this.child1Ids.length > 0 || this.child2Ids.length > 0;
          } else {
            return false;
          }
        } else {
          if (this.details.hasOwnProperty("total")) {
            return this.details.total > 0 || this.child1Ids.length > 0 || this.child2Ids.length > 0;
          } else {
            return false;
          }
        }
      } else {
        return false;
      }
      // }
    },
    selectedMatch() {
      return this.$store.getters.selectedMatch;
    },
    isDetailsTotal() {
      return this.details.total > 0;
    },
    isDetailsFull() {
      return this.details.full > 0;
    },
    isDetailsHalf() {
      return this.details.half > 0;
    },
    isChild1() {
      return this.child1Ids.length > 0;
    },
    isChild2() {
      return this.child2Ids.length > 0;
    },
    menu3() {
      return this.$store.state.layout.menu3;
    },
    language() {
      return this.$store.getters.language;
    },
    pageType() {
      return this.$store.getters.pageDisplay.pageType;
    },
    esportTab() {
      var sportTab = {};

      if (this.child1Ids) {
        this.child2Ids.forEach((v, k) => {
          if (config.esportsTab.hasOwnProperty(v[2]) && this.isDataEmptyEsportMore(this.hdpou(v[0]))) {
            sportTab[v[2]] = config.esportsTab[v[2]];
          }
        });
      }
      return sportTab;
    },
    sport4dTab() {
      var sportTab = {};
      if (this.child1Ids) {
        this.child2Ids.forEach((v, k) => {
          if (config.sports4dTab.hasOwnProperty(v[2]) && this.isDataEmptyEsportMore(this.hdpou(v[0]))) {
            sportTab[v[2]] = config.sports4dTab[v[2]];
          }
        });
      }
      return sportTab;
    },
    sportSFVTab() {
      var sportTab = {};
      if (this.child1Ids) {
        this.child2Ids.forEach((v, k) => {
          if (config.sportsSFVTab.hasOwnProperty(v[2]) && this.isDataEmptyEsportMore(this.hdpou(v[0]))) {
            sportTab[v[2]] = config.sportsSFVTab[v[2]];
          }
        });
      }
      return sportTab;
    },
    checkTabMode3() {
      var flag = false;
      this.child1Ids.forEach((v, k) => {
        if (this.isDataEmptyModeMore(this.hdpou(v[0]))) {
          flag = true;
        }
      });
      return flag;
    },
    checkTabMode4() {
      var flag = false;
      this.child2Ids.forEach((v, k) => {
        if (this.isDataEmptyModeMore(this.hdpou(v[0]))) {
          flag = true;
        }
      });
      return flag;
    },
    special() {
      return config.special;
    },
  },
  watch: {
    esportTab(newVal, oldVal) {
      try {
        var a = Object.keys(newVal);
        var b = Object.keys(oldVal);
        if (a.length != b.length) {
          if (a.length > 0) {
            this.goToTab(a[0]);
          }
        }
      } catch {}
    },
    sport4dTab(newVal, oldVal) {
      try {
        var a = Object.keys(newVal);
        var b = Object.keys(oldVal);
        if (a.length != b.length) {
          if (a.length > 0) {
            this.goToTab(a[0]);
          }
        }
      } catch {}
    },
    sportSFVTab(newVal, oldVal) {
      try {
        var a = Object.keys(newVal);
        var b = Object.keys(oldVal);
        if (a.length != b.length) {
          if (a.length > 0) {
            this.goToTab(a[0]);
          }
        }
      } catch {}
    },
  },
  destroyed() {
    EventBus.$off("PROCESS_ODDS", this.forceUpdate);
    EventBus.$off("PROCESS_MMO", this.forceUpdate);
  },
  mounted() {
    this.setDefaultTab();
    EventBus.$on("PROCESS_ODDS", this.forceUpdate);
    EventBus.$on("PROCESS_MMO", this.forceUpdate);
  },
  methods: {
    forceUpdate() {
      this.$forceUpdate();
    },
    isDataEmptyModeMore(details) {
      var hdp = details["hdp"] != null && details["hdp"][0] != null;
      var hdph = details["hdph"] != null && details["hdph"][0] != null;
      var ou = details["ou"] != null && details["ou"][0] != null;
      var ouh = details["ouh"] != null && details["ouh"][0] != null;
      var oxt = details["oxt"] != null && details["oxt"][0] != null;
      var oxth = details["oxth"] != null && details["oxth"][0] != null;
      var oe = details["oe"] != null && details["oe"][0] != null;
      var oeh = details["oeh"] != null && details["oeh"][0] != null;
      var ml = details["ml"] != null && details["ml"][0] != null;
      var mlh = details["mlh"] != null && details["mlh"][0] != null;
      return hdp || ou || oe || oxt || hdph || ouh || oxth || oeh || ml || mlh;
    },
    isDataEmptyEsportMore(details) {
      var hdp = details["hdp"] != null && details["hdp"][0] != null;
      var ou = details["ou"] != null && details["ou"][0] != null;
      var ml = details["ml"] != null && details["ml"][0] != null;
      var oe = details["oe"] != null && details["oe"][0] != null;
      return hdp || ou || oe || ml;
    },
    isDataEmptyHDPSFVMore(details) {
      var hdp = details["hdp"] != null && details["hdp"][0] != null;

      return hdp;
    },
    isDataEmptyOUSFVMore(details) {
      var ou = details["ou"] != null && details["ou"][0] != null;

      return ou;
    },
    show4dStats(e) {
      alert(e);
    },
    handleDefaultTab() {
      switch (this.mode) {
      case 0:
        if (this.details.total <= 0) {
          if (this.child1Ids.length > 0) {
            this.goToTab(3);
          } else {
            if (this.child2Ids.length > 0) {
              this.goToTab(0);
            }
          }
        }
        break;
      case 1:
        if (this.details.full <= 0) {
          this.goToTab(0);
        }
        break;
      case 2:
        if (this.details.half <= 0) {
          this.goToTab(0);
        }
        break;
      case 3:
        if (this.child1Ids.length <= 0) {
          this.goToTab(0);
        }
        break;
      case 4:
        if (this.child2Ids.length <= 0) {
          this.goToTab(0);
        }
        break;
      }

      if (this.special.includes(this.sportsType) || [20].includes(this.sportsType)) {
        var done = false;
        if (this.child2Ids) {
          this.child2Ids.forEach((v, k) => {
            if (!done) {
              this.emode = v[2];
              done = true;
            }
            return false;
          });
        }
        this.goToTab(this.emode);
      }
    },
    setDefaultTab() {
      setTimeout(() => {
        if (this.details != null && this.details.hasOwnProperty("total")) {
          this.handleDefaultTab();
        } else {
          if ((this.child1Ids != null && this.child1Ids.length > 0) || (this.child2Ids != null && this.child2Ids.length > 0)) {
            this.handleDefaultTab();
          } else {
            this.setDefaultTab();
          }
        }
      }, 1000);
    },
    league() {
      return this.$store.getters.data.league;
    },
    head() {
      return this.$store.getters.data.head;
    },
    match() {
      return this.$store.getters.data.match;
    },
    leagueName(data) {
      var result = data["league"].split(" - ");
      if (result.length >= 2) {
        return result[result.length - 1].toLowerCase().trim();
      }
      return this.details["league"].toLowerCase().trim();
    },
    odds() {
      if (this.$store.getters.data.hasOwnProperty("odds")) {
        if (this.$store.getters.data.odds[this.layoutIndex] != null) {
          return this.$store.getters.data.odds[this.layoutIndex];
        } else {
          return {};
        }
      }
      return {};
    },
    mmo() {
      if (this.$store.getters.data.hasOwnProperty("mmo")) {
        if (this.$store.getters.data.mmo[this.layoutIndex] != null) {
          return this.$store.getters.data.mmo[this.layoutIndex];
        } else {
          return {};
        }
      }
      return {};
    },
    hdpouMMO(e) {
      var odds = this.mmo();

      var r = {};
      r["tn"] = 0;
      r["team"] = null;
      r["oxtn"] = 0;
      r["more"] = 0;

      if (odds != null) {
        if (odds.hasOwnProperty("hdp")) {
          if (odds["hdp"][e] != null) {
            r["hdp"] = odds["hdp"][e].filter((v, i, r) => {
              return v[4] == "HDP";
            });
            r["hdph"] = odds["hdp"][e].filter((v, i, r) => {
              return v[4] == "HDPH";
            });
            if (r["hdp"] ? r["hdp"].length : 0 > r["tn"]) {
              r["tn"] = r["hdp"].length;
              if (r["hdp"][0][8] != null && r["hdp"][0][8] != "0") {
                r["team"] = r["hdp"][0][7];
              } else {
                r["team"] = 2;
              }
            } else {
              r["hdp"] = null;
            }
            if (r["hdph"] ? r["hdph"].length : 0 > r["tn"]) {
              if (r["tn"] < r["hdph"].length) {
                r["tn"] = r["hdph"].length;
              }
              if (r["team"] == null) {
                if (r["hdph"][0][8] != null && r["hdph"][0][8] != "0") {
                  r["team"] = r["hdph"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["hdph"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("ou")) {
          if (odds["ou"][e]) {
            r["ou"] = odds["ou"][e].filter((v, i, r) => {
              return v[4] == "OU";
            });
            r["ouh"] = odds["ou"][e].filter((v, i, r) => {
              return v[4] == "OUH";
            });
            if (r["ou"] ? r["ou"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ou"].length) {
                r["tn"] = r["ou"].length;
              }
              if (r["team"] == null) {
                if (r["ou"][0][8] != null && r["ou"][0][8] != "0") {
                  r["team"] = r["ou"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["ou"] = null;
            }
            if (r["ouh"] ? r["ouh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ouh"].length) {
                r["tn"] = r["ouh"].length;
              }
              if (r["team"] == null) {
                if (r["ouh"][0][8] != null && r["ouh"][0][8] != "0") {
                  r["team"] = r["ouh"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["ouh"] = null;
            }
          }
        }
      }

      return r;
    },
    hdpou(e) {
      var odds = this.odds();
      var r = {};
      r["tn"] = 0;
      r["team"] = null;
      r["oxtn"] = 0;
      r["more"] = 0;

      r["mmo"] = {};

      if (odds != null) {
        if (odds.hasOwnProperty("hdp")) {
          if (odds["hdp"][e] != null) {
            r["hdp"] = odds["hdp"][e].filter((v, i, r) => {
              return v[4] == "HDP";
            });
            r["hdph"] = odds["hdp"][e].filter((v, i, r) => {
              return v[4] == "HDPH";
            });
            if (r["hdp"] ? r["hdp"].length : 0 > r["tn"]) {
              r["tn"] = r["hdp"].length;
              if (r["hdp"][0][8] != null && r["hdp"][0][8] != "0") {
                r["team"] = r["hdp"][0][7];
              } else {
                r["team"] = 2;
              }
            } else {
              r["hdp"] = null;
            }
            if (r["hdph"] ? r["hdph"].length : 0 > r["tn"]) {
              if (r["tn"] < r["hdph"].length) {
                r["tn"] = r["hdph"].length;
              }
              if (r["team"] == null) {
                if (r["hdph"][0][8] != null && r["hdph"][0][8] != "0") {
                  r["team"] = r["hdph"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["hdph"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("ou")) {
          if (odds["ou"][e]) {
            r["ou"] = odds["ou"][e].filter((v, i, r) => {
              return v[4] == "OU";
            });
            r["ouh"] = odds["ou"][e].filter((v, i, r) => {
              return v[4] == "OUH";
            });
            if (r["ou"] ? r["ou"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ou"].length) {
                r["tn"] = r["ou"].length;
              }
              if (r["team"] == null) {
                if (r["ou"][0][8] != null && r["ou"][0][8] != "0") {
                  r["team"] = r["ou"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["ou"] = null;
            }
            if (r["ouh"] ? r["ouh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ouh"].length) {
                r["tn"] = r["ouh"].length;
              }
              if (r["team"] == null) {
                if (r["ouh"][0][8] != null && r["ouh"][0][8] != "0") {
                  r["team"] = r["ouh"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["ouh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("oxt")) {
          if (odds["oxt"][e]) {
            r["oxt"] = odds["oxt"][e].filter((v, i, r) => {
              return v[4] == "1X2";
            });
            r["oxth"] = odds["oxt"][e].filter((v, i, r) => {
              return v[4] == "1X2H";
            });
            if (r["oxt"] ? r["oxt"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oxt"].length) {
                r["tn"] = r["oxt"].length;
              }
              r["oxtn"] = 1;
            } else {
              r["oxt"] = null;
            }
            if (r["oxth"] ? r["oxth"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oxth"].length) {
                r["tn"] = r["oxth"].length;
              }
              r["oxtn"] = 1;
            } else {
              r["oxth"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("oe")) {
          if (odds["oe"][e]) {
            r["oe"] = odds["oe"][e].filter((v, i, r) => {
              return v[4] == "OE";
            });
            r["oeh"] = odds["oe"][e].filter((v, i, r) => {
              return v[4] == "OEH";
            });
            if (r["oe"] ? r["oe"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oe"].length) {
                r["tn"] = r["oe"].length;
              }
              r["oen"] = 1;
            } else {
              r["oe"] = null;
            }
            if (r["oeh"] ? r["oeh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oeh"].length) {
                r["tn"] = r["oeh"].length;
              }
              r["oehn"] = 1;
            } else {
              r["oeh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("ml")) {
          if (odds["ml"][e]) {
            r["ml"] = odds["ml"][e].filter((v, i, r) => {
              return v[4] == "ML";
            });
            r["mlh"] = odds["ml"][e].filter((v, i, r) => {
              return v[4] == "MLH";
            });
            if (r["ml"] ? r["ml"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ml"].length) {
                r["tn"] = r["ml"].length;
              }
              r["mln"] = 1;
            } else {
              r["ml"] = null;
            }
            if (r["mlh"] ? r["mlh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["mlh"].length) {
                r["tn"] = r["mlh"].length;
              }
              r["mlhn"] = 1;
            } else {
              r["mlh"] = null;
            }
          }
        }

        var head = this.head();
        var match = this.match();
        if (head[e] == undefined) {
          r["more"] = 0;
        } else {
          r["head"] = head[e][0];
          r["match"] = match[e];
          if (r["head"] != null) {
            r["more"] = head[e][0][7];
          }
          if (r["match"] != null) {
            r["league"] = this.league()[r["match"][1]][4];
          }
        }
      }

      if (this.mmoMode) {
        r["mmo"] = this.hdpouMMO(e);
      }

      this.result = null;
      this.result = r;
      return r;
    },
    goToTab(mode) {
      if (this.special.includes(this.sportsType) || [20].includes(this.sportsType)) {
        this.emode = mode;
        return;
      }
      this.mode = mode;
    },
    checkOdds(e, t) {
      var result = false;

      switch (t) {
      case 1:
        e.forEach((value) => {
          if (value[5] != null && value[5] != "" && value[7] != null && value[7] != "") {
            result = this.menu3 != "parlay" ? true : value[8] == 1;
          }
        });
        break;
      case 2:
        e.forEach((value) => {
          if (value[5] != null && value[5] != "" && value[6] != null && value[6] != "" && value[7] != null && value[7] != "") {
            result = this.menu3 != "parlay" ? true : value[8] == 1;
          }
        });
        break;
      default:
        e.forEach((value) => {
          if (value[5] != null && value[5] != "") {
            result = this.menu3 != "parlay" ? true : value[8] == 1;
          }
        });
        break;
      }

      return result;
    },
  },
};
</script>
