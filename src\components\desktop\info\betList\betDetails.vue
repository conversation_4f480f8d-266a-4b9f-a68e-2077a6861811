<template lang="pug">
  div
    template(v-for="(item, index) in items")
      .bet-info
        .bet-type.blue {{ index + 1 }}. {{ sports[item.sports_type] }} - {{ getBetTypeName(item.bet_type)  }}
        .bet-detail.blue.pl-2
          .name {{ betDisplay(item) }}
          .oddsdetail
            .d-flex.justify-content-start
              template(v-if="item.criteria2")
                .selector-name(v-if="['HDP', 'HDPH', 'OU', 'OUH', '1X2HDP', '1X2HDPH'].includes(item.bet_type)")
                  | {{ ballDisplayMMO(item) }}({{ $numeral(item.criteria2).format("0") }})
                .selector-score(v-if="item.market_type == 3 && item.home_running_score != null") [{{ item.home_running_score }}-{{ item.away_running_score }}]
                .ml-1.text-muted.text-uppercase(v-if="item.payout_odds!=undefined") ({{ getMMOParlayMatchResult(item) }})
              template(v-else)
                .selector-name(v-if="['HDP', 'HDPH', 'OU', 'OUH', '1X2HDP', '1X2HDPH'].includes(item.bet_type)") {{ ballDisplay(item) }}
                .selector-score(v-if="item.market_type == 3 && item.home_running_score != null") [{{ item.home_running_score }}-{{ item.away_running_score }}]
                .selector-other @
                .selector-odds.accent.small(:class="getNumberClass(item.odds_display)") {{ item.odds_display }}
                .ml-1.text-muted.text-uppercase(v-if="item.payout_odds!=undefined") ({{ getParlayMatchResult(item) }})
      .match-info.d-flex.flex-column.pl-2
        .name-home
          span {{ getHomeTeam(item) }}
          span(v-if="item.bet_type != 'OR'") &nbsp;-vs-&nbsp;{{ getAwayTeam(item) }}
        .name-league {{ getLeague(item) }}

</template>

<script>
import naming from "@/library/_name";

export default {
  props: {
    items: {
      type: Array,
      default: []
    }
  },
  computed: {
    sports() {
      return this.$store.state.layout.sports;
    },
    language() {
      return this.$store.getters.language;
    }
  },
  methods: {
    ballDisplayMMO(e) {
      return naming.ballDisplayMMO2(e, this);
    },
    getHomeTeam(e) {
      var r = e["home_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.home_team_name;
    },
    getAwayTeam(e) {
      var r = e["away_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.away_team_name;
    },
    getLeague(e) {
      var r = e["name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.league_name;
    },
    getNumberClass(e) {
      return parseFloat(e) >= 0 ? "" : "text-red";
    },
    getBetTypeName(e) {
      return this.$t("m.BT_" + e);
    },
    betDisplay(bet) {
      return naming.betDisplay(bet, this, this.language);
    },
    ballDisplay(e) {
      return naming.ballDisplay(e, this);
    },
    getParlayMatchResult(e) {
      // console.log(e);
      return naming.parlayResult(e, this);
    },
    getMMOParlayMatchResult(e) {
      // console.log(e);
      return naming.mmoParlayResult(e, this);
    }
  }
};
</script>
