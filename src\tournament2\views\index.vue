<template lang="pug">
.tournament-main.full-auto.full-height
  ads
  //- adsPop
  room-rules
  room-create
  room-bet-list
  room-player-betslip
  .tournament-top.tournament-top-plus
    .tournament-container
      .tournament-header
        .tournament-header-left(v-if="!whiteLabel")
          logo
        .tournament-header-left(v-else)
          .back-link
            i.fas.fa-chevron-left
            router-link(to="/desktop") {{ $t("ui.back") }}
        .tournament-header-right#v-step-0
          | {{ $t('ui.tournament') }}
          i.fas.fa-plus
  .tournament-content
    router-view
    v-tour(name="tour1" :steps="steps" :callbacks="callbacks" :options="options")
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {
    ads: () => import("@/tournament2/components/ads"),
    adsPop: () => import("@/tournament2/components/adsPop"),
    logo: () => import("@/components/desktop/logo"),
    roomRules: () => import("@/tournament2/components/roomRules.vue"),
    roomCreate: () => import("@/tournament2/components/roomCreate.vue"),
    roomBetList: () => import("@/tournament2/components/roomBetList.vue"),
    roomPlayerBetslip: () => import("@/tournament2/components/roomPlayerBetslip.vue"),
  },
  data() {
    return {
      options: {
        labels: {
          buttonSkip: this.$t("message.button_skip"),
          buttonPrevious: this.$t("message.button_previous"),
          buttonNext: this.$t("message.button_next"),
          buttonStop: this.$t("message.button_continue"),
        },
      },
      callbacks: {
        onStart: this.onStart,
        onPreviousStep: this.onPreviousStep,
        onNextStep: this.onNextStep,
        onSkip: this.onSkip,
        onFinish: this.onFinish,
        onStop: this.onStop,
      },
      steps: [
        {
          target: "#v-step-0",
          content: this.$t("message.tour_content_1"),
        },
        {
          target: "#v-step-1",
          content: this.$t("message.tour_content_2"),
        },
        {
          target: "#v-step-2",
          content: this.$t("message.tour_content_3"),
        },
        {
          target: "#v-step-3",
          content: this.$t("message.tour_content_4"),
        },
        {
          target: ".tournament-pool-details",
          content: this.$t("message.tour_content_8"),
        },
        {
          target: ".tournament-create-room",
          content: this.$t("message.tour_content_9"),
        },
        {
          target: ".tournament-pool-fee",
          content: this.$t("message.tour_content_5"),
        },
      ],
    };
  },
  computed: {
    tour1() {
      return this.$store.state.layout.tour1;
    },
    whiteLabel() {
      return config.whiteLabel;
    },
  },
  destroyed() {},
  mounted() {
    if (this.tour1 == false) this.$tours["tour1"].start();
  },
  created() {
    this.addClass();
  },
  methods: {
    onStart() {},
    onPreviousStep(currentStep) {},
    onNextStep(currentStep) {},
    onSkip() {
      this.$store.dispatch("layout/setTour1", true);
    },
    onFinish() {
      $(".tournament-pool-fee")[0].click();
    },
    onStop() {
      // this.$store.dispatch("layout/setTour1", true);
    },
    addClass() {
      $("html").addClass("minimal hl noscrollbar");
      $("body").addClass("minimal hl noscrollbar");
    },
  },
};
</script>

<style></style>
