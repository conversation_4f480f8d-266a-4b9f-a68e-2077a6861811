<template lang="pug">
  .hx-table.hx-top-rounded.hx-mt-2(:class="source.marketId == 3 ? 'live' : 'non-live'")
    .hx-cell.flex-fill
      .hx-row.h-100
        .d-flex.flex-row.align-items-center.h-100
          .pl-1
            img(:src="'/v1/images/icon-sport-svg/' + getImage(source.sportsId)" width="22")
          .d-flex.flex-row.align-items-baseline.align-item-margin
            .hx-header-title(:class="{ 'live' : source.marketId == 3 }") {{ source.marketType }}
            .hx-header-subtitle {{ source.sportsType }}
    .hx-cell.w-262
      .hx-row
        .hx-col.b-0.w-262
          .hx.text-center {{ $t("ui.full_time") }}
      .hx-row
        .hx-col.w-98
          .hx.text-center {{ $t("ui.hdp") }}
        .hx-col.w-98
          .hx.text-center {{ $t("ui.ou") }}
        .hx-col.w-66
          .hx.text-center {{ $t("ui.oxt") }}
    .hx-cell.w-262
      .hx-row
        .hx-col.w-262
          .hx.text-center {{ $t("ui.half_time") }}
      .hx-row
        .hx-col.b-0.w-98
          .hx.text-center {{ $t("ui.hdp") }}
        .hx-col.w-98
          .hx.text-center {{ $t("ui.ou") }}
        .hx-col.w-66
          .hx.text-center {{ $t("ui.oxt") }}
    .hx-cell.w-40
      .hx-row
</template>

<script>
import config from "@/config";

export default {
  props: {
    source: {
      type: Object
    }
  },
  methods: {
    getImage(e) {
      return config.getSportsImage(e);
    }
  }
};
</script>
