export default {
  login: {
    succeed: "loginSucceed",
    failed: "loginFailed",
    usernameRequired: "usernameRequired",
    passwordRequired: "passwordRequired"
  },
  logout: {
    succeed: "logoutSucceed",
    failed: "logoutFailed"
  },
  changePassword: {
    succeed: "changePasswordSucceed",
    failed: "changePasswordFailed",
    currPasswordRequired: "currPasswordRequired",
    newPasswordRequired: "currPasswordRequired",
    confirmNewPasswordRequired: "confirmNewPasswordRequired",
    passwordsNotMatch: "passwordsNotMatch"
  },
  changeNickname: {
    succeed: "changeNicknameSucceed",
    failed: "changeNicknameFailed",
    nickNameRequired: "nickNameRequired"
  },
  request: {
    succeed: "requestSucceed",
    failed: "requestFailed",
    processing: "requestProcessing",
    pending: "requestPending",
    incompleted: "incompletedRequest"
  },
  session: {
    invalidSession: "invalidSession"
  },
  message: {
    succeed: "getMessageSucceed",
    failed: "getMessageFailed",
    startDateRequired: "startDateRequired",
    endDateRequired: "endDateRequired",
    anTypeRequired: "anTypeRequired"
  },
  statement: {
    succeed: "getStatementSucceed",
    failed: "getStatementFailed",
    startDateRequired: "startDateRequired",
    endDateRequired: "endDateRequired",
    workingDateRequired: "workingDateRequired",
    pageSizeRequired: "pageSizeRequired",
    pageNumberRequired: "pageNumberRequired",
    sportsTypeRequired: "sportsTypeRequired"
  },
  result: {
    succeed: "getResultSucceed",
    failed: "getResultessageFailed",
    workingDateRequired: "workingDateRequired",
    sportsTypeRequired: "sportsTypeRequired",
    leagueIdRequired: "leagueIdRequired",
    isOutrightRequired: "isOutrightRequired"
  }
};
