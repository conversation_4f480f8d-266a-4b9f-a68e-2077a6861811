.landing.bt .top .login-wrapper {
    background-color: #212143;
}

.landing.bt .logo {
    max-height: 80px;
}

.landing.bt .logo img {
    max-width: 80px;
}

.bt .btn-select {
    border: 1px solid #575d78;
    background-color: #343a56;
}

.bt .lobby-wrap:before {
    background-image: url(https://r.myw0011001.com/images/bt/line1.svg);
}


.bt .inner-wrap {
    background-color: #212143;
}

.bt .lobbyinner .lobby-item .front .info {
    background-color: #8cccf0;
}

.bt .lobbyinner .lobby-item .back h3 {
    color: #212142;
}

.bt .lobbyinner .lobby-item .back .btn {
    background-color: #8cccf0;
}

.landing.bt .login-btn,
.landing.bt .join-btn {
    color: #fff;
    text-shadow: none;
}

.bt .feature-wrap:before,
.bt .feature-wrap:after {
    background-image: url(https://r.myw0011001.com/images/bt/line2.svg);
}

.bt .feature-wrap {
    background-color: #313750;
}

.bt .deco1 {
    right: 0px;
}

.bt .deco2 {
    left: 0px;
}

.bt .inner-wrap {
    background-image: url(https://r.myw0011001.com/images/bt/lobby_bg.jpg);
}

.bt .feature-item .feature-content h1,
.bt .feature-item .feature-content p {
    color: #fff;
}

.landing.bt .license .image-wrap {
    border: 1px rgba(0, 0, 0, 0.2) solid;
}

.landing.bt .footer .tnc {
    border-top: 1px rgba(0, 0, 0, 0.2) solid;
}

.landing.bt .footer {
    color: #fff;
    background-color: #3f476c;
}

.bt .lang-dropdown-wrap,
.bt #lang-dropdown-list {
    background-color: #3f476c;
}

@media (min-width: 768px) {
    .landing.bt .top-wrapper.small-top {
        background-color: #212143;
    }

    .landing.bt .logo img {
        max-width: 150px
    }

    .landing.bt .top-wrapper.small-top .top .logo img {
        max-width: 50px;
    }
}

@media (min-width: 960px) {
    .landing.bt .footer {
        color: #000;
        background-color: #fff;
    }
}

header .topbar .container .logo {
	height: 64px;
	width: inherit;
}

/* header .topbar .container .logo img.bt {
	padding-top: 0;
	height: 56px;
	width: inherit;
} */
