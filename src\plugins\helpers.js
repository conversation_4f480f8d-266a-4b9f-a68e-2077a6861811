import { EventBus } from "@/library/_event-bus.js";

export default {
  install: (Vue, options) => {
    Vue.prototype.$helpers = {
      info(url) {
        if (options.$wind == null) {
          options.$wind = [];
        }
        // console.log("toolbar=yes,scrollbars=yes,resizable=yes,top=1000,left=0,width=1000,height=970")
        var wind = window.open(url, "info", "toolbar=yes,scrollbars=yes,resizable=yes,top=1000,left=0,width=1000,height=970");
        options.$wind.push(wind);
      },
      open(url, name) {
        if (options.$wind == null) {
          options.$wind = [];
        }
        var wind = window.open(url, name, "toolbar=no,scrollbars=yes,resizable=yes,width=1280,height=720");
      },
      logout() {
        for (var n in options.$wind) {
          options.$wind[n].close();
        }
        if (options.$app.$store.getters.isLoggedIn) {
          if (EventBus.resetLayout) {
            EventBus.resetLayout();
          }

          options.$app.$store.dispatch("user/doLogout").then(
            (res) => {
              if (options.$app.$router) {
                try {
                  options.$app.$router.push("/").catch((err) => {
                    console.trace(err);
                  });
                } catch (err) {
                  console.warn(err);
                }
              }
            },
            (err) => {
              console.warn("logout", err);
            }
          );
        }
      },
      handleFeedback(e, force) {
        var result = false;
        var isLoggedIn = true;
        if (options.$app.$store) {
          isLoggedIn = options.$app.$store.getters.isLoggedIn;
        }
        if (e != null && e.includes(" ")) {
          // this.logout();
          options.$app.$swal("Error", e, "error");
          return result;
        }
        switch (e) {
          case "account_not_exists":
          case "account_inactive":
          case "login_id_not_exists":
          case "session_not_exists":
          case "session_expired":
          case "invalidSession":
          case "permission_denied":
          case "accountIdRequired":
          case "BLOCK":
          case "CLOSE":
            if (isLoggedIn) {
              options.$app.$snotify.warning(options.$i18n.t("error." + e));
            } else {
              options.$app.$swal("Error", options.$i18n.t("error." + e), "error").then((result) => {});
            }
            this.logout();
            break;
          case "requestLimit":
          case "requestProcessing":
          case "requestPending":
          case "requestTimeout":
          case undefined:
          case null:
          case "loginLimit":
          case "timeoutError":
          case "systemError":
          case "insufficient_balance":
            console.warn(e);
            if (force === true) {
              options.$app.$swal(options.$i18n.t("ui.error"), options.$i18n.t("error." + e), "error").then((result) => {});
            }
            result = true;
            break;
          case "requestFailed":
            options.$app.$store.dispatch("layout/setNetwork", "failed");
            console.warn(e);
            if (force === true) {
              options.$app.$snotify.warning(options.$i18n.t("error." + e));
              // options.$app.$swal(options.$i18n.t("ui.error"), options.$i18n.t("error." + e), "error").then((result) => {});
            }
            result = true;
            break;
          case "incompletedRequest":
          case "unableToGetBalanceAtTheMoment":
          case "duplicate_debit_record":
            // case "permission_denied":
            options.$app.$snotify.warning(options.$i18n.t("error." + e));
            // options.$app.$swal(options.$i18n.t("ui.error"), options.$i18n.t("error." + e), "error").then((result) => {});
            result = true;
            break;
          case "liveCasinoDisabled":
          case "lotteryDisabled":
          case "MiniGameDisabled":
            break;
          case "betNotExists":
            options.$app.$swal(options.$i18n.t("ui.notice"), options.$i18n.t("error." + e), "error").then((result) => {});
            break;
          default:
            options.$app.$swal(options.$i18n.t("ui.error"), options.$i18n.t("error." + e), "error").then((result) => {});
            break;
        }
        return result;
      },
      showDialog(title, message, variant) {
        options.$app.$swal(title, message, variant);
      },
    };
  },
};
