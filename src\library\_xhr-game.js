import config from "@/config";
import errors from "@/errors";
import Vue from "vue";
import { createGameListGetter, createGameLauncher } from "./_xhr-game-utils.js";

export default {
  loading: {
    gamingsoft: false,
    gameplay: false,
    sagaming: false,
    ctgaming: false,
    evolution: false,
    live22: false,
    minigame: false,
    yeebet: false,
    pgsoft: false,
    pretty: false,
    pragmatic: false,
    pragmaticGameList: false,
    joker: false,
    jokerGameList: false,
    w4d: false,
    e2: false,
    spp: false,
    jili: false,
    jiliGameList: false,
    uusl: false,
    uuslGameList: false,
    next: false,
    live22: false,
    live22GameList: false,
    wf: false,
    wfGameList: false,
    epw: false,
    epwGameList: false,
    awc: false,
    awcGameList: false,
    aio: false
  },

  launchGS(args) {
    return createGameLauncher('gs', 'playerIdPassword', this.loading)(args);
  },

  launchL22(args) {
    return createGameLauncher('l22', 'playerIdPassword', this.loading)(args);
  },

  launchGP(args) {
    return createGameLauncher('gp', 'playerIdPassword', this.loading)(args);
  },

  launchSA(args) {
    return createGameLauncher('sa', 'usernameSessionId', this.loading)(args);
  },

  launchCT(args) {
    return createGameLauncher('ct', 'usernameSessionId', this.loading)(args);
  },

  launchPG(args) {
    return createGameLauncher('pg', 'usernameSessionId', this.loading)(args);
  },

  launchEvo(args) {
    return createGameLauncher('evo', 'usernameSessionId', this.loading)(args);
  },

  launchYb(args) {
    return createGameLauncher('yb', 'usernameSessionId', this.loading)(args);
  },

  launchPgs(args) {
    return createGameLauncher('pgs', 'usernameSessionId', this.loading)(args);
  },

  launchEsports2(args) {
    return createGameLauncher('e2', 'usernameSessionId', this.loading)(args);
  },

  getPragmaticGameList(args) {
    return createGameListGetter('pragmatic', this.loading)(args);
  },

  launchPragmatic(args, isLiveCasino) {
    return createGameLauncher('pragmatic', 'usernameSessionId', this.loading)(args, { isLiveCasino });
  },

  launchMiniGame(args) {
    return createGameLauncher('minigame', 'usernameSessionId', this.loading)(args);
  },

  launchW4D(args) {
    return createGameLauncher('w4d', 'usernameSessionId', this.loading)(args);
  },

  getJokerGameList(args) {
    return createGameListGetter('joker', this.loading)(args);
  },

  launchJoker(args) {
    return createGameLauncher('joker', 'usernameSessionId', this.loading)(args);
  },

  launchSimplePlay(args) {
    return createGameLauncher('spp', 'usernameSessionId', this.loading)(args);
  },

  launchJili(args) {
    return createGameLauncher('jili', 'usernameSessionId', this.loading)(args);
  },

  getJiliGameList(args) {
    return createGameListGetter('jili', this.loading)(args);
  },

  launchUusl(args) {
    return createGameLauncher('uusl', 'usernameSessionId', this.loading)(args);
  },

  getUuslGameList(args) {
    return createGameListGetter('uusl', this.loading)(args);
  },

  launchNextSpin(args) {
    return createGameLauncher('next', 'usernameSessionId', this.loading)(args);
  },

  launchDbHash(args) {
    return createGameLauncher('dbhash', 'usernameSessionId', this.loading)(args);
  },

  launchDbPoker(args) {
    return createGameLauncher('dbpoker', 'usernameSessionId', this.loading)(args);
  },

  launchWowGaming(args) {
    return createGameLauncher('wow', 'usernameSessionId', this.loading)(args);
  },

  launchAICasino(args) {
    return createGameLauncher('ai', 'usernameSessionId', this.loading)(args);
  },

  launchLive22(args) {
    return createGameLauncher('live22', 'usernameSessionId', this.loading)(args);
  },

  getLive22GameList(args) {
    return createGameListGetter('live22', this.loading)(args);
  },

  launchWf(args) {
    return createGameLauncher('wf', 'usernameSessionId', this.loading)(args);
  },

  getWfGameList(args) {
    return createGameListGetter('wf', this.loading)(args);
  },

  launchEpw(args) {
    return createGameLauncher('epw', 'usernameSessionId', this.loading)(args);
  },

  getEpwGameList(args) {
    return createGameListGetter('epw', this.loading)(args);
  },

  launchAwc(args) {
    return createGameLauncher('awc', 'usernameSessionId', this.loading)(args);
  },

  getAwcGameList(args) {
    return createGameListGetter('awc', this.loading, { skipValidation: true })(args);
  },

  launchAio(args) {
    return createGameLauncher('aio', 'usernameSessionId', this.loading)(args);
  },

};
