.tournament-float {
  position: absolute;
  bottom: 4px;
  left: 4px;
}

.tournament-float > .tournament-float-btn {
  padding: 0;
  background: #00000088;
  margin: 4px;
  cursor: pointer;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffffaa;
}

.tournament-float > .tournament-float-btn:hover {
  background: #000000dd;
  color: #ffffffee;
}

.tournament-content .v-tour > .v-step {
  font-family: "Lato", sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #e9faff;
  line-height: 1;
  text-shadow: 0 0 3px #00000029;
  border: 1px solid #5991c1;
  border-radius: 5px;
  background: url(/images/tbg3.png) no-repeat;
  background-size: cover;
  box-shadow: 0px 0px 8px #00000029;
}

.tournament-content .v-tour > .v-step p {
  color: #c3f1fe;
}

.tournament-content .v-step__content {
  text-align: left;
  font-size: 0.8rem;
}

.tournament-content .v-step__buttons {
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.tournament-content .v-step__buttons .v-step__button {
  border: 0.05rem solid #a2dcec;
  color: #c3f1fe;
  text-shadow: 0 0 3px #00000029;
  border-radius: 3px;
}
.tournament-content .v-step__buttons .v-step__button:hover {
  background: #a2dcec88;
  text-shadow: 0 0 3px #00000029;
  color: #50596c;
}

.tournament-content .v-step__buttons .v-step__button.v-step__button-skip {
  border: 0.05rem solid transparent;
  color: #a9e2f2;
}

.tournament-content .v-step__buttons .v-step__button.v-step__button-skip:hover {
  color: #50596c;
}

.tournament-content .v-step__arrow,
.tournament-content .v-step__arrow::before {
  background: #117ba2 !important;
}

.tournament-main .modal-title .modal-title-right {
  text-transform: none;
}

.group-hover .product-wrap .product .suit-wrap img {
  // width: 100%;
  height: 100px;
}

.thin-font-1 {
  width: 125px;
  font-size: 13px;
  font-weight: 400;
}
