<template lang="pug">
div.ng-result
  header.short
    .topbar.info-top
      .d-flex.info-content.align-items-center
        .logo
          img(:src='"img/logo4d/" + domain + ".png"')
          span {{ title }}
        .nav-info.flex-fill
        .nav-info
          a.nav-link(:href="url" :title="domain" target="_blank" ) {{ $t("ui.official_link") }}
  .info-wrapper
    .info-tablewrap.magicZ
      ul.nav.nav-tabs#r-tab(role='tablist')
        li.nav-item(v-for="(gitem, gk, gindex) in tabs")
          a.nav-link(
            :href="'#rb-' + gk"
            :id="'rh-' + gk"
            data-toggle="tab"
            role="tab"
            :aria-controls="'rb-' + gk")
            | {{ $t(getTab(gitem)) }}

      .table-responsive
        .tab-content
          template(v-for="(gitem, gk, gindex) in tabs")
            .tab-pane.fade(:id="'rb-' + gk" role="tabpanel" :aria-labelledby="'rh-' + gk")
              table.table.table-bordered.text-center.table-digits
                tr.bg-color01.text-white
                  //- have to change to multilanguage
                  th(rowspan="2" width="60px" style="vertical-align: middle")  {{ $t('ui.date') }}
                  th(rowspan="2" width="60px" style="vertical-align: middle")  {{ $t('ui.result') }}
                  th(colspan="2" width="60px") {{ $t('ui.first_digit') }}
                  th(colspan="2" width="60px") {{ $t('ui.second_digit') }}
                  th(colspan="2" width="60px") {{ $t('ui.third_digit') }}
                  th(colspan="2" width="60px") {{ $t('ui.forth_digit') }}
                  th(colspan="2" width="60px") {{ $t('ui.total_4d') }}
                  th(colspan="2" width="60px") {{ $t('ui.total_3d') }}
                  th(colspan="2" width="60px") {{ $t('ui.total_1st2d') }}
                  th(colspan="2" width="60px") {{ $t('ui.total_last2d') }}
                tr.text-white
                  th {{ $t('m.LOT_BS') }}
                  th {{ $t('m.LOT_OE') }}
                  th {{ $t('m.LOT_BS') }}
                  th {{ $t('m.LOT_OE') }}
                  th {{ $t('m.LOT_BS') }}
                  th {{ $t('m.LOT_OE') }}
                  th {{ $t('m.LOT_BS') }}
                  th {{ $t('m.LOT_OE') }}
                  th {{ $t('m.LOT_OU') }}
                  th {{ $t('m.LOT_OE') }}
                  th {{ $t('m.LOT_OU') }}
                  th {{ $t('m.LOT_OE') }}
                  th {{ $t('m.LOT_OU') }}
                  th {{ $t('m.LOT_OE') }}
                  th {{ $t('m.LOT_OU') }}
                  th {{ $t('m.LOT_OE') }}
                tr.bg-color03(v-for="(item, key, index) in result" v-if="item.r[gk]")
                  td {{ $dayjs(item.working_date).format("MM/DD/YYYY") }}
                  td {{ item.r[gk] }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][0] == 'BIG' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][0]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][1] == 'ODD' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][1]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][2] == 'BIG' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][2]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][3] == 'ODD' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][3]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][4] == 'BIG' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][4]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][5] == 'ODD' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][5]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][6] == 'BIG' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][6]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][7] == 'ODD' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][7]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][8] == 'OVER' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][8]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][9] == 'ODD' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][9]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][10] == 'OVER' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][10]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][11] == 'ODD' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][11]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][12] == 'OVER' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][12]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][13] == 'ODD' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][13]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][14] == 'OVER' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][14]) }}
                  td
                    .digits.rounded-circle.mx-auto.text-white(:class="item.stats[gk][15] == 'ODD' ? 'bg-danger' : 'bg-primary'") {{ $t('m.LOT_'+item.stats[gk][15]) }}

</template>

<script>
import config from "@/config";
import errors from "@/errors";
import SpinButton from "@/components/ui/SpinButton";
import mixinExt from "@/library/mixinExt.js";

export default {
  components: {
    SpinButton
  },
  mixins: [mixinExt],
  data() {
    return {
      leagueId: 0,
      title: "",
      url: "",
      img: "",
      domain: "",
      tabs: 0,
      result: []
    };
  },
  computed: {},
  created() {
    // $("body").addClass("wbet-bg");
  },
  destroyed() {},
  mounted() {
    var q = this.$route.query;

    if (q.q) {
      this.leagueId = q.q;
      this.title = q.n.replace("$", "+");
      this.url = q.u;
      this.domain = this.extractDomain(q.u);
      setTimeout(() => {
        this.query();
      }, 500);
    }
  },
  methods: {
    getTab(e) {
      return config.sports4dTab[100 + e];
    },
    extractDomain(url) {
      var domain;
      //find & remove protocol (http, ftp, etc.) and get domain
      if (url.indexOf("://") > -1) {
        domain = url.split("/")[2];
      } else {
        domain = url.split("/")[0];
      }

      //find & remove www
      if (domain.indexOf("www.") > -1) {
        domain = domain.split("www.")[1];
      }

      domain = domain.split(":")[0]; //find & remove port number
      domain = domain.split("?")[0]; //find & remove url params

      return domain;
    },
    genResultList() {
      var n = this.schedule.NgResult;
      this.tabs = 0;

      for (var i = 0; i < n.length; i++) {
        var m = n[i].result1;
        n[i].r = n[i].result1.split(",");

        if (this.tabs < n[i].r.length) {
          this.tabs = n[i].r.length;
        }
        n[i].stats = [];
        for (var j = 0; j < n[i].r.length; j++) {
          var m = n[i].r[j];

          var stats = [];

          for (var q = 0; q <= 3; q++) {
            if (parseInt(m[q]) > 4.5) {
              stats.push("BIG");
            } else {
              stats.push("SMALL");
            }

            if (parseInt(m[q]) % 2 != 0) {
              stats.push("ODD");
            } else {
              stats.push("EVEN");
            }
          }

          var dg1 = parseInt(m[0]);
          var dg2 = parseInt(m[1]);
          var dg3 = parseInt(m[2]);
          var dg4 = parseInt(m[3]);

          //4digit
          var t4 = dg1 + dg2 + dg3 + dg4;
          if (t4 > 18.5) {
            stats.push("OVER");
          } else {
            stats.push("UNDER");
          }

          if (t4 % 2 != 0) {
            stats.push("ODD");
          } else {
            stats.push("EVEN");
          }

          //3digit
          var t3 = dg2 + dg3 + dg4;
          if (t3 > 13.5) {
            stats.push("OVER");
          } else {
            stats.push("UNDER");
          }

          if (t3 % 2 != 0) {
            stats.push("ODD");
          } else {
            stats.push("EVEN");
          }

          //1st 2 digit
          var tf2 = dg1 + dg2;
          if (tf2 > 9.5) {
            stats.push("OVER");
          } else {
            stats.push("UNDER");
          }

          if (tf2 % 2 != 0) {
            stats.push("ODD");
          } else {
            stats.push("EVEN");
          }

          //last 2 digit
          var tl2 = dg3 + dg4;
          if (tl2 > 9.5) {
            stats.push("OVER");
          } else {
            stats.push("UNDER");
          }

          if (tl2 % 2 != 0) {
            stats.push("ODD");
          } else {
            stats.push("EVEN");
          }

          n[i].stats.push(stats);
        }
      }

      this.result = n;
    },
    query() {
      // this function is in mixinExt.js
      this.getNgResult(this.leagueId).then(() => {
        this.genResultList();
        this.$nextTick(() => {
          setTimeout(() => {
            $("ul li:first-child a").tab("show");
          }, 1000);
        });
      });
    }
  }
};
</script>
