<template lang="pug">
#room-create.modal.fade.modal-room
  .modal-dialog.modal-xl.modal-dialog-centered.modal-dialog-scrollable
    .modal-content
      .modal-header
        .modal-title
          .modal-title-left {{ $t('ui.select_matches') }}
          .modal-title-right(v-if="selected.length < maxMatch")
            | {{ $t('ui.selected') }}
            span.modal-match-no {{ selected.length }}
            | {{ $t('ui.matches') }}
          .modal-title-right.modal-match-no.blink(v-else)
            | {{ $t('error.maxMatch').replace('{max}', selected.length.toString()) }}

      template(v-if="listMode == 0")
        .modal-body
          .date-wrapper
            template(v-for="day in days")
              .date-block(:class="{ 'today': compareDates(day, selectedWeekday) }", @click="selectThisWeekday(day)")
                .select-day-top(v-if="compareDates(day, today)" :class="{ 'today': compareDates(day, selectedWeekday) }") {{ $t('ui.today') }}
                .select-day-top(v-else :class="{ 'today': compareDates(day, selectedWeekday) }")
                  | {{ compareDates(day, today, 1) ? $t('ui.tomorrow') : $t('ui.' + $dayjs(day).format('dddd').toLowerCase()) }}
                .select-day-bottom {{ $dayjs(day).locale('cn').format('MMMM DD') }}
                .select-day-badge(v-if="getMatchCountByDate(day) > 0") {{ getMatchCountByDate(day) }}
          .select-league-wrapper(v-if="matchList[selectedWeekdayFormatted] != null && Object.keys(matchList[selectedWeekdayFormatted]).length > 0")
            .select-league-header
              .tournament-pagination.flex-fill
                v-pagination(
                  :value="pages[selectedWeekdayFormatted].value"
                  :page-count="pages[selectedWeekdayFormatted].count"
                  :classes="bootstrapPaginationClasses"
                  :labels="paginationAnchorTexts"
                  @input="changePage($event)"
                )
              .tournament-pagination
                .tournament-arrow(@click="toggleAllLeagues")
                  i.fad.ml-2(:class="collapseAll == true ? 'fa-chevron-up' : 'fa-chevron-down'")
            template(v-for="league in matchList[selectedWeekdayFormatted]")
              .select-league
                .select-league-title
                  .select-league-checkbox
                    label.date-check
                      input(type="checkbox" @change="toggleLeague($event.target, league)" :checked="checkLeague(selectedWeekdayFormatted, league.league_id)")
                      span.checkmark-date
                  .select-league-box.collapsed(
                    data-toggle="collapse"
                    :data-target="'#room-create-league-' + league.league_id"
                  )
                    .tournament-league-club
                      img(v-if="league.logo" :src="getLogo('league', league.logo)")
                    .tournament-text.flex-fill {{ getLeagueName(league) }}
                    .tournament-arrow
                      i.fad.ml-2.fa-chevron-up
                .select-league-body.collapse(:id="'room-create-league-' + league.league_id")
                  template(v-for="match in league.matches")
                    .select-league-row(:class="{ 'selected': selected.includes(match.match_id) }", @click="toggleMatch(match.match_id)" style="padding-left: 54px;")
                      .select-league-checkbox
                        label.date-check
                          input(type="checkbox" :checked="selected.includes(match.match_id)" disabled)
                          span.checkmark-date
                      .select-league-time
                        span(v-if="match.match_time") {{ $dayjs(match.match_time).format("MM/DD, hh:mm A") }}
                        span(v-else) --:--
                      .select-league-teams
                        | {{ getName('home_name', match) }}
                        span.home-vs-away -vs-
                        | {{ getName('away_name', match) }}
          .select-league-wrapper(v-else)
            .round-alert.m-3 {{ $t('message.no_matches') }}

        .modal-footer
          .room-bottom
            .room-rate
              .room-rate-text {{ $t('ui.entry_fee') }}:
              .room-rate-select
                select(v-model="room.rate")
                  option(v-for="rate in [20, 50, 100, 200, 500, 1000]" :value="rate")
                    | {{ rate }}
                    | &nbsp;({{ currency_code }} {{ $numeral(rate / currency_rate).format('0,0.00') }})
            .room-rate
              .room-limit-text {{ $t('ui.players') }}:
              .room-limit-select
                select(v-model="room.limit")
                  option(v-for="limit in [3,4,5,6,7,8,9,10,12,15,20,30,50,80,100]" :value="limit") {{ limit }}
            .room-rate
              .room-limit-text {{ $t('ui.winner') }}:
              .room-limit-select
                select(v-model="room.formula")
                  option(value="1") 3
                  option(value="2") 1
            .room-limit
              .room-limit-text {{ $t('ui.room_password') }}:
              .room-rate-input
                input.form-control(type="text" v-model="password" :placeholder="$t('message.pin_code')" style="width: 150px;" minlength="6" maxlength="6" pattern="[0-9]*" inputmode="numeric" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false")
              small.room-limit-text.ml-2(style="color: #00ff0088;" v-if="feedback.password") {{ feedback.password }}
            .tournament-create-room.tournament-button-hover(@click="proceedNext" v-if="selected.length >= 3 && password != null && password != ''" :class="{ 'disabled': loading.roomCreate }")
              .tournament-text {{ $t('ui.next') }}
              .tournament-icon
                i.fas.fa-chevron-circle-right
          //- small {{ selected }}
      template(v-else)
        .modal-body
          .m-1
            iframe(:src="rulesUrl" loading="lazy")
          .tournament-room.mx-3.mt-1.mb-3.d-flex.align-items-center
            .select-league-row.flex-fill.border-0
              .select-league-checkbox
                label.date-check
                  input(type="checkbox" v-model="isAgree")
                  span.checkmark-date
              .select-league-teams(style="font-size: 13px;") {{ $t("message.accept_terms") }}
            .tournament-button.tournament-button-primary.tournament-button-hover.mr-3(@click="proceedNext" v-if="isAgree && selected.length >= 3 && password != null && password != ''" :class="{ 'disabled': loading.roomCreate }")
              .tournament-text {{ $t('ui.create_room') }}
              .tournament-icon
                i.fad.fa-plus-circle
            .tournament-button.tournament-button-secondary.tournament-button-hover(@click="cancel")
              .tournament-text {{ $t('ui.cancel') }}
              .tournament-icon
                i.fas.fa-times
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import service from "@/tournament2/library/_xhr.js";
import vPagination from "vue-plain-pagination";

import { required, numeric, minLength, maxLength } from "vuelidate/lib/validators";

export default {
  components: {
    vPagination,
  },
  data() {
    return {
      options: {
        slidesToShow: 3,
        slidesToScroll: 1,
        // dots: false,
        // arrows: true,
        // variableWidth: true,
        // infinite: true,
        // focusOnSelect: true,
        // centerMode: true,
        // centerPadding: "24px",
      },
      maxMatch: 50,
      isAgree: false,
      listMode: 1,
      collapseAll: false,
      bootstrapPaginationClasses: {
        ul: "pagination",
        li: "page-item",
        liActive: "active",
        liDisable: "disabled",
        button: "page-link",
        buttonActive: "active",
        buttonDisable: "disable",
      },
      paginationAnchorTexts: {
        first: "<i class='fas fa-angle-double-left'></i>",
        prev: "<i class='fas fa-angle-left'></i>",
        next: "<i class='fas fa-angle-right'></i>",
        last: "<i class='fas fa-angle-double-right'></i>",
      },
      selected: [],
      days: [],
      today: new Date(),
      selectedWeekday: new Date(),
      matchList: {},
      pages: {},
      loading: {
        getMatchLookup: false,
        roomCreate: false,
        validateTournament: false,
      },
      room: {
        rate: 100,
        limit: 10,
        formula: 1,
      },
      password: "",
      feedback: {
        password: "",
        timeout: null,
      },
      currency_rate: 0,
    };
  },
  validations: {
    password: {
      required,
      numeric,
      minLength: minLength(6),
      maxLength: maxLength(6),
    },
  },
  computed: {
    rulesUrl() {
      var lang = "en";
      switch (this.$store.getters.language) {
      case "cn":
        lang = this.$store.getters.language;
        break;
      default:
        lang = "en";
      }
      return config.rulesUrl + "/tournament/rules_" + lang + ".html";
    },
    selectedWeekdayFormatted() {
      return this.$dayjs(this.selectedWeekday).format("YYYY-MM-DD");
    },
    todayFormatted() {
      return this.$dayjs(this.today).format("YYYY-MM-DD");
    },
    language() {
      return this.$store.getters.language;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
    pageSize() {
      return 100;
    },
  },
  destroyed() {
    EventBus.$off("ROOM_CREATE", this.show);
  },
  mounted() {
    this.generateThisWeek();
    this.getMatchLookup().then(() => {});
    EventBus.$on("ROOM_CREATE", this.show);
  },
  methods: {
    cancel() {
      this.isAgree = false;
      this.listMode = 0;
    },
    show(e) {
      this.generateThisWeek();
      this.getMatchLookup().then(() => {
        $("#room-create").modal("show");
      });
    },
    checkLeague(e, f) {
      var m = this.matchList[e][f]["matches"];
      var r = false;
      for (var n = 0; n < m.length; n++) {
        r = this.selected.includes(m[n].match_id);
        if (r == false) break;
      }
      return r;
    },
    toggleLeague(e, f) {
      if (e.checked) {
        if (f.matches.length + this.selected.length < this.maxMatch) {
          for (var i = 0; i < f.matches.length; i++) {
            if (this.selected.indexOf(f.matches[i].match_id) == -1) {
              this.selected.push(f.matches[i].match_id);
            }
          }
        } else {
          e.checked = false;
          this.$swal("Warning", this.$t("message.over_max_match"), "warning");
        }
      } else {
        for (var i = 0; i < f.matches.length; i++) {
          if (this.selected.indexOf(f.matches[i].match_id) != -1) {
            this.selected.splice(this.selected.indexOf(f.matches[i].match_id), 1);
          }
        }
      }
    },
    toggleAllLeagues() {
      if (this.collapseAll === false) {
        $(".select-league-body").collapse("show");
        this.collapseAll = true;
      } else {
        $(".select-league-body").collapse("hide");
        this.collapseAll = false;
      }
    },
    toggleMatch(e) {
      if (this.selected.includes(e)) {
        this.selected.splice(this.selected.indexOf(e), 1);
      } else {
        if (this.selected.length < this.maxMatch) {
          this.selected.push(e);
        }
      }
    },
    getMatchCountByDate(day) {
      var count = 0;
      var d = this.$dayjs(day).format("YYYY-MM-DD");

      if (this.matchList[d]) {
        var keys = Object.keys(this.matchList[d]);
        for (var i = 0; i < keys.length; i++) {
          var value = this.matchList[d][keys[i]];
          for (var j = 0; j < value.matches.length; j++) {
            if (this.selected.includes(value.matches[j].match_id)) {
              count++;
            }
          }
        }
      }
      return count;
    },
    getName(p, e) {
      var name = e[p + "_" + this.language];
      if (name == null || name == "" || !name) {
        name = e[p];
      }
      return name;
    },
    getLeagueName(e) {
      var name = e["name_" + this.language];
      if (name == null || name == "" || !name) {
        name = e.league_name;
      }
      return name;
    },
    getLogo(p, e) {
      return config.flagPath + p + "/" + e;
    },
    compareDates(date1, date2, day = 0) {
      return date1.getDate() === date2.getDate() + day && date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear();
    },
    generateThisWeek() {
      this.cancel();
      this.selected = [];
      this.days = [];
      this.today = new Date();
      this.selectedWeekday = this.today;
      for (let i = 0; i < 30; i++) {
        const date = new Date(this.today);
        date.setDate(date.getDate() + i);
        this.days.push(date);
      }
    },
    selectThisWeekday(e) {
      this.selectedWeekday = e;
      this.getMatchLookup().then(() => {});
    },
    changePage(e) {
      this.pages[this.selectedWeekdayFormatted].value = e;
      this.getMatchLookup().then(() => {});
    },
    getMatchLookup() {
      return new Promise((resolve, reject) => {
        var d = this.selectedWeekdayFormatted;
        var p = 1;
        if (this.pages[d]) {
          p = this.pages[d].value;
        } else {
          this.pages[d] = {
            value: 1,
            count: 1,
            total: 0,
          };
        }
        var json = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          sports_type: 1,
          match_date: d,
          page_number: p,
          page_size: this.pageSize,
          currency_code: this.$store.getters.currencyCode,
        };

        var feedback = {
          success: false,
          status: errors.session.invalidSession,
        };

        this.loading.getMatchLookup = true;
        service.getMatchLookup(config.tournamentUrl().matchlookup, json).then(
          (result) => {
            this.loading.getMatchLookup = false;
            if (result) {
              feedback.success = result.success;
              feedback.status = result.status;
              if (result.success == true) {
                var rate = result.data.rate;
                if (rate != null) {
                  this.currency_rate = rate;
                }

                var matches = result.data.value;

                var leagues = {};
                matches.forEach((match) => {
                  if (!leagues.hasOwnProperty(match.league_id)) {
                    leagues[match.league_id] = {
                      league_id: match.league_id,
                      league_name: match.league_name,
                      logo: match.logo,
                      name_kr: match.name_kr,
                      name_en: match.name_en,
                      name_cn: match.name_cn,
                      name_jp: match.name_jp,
                      name_th: match.name_th,
                      name_vn: match.name_vn,
                      name_id: match.name_id,
                      name_my: match.name_my,
                      matches: [],
                    };
                  }
                  leagues[match.league_id].matches.push(match);
                });

                if (!this.matchList.hasOwnProperty(d)) {
                  this.$set(this.matchList, d, {});
                }
                if (!this.pages.hasOwnProperty(d)) {
                  this.$set(this.pages, d, {
                    value: 1,
                    count: 1,
                    total: 0,
                  });
                }
                this.$set(this.matchList, d, leagues);
                if (matches.length > 0) {
                  this.pages[d].total = matches[0].totalrows;
                  if (this.pages[d].total % this.pageSize != 0) this.pages[d].count = Math.ceil(parseFloat(this.pages[d].total) / parseFloat(this.pageSize));
                  else this.pages[d].count = parseFloat(this.pages[d].total) / parseFloat(this.pageSize);
                } else {
                  this.pages[d].count = 1;
                  this.pages[d].value = 1;
                  this.pages[d].total = 0;
                }
              } else {
                this.$helpers.handleFeedback(feedback.status);
              }
            }
            resolve(result);
          },
          (err) => {
            this.loading.getMatchLookup = false;
            feedback.success = false;
            feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);
            reject(err);
          }
        );
      });
    },
    proceedNext() {
      this.$v.$touch();
      clearTimeout(this.feedback.timeout);

      if (!this.$v.$invalid) {
        if (this.listMode == 0) {
          this.listMode = 1;
        } else {
          this.validateTournament(this.askForConfirmation);
        }
      } else {
        this.reset();
        this.$swal({
          title: "Warning",
          text: this.$t("message.room_password_rule"),
          type: "warning",
          showCancelButton: false,
        });
      }

      this.feedback.timeout = setTimeout(() => {
        this.reset();
        this.$v.$reset();
      }, 5000);
    },
    askForConfirmation() {
      this.$v.$touch();
      clearTimeout(this.feedback.timeout);

      if (!this.$v.$invalid) {
        this.$swal
          .fire({
            title: this.$t("ui.create_room"),
            text: this.$t("message.are_you_sure"),
            icon: "warning",
            showCancelButton: true,
            confirmButtonText: this.$t("ui.yes"),
          })
          .then((result) => {
            if (result.value) {
              this.roomCreate();
              $("#room-create").modal("hide");
            }
          });
      } else {
        this.reset();
        this.$swal({
          title: "Warning",
          text: this.$t("message.room_password_rule"),
          type: "warning",
          showCancelButton: false,
        });
      }

      this.feedback.timeout = setTimeout(() => {
        this.reset();
        this.$v.$reset();
      }, 5000);
    },
    validateTournament(callback) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      this.loading.validateTournament = true;
      service.validateTournament(json).then(
        (result) => {
          this.loading.validateTournament = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              if (result.data.result === "true") {
                if (callback) callback();
              } else {
                this.$helpers.handleFeedback("tournamentDisabled");
              }
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          this.loading.validateTournament = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    reset() {
      this.feedback.password = "";
    },
    roomCreate() {
      if (this.loading.roomCreate) {
        return;
      }

      if (this.selected.length == 0) {
        this.$helpers.handleFeedback("noMatchSelected");
        return;
      }

      if (isNaN(this.room.rate) || this.room.rate < 10 || this.room.rate > 1000) {
        this.$helpers.handleFeedback("invalidRoomRate");
        return;
      }

      if (isNaN(this.room.limit) || this.room.limit < 3 || this.room.limit > 100) {
        this.$helpers.handleFeedback("invalidRoomLimit");
        return;
      }

      if (isNaN(this.password) || this.password.length != 6) {
        this.$helpers.handleFeedback("invalidRoomPassword");
        return;
      }

      var matchIds = "";
      for (let i = 0; i < this.selected.length; i++) {
        if (i == 0) {
          matchIds = this.selected[i];
        } else {
          matchIds = matchIds + "," + this.selected[i];
        }
      }

      var operator_type = this.$store.getters.operatorType;
      var parent_id = this.$store.getters.parentId;

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        match: matchIds,
        room_type: 2,
        room_rate: this.room.rate,
        room_limit: this.room.limit,
        room_pwd: this.password,
        operator_type: operator_type,
        parent_id: parent_id,
        pay_type: 2,
        formula: Number(this.room.formula),
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      var url = config.tournamentUrl().roomcreate;
      if (operator_type && operator_type == 2) {
        url = config.tournamentUrl().vroomcreate;
      }

      this.loading.roomCreate = true;
      service.roomCreate(url, json).then(
        (result) => {
          this.loading.roomCreate = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              this.selected = [];
              this.room = {
                rate: 100,
                limit: 10,
                password: "",
              };
              this.matchList = {};
              var room_id = 0;
              if (result.data.value[0]) {
                room_id = result.data.value[0].room_id;
              }
              EventBus.$emit("roomEnterById2", room_id);
            } else {
              if (feedback.status == "maxMatch") {
                this.$swal("Error", this.$t("error.maxMatch").replace("{max}", this.maxMatch.toString()), "error").then((result) => {});
              } else {
                this.$helpers.handleFeedback(feedback.status, true);
              }
            }
          }
        },
        (err) => {
          this.loading.roomCreate = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          if (err.status == "maxMatch") {
            this.$swal("Error", this.$t("error.maxMatch").replace("{max}", this.maxMatch.toString()), "error").then((result) => {});
          } else {
            this.$helpers.handleFeedback(err.status, true);
          }
        }
      );
    },
  },
};
</script>
