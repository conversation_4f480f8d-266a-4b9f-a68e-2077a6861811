<template lang="pug">
.info-wrapper
  .info-title
    .info-icon
      i.fad.fa-receipt
    .page-title(aria-label='breadcrumb')
      ol.breadcrumb.p-0.m-0
        li.breadcrumb-item(aria-current='page') {{ $t("ui.bet_list") }}
        li.breadcrumb-item.active(v-if="mode == 0" aria-current='page') {{ $t("ui.sportsbook") }}
        li.breadcrumb-item.active(v-if="mode == 1" aria-current='page') {{ $t("ui.w4d") }}
    template(v-if="!whiteLabel && w4dSupport")
      SpinButton(:text="$t('ui.sportsbook')" :css="'btn-sm mr-2 px-3 text-uppercase ' + (mode == 0 ? 'btn-warning' : 'btn-secondary')" @click="setMode(0)" img="")
      SpinButton(:text="$t('ui.w4d')" :css="'btn-sm px-3 text-uppercase ' + (mode == 1 ? 'btn-warning' : 'btn-secondary')" @click="setMode(1)" img="")
      .btn-seperator
    SpinButton(text="" :loading="feedback.loading" css="btn-sm btn-info" @click="refreshList" img="fad fa-sync-alt w-16px")
  betListSportsbook(v-if="mode == 0" @loading="handleLoading")
  betList4d(v-if="mode == 1" @loading="handleLoading")
  .notes
    p.mb-0 {{ $t("ui.note") }}:
    ul
      li {{ $t("message.time_display_in_gmt_plus_8") }}
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import SpinButton from "@/components/ui/SpinButton";
import betListSportsbook from "@/components/desktop/info/betList/betListSportsbook";
import betList4d from "@/components/desktop/info/betList/betList4d";

export default {
  components: {
    SpinButton,
    betListSportsbook,
    betList4d
  },
  data() {
    return {
      mode: 0,
      feedback: {
        loading: false,
        success: false,
        status: errors.session.invalidSession,
        source: ""
      }
    };
  },
  computed: {
    w4dSupport() {
      return config.w4dSupport;
    },
    whiteLabel() {
      return config.whiteLabel;
    }
  },
  methods: {
    setMode(e) {
      this.feedback.loading = false;
      this.mode = e;
    },
    handleLoading(e) {
      this.feedback.loading = e;
    },
    refreshList() {
      EventBus.$emit("BETLIST_REFRESH");
    }
  }
};
</script>
