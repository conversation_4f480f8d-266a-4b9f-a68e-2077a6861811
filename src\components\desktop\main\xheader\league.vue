<template lang="pug">
.hx-league(:class="source.marketId == 3 ? 'live' : ([41, 42, 43, 44].includes(source.sportsId) == true ? 'live' : 'non-live')")
  .hx-corner-btn(:id="source.id", @click="toggleGroup()", :class="['c-league', source.categoryId]")
    i.fal.fa-chevron-up
  .hx-button.hx-fav(@click="xToggleFavs(source.matches, $event)")
    .hx-icon
      i(:class="fav == true ? 'selected fas fa-star' : 'fal fa-star'")
  .hx.flex-fill.d-flex.pointable.dark(@click="toggleGroup()")
    .hx(v-if="[40].includes(source.sportsId)") {{ $dayjs(source.lastMatchTime).format('MM/DD hh:mm A') }} {{ source.leagueName }}
    .hx(v-else, :data-game-type="source.gameType", :data-is-parent="source.isParent") {{ source.leagueName }}
    .hx-info(v-if="![40].includes(source.sportsId) && source.leagueInfo != null", :title="source.leagueInfo", data-toggle="tooltip")
      i.fas.fa-info-circle
  //- .hx.flex-fill
  .hx.d-flex.align-items-center.justify-content-end
    span.pointable.dark.px-1(@click="refresh()")
      template(v-if="source.marketId == 3")
        template(v-if="state.GET_LIVE || state.GET_MARKET || state.INVALIDATE")
          i.fad.fa-spinner.fa-spin
        template(v-else)
          i.fas.fa-redo(style="font-size: 11px;")
      template(v-else)
        template(v-if="state.GET_NONLIVE || state.GET_MARKET || state.INVALIDATE")
          i.fad.fa-spinner.fa-spin
        template(v-else)
          i.fas.fa-redo(style="font-size: 11px;")
</template>

<script>
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";
import mixinDelay from "@/library/mixinDelay";

export default {
  mixins: [mixinDelay],
  props: {
    source: {
      type: Object,
    },
  },
  data() {
    return {
      fav: false,
      state: {
        GET_LIVE: false,
        GET_NONLIVE: false,
        GET_MARKET: false,
        INVALIDATE: false,
      },
      func: {
        GET_LIVE: null,
        GET_NONLIVE: null,
        GET_MARKET: null,
        INVALIDATE: null,
      },
    };
  },
  computed: {
    favorite() {
      return this.$store.getters.favorite;
    },
    menu0() {
      return this.$store.getters.menu0;
    },
  },
  destroyed() {
    EventBus.$off("GET_LIVE", this.runLive);
    EventBus.$off("GET_NONLIVE", this.runNonLive);
    EventBus.$off("GET_MARKET", this.runMarket);
    EventBus.$off("INVALIDATE", this.runInvalidate);
  },
  mounted() {
    if (this.favorite) {
      var isAll = true;
      var items = this.source.matches;
      for (var n in items) {
        if (this.favorite.indexOf(items[n][2]) == -1) {
          isAll = false;
        }
      }
      this.fav = isAll;
    }

    if (this.menu0 == "favorite") {
      this.fav = true;
    }
    EventBus.$on("GET_LIVE", this.runLive);
    EventBus.$on("GET_NONLIVE", this.runNonLive);
    EventBus.$on("GET_MARKET", this.runMarket);
    EventBus.$on("INVALIDATE", this.runInvalidate);
    this.func.GET_LIVE = this.debounce(this.getLive, 500);
    this.func.GET_NONLIVE = this.debounce(this.getNonLive, 500);
    this.func.GET_MARKET = this.debounce(this.getMarket, 500);
    this.func.INVALIDATE = this.debounce(this.invalidate, 500);
  },
  methods: {
    grouper() {
      var n = false;
      switch (this.source.marketId) {
      case 3:
        if (this.source.grouper >= 20) {
          n = true;
        }
        break;
      default:
        if (this.source.grouper >= 5) {
          n = true;
        }
        break;
      }
      if (n == true) {
        this.$nextTick(() => {
          var elem = $("#" + this.source.id);
          var a = $(".c-match." + this.source.id);
          elem.addClass("collapsed").attr("aria-expanded", "false");
          a.removeClass("show");
        });
        // } else {
        //   this.$nextTick(() => {
        //     var a = $(".c-match." + this.source.id);
        //     a.each((index) => {
        //       setTimeout(() => {
        //         $(a[index]).addClass("show");
        //       }, 100);
        //     });
        //     // setTimeout(() => {
        //     //   a.addClass("show");
        //     // }, 2000);
        //   });
      }
    },
    getLive() {
      EventBus.$emit("GET_LIVE");
    },
    getNonLive() {
      EventBus.$emit("GET_NONLIVE");
    },
    getMarket() {
      EventBus.$emit("GET_MARKET");
    },
    invalidate() {
      EventBus.$emit("INVALIDATE");
    },
    runLive(e) {
      this.state.GET_LIVE = true;
      setTimeout(() => {
        this.state.GET_LIVE = false;
      }, 1000);
    },
    runNonLive(e) {
      this.state.GET_NONLIVE = true;
      setTimeout(() => {
        this.state.GET_NONLIVE = false;
      }, 2000);
    },
    runMarket(e) {
      this.state.GET_LIVE = true;
      this.state.GET_NONLIVE = true;
      this.state.GET_MARKET = true;
      setTimeout(() => {
        this.state.GET_LIVE = false;
        this.state.GET_NONLIVE = false;
        this.state.GET_MARKET = false;
      }, 3000);
    },
    runInvalidate(e) {
      this.state.GET_LIVE = true;
      this.state.GET_NONLIVE = true;
      this.state.GET_MARKET = true;
      this.state.INVALIDATE = true;
      setTimeout(() => {
        this.state.GET_LIVE = false;
        this.state.GET_NONLIVE = false;
        this.state.GET_MARKET = false;
        this.state.INVALIDATE = false;
      }, 5000);
    },

    hardRefresh() {
      EventBus.$emit("INVALIDATE");
    },
    refresh() {
      if (this.source.marketId == 3) {
        if (this.func.GET_LIVE) {
          this.func.GET_LIVE();
        }
      } else {
        if (this.func.GET_NONLIVE) {
          this.func.GET_NONLIVE();
        }
      }
    },
    toggleGroup() {
      var elem = $("#" + this.source.id);
      var a = $(".c-match." + this.source.id);
      if (elem.hasClass("collapsed")) {
        elem.removeClass("collapsed").attr("aria-expanded", "true");
        setTimeout(() => {
          a.addClass("show");
        }, 100);
      } else {
        elem.addClass("collapsed").attr("aria-expanded", "false");
        setTimeout(() => {
          a.removeClass("show");
        }, 100);
      }
    },
    xToggleFavs(items, e) {
      var favList = [];
      for (var n in items) {
        favList.push(items[n][2]);
      }
      if (this.fav == false) {
        this.$store.dispatch("layout/setFavorite", favList);
        this.fav = true;
      } else {
        this.$store.dispatch("layout/delFavorite", favList);
        this.fav = false;
      }

      if (this.menu0 == "favorite") {
        this.fav = true;
        EventBus.$emit("GET_MARKET");
      }
    },
  },
};
</script>
