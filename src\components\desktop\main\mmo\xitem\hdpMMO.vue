<template lang="pug">
  .hx-col.hx-cols(:class="cls ? cls : 'w-98'")
    template(v-if="details[betType] != null && details[betType][i] && details[betType][i][9] != 0 && details[betType][i][10] != 0 && details[betType][i][9] != '' && details[betType][i][10] != ''")
      .hx
        .hxs.w-49
          .ball-value.ball-mmo(v-if="details[betType][i][7] == 1").d-flex
            .mmo(:class="{ red : details[betType][i][25] < 0 }") {{ details[betType][i][25] }}
            .percent(:class="{ red : details[betType][i][24] < 0 }") ({{ details[betType][i][24] }})
            .giving H
        .hxs.w-49r
          mmoItem(:odds="details[betType][i]" :idx="details[betType][i][7] == 1 ? '10' : '9'" pos=22 :typ="oddsType" dataType="1")
      .hx
        .hxs.w-49
          .ball-value.ball-mmo(v-if="details[betType][i][7] == 0").d-flex
            .mmo(:class="{ red : details[betType][i][25] < 0 }") {{ details[betType][i][25] }}
            .percent(:class="{ red : details[betType][i][24] < 0 }") ({{ details[betType][i][24] }})
            .giving A
        .hxs.w-49r
          mmoItem(:odds="details[betType][i]" :idx="details[betType][i][7] == 1 ? '9' : '10'" pos=22 :typ="oddsType" dataType="1")
</template>

<script>
export default {
  components: {
    mmoItem: () => import("@/components/desktop/main/mmo/mmoItem")
  },
  props: {
    cls: {
      type: String
    },
    details: {
      type: Object
    },
    oddsType: {
      type: String
    },
    item: {
      type: String
    },
    i: {
      type: [String, Number]
    },
    betType: {
      type: String
    }
  },
};
</script>
