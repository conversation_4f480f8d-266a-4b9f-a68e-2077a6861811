export default {
  OK: "Baik",
  CLOSE: "Akun telah ditutup!",
  BLOCK: "Akun telah diblokir!",
  FREEZE: "Akun telah dibekukan!",
  SUSPEND: "Akun Anda telah berubah menjadi hanya LIHAT.",
  passwordRequired: "Diperlukan kata sandi!",
  usernameRequired: "Nama pengguna harus diisi!",
  incompletedRequest: "Something goes wrong...",

  session_not_exists: "Sesi berakhir! Silakan masuk lagi untuk melanjutkan...",
  session_expired: "Sesi berakhir! Silakan masuk lagi untuk melanjutkan...",
  sessionExpired: "Sesi berakhir! Silakan masuk lagi untuk melanjutkan...",
  invalidSession: "Sesi berakhir! Silakan masuk lagi untuk melanjutkan...",
  accountIdRequired: "Sesi berakhir! Silakan masuk lagi untuk melanjutkan...",
  NoUserAccessRight: "Izin ditolak! Silakan hubungi upline untuk melanjutkan ...",
  account_not_exists: "Kata sandi salah",
  invalid_password: "Kata sandi salah",
  secondary_account_exists: "Nama panggilan Anda sudah dibuat.",
  systemError: "Kesalahan Sistem Internal",
  new_login_id_exists: "Nama panggilan tidak valid, cobalah untuk mengatur nama panggilan yang berbeda.",
  loginLimit: "Anda terlalu sering masuk, silakan coba lagi setelah 1 menit",
  requestLimit: "You have request too frequent.",

  insufficient_balance: "Saldo tidak mencukupi",
  loginFailed: "Gagal masuk, silakan coba lagi nanti",
  requestFailed: "Koneksi Anda tidak stabil. Periksa koneksi Anda dan coba lagi.",
  requestPending: "Permintaan tertunda",
  close: "Akun ditutup! Silakan hubungi upline Anda untuk melanjutkan",
  suspend: "Akun ditangguhkan! Silakan hubungi upline Anda untuk melanjutkan",
  freeze: "Akun dibekukan! Silakan hubungi upline Anda untuk melanjutkan",
  alphaNumOnly: "Hanya alfanumerik",
  // vuelidate
  isRequired: "Silakan masuk {fname}.",
  isMinValue: "{fname} harus memiliki minimal {fvalue}.",
  isMaxValue: "harus kurang dari atau sama dengan",
  isMinLength: "{fname} harus memiliki setidaknya {fvalue} karakter",
  isMaxLength: "harus kurang dari atau sama dengan",
  isAlpha: "hanya menerima alfabet",
  isAlphaNum: "hanya menerima alfanumerik",
  isNumeric: "hanya menerima angka",
  isEmail: "hanya menerima alamat email yang valid",
  isIpAddress: "hanya menerima alamat IPv4 yang valid",
  isSameAs: "{fname} harus sama dengan {fname2}.",
  isUrl: "hanya menerima URL",
  containAlphaNum: "harus memiliki setidaknya satu alfabet dan satu nomor.",
  selectOption: "Silakan pilih",
  notSameAs: "{fname} dan {fname2} tidak bisa sama.",
  currPasswordRequired: "Kata sandi saat ini diperlukan",
  newPasswordRequired: "Diperlukan kata sandi baru!",
  confirmNewPasswordRequired: "Diperlukan kata sandi baru!",
  passwordsNotMatch: "Sandi tidak cocok!",
  nickNameRequired: "Nama panggilan wajib diisi!",
  startDateRequired: "Diperlukan tanggal mulai!",
  endDateRequired: "Tanggal akhir diperlukan!",
  pageSizeRequired: "Diperlukan ukuran halaman!",
  pageNumberRequired: "Nomor halaman wajib diisi!",
  sportsTypeRequired: "Jenis olahraga diperlukan!",
  workingDateRequired: "Tanggal kerja diperlukan!",
  leagueIdRequired: "ID Liga diperlukan!",
  isOutrightRequired: "Is outright is required!",

  duplicate_debit_record: "Please wait a moment while odds is updating...",
  invalidOddsBall: "Please wait a second while odds is updating...",
  oddsDisplayInvalid: "Please wait while odds is updating...",
  oddsIsUpdating: "Odds Is Updating...",
  unableToGetBalanceAtTheMoment: "Kesalahan server internet",
  atLeastTwoMatchToBetParlay: "Setidaknya mencampur 2 pertandingan untuk bertaruh dalam parlay",
  atLeastThreeMatchToBetParlay: "Setidaknya mencampur 3 pertandingan untuk bertaruh dalam parlay",
  maxParlayTicket: "Reach maximum ticket per parlay!",
  matchHide: "Pasar ini tidak tersedia, silakan coba pasar lain.",
  matchNotAllowBet: "Pasar ini tidak tersedia, silakan coba pasar lain.",
  invalidBets: "Pasar ini tidak tersedia, silakan coba pasar lain.",
  invalidBet: "Pasar ini tidak tersedia, silakan coba pasar lain.",
  betOverMaxPerMatch: "Your stake exceeds the max stake.",
  betOverMax: "Your stake exceeds the max stake.",
  betLowerMin: "Your stake less than the min stake.",
  betOverMaxPayout: "Your stake exceeds the max payout.",
  invalidBetType: "Pasar ini tidak tersedia, silakan coba pasar lain.",
  memberInactive: "Status akun ini tidak aktif, silakan hubungi kontak upline Anda",
  memberCommissionNotSetup: "Komisi tidak disiapkan.",
  matchNotActive: "Pasar ini tidak tersedia, silakan coba pasar lain.",
  invalidBetTeam: "Pasar ini tidak tersedia, silakan coba pasar lain.",
  invalidOdds: "Pasar ini tidak tersedia, silakan coba pasar lain.",
  betOverMaxBet: "Jumlah taruhan melebihi batas Max. Silakan periksa batas taruhan Anda.",
  invalidCurrency: "Mata uang tidak valid untuk bertaruh",
  betOverLimit: "Your stake exceeds the limit.",
  selectLeague: "Silakan pilih setidaknya 1 liga",
  game_maintenance: "Game sedang dalam perbaikan",
  betTypeRequired: "Jenis taruhan diperlukan",

  liveCasinoDisabled: "Live Kasino Dinonaktifkan",
  lotteryDisabled: "Lottery Dinonaktifkan",
  MiniGameDisabled: "Mini Game Dinonaktifkan"
};
