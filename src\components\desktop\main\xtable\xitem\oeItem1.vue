<template lang="pug">
  .hx-col.hx-cols.w-66
    template(v-if="details[betType] != null && details[betType][i] != null && details[betType][i][5] != 0 && details[betType][i][7] != 0 && details[betType][i][5] != '' && details[betType][i][7] != ''")
      .hx
        .hxs.w-15
          .ball-value O
        .hxs.w-15r
          oddsItem(v-if="details[betType][0][5] != 0" :odds="details[betType][i]" cls="" idx=5 :typ="oddsType" dataType="2")
      .hx
        .hxs.w-15
          .ball-value E
        .hxs.w-15r
          oddsItem(v-if="details[betType][0][7] != 0" :odds="details[betType][i]" cls="" idx=7 :typ="oddsType" dataType="2")
</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";

export default {
  components: {
    oddsItem
  },
  props: {
    details: {
      type: Object
    },
    oddsType: {
      type: String
    },
    item: {
      type: String
    },
    i: {
      type: [String, Number]
    },
    betType: {
      type: String
    }
  },
  // updated: function() {
  //   this.$nextTick(function() {
  //     console.log("cs", new Date(), this.oddsType, this.item);
  //   });
  // }
};
</script>
