<template lang="pug">
header
  .topbar(:class="{ active: !header }" v-if="!whiteLabel.mode")
    .container.d-flex.flex-row
      .logo.d-flex(@click="hardRefresh()")
        logo
      menuBar.d-flex
      .menu.d-flex.align-items-center.justify-content-end
        .loggedin.d-flex.align-items-center.justify-content-end(v-if="isLoggedIn")
          #user-info.flex-fill
            .player.d-flex.flex-row.align-items-center.justify-content-center
              .user.flex-fill.text-left {{ displayName }}
            .details.d-flex.flex-row
              .unit.text-left {{ currency_code }}
              .amount.flex-fill.text-left {{ $numeral(available_balance).format("0,0.00") }}
                button.y-btn.ml-1(href="javascript:void(0);" role="button" @click="getBalance" :disabled="loading")
                  i(:class="loading ? 'fad fa-spinner fa-spin' : 'fad fa-redo' ")
          #user-profile.icon-header(data-toggle="dropdown" aria-haspopup="true" aria-expanded="false")
            i.fad.fa-user-cog
          .dropdown-menu.dropdown-menu-right.profile(aria-labelledby="user-profile")
            .dropdown-bar.d-flex.m-1.mb-2.align-items-center.justify-content-between
              .dropdown-title {{ $t('ui.account') }}
            .dropdown-panel
              .dropdown-li
                .caption {{ $t('ui.id') }}
                .value {{ account_id }}
              .dropdown-li
                .caption {{ $t('ui.nickname') }}
                .value(v-if="nickname") {{ nickname }}
                .value(v-else)
                  router-link.x-btn.x-btn-default.x-btn-sm(role="button" to="info/settings"  target="_blank" onclick="window.open(this.href,'info','top=10,height=600,width=1280,status=no,toolbar=no,menubar=no,location=no');return false;")
                    i.fad.fa-pencil-alt
                    span {{ $t('ui.edit') }}
              .dropdown-li
                .caption {{ $t('ui.cash_balance') }}
                .value.d-flex.flex-row.align-items-start.justify-content-end
                  .unit {{ currency_code }}
                  .amount {{ cash_balance }}
              .dropdown-li
                .caption {{ $t('ui.outstanding') }}
                .value.d-flex.flex-row.align-items-start.justify-content-end
                  .unit {{ currency_code }}
                  .amount {{ frozen_balance }}
              .dropdown-li
                .caption {{ $t('ui.bet_credit') }}
                .value.d-flex.flex-row.align-items-start.justify-content-end
                  .unit {{ currency_code }}
                  .amount {{ available_balance }}
              .dropdown-li
                .caption {{ $t('ui.given_credit') }}
                .value.d-flex.flex-row.align-items-start.justify-content-end
                  .unit {{ currency_code }}
                  .amount {{ given_credit }}
              .dropdown-li
                .caption {{ $t('ui.last_login') }}
                .value.d-flex.flex-row.align-items-start.justify-content-end
                  .unit {{ last_login_date }}
                  .amount {{ last_login_time }}
            .action-panel
              router-link.x-btn(role="button" to="info/settings"  target="_blank" onclick="window.open(this.href,'info','top=10,height=600,width=1280,status=no,toolbar=no,menubar=no,location=no');return false;")
                i.fad.fa-key
                span {{ $t("ui.password") }}
              a.x-btn.x-btn-default(href="javascript:void(0);" role="button" @click="logout")
                i.fad.fa-sign-out-alt
                span {{ $t("ui.logout") }}
              .clearfix
          router-link#user-message.icon-header.position-relative(to="info/message" target="_blank" onclick="window.open(this.href,'info','top=10,height=600,width=1280,status=no,toolbar=no,menubar=no,location=no');return false;")
            i.fad.fa-envelope
            .email-counter(v-if="unreadCount != 0") {{ unreadCount }}
          #dropdown-language.dropdown.language-selector
            a.dropdown-toggle.d-flex.flex-row.align-items-center.justify-content-center(data-toggle="dropdown" data-close-others="true" aria-expanded="false")
              img(:src="'/v1/images/lang/' + language + '.svg?v=muQbdTxYGU'")
            ul.dropdown-menu.pull-right
              li(v-for="(item, key) in availableLanguage" @click="changeLanguage(item.id)")
                a(href="javascript:void(0)" :title="item.lang")
                  img(:src="'/v1/images/lang/' + item.id + '.svg?v=muQbdTxYGU'")
  toolBar
</template>

<script>
import config from "@/config";
import mixinDelay from "@/library/mixinDelay";

import { EventBus } from "@/library/_event-bus.js";
import errors from "@/errors";
import service from "@/library/_xhr-message";

import logo from "@/components/desktop/logo";

export default {
  components: {
    logo,
    menuBar: () => import("@/components/desktop/menuBar"),
    toolBar: () => import("@/components/desktop/toolbar"),
  },
  data() {
    return {
      loading: false,
      clock: "",
      availableLanguage: config.languageAvailable,
      feedback: {
        success: false,
        status: errors.session.invalidSession,
      },
      unreadCount: 0,
      intervals: [],
      timeouts: [],
    };
  },
  computed: {
    whiteLabel() {
      return this.$store.getters.whiteLabel;
    },
    header() {
      return this.$store.getters.header;
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
    displayName() {
      if (this.nickname) return this.nickname;
      else return this.account_id;
    },
    account_id() {
      return this.$store.getters.accountId;
    },
    nickname() {
      return this.$store.getters.nickName;
    },
    last_login_date() {
      if (this.$store.getters.playerInfo && this.$store.getters.playerInfo.last_login_time) return this.$dayjs(this.$store.getters.playerInfo.last_login_time).format("MM/DD/YYYY");
      else return "";
    },
    last_login_time() {
      if (this.$store.getters.playerInfo && this.$store.getters.playerInfo.last_login_time) return this.$dayjs(this.$store.getters.playerInfo.last_login_time).format("h:mm:ss A");
      else return "-";
    },
    wallet() {
      if (this.$store.getters.playerWallet) return this.$store.getters.playerWallet;
      return null;
    },
    bet_limit() {
      if (this.$store.getters.playerBetLimit) return this.$store.getters.playerBetLimit;
      return null;
    },
    balance() {
      return this.$store.getters.balance;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
    available_balance() {
      if (this.wallet) {
        return this.$numeral(this.balance).format("0,0.00");
      }
      return "-";
    },
    cash_balance() {
      if (this.wallet) {
        return this.$numeral(this.wallet.cash_balance).format("0,0.00");
      }
      return "-";
    },
    frozen_balance() {
      if (this.wallet) {
        return this.$numeral(this.wallet.frozen_balance).format("0,0.00");
      }
      return "-";
    },
    given_credit() {
      if (this.wallet) {
        return this.$numeral(this.wallet.credit_limit).format("0,0.00");
      }
      return "-";
    },
    language() {
      return this.$store.getters.language;
    },
  },
  beforeDestroy() {
    this.intervals.forEach(clearInterval);
    this.intervals = [];
    this.timeouts.forEach(clearTimeout);
    this.timeouts = [];
  },
  mounted() {
    // this.availableLanguage = config.languageAvailable;
    this.runner();
    const runnerInterval = setInterval(this.runner, 60000);
    this.intervals.push(runnerInterval);
    EventBus.getBalance = this.getBalance;
  },
  methods: {
    hardRefresh() {
      window.location.reload(true);
    },
    runner() {
      this.populateBalance();
      this.getPersonalUnread();
    },
    logout() {
      this.$helpers.logout();
    },
    go(e) {
      var routeData = this.$router.resolve(e);
      this.$helpers.info(routeData.href);
    },
    changeLanguage(lang) {
      this.$store.dispatch("layout/setLanguage", lang);
      EventBus.$emit("INVALIDATE");
    },
    setTrackedTimeout(fn, delay) {
      const id = setTimeout(fn, delay);
      this.timeouts.push(id);
      return id;
    },
    getBalance() {
      if (this.isLoggedIn) {
        this.loading = true;
        this.setTrackedTimeout(() => {
          this.$store.dispatch("user/getBalance").then(
            (res) => {
              this.loading = false;
              if (!res.success) {
                this.$helpers.handleFeedback(res.status);
              }
            },
            (err) => {
              this.loading = false;
              this.$helpers.handleFeedback(err.status);
            }
          );
        }, 2000);
      }
    },
    populateBalance(callback) {
      if (this.loading == true) return;
      if (this.isLoggedIn) {
        this.loading = true;
        this.$store.dispatch("user/getBalance").then(
          (res) => {
            this.loading = false;
            if (res.success) {
              if (callback) callback();
            } else {
              if (this.$helpers.handleFeedback(res.status)) {
                if (callback) callback();
              }
            }
          },
          (err) => {
            this.loading = false;
            if (this.$helpers.handleFeedback(err.status)) {
              if (callback) callback();
            }
          }
        );
      }
    },
    getPersonalUnread() {
      if (this.$store.getters.isLoggedIn) {
        var json = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
        };

        this.loading = true;

        service.getPersonalUnread(json).then(
          (result) => {
            this.loading = false;
            if (result) {
              this.feedback.status = result.status;
              if (result.success == true) {
                this.unreadCount = result.data[0].count_unread;
              } else {
                this.$helpers.handleFeedback(this.feedback.status);
              }
            }
          },
          (err) => {
            this.loading = false;
            this.feedback.success = false;
            this.feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);
          }
        );
      }
    },
  },
};
</script>
