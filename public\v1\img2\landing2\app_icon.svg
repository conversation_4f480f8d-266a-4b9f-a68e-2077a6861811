<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 21.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 1200 1200" style="enable-background:new 0 0 1200 1200;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{fill:#FFFFFF;}
	.st2{fill:url(#SVGID_2_);}
	.st3{fill:#E6E6E6;}
</style>
<radialGradient id="SVGID_1_" cx="601.5912" cy="596.7531" r="578.8" gradientTransform="matrix(1 0 0 -1 0 1200)" gradientUnits="userSpaceOnUse">
	<stop  offset="0" style="stop-color:#046499"/>
	<stop  offset="0.1649" style="stop-color:#075489"/>
	<stop  offset="0.4025" style="stop-color:#094478"/>
	<stop  offset="0.6654" style="stop-color:#0B3B6E"/>
	<stop  offset="1" style="stop-color:#0B386B"/>
</radialGradient>
<path class="st0" d="M1015.9,1182H187.3c-90.8,0-164.5-73.6-164.5-164.5V188.9c0-90.8,73.7-164.5,164.5-164.5h828.6
	c90.8,0,164.5,73.6,164.5,164.5v828.6C1180.4,1108.4,1106.7,1182,1015.9,1182z"/>
<g>
	<g>
		<path class="st1" d="M678.7,551.4c6.4-8.7,9.5-19,9.5-31.1c0-14.6-5.8-25.8-17.3-33.7s-28.1-11.8-49.5-11.8h-96.1l-39.8,198.9
			h104.2c27.1,0,48.8-5.3,65.1-15.9c16.3-10.7,24.4-26.3,24.4-46.9c0-9.7-2.3-17.9-6.9-24.5c-4.7-6.8-11.2-11.8-19.8-15.2
			C663.7,566.6,672.4,560,678.7,551.4z M622.9,631.6c-7.2,4.9-17.2,7.4-30.1,7.4h-54.3l9.7-49.2h55.7c19.9,0,29.9,6.8,29.9,20.4
			C633.6,619.7,630.1,626.7,622.9,631.6z M629.8,549.1c-6.8,4.9-16.2,7.2-28.2,7.2h-46.9l9.4-46.9h46.6c19.5,0,29.3,6.3,29.3,18.7
			C639.9,537.3,636.6,544.2,629.8,549.1z"/>
		<polygon class="st1" points="694.2,673.7 848.2,673.7 855.8,636.8 747.3,636.8 756.4,590.7 849.1,590.7 856.1,554.9 763.8,554.9 
			772.3,511.7 876.6,511.7 884.3,474.8 734,474.8 		"/>
		<polygon class="st1" points="911,474.8 903.3,512.3 966.9,512.3 934.9,673.7 980.9,673.7 1013.2,512.3 1076.7,512.3 1084.3,474.8 
					"/>
	</g>
	<g>
		
			<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="148.4166" y1="511.8438" x2="437.6456" y2="801.0729" gradientTransform="matrix(1 0 0 -1 0 1200)">
			<stop  offset="0" style="stop-color:#B48B00"/>
			<stop  offset="6.940227e-03" style="stop-color:#B99209"/>
			<stop  offset="4.255553e-02" style="stop-color:#CFB231"/>
			<stop  offset="7.758258e-02" style="stop-color:#E1CB50"/>
			<stop  offset="0.1114" style="stop-color:#EEDD67"/>
			<stop  offset="0.1435" style="stop-color:#F5E874"/>
			<stop  offset="0.172" style="stop-color:#F8EC79"/>
			<stop  offset="0.2713" style="stop-color:#F1E167"/>
			<stop  offset="0.6108" style="stop-color:#DABD30"/>
			<stop  offset="0.8637" style="stop-color:#CCA70D"/>
			<stop  offset="1" style="stop-color:#C79F00"/>
		</linearGradient>
		<path class="st2" d="M508.9,470.1L386.3,697.8h-63.5l-25.6-132.5l-76.4,132.5h-62.7l-39.2-227.7h58.6l24.3,155.1l74.7-132.1h61.1
			l28.1,132.1l82.6-155.1H508.9z"/>
	</g>
	<g>
		<path class="st3" d="M518.4,735.4l-6.7-21.2c-0.4-1.3-1.2-4.3-2.4-8.8H509c-0.9,3.8-1.6,6.8-2.3,8.8l-6.8,21.1h-6.3l-9.9-36h5.7
			c2.3,9,4.1,16,5.3,20.7c1.2,4.8,1.9,7.9,2.1,9.6h0.3c0.3-1.2,0.7-2.9,1.1-4.9c0.6-2,1-3.5,1.4-4.8l6.7-20.7h5.9l6.5,20.7
			c1.2,3.8,2.1,6.9,2.5,9.5h0.3c0.1-0.8,0.3-2,0.7-3.6c0.4-1.6,2.7-10.5,6.8-26.5h5.7l-10,36H518.4z"/>
		<path class="st3" d="M588.5,698.7c4.8,0,8.5,1.6,11,4.9c2.7,3.2,3.9,7.8,3.9,13.8c0,5.9-1.3,10.6-4,13.8c-2.7,3.3-6.3,4.9-11,4.9
			c-2.4,0-4.5-0.5-6.5-1.3c-1.9-0.9-3.6-2.2-4.9-4h-0.4l-1.1,4.7h-3.9v-51.3h5.4v12.5c0,2.8-0.1,5.3-0.3,7.5h0.3
			C579.7,700.5,583.4,698.7,588.5,698.7z M587.7,703.3c-3.7,0-6.4,1-8.1,3.2c-1.6,2.2-2.5,5.7-2.5,10.8c0,5,0.9,8.7,2.6,10.8
			c1.7,2.2,4.4,3.2,8.1,3.2c3.3,0,5.9-1.2,7.5-3.7c1.6-2.5,2.5-6,2.5-10.6c0-4.7-0.9-8.2-2.5-10.5
			C593.7,704.4,591.1,703.3,587.7,703.3z"/>
		<path class="st3" d="M658.6,736c-5.3,0-9.5-1.6-12.6-4.9c-3-3.2-4.7-7.8-4.7-13.5c0-5.8,1.4-10.5,4.3-13.9
			c2.9-3.4,6.8-5.1,11.5-5.1c4.6,0,8.1,1.5,10.7,4.5c2.7,2.9,4,6.9,4,11.8v3.4H647c0.1,4.3,1.1,7.4,3.2,9.6c2,2.2,4.9,3.3,8.6,3.3
			c3.9,0,7.7-0.9,11.5-2.5v4.9c-1.9,0.9-3.8,1.4-5.5,1.8C663.1,735.9,661,736,658.6,736z M657.1,703.2c-2.9,0-5.2,1-6.9,2.9
			c-1.7,1.9-2.8,4.5-3,7.8H666c0-3.4-0.8-6.1-2.3-7.9C662.2,704.1,660,703.2,657.1,703.2z"/>
		<path class="st3" d="M723.1,731.5c1,0,1.9-0.1,2.8-0.2s1.6-0.3,2.1-0.5v4.2c-0.6,0.3-1.4,0.5-2.7,0.7c-1.1,0.2-2.2,0.3-3.1,0.3
			c-6.9,0-10.5-3.7-10.5-11v-21.5h-5.1v-2.7l5.1-2.3l2.3-7.7h3.1v8.4h10.5v4.3h-10.5v21.2c0,2.2,0.5,3.8,1.5,5
			C719.9,730.9,721.3,731.5,723.1,731.5z"/>
		<path class="st3" d="M764.9,731.9c0-1.4,0.4-2.6,1-3.3c0.7-0.8,1.6-1.1,2.9-1.1c1.2,0,2.3,0.4,2.9,1.1c0.7,0.8,1,1.9,1,3.3
			s-0.4,2.6-1,3.3s-1.7,1.1-2.9,1.1c-1.1,0-2.1-0.4-2.8-1C765.3,734.5,764.9,733.5,764.9,731.9z"/>
		<path class="st3" d="M828.3,736c-5.2,0-9.2-1.6-12.2-4.9c-2.9-3.2-4.3-7.8-4.3-13.7c0-6,1.4-10.7,4.4-14s7-4.9,12.5-4.9
			c1.7,0,3.4,0.2,5.2,0.6c1.7,0.4,3.1,0.9,4.1,1.3l-1.7,4.7c-1.2-0.5-2.6-0.9-4-1.2c-1.4-0.3-2.7-0.5-3.8-0.5c-7.3,0-11,4.7-11,14
			c0,4.5,0.9,7.8,2.7,10.2c1.8,2.4,4.5,3.5,8,3.5c3,0,6.1-0.7,9.2-1.9v4.9C835.1,735.4,832,736,828.3,736z"/>
		<path class="st3" d="M907.6,717.3c0,5.9-1.5,10.5-4.5,13.8c-2.9,3.3-7,4.9-12.3,4.9c-3.2,0-6.1-0.8-8.6-2.3
			c-2.5-1.5-4.5-3.7-5.8-6.6c-1.3-2.9-2-6.2-2-10c0-5.9,1.4-10.5,4.4-13.8c2.9-3.2,7-4.9,12.3-4.9c5,0,9,1.7,12.1,5
			C906,707.1,907.6,711.5,907.6,717.3z M880.1,717.3c0,4.6,1,8.1,2.8,10.6c1.8,2.4,4.6,3.6,8.1,3.6c3.5,0,6.3-1.2,8.2-3.6
			c1.9-2.4,2.8-5.9,2.8-10.6c0-4.6-1-8.1-2.8-10.5s-4.6-3.6-8.2-3.6c-3.6,0-6.3,1.1-8.1,3.5C880.9,709.2,880.1,712.7,880.1,717.3z"
			/>
		<path class="st3" d="M993.4,735.4v-23.5c0-2.9-0.6-5-1.8-6.5c-1.2-1.4-3.1-2.2-5.7-2.2c-3.4,0-5.9,1-7.5,2.9c-1.6,2-2.5,4.9-2.5,9
			v20.2h-5.4v-23.5c0-2.9-0.6-5-1.8-6.5c-1.2-1.4-3.1-2.2-5.8-2.2c-3.4,0-5.9,1-7.5,3c-1.6,2.1-2.4,5.4-2.4,10.1v18.9h-5.4v-36h4.5
			l0.9,4.9h0.3c1-1.7,2.5-3.1,4.4-4.1c1.9-1,4-1.5,6.3-1.5c5.6,0,9.3,2,11,6.1h0.3c1-1.9,2.7-3.4,4.7-4.5c2-1.1,4.4-1.6,6.9-1.6
			c4.1,0,7.1,1,9.1,3.1c2,2.1,3,5.4,3,10.1v23.5L993.4,735.4L993.4,735.4z"/>
	</g>
</g>
</svg>
