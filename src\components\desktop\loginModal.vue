<template lang="pug">
#login-modal.modal.login-modal(tabindex="-1" role="dialog" aria-labelledby="loginLabel" aria-hidden="true")
  .modal-dialog(role="document")
    .modal-content
      .banner
        .text
          h1 {{ $t("ui.welcome") }}
          p {{ $t("message.login2") }}
          small {{ title }} ({{ version }})
      .modal-body
        .login-field
          label {{ $t("ui.display_language") }}:
          .input-group
            .lang-select
              .btn-select
                li
                  img(:src="'/v1/images/lang/' + language + '.svg?v=muQbdTxYGU'")
                  span {{ $t("language." + language) }}
              .lang-dropdown-wrap
                ul#lang-dropdown-list
                  li(v-for="item in languageList" @click="setLanguage(item.id)")
                    img(:src="'/v1/images/lang/' + item.id + '.svg?v=muQbdTxYGU'")
                    span {{ item.lang }}
          .login-field.mt-3
            label {{ $t("ui.account_id") }}:
            .input-group
              .input-group-prepend
                .input-group-text
                  i.fad.fa-user
              input#username.form-control(v-model="username" type="text" :placeholder="$t('ui.account_id')" autofocus maxlength="20")
              .invalid-feedback(v-if="feedback.username") {{ feedback.username }}
          .login-field
            label {{ $t("ui.password") }}:
            .input-group
              .input-group-prepend
                .input-group-text
                  i.fad.fa-key
              input#password.form-control(v-model="password" type="password" :placeholder="$t('ui.password')" maxlength="20" @keyup.enter="login")
              .invalid-feedback(v-if="feedback.password") {{ feedback.password }}
          .login-field.text-right.pt-2
            SpinButton(:text="$t('ui.login')" :loading="feedback.loading" css="login-btn" @click="login")
        .modal-footer-sticky(v-if="result && result != 'OK'")
          span.text-capitalize {{ $t("error." + result) }}
</template>

<script>
import jwt from "jsonwebtoken";
import { required, alphaNum } from "vuelidate/lib/validators";
import config from "@/config";
import errors from "@/errors";
import SpinButton from "@/components/ui/SpinButton";
import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {
    SpinButton
  },
  data() {
    return {
      languageList: config.languageAvailable,
      username: "",
      password: "",
      feedback: {
        username: "",
        password: "",
        loading: false,
        timeout: null
      },
      result: ""
    };
  },
  computed: {
    language() {
      return this.$store.getters.language;
    },
    version() {
      return config.appVersion;
    },
    title() {
      return config.appTitle;
    },
    currency_code() {
      return this.$store.getters.currencyCode;
    }
  },
  watch: {
    result(newVal) {
      if (newVal) {
        setTimeout(() => {
          this.result = "";
        }, 5000);
      }
    }
  },
  validations: {
    username: {
      required
    },
    password: {
      required
    }
  },
  mounted() {
    $(".btn-select").click(function() {
      $(".lang-dropdown-wrap").toggle();
    });

    $("#login-modal").on("shown.bs.modal", function() {
      $("#username").trigger("focus");
    });
  },
  methods: {
    setLanguage(lang) {
      // console.log(lang);
      this.$store.dispatch("layout/setLanguage", lang);
      $(".lang-dropdown-wrap").toggle();
    },
    reset() {
      this.feedback.username = "";
      this.feedback.password = "";
    },
    login() {
      // must invoke touch to validate the errors
      this.$v.$touch();
      clearTimeout(this.feedback.timeout);
      if (!this.$v.$invalid) {
        this.feedback.loading = true;
        this.reset();
        this.$store.dispatch("layout/reset");
        this.$store.dispatch("user/doLogin", { username: this.username, password: this.password }).then(
          res => {
            this.feedback.loading = false;
            if (res.success) {
              this.feedback.loading = true;
              this.$store.dispatch("user/getBalance").then(
                res => {
                  this.feedback.loading = false;
                  if (res.success) {
                    $("#login-modal").modal("hide");
                    this.$store.dispatch("layout/setRadarId", null);
                    this.$store.dispatch("layout/setSingleBetting", { property: "quickBet", value: false });

                    EventBus.$emit("INVALIDATE");
                    this.$router.push("/desktop").catch(err => {
                      console.trace(err);
                    });
                  } else {
                    this.result = res.status;
                    if (this.$helpers.handleFeedback(res.status)) {
                    }
                  }
                },
                err => {
                  this.feedback.loading = false;
                  this.result = res.status;
                  if (this.$helpers.handleFeedback(err.status)) {
                  }
                }
              );
            } else {
              this.result = res.status;
              this.$helpers.handleFeedback(res.status);
            }
          },
          err => {
            this.feedback.loading = false;
            this.result = err.status;
            this.$helpers.handleFeedback(err.status);
          }
        );
      } else {
        this.feedback.loading = false;
        this.reset();
        if (!this.$v.username.required) {
          this.feedback.username = this.$t("error.usernameRequired");
        }

        // if (!this.$v.username.alphaNum) {
        //   this.feedback.username = this.$t("error.alphaNumOnly");
        // }

        if (!this.$v.password.required) {
          this.feedback.password = this.$t("error.passwordRequired");
        }
      }

      // reset the error message after 10 s
      this.feedback.timeout = setTimeout(() => {
        this.$v.$reset();
        this.reset();
      }, 10000);
    }
  }
};
</script>
