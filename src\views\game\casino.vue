<template lang="pug">
.wrapper.h-100v.p-3
  .preloader.d-flex.align-items-center.justify-content-center(v-if="error")
    .game-warning
      i.fad.fa-exclamation-triangle.mr-1
      span {{ error }}
  .lds-overlay(v-else)
    .lds-roller
      div
      div
      div
      div
      div
      div
      div
      div
</template>

<script>
import config from "@/config";
import xhr from "@/library/_xhr-game.js";

export default {
  data() {
    return {
      url: "",
      error: ""
    };
  },
  computed: {
    whiteLabel() {
      return this.$store.getters.whiteLabel.mode;
    }
  },

  mounted() {
    $("html").addClass("minimal");
    $("body").addClass("minimal");
  },
  created() {
    $("html").addClass("minimal");
    $("body").addClass("minimal");

    if (this.whiteLabel) return;

    var q = this.$route.query;
    var suite = "1";
    if (q.suite) {
      suite = q.suite;
    }

    switch (suite) {
    case "0":
      // Game Play in Mini Casino
      this.launchGP("1", "h5");
      break;
    case "1":
      // Game Play Suite 1
      var clientType = "desk";
      if (this.isMobileTablet()) {
        clientType = "h5";
      }
      this.launchGP("1", clientType);
      break;
    case "2":
      // Game Play Suite 2
      var clientType = "desk";
      if (this.isMobileTablet()) {
        clientType = "h5";
      }
      this.launchGP("2", clientType);
      break;
    case "3":
      // SA Gaming
      var mobile = false;
      if (this.isMobileTablet()) {
        mobile = true;
      }
      this.launchSA(mobile);
      break;
    case "4":
      // CT855
      var mobile = false;
      if (this.isMobileTablet()) {
        mobile = true;
      }
      this.launchCT(mobile);
      break;
    case "5":
      // Evolution
      var mobile = false;
      if (this.isMobileTablet()) {
        mobile = true;
      }
      this.launchEvo(mobile);
      break;
    case "6":
      // Big Gaming
      break;
    case "7":
      // Pretty Gaming
      var mobile = false;
      if (this.isMobileTablet()) {
        mobile = true;
      }
      this.launchPG(mobile);
      break;
    case "8":
      // Yee Bet
      var mobile = false;
      if (this.isMobileTablet()) {
        mobile = true;
      }
      this.launchYb(mobile);
      break;
    case "9":
      // Pragmatic Casino
      var mobile = false;
      if (this.isMobileTablet()) {
        mobile = true;
      }
      this.launchPragmatic(mobile);
      break;
    case "10":
      // AI Casino
      var mobile = false;
      if (this.isMobileTablet()) {
        mobile = true;
      }
      this.launchAICasino(mobile);
      break;
    case "11":
      // Sexy Bacarrat
      var mobile = false;
      if (this.isMobileTablet()) {
        mobile = true;
      }
      this.launchSexyBacarrat(mobile);
      break;
    }
  },
  methods: {
    getCountry() {
      const url = "https://geo.wbet.workers.dev/";
      return new Promise((resolve, reject) => {
        this.$http.get(url).then(
          res => {
            resolve(res.bodyText);
          },
          err => {
            reject(err);
          }
        );
      });
    },
    isMobileTablet() {
      var check = false;
      (function(a) {
        if (
          /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(
            a
          ) ||
          /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(
            a.substr(0, 4)
          )
        )
          check = true;
      })(navigator.userAgent || navigator.vendor || window.opera);

      if (!check) {
        if (navigator.userAgent.match(/Mac/) && navigator.maxTouchPoints && navigator.maxTouchPoints > 2) {
          check = true;
        }
      }
      return check;
    },
    selectError(obj) {
      switch (obj.status) {
      case "lotteryDisabled":
      case "currencyNotSupported":
      case "liveCasinoDisabled":
      case "EsportsDisabled":
      case "MiniGameDisabled":
        this.error = this.$t("error." + obj.status);
        break;
      case "account_status_suspend":
        this.error = this.$t("error.SUSPEND");
        break;
      default:
        this.error = this.$t("error.game_maintenance");
        break;
      }
    },
    launchGP(suite, t) {
      $("html").addClass("minimal");
      $("body").addClass("minimal");
      var clientType = "desk";
      if (t) {
        clientType = t;
      }
      var args = {
        player_id: this.$store.getters.accountId,
        password: this.$store.getters.sessionToken,
        studio: suite,
        clienttype: clientType,
        currency: this.$store.getters.currencyCode,
        lang: this.$store.getters.language
      };

      xhr.launchGP(args).then(
        res => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  window.location.replace(this.url);
                }, 100);
              } else {
                this.selectError(res);
              }
            } else {
              this.selectError(res);
            }
          }
          console.warn(res);
        },
        err => {
          this.loading = false;
          this.selectError(err);
        }
      );
    },
    launchSA(mode) {
      $("html").addClass("minimal");
      $("body").addClass("minimal");
      var mobile = false;
      if (mode) {
        mobile = mode;
      }
      var args = {
        username: this.$store.getters.accountId,
        sessionid: this.$store.getters.sessionToken,
        currency: this.$store.getters.currencyCode,
        lang: this.$store.getters.language,
        mobile: mobile.toString()
      };

      xhr.launchSA(args).then(
        res => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  window.location.replace(this.url);
                }, 100);
              } else {
                this.selectError(res);
              }
            } else {
              this.selectError(res);
            }
          }
        },
        err => {
          this.loading = false;
          this.selectError(err);
        }
      );
    },
    launchCT(mode) {
      $("html").addClass("minimal");
      $("body").addClass("minimal");
      var mobile = false;
      if (mode) {
        mobile = mode;
      }
      var args = {
        username: this.$store.getters.accountId,
        sessionid: this.$store.getters.sessionToken,
        currency: this.$store.getters.currencyCode,
        lang: this.$store.getters.language,
        mobile: mobile.toString()
      };

      xhr.launchCT(args).then(
        res => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  window.location.replace(this.url);
                }, 100);
              } else {
                this.selectError(res);
              }
            } else {
              this.selectError(res);
            }
          }
        },
        err => {
          this.loading = false;
          this.selectError(err);
        }
      );
    },
    launchPG(mode) {
      $("html").addClass("minimal");
      $("body").addClass("minimal");
      var mobile = false;
      if (mode) {
        mobile = mode;
      }
      var args = {
        username: this.$store.getters.accountId,
        sessionid: this.$store.getters.sessionToken,
        currency: this.$store.getters.currencyCode,
        lang: this.$store.getters.language,
        mobile: mobile.toString()
      };

      xhr.launchPG(args).then(
        res => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  window.location.replace(this.url);
                }, 100);
              } else {
                this.selectError(res);
              }
            } else {
              this.selectError(res);
            }
          }
        },
        err => {
          this.loading = false;
          this.selectError(err);
        }
      );
    },
    launchPragmatic(mode) {
      $("html").addClass("minimal");
      $("body").addClass("minimal");
      var mobile = false;
      if (mode) {
        mobile = mode;
      }
      var args = {
        username: this.$store.getters.accountId,
        sessionid: this.$store.getters.sessionToken,
        currency: this.$store.getters.currencyCode,
        lang: this.$store.getters.language,
        mobile: mobile.toString()
      };

      xhr.launchPragmatic(args, true).then(
        res => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  window.location.replace(this.url);
                }, 100);
              } else {
                this.selectError(res);
              }
            } else {
              this.selectError(res);
            }
          }
        },
        err => {
          this.loading = false;
          this.selectError(err);
        }
      );
    },
    launchYb(mode) {
      $("html").addClass("minimal");
      $("body").addClass("minimal");
      var mobile = false;
      if (mode) {
        mobile = mode;
      }
      var args = {
        username: this.$store.getters.accountId,
        sessionid: this.$store.getters.sessionToken,
        lang: this.$store.getters.language,
        mobile: mobile.toString()
      };

      xhr.launchYb(args).then(
        res => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  window.location.replace(this.url);
                }, 100);
              } else {
                this.selectError(res);
              }
            } else {
              this.selectError(res);
            }
          }
        },
        err => {
          this.loading = false;
          this.selectError(err);
        }
      );
    },
    launchEvo(mode) {
      var currency = this.$store.getters.currencyCode;

      this.getCountry().then(
        res => {
          var json = JSON.parse(res);
          if (json.country == "SG") {
            if (currency == "SGD") {
              this.launchEvoInternal(mode);
            } else {
              this.error = this.$t("error.forbiddenAccess");
            }
          } else {
            this.launchEvoInternal(mode);
          }
        },
        err => {
          this.error = err;
        }
      );
    },
    launchEvoInternal(mode) {
      var mobile = false;
      if (mode) {
        mobile = mode;
      }
      var args = {
        username: this.$store.getters.accountId,
        sessionid: this.$store.getters.sessionToken,
        currency: this.$store.getters.currencyCode,
        lang: this.$store.getters.language,
        mobile: mobile.toString()
      };

      xhr.launchEvo(args).then(
        res => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  window.location.replace(this.url);
                }, 100);
              } else {
                this.selectError(res);
              }
            } else {
              this.selectError(res);
            }
          }
        },
        err => {
          this.loading = false;
          this.selectError(err);
        }
      );
    },
    launchAICasino(mode) {
      $("html").addClass("minimal");
      $("body").addClass("minimal");
      var mobile = false;
      if (mode) {
        mobile = mode;
      }
      var args = {
        username: this.$store.getters.accountId,
        sessionid: this.$store.getters.sessionToken,
        lang: this.$store.getters.language,
        mobile: mobile.toString()
      };

      xhr.launchAICasino(args).then(
        res => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  window.location.replace(this.url);
                }, 100);
              } else {
                this.selectError(res);
              }
            } else {
              this.selectError(res);
            }
          }
        },
        err => {
          this.loading = false;
          this.selectError(err);
        }
      );
    },
    launchSexyBacarrat(mode) {
      $("html").addClass("minimal");
      $("body").addClass("minimal");
      var mobile = false;
      if (mode) {
        mobile = mode;
      }
      var args = {
        username: this.$store.getters.accountId,
        sessionid: this.$store.getters.sessionToken,
        lang: this.$store.getters.language,
        game_code: "MX-LIVE-001",
        mobile: mobile.toString()
      };

      xhr.launchAwc(args).then(
        res => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  window.location.replace(this.url);
                }, 100);
              } else {
                this.selectError(res);
              }
            } else {
              this.selectError(res);
            }
          }
        },
        err => {
          this.loading = false;
          this.selectError(err);
        }
      );
    },
  }
};
</script>
