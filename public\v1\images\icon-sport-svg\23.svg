<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 19.2.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 50 50" style="enable-background:new 0 0 50 50;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FF772C;}
	.st1{fill:#9E532F;}
	.st2{opacity:0.2;fill:#AF631E;enable-background:new    ;}
	.st3{opacity:0.2;fill:#8C3B0E;enable-background:new    ;}
	.st4{fill:#E6E9ED;}
</style>
<g>
	<g>
		<g id="design">
			<g id="Layer_4">
				<g>
					<circle class="st0" cx="25.1" cy="25" r="24"/>
					<path class="st1" d="M2.1,17.8c0.2-0.4,0.4-0.9,0.6-1.3c0.1,0.1,0.2,0.2,0.3,0.4C2.6,17.3,2.4,17.5,2.1,17.8z"/>
					<path class="st1" d="M3.7,16c0.2-0.7,0.4-1.1,0.6-1.8c0.2,0.2,0.3,0.3,0.5,0.6C4.4,15.3,4.2,15.5,3.7,16z"/>
					<path class="st1" d="M6.1,10.3c-0.3,1-0.4,1.4-0.7,2.3c-0.2-0.2-0.3-0.4-0.5-0.6C5.3,11.4,5.5,11,6.1,10.3z"/>
					<path class="st1" d="M5.9,13.6c0.3-0.9,0.5-1.3,0.8-2.3l0.7,1C6.8,12.9,6.5,13.1,5.9,13.6z"/>
					<path class="st1" d="M8.9,11.2c0.4-1,0.6-1.5,1-2.6c0.4,0.5,0.6,0.8,1,1.4C10.3,10.4,9.6,10.8,8.9,11.2z"/>
					<path class="st1" d="M13,9c0.5-1.1,0.8-1.7,1.3-2.9c0.5,0.7,0.8,1.1,1.4,1.9C14.8,8.3,13.9,8.6,13,9z"/>
					<path class="st1" d="M18.3,7.3l1.5-2.9c0.6,0.9,0.9,1.4,1.5,2.4C20.3,7,19.3,7.1,18.3,7.3z"/>
					<path class="st1" d="M24,6.7c0.6-1.1,1-1.6,1.6-2.7l1.6,2.7C26.1,6.7,25.1,6.7,24,6.7z"/>
					<path class="st1" d="M29.8,7c0.6-0.9,0.9-1.4,1.5-2.3l1.4,2.9C31.7,7.4,30.8,7.2,29.8,7z"/>
					<path class="st1" d="M35,8.4c0.5-0.8,0.8-1.2,1.3-1.9l1.2,3C36.7,9,35.9,8.6,35,8.4z"/>
					<path class="st1" d="M39.3,10.4c0.4-0.6,0.6-1,0.9-1.6c0.4,1.1,0.6,1.6,1,2.6C40.5,11.1,40.2,10.9,39.3,10.4z"/>
					<path class="st1" d="M42.2,8.1C42,8.7,41.9,9,41.6,9.5c-0.3-1.1-0.5-1.6-0.9-2.8C41.4,7.3,41.7,7.6,42.2,8.1z"/>
					<path class="st1" d="M42.5,12.3c0.2-0.6,0.3-0.8,0.5-1.4c0.3,1,0.5,1.4,0.8,2.3C43.3,13,42.9,12.7,42.5,12.3z"/>
					<path class="st1" d="M44.3,10.5c-0.1,0.5-0.2,0.7-0.3,1.2c-0.3-1-0.4-1.4-0.7-2.5C43.7,9.7,43.9,9.9,44.3,10.5z"/>
					<path class="st1" d="M44.7,14.2c0.1-0.5,0.2-0.7,0.3-1.1c0.3,0.9,0.4,1.3,0.7,2.1C45.3,14.7,45.1,14.5,44.7,14.2z"/>
					<path class="st1" d="M46.5,16c0.1-0.4,0.1-0.5,0.2-0.9c0.3,0.8,0.4,1.2,0.7,1.9L46.5,16z"/>
					<path class="st1" d="M1.6,20.1C1.8,19.8,2,19.4,2.2,19l0.3,0.7C2.2,19.9,1.9,20,1.6,20.1z"/>
					<path class="st1" d="M3.7,16.8c-0.3,0.7-0.4,1-0.6,1.6c-0.2-0.3-0.2-0.4-0.3-0.6C3.1,17.4,3.3,17.2,3.7,16.8z"/>
					<path class="st1" d="M3.4,19.3c0.3-0.6,0.4-0.9,0.7-1.6c0.2,0.3,0.3,0.5,0.5,0.9C4.1,19,3.9,19.1,3.4,19.3z"/>
					<path class="st1" d="M5.9,14.8c-0.3,0.8-0.5,1.2-0.7,2c-0.2-0.4-0.3-0.6-0.5-0.9C5.1,15.5,5.3,15.3,5.9,14.8z"/>
					<path class="st1" d="M5.7,18c0.3-0.8,0.5-1.2,0.8-2c0.3,0.4,0.4,0.7,0.8,1.2C6.6,17.6,6.3,17.7,5.7,18z"/>
					<path class="st1" d="M8.9,12.6c-0.4,1-0.6,1.4-1,2.3c-0.3-0.5-0.5-0.7-0.8-1.2C7.7,13.4,8.3,13,8.9,12.6z"/>
					<path class="st1" d="M8.8,16.6l1-2.3l1.1,1.5C10.2,16,9.5,16.3,8.8,16.6z"/>
					<path class="st1" d="M13,10.7l-1.2,2.6l-1.1-1.6C11.4,11.3,12.2,11,13,10.7z"/>
					<path class="st1" d="M13,15.3l1.4-2.5c0.6,0.8,0.8,1.1,1.4,1.9C14.6,15,14,15.1,13,15.3z"/>
					<path class="st1" d="M18.3,9.2l-1.5,2.7c-0.6-0.8-0.9-1.2-1.4-2C16.3,9.6,17.3,9.4,18.3,9.2z"/>
					<path class="st1" d="M18.2,14.4l1.6-2.6l1.5,2.3C20.1,14.1,19.4,14.2,18.2,14.4z"/>
					<path class="st1" d="M24,8.6l-1.6,2.6l-1.6-2.4C21.9,8.7,23,8.6,24,8.6z"/>
					<path class="st1" d="M24,14l1.6-2.5c0.6,1,1,1.5,1.6,2.5C25.9,13.9,25.3,13.9,24,14z"/>
					<path class="st1" d="M29.8,9l-1.5,2.3c-0.6-1-0.9-1.6-1.6-2.6C27.7,8.7,28.8,8.8,29.8,9z"/>
					<path class="st1" d="M29.8,14.2l1.5-2.2c0.6,1.1,0.9,1.6,1.5,2.6C31.6,14.4,31,14.3,29.8,14.2z"/>
					<path class="st1" d="M35,10.2l-1.3,2l-1.4-2.7C33.2,9.6,34.1,9.9,35,10.2z"/>
					<path class="st1" d="M35.1,15l1.3-1.8c0.5,1,0.7,1.5,1.2,2.5C36.8,15.4,36,15.2,35.1,15z"/>
					<path class="st1" d="M39.4,12c-0.4,0.6-0.6,1-1,1.6c-0.5-1-0.7-1.5-1.2-2.6C38.1,11.4,38.5,11.6,39.4,12z"/>
					<path class="st1" d="M39.6,16.3c0.4-0.6,0.6-1,0.9-1.5c0.4,0.9,0.6,1.4,1,2.2C40.8,16.7,40.4,16.5,39.6,16.3z"/>
					<path class="st1" d="M42.6,13.7c-0.2,0.6-0.4,0.8-0.6,1.4c-0.4-0.9-0.6-1.3-1-2.3C41.7,13.2,42,13.4,42.6,13.7z"/>
					<path class="st1" d="M42.9,17.5c0.2-0.5,0.4-0.8,0.6-1.3c0.3,0.8,0.5,1.2,0.8,2C43.8,17.9,43.3,17.7,42.9,17.5z"/>
					<path class="st1" d="M44.9,15.4c-0.1,0.5-0.2,0.7-0.4,1.2c-0.3-0.8-0.4-1.2-0.7-2.1C44.2,14.8,44.4,15,44.9,15.4z"/>
					<path class="st1" d="M45.2,18.7c0.2-0.5,0.2-0.7,0.4-1.1c0.3,0.7,0.4,1.1,0.7,1.8C45.9,19.1,45.5,18.9,45.2,18.7z"/>
					<path class="st1" d="M46.7,17.1c-0.1,0.4-0.2,0.6-0.3,1c-0.3-0.7-0.4-1.1-0.7-1.9C46.1,16.5,46.3,16.7,46.7,17.1z"/>
					<path class="st1" d="M47.1,19.9l0.3-0.9c0.3,0.7,0.4,1,0.7,1.6L47.1,19.9z"/>
					<path class="st1" d="M1.1,22.7c0.2-0.3,0.5-0.6,0.7-0.9c0.2,0.4,0.2,0.6,0.4,1.1C1.8,22.9,1.5,22.8,1.1,22.7z"/>
					<path class="st1" d="M3.3,20.2c-0.2,0.5-0.4,0.9-0.7,1.3c-0.2-0.4-0.3-0.6-0.4-1C2.6,20.5,3,20.4,3.3,20.2z"/>
					<path class="st1" d="M3.2,22.8c0.3-0.5,0.4-0.8,0.7-1.4l0.6,1.2C4,22.7,3.6,22.8,3.2,22.8z"/>
					<path class="st1" d="M5.7,19.2c-0.3,0.7-0.5,1.1-0.8,1.7l-0.6-1.1C4.8,19.6,5.1,19.4,5.7,19.2z"/>
					<path class="st1" d="M5.6,22.4c0.3-0.7,0.5-1,0.8-1.8l0.8,1.4L5.6,22.4z"/>
					<path class="st1" d="M8.8,17.9l-1,2L7,18.6C7.7,18.3,8,18.2,8.8,17.9z"/>
					<path class="st1" d="M8.7,21.8l1-2l1.1,1.7C9.9,21.6,9.5,21.6,8.7,21.8z"/>
					<path class="st1" d="M13,16.9l-1.3,2.2l-1.1-1.7C11.4,17.2,12.2,17,13,16.9z"/>
					<path class="st1" d="M13,21.3c0.5-0.8,0.8-1.3,1.4-2.2c0.6,0.8,0.8,1.2,1.4,2C14.6,21.2,14,21.2,13,21.3z"/>
					<path class="st1" d="M18.2,16.1l-1.5,2.4c-0.6-0.8-0.9-1.2-1.4-2C16.4,16.3,17,16.2,18.2,16.1z"/>
					<path class="st1" d="M18.2,21l1.5-2.4l1.6,2.2C20.1,20.9,19.4,20.9,18.2,21z"/>
					<path class="st1" d="M24,15.8l-1.6,2.4l-1.6-2.3C22.1,15.8,22.7,15.8,24,15.8z"/>
					<path class="st1" d="M24,20.8l1.6-2.3c0.6,1,1,1.4,1.6,2.3C25.9,20.8,25.3,20.8,24,20.8z"/>
					<path class="st1" d="M29.8,15.9l-1.5,2.2c-0.6-1-1-1.4-1.6-2.4C28,15.8,28.6,15.9,29.8,15.9z"/>
					<path class="st1" d="M29.9,20.9l1.5-2.2c0.6,0.9,0.9,1.4,1.5,2.3C31.7,20.9,31.1,20.9,29.9,20.9z"/>
					<path class="st1" d="M35.2,16.6l-1.3,2c-0.6-0.9-0.9-1.4-1.5-2.4C33.5,16.3,34.1,16.4,35.2,16.6z"/>
					<path class="st1" d="M35.3,21.1l1.2-1.9c0.5,0.9,0.8,1.3,1.3,2.1C36.9,21.3,36.3,21.2,35.3,21.1z"/>
					<path class="st1" d="M39.6,17.7c-0.4,0.6-0.6,1-1.1,1.6c-0.5-0.8-0.7-1.3-1.2-2.2L39.6,17.7z"/>
					<path class="st1" d="M39.8,21.6c0.4-0.7,0.6-1,1-1.6c0.4,0.8,0.6,1.2,1,1.9C41.1,21.8,40.7,21.7,39.8,21.6z"/>
					<path class="st1" d="M43,18.7c-0.3,0.5-0.4,0.8-0.7,1.4l-1-2C42,18.4,42.4,18.5,43,18.7z"/>
					<path class="st1" d="M43.2,22.2c0.3-0.6,0.4-0.8,0.6-1.4c0.3,0.7,0.5,1,0.7,1.7C44,22.4,43.8,22.3,43.2,22.2z"/>
					<path class="st1" d="M45.3,19.8c-0.2,0.5-0.3,0.7-0.4,1.2c-0.3-0.7-0.4-1-0.7-1.8C44.6,19.4,44.9,19.5,45.3,19.8z"/>
					<path class="st1" d="M45.5,22.8c0.2-0.5,0.3-0.7,0.4-1.2l0.6,1.5C46.1,23,45.9,22.9,45.5,22.8z"/>
					<path class="st1" d="M47.2,20.9l-0.4,1c-0.2-0.6-0.4-0.9-0.6-1.6C46.6,20.5,46.8,20.6,47.2,20.9z"/>
					<path class="st1" d="M47.3,23.5c0.2-0.4,0.3-0.6,0.4-1c0.2,0.6,0.4,0.8,0.6,1.4L47.3,23.5z"/>
					<path class="st1" d="M3.1,23.8c-0.3,0.5-0.4,0.7-0.6,1.2c-0.2-0.5-0.3-0.8-0.5-1.2C2.3,23.8,2.7,23.8,3.1,23.8z"/>
					<path class="st1" d="M3.1,26.5c0.2-0.4,0.4-0.7,0.6-1.2l0.7,1.4C3.9,26.7,3.5,26.6,3.1,26.5z"/>
					<path class="st1" d="M5.6,23.5l-0.7,1.6l-0.7-1.4C4.7,23.7,5,23.6,5.6,23.5z"/>
					<path class="st1" d="M5.6,26.8l0.8-1.6l0.8,1.6C6.5,26.9,6.2,26.9,5.6,26.8z"/>
					<path class="st1" d="M8.7,23.1c-0.4,0.8-0.6,1.1-1,1.8l-0.9-1.6C7.6,23.2,7.9,23.2,8.7,23.1z"/>
					<path class="st1" d="M8.7,26.9c0.4-0.7,0.6-1.1,1-1.8l1.1,1.9C9.9,27,9.5,26.9,8.7,26.9z"/>
					<path class="st1" d="M12.9,22.8c-0.5,0.8-0.8,1.2-1.3,2l-1.2-1.9C11.4,22.8,11.9,22.8,12.9,22.8z"/>
					<path class="st1" d="M12.9,27.1c0.5-0.8,0.8-1.2,1.4-2c0.6,0.9,0.8,1.3,1.4,2.2C14.6,27.3,14,27.2,12.9,27.1z"/>
					<path class="st1" d="M18.2,22.7l-1.5,2.3c-0.6-0.9-0.9-1.3-1.4-2.1C16.4,22.7,17,22.7,18.2,22.7z"/>
					<path class="st1" d="M18.2,27.5l1.5-2.2l1.6,2.3C20,27.5,19.4,27.5,18.2,27.5z"/>
					<path class="st1" d="M24,22.5l-1.6,2.3l-1.6-2.3C22.1,22.6,22.7,22.5,24,22.5z"/>
					<path class="st1" d="M24,27.5l1.6-2.3c0.6,0.9,1,1.4,1.6,2.3C26,27.5,25.3,27.5,24,27.5z"/>
					<path class="st1" d="M29.9,22.5l-1.5,2.3c-0.6-0.9-1-1.4-1.6-2.3H29.9z"/>
					<path class="st1" d="M30,27.3l1.5-2.3c0.6,0.9,0.9,1.3,1.5,2.1C31.8,27.3,31.2,27.3,30,27.3z"/>
					<path class="st1" d="M35.3,22.7L34,24.7c-0.6-0.8-0.9-1.3-1.5-2.1C33.7,22.6,34.2,22.6,35.3,22.7z"/>
					<path class="st1" d="M35.4,27l1.3-2c0.5,0.8,0.8,1.2,1.3,1.9C37,26.9,36.5,27,35.4,27z"/>
					<path class="st1" d="M39.9,22.9c-0.4,0.7-0.6,1-1.1,1.7c-0.5-0.7-0.8-1.1-1.3-1.9L39.9,22.9z"/>
					<path class="st1" d="M40,26.7c0.4-0.7,0.6-1.1,1-1.7c0.4,0.7,0.6,1,1,1.7C41.3,26.7,41,26.7,40,26.7z"/>
					<path class="st1" d="M43.3,23.3c-0.3,0.6-0.4,0.9-0.7,1.5c-0.4-0.7-0.6-1-1-1.7C42.3,23.2,42.7,23.2,43.3,23.3z"/>
					<path class="st1" d="M43.4,26.6c0.3-0.6,0.4-0.9,0.6-1.5c0.3,0.6,0.4,0.9,0.7,1.5C44.2,26.7,44,26.7,43.4,26.6z"/>
					<path class="st1" d="M45.6,23.8l-0.5,1.3c-0.2-0.6-0.4-0.9-0.7-1.5C44.9,23.7,45.1,23.7,45.6,23.8z"/>
					<path class="st1" d="M45.6,26.7c0.2-0.5,0.3-0.8,0.5-1.3c0.2,0.6,0.3,0.8,0.5,1.4L45.6,26.7z"/>
					<path class="st1" d="M47.4,24.4c-0.2,0.4-0.3,0.6-0.5,1.1c-0.2-0.5-0.3-0.8-0.5-1.4L47.4,24.4z"/>
					<path class="st1" d="M47.4,26.9c0.2-0.4,0.3-0.7,0.5-1.1c0.2,0.5,0.3,0.7,0.5,1.2L47.4,26.9z"/>
					<path class="st1" d="M3.1,27.5c-0.2,0.4-0.3,0.6-0.6,1c-0.3-0.6-0.4-1-0.7-1.5C2.3,27.1,2.7,27.3,3.1,27.5z"/>
					<path class="st1" d="M3.3,30.3c0.2-0.4,0.3-0.6,0.5-1.1l0.8,1.7C4.1,30.8,3.7,30.5,3.3,30.3z"/>
					<path class="st1" d="M5.6,28l-0.6,1.4l-0.7-1.6C4.7,27.9,5.1,27.9,5.6,28z"/>
					<path class="st1" d="M5.7,31.4c0.3-0.5,0.4-0.8,0.7-1.4l0.9,1.9C6.6,31.7,6.3,31.6,5.7,31.4z"/>
					<path class="st1" d="M8.7,28.3c-0.4,0.7-0.6,1-0.9,1.7l-0.9-1.8C7.6,28.2,7.9,28.2,8.7,28.3z"/>
					<path class="st1" d="M8.8,32.2c0.4-0.7,0.6-1,1-1.7l1.1,2.1L8.8,32.2z"/>
					<path class="st1" d="M12.9,28.7c-0.5,0.7-0.8,1.1-1.3,1.8l-1.2-2.1C11.4,28.5,11.9,28.5,12.9,28.7z"/>
					<path class="st1" d="M13,33.1c0.5-0.7,0.8-1.1,1.4-1.8c0.6,1,0.8,1.4,1.4,2.4C14.6,33.5,14,33.4,13,33.1z"/>
					<path class="st1" d="M18.2,29.2l-1.5,2.1L15.3,29C16.4,29.1,17,29.1,18.2,29.2z"/>
					<path class="st1" d="M18.2,34.1l1.5-2.2l1.6,2.4C20,34.2,19.4,34.2,18.2,34.1z"/>
					<path class="st1" d="M24,29.3l-1.6,2.3l-1.6-2.3H24z"/>
					<path class="st1" d="M24,34.3l1.6-2.4c0.7,0.9,1,1.4,1.6,2.3C26,34.2,25.3,34.3,24,34.3z"/>
					<path class="st1" d="M30,29l-1.6,2.4c-0.6-0.9-1-1.3-1.6-2.2C28.1,29.1,28.7,29.1,30,29z"/>
					<path class="st1" d="M30,33.9l1.5-2.5c0.6,0.8,1,1.2,1.6,2C31.9,33.7,31.3,33.8,30,33.9z"/>
					<path class="st1" d="M35.5,28.6l-1.4,2.3c-0.6-0.8-0.9-1.2-1.5-2C33.8,28.8,34.4,28.7,35.5,28.6z"/>
					<path class="st1" d="M35.6,33.1l1.3-2.3l1.3,1.7C37.2,32.7,36.7,32.9,35.6,33.1z"/>
					<path class="st1" d="M40.1,28.1c-0.4,0.8-0.6,1.1-1.1,2c-0.5-0.7-0.8-1-1.3-1.7C38.7,28.2,39.2,28.1,40.1,28.1z"/>
					<path class="st1" d="M40.2,32c0.4-0.8,0.6-1.3,1-2c0.4,0.6,0.6,1,1,1.6L40.2,32z"/>
					<path class="st1" d="M43.4,27.8c-0.3,0.6-0.4,1-0.7,1.7c-0.3-0.6-0.5-0.9-0.9-1.6C42.5,27.9,42.9,27.8,43.4,27.8z"/>
					<path class="st1" d="M43.5,31.2l0.6-1.7c0.3,0.6,0.4,0.9,0.6,1.4L43.5,31.2z"/>
					<path class="st1" d="M45.6,27.8c-0.2,0.5-0.3,0.8-0.5,1.4c-0.2-0.5-0.3-0.8-0.6-1.4C45,27.8,45.2,27.8,45.6,27.8z"/>
					<path class="st1" d="M45.6,30.7c0.2-0.6,0.3-0.9,0.5-1.4c0.2,0.5,0.2,0.8,0.4,1.2L45.6,30.7z"/>
					<path class="st1" d="M47.3,27.8c-0.2,0.5-0.3,0.7-0.5,1.2c-0.1-0.5-0.2-0.7-0.4-1.2L47.3,27.8z"/>
					<path class="st1" d="M47.1,30.3c0.2-0.5,0.3-0.8,0.6-1.2c0.1,0.4,0.2,0.6,0.3,1L47.1,30.3z"/>
					<path class="st1" d="M3.4,31.3L3,32.1l-0.8-1.8C2.5,30.7,3,31,3.4,31.3z"/>
					<path class="st1" d="M3.9,34.3C4,34,4.1,33.8,4.2,33.4c0.3,0.8,0.5,1.2,0.9,2C4.6,35,4.2,34.7,3.9,34.3z"/>
					<path class="st1" d="M5.8,32.6c-0.2,0.5-0.3,0.8-0.5,1.3c-0.3-0.8-0.5-1.2-0.8-1.9C4.9,32.2,5.3,32.4,5.8,32.6z"/>
					<path class="st1" d="M6.2,36.2c0.2-0.5,0.3-0.8,0.5-1.3c0.4,0.8,0.5,1.3,0.9,2.2C7.1,36.8,6.6,36.5,6.2,36.2z"/>
					<path class="st1" d="M8.9,33.6c-0.4,0.6-0.5,1-0.8,1.6c-0.4-0.9-0.6-1.3-1-2.1C7.7,33.3,8.3,33.4,8.9,33.6z"/>
					<path class="st1" d="M9.1,37.7c0.3-0.6,0.5-1,0.9-1.6c0.4,0.9,0.7,1.4,1.1,2.5C10.2,38.2,9.8,38.1,9.1,37.7z"/>
					<path class="st1" d="M13,34.7c-0.5,0.7-0.8,1.1-1.2,1.7c-0.5-1-0.7-1.4-1.2-2.4L13,34.7z"/>
					<path class="st1" d="M13,39.4c0.5-0.7,0.8-1.1,1.3-1.8c0.6,1.1,0.8,1.6,1.4,2.7C14.6,40,14,39.7,13,39.4z"/>
					<path class="st1" d="M18.2,35.8l-1.5,2.1l-1.4-2.6C16.3,35.5,17.2,35.7,18.2,35.8z"/>
					<path class="st1" d="M18.2,40.9l1.5-2.2l1.6,2.6C20.2,41.3,19.2,41.1,18.2,40.9z"/>
					<path class="st1" d="M24,36.1l-1.6,2.5L20.8,36C22.1,36.1,22.7,36.1,24,36.1z"/>
					<path class="st1" d="M24,41.4l1.6-2.5l1.6,2.4C26.2,41.4,25.1,41.4,24,41.4z"/>
					<path class="st1" d="M30,35.7l-1.6,2.6L26.8,36C28.1,35.9,28.8,35.8,30,35.7z"/>
					<path class="st1" d="M30.1,40.9l1.6-2.7l1.6,2.1C32,40.6,31.3,40.7,30.1,40.9z"/>
					<path class="st1" d="M35.6,34.7l-1.4,2.6l-1.5-2C33.9,35.1,34.5,35,35.6,34.7z"/>
					<path class="st1" d="M35.7,39.6c0.6-1.1,0.8-1.7,1.3-2.7l1.3,1.7C37.5,38.9,36.6,39.3,35.7,39.6z"/>
					<path class="st1" d="M40.3,33.4c-0.4,0.9-0.6,1.4-1.1,2.3c-0.5-0.6-0.8-1-1.3-1.6C38.8,33.8,39.4,33.6,40.3,33.4z"/>
					<path class="st1" d="M40.3,37.6l1-2.4c0.4,0.6,0.6,0.9,0.9,1.5C41.6,37.1,41.2,37.2,40.3,37.6z"/>
					<path class="st1" d="M43.5,32.4l-0.7,2c-0.3-0.6-0.5-0.9-0.8-1.5L43.5,32.4z"/>
					<path class="st1" d="M43.5,36.1c0.3-0.9,0.4-1.2,0.6-2c0.2,0.6,0.3,0.8,0.5,1.3C44.2,35.7,44,35.8,43.5,36.1z"/>
					<path class="st1" d="M45.5,31.8c-0.2,0.6-0.3,1-0.5,1.7c-0.1-0.5-0.3-0.9-0.5-1.3C45,32,45.2,31.9,45.5,31.8z"/>
					<path class="st1" d="M45.3,34.9c0.2-0.7,0.3-1.1,0.5-1.7c0.1,0.5,0.1,0.7,0.2,1.1C45.8,34.5,45.6,34.7,45.3,34.9z"/>
					<path class="st1" d="M47,31.2c-0.2,0.5-0.3,0.8-0.5,1.4c-0.1-0.4-0.1-0.7-0.3-1.1L47,31.2z"/>
					<path class="st1" d="M46.6,33.8c0.2-0.6,0.3-0.9,0.6-1.5c0.1,0.4,0.1,0.5,0.2,0.9C47.1,33.5,46.9,33.6,46.6,33.8z"/>
					<path class="st1" d="M4.1,35.4C4,35.7,4,35.9,3.9,36.1c-0.4-0.9-0.6-1.3-1-2.1C3.4,34.6,3.6,34.9,4.1,35.4z"/>
					<path class="st1" d="M6.3,37.5C6.2,38,6.1,38.2,6,38.6c-0.4-0.9-0.6-1.3-0.9-2.2C5.5,36.8,5.9,37.1,6.3,37.5z"/>
					<path class="st1" d="M9.2,39.2c-0.3,0.6-0.4,0.9-0.7,1.5c-0.4-1-0.6-1.5-1-2.4C8.1,38.7,8.5,38.8,9.2,39.2z"/>
					<path class="st1" d="M9.5,43.6c0.2-0.5,0.4-1,0.7-1.5c0.4,1.1,0.6,1.6,1.1,2.8C10.7,44.5,10.1,44.1,9.5,43.6z"/>
					<path class="st1" d="M13.1,41c-0.5,0.7-0.7,1-1.1,1.7c-0.5-1.1-0.7-1.7-1.1-2.7C11.6,40.4,12.1,40.6,13.1,41z"/>
					<path class="st1" d="M18.1,42.8l-1.5,2.1c-0.6-1.2-0.9-1.8-1.4-2.9C16.2,42.3,17.2,42.6,18.1,42.8z"/>
					<path class="st1" d="M24,43.4l-1.7,2.6l-1.6-2.8C21.8,43.3,22.9,43.4,24,43.4z"/>
					<path class="st1" d="M30.1,42.8l-1.6,2.9l-1.6-2.5C27.9,43.2,29,43,30.1,42.8z"/>
					<path class="st1" d="M35.7,41.3c-0.6,1.2-0.8,1.8-1.4,3c-0.6-0.8-0.9-1.2-1.5-2.1C33.7,42,34.7,41.7,35.7,41.3z"/>
					<path class="st1" d="M40.4,39.2l-1.1,2.9c-0.5-0.7-0.7-1-1.3-1.7C39,39.9,39.5,39.6,40.4,39.2z"/>
					<path class="st1" d="M43.4,37.4l-0.7,2.4c-0.2-0.5-0.5-1-0.7-1.5C42.7,37.9,42.9,37.8,43.4,37.4z"/>
					<path class="st1" d="M45.2,36.1c-0.2,0.8-0.3,1.2-0.5,2c-0.1-0.4-0.2-0.9-0.3-1.3C44.7,36.5,45,36.3,45.2,36.1z"/>
					<path class="st1" d="M46.5,34.8c-0.2,0.7-0.3,1-0.6,1.7c0-0.3,0-0.7-0.1-1C46.1,35.2,46.2,35.1,46.5,34.8z"/>
					<path class="st1" d="M1.5,28c-0.1,0.1-0.2,0.2-0.2,0.3c0,0.2,0.1,0.4,0.1,0.6c0.3,0.3,0.6,0.5,0.9,0.7L1.5,28z"/>
					<path class="st1" d="M2,31.3c0,0-0.1,0.1-0.1,0.1c0.1,0.3,0.2,0.7,0.3,1c0.2,0.3,0.4,0.5,0.7,0.8C2.5,32.4,2.3,32,2,31.3z"/>
					<path class="st0" d="M5,38.2c0.2,0.3,0.4,0.6,0.6,0.9c-0.2-0.4-0.3-0.8-0.6-1.3C5,37.9,5,38.1,5,38.2z"/>
					<path class="st1" d="M7.1,40.9c0.3,0.3,0.6,0.7,0.9,1c-0.2-0.6-0.4-1.1-0.7-1.9C7.2,40.3,7.1,40.6,7.1,40.9z"/>
					<path class="st1" d="M13.2,45.9c0.7,0.4,1.5,0.8,2.3,1.1c-0.4-1-0.7-1.7-1.2-2.8C13.9,45,13.6,45.3,13.2,45.9z"/>
					<path class="st1" d="M18.3,48.1c0.9,0.3,1.8,0.5,2.8,0.6L19.7,46C19.1,46.9,18.8,47.3,18.3,48.1z"/>
					<path class="st1" d="M4,13.5c-0.4,0.6-0.5,1-0.9,1.6c0,0,0.1,0,0.1,0.1C3.4,14.6,3.7,14.1,4,13.5C3.9,13.5,3.9,13.5,4,13.5z"/>
					<path class="st1" d="M9,7.1C9,7.1,9,7.1,9,7.1c-0.6,0.5-1.2,1-1.7,1.7c0,0,0,0,0,0C7.8,8.2,8.4,7.7,9,7.1z"/>
					<path class="st1" d="M0.9,25.5c0.1,0,0.1,0.1,0.2,0.1c0-0.1,0-0.2,0-0.3C1,25.4,0.9,25.5,0.9,25.5z"/>
					<path class="st0" d="M3.4,15.3c0.2-0.7,0.3-1,0.5-1.7c-0.3,0.5-0.5,1-0.8,1.5C3.3,15.2,3.3,15.2,3.4,15.3z"/>
					<path class="st1" d="M9,7.1C8.4,7.7,7.8,8.2,7.3,8.8c0.3,0.4,0.4,0.6,0.8,1C8.4,8.8,8.6,8.2,9,7.1z"/>
					<path class="st1" d="M13,4.2c-0.8,0.4-1.5,0.9-2.2,1.5c0.4,0.5,0.6,0.8,1.1,1.4C12.3,6,12.5,5.4,13,4.2z"/>
					<path class="st1" d="M18.2,2c-1,0.3-1.9,0.6-2.8,1c0.5,0.7,0.8,1.1,1.4,1.9L18.2,2z"/>
					<path class="st1" d="M24,1C23,1,22,1.1,21,1.3l1.5,2.4L24,1z"/>
					<path class="st1" d="M29.7,1.4c-1-0.2-1.9-0.3-2.9-0.4l1.5,2.8C28.9,2.8,29.2,2.4,29.7,1.4z"/>
					<path class="st1" d="M34.9,3.1c-0.8-0.4-1.7-0.7-2.6-1c0.5,1.2,0.8,1.8,1.3,2.9C34.2,4.2,34.5,3.8,34.9,3.1z"/>
					<path class="st1" d="M39.2,5.6c-0.7-0.5-1.3-0.9-2.1-1.3c0.4,1.2,0.7,1.9,1.1,3C38.7,6.5,38.9,6.2,39.2,5.6z"/>
					<path class="st1" d="M45.9,13c-0.2-0.4-0.5-0.8-0.8-1.3c0.2,0.8,0.4,1.2,0.6,2C45.8,13.4,45.9,13.2,45.9,13z"/>
					<path class="st1" d="M48.2,19.6c0.1-0.2,0.1-0.3,0.2-0.4c0-0.2-0.1-0.3-0.1-0.5c-0.3-0.3-0.5-0.5-0.8-0.8
						C47.8,18.6,47.9,18.9,48.2,19.6z"/>
					<path class="st1" d="M48.6,22.8c0.2-0.2,0.2-0.4,0.3-0.5c0-0.1,0-0.2,0-0.4c-0.3-0.2-0.6-0.3-0.9-0.5
						C48.2,22,48.4,22.3,48.6,22.8z"/>
					<path class="st0" d="M1.5,24.8C1.4,25,1.2,25.2,1,25.3c0,0.1,0,0.2,0,0.3c0.3,0.2,0.7,0.4,1,0.5C1.8,25.6,1.7,25.3,1.5,24.8z"
						/>
					<path class="st1" d="M48.8,25.9c0.1-0.2,0.2-0.4,0.4-0.6c0-0.1,0-0.2,0-0.3c0,0,0,0,0-0.1c-0.3-0.1-0.5-0.2-0.9-0.3
						C48.4,25.2,48.5,25.4,48.8,25.9z"/>
					<path class="st1" d="M48.1,27.8c0.2,0.4,0.2,0.6,0.4,1c0.1-0.2,0.3-0.4,0.4-0.6c0-0.1,0-0.3,0.1-0.4
						C48.7,27.8,48.4,27.8,48.1,27.8z"/>
					<path class="st1" d="M48,31.8c0.1-0.2,0.2-0.4,0.3-0.5c0.1-0.2,0.1-0.4,0.2-0.6l-0.7,0.3C47.9,31.3,47.9,31.5,48,31.8z"/>
					<path class="st1" d="M24,49c0.3,0,0.7,0,1,0c0.7,0,1.4,0,2.2-0.1l-1.6-2.6L24,49z"/>
					<path class="st1" d="M30.1,48.5c1-0.2,2.1-0.5,3-0.9c-0.5-0.8-0.9-1.3-1.5-2.1L30.1,48.5z"/>
					<path class="st1" d="M35.8,46.5c0.8-0.4,1.6-0.9,2.4-1.4c-0.4-0.6-0.6-0.9-1.1-1.6C36.6,44.7,36.3,45.4,35.8,46.5z"/>
					<path class="st1" d="M40.4,43.5c0.1,0,0.1-0.1,0.2-0.1c0.5-0.5,1.1-0.9,1.6-1.4c-0.2-0.5-0.5-0.9-0.7-1.4L40.4,43.5z"/>
					<path class="st1" d="M43.4,40.6c0.3-0.3,0.5-0.6,0.8-1c-0.1-0.3-0.2-0.7-0.3-1C43.7,39.4,43.6,39.8,43.4,40.6z"/>
					<path class="st1" d="M45,38.5c0.1-0.2,0.3-0.4,0.4-0.6c0-0.3,0-0.6,0-0.9C45.2,37.6,45.1,37.9,45,38.5z"/>
					<path class="st0" d="M46.3,36.3c0.1-0.1,0.1-0.2,0.2-0.3v-0.1C46.4,36,46.4,36.1,46.3,36.3z"/>
					<path class="st1" d="M47.1,34.2c0,0.2,0,0.3,0,0.4c0.1-0.3,0.2-0.6,0.4-0.8C47.3,33.9,47.2,34,47.1,34.2z"/>
				</g>
			</g>
		</g>
		<path class="st2" d="M49.1,25c0,1.1-0.1,2.1-0.2,3.2c0,0.2,0,0.3-0.1,0.5c-0.5,3.4-1.7,6.6-3.6,9.4l-0.3,0.5
			c-2.1,3.1-4.9,5.6-8.2,7.5l0,0c-0.2,0.1-0.5,0.3-0.7,0.4c-3.4,1.7-7.1,2.6-10.9,2.6c-0.3,0-0.7,0-1,0c-0.8,0-1.6-0.1-2.4-0.2l0,0
			c-4.5-0.6-8.8-2.5-12.2-5.5c-0.3-0.2-0.5-0.4-0.7-0.7c-1-1-2-2-2.8-3.1c-0.1-0.2-0.3-0.3-0.4-0.5c-1.3-1.8-2.3-3.7-3.1-5.8
			c1.6,0.6,3.2,1,4.8,1.2l0,0c0.8,0.1,1.6,0.2,2.4,0.2c0.3,0,0.7,0,1,0c3.8,0,7.6-0.9,11-2.6l0.7-0.4l0,0c3.3-1.8,6.1-4.4,8.2-7.5
			c0.1-0.2,0.2-0.3,0.3-0.5c1.9-2.8,3.1-6.1,3.6-9.4c0-0.2,0.1-0.3,0.1-0.5c0.5-3.9,0.1-7.8-1.2-11.4C42.8,5.9,49.1,14.9,49.1,25z"
			/>
		<path class="st3" d="M49.1,25c0,1.1-0.1,2.1-0.2,3.2c0,0.2,0,0.3-0.1,0.5c-0.5,3.4-1.7,6.6-3.6,9.4l-0.3,0.5
			c-2.1,3.1-4.9,5.6-8.2,7.5l0,0c-0.2,0.1-0.5,0.3-0.7,0.4c-3.4,1.7-7.1,2.6-10.9,2.6c-0.3,0-0.7,0-1,0c-0.8,0-1.6-0.1-2.4-0.2l0,0
			c-4.5-0.6-8.8-2.5-12.2-5.5c-0.3-0.2-0.5-0.4-0.7-0.7c-1-1-2-2-2.8-3.1C9.1,42,13,43.6,17,44.2l0,0c0.8,0.1,1.6,0.2,2.4,0.2
			c0.3,0,0.7,0,1,0c3.8,0,7.6-0.9,10.9-2.6l0.7-0.4l0,0c3.3-1.8,6.1-4.4,8.3-7.5l0.3-0.5c1.9-2.8,3.1-6.1,3.6-9.4
			c0-0.2,0.1-0.3,0.1-0.5c0.8-6.3-0.8-12.7-4.7-17.7C45.6,10.4,49.1,17.5,49.1,25z"/>
	</g>
	<path class="st4" d="M25,1c13.3,0,24,10.8,24,24S38.3,49,25,49C11.7,49,1,38.3,1,25S11.7,1,25,1 M25,0C11.2,0,0,11.2,0,25
		s11.2,25,25,25s25-11.2,25-25S38.8,0,25,0L25,0z"/>
</g>
</svg>
