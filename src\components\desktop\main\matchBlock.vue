<template lang="pug">
.match-block
  .running-time-block(v-if="rt" :class="getStyle(rt)") {{ getLabel(rt) }}
  .running-score-block {{ rs }}
</template>

<script>
export default {
  props: {
    rt: {
      type: String,
    },
    rs: {
      type: String,
    },
  },
  data() {
    return {};
  },

  methods: {
    getStyle(e) {
      switch (e) {
      case "LIVE":
        return "danger";
        break;
      case "HT":
        return "info";
        break;
      case "DELAYED":
      case "PEN":
        return "warn";
        break;
      default:
        return "label";
        break;
      }
    },
    getLabel(e) {
      if (/\d/.test(e)) {
        return e.trim();
      } else {
        var m = e.trim();
        if (m)
          return this.$t("m.BT_" + e.trim());
        else
          return null;
      }
    },
  },
};
</script>