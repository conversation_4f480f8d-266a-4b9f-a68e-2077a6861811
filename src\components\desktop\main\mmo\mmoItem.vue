<template lang="pug">
.bet-value.bet-mmo(:class="[valueClass]", @click="placeBet($event.target)") {{ value }}
  //- small {{ dv }}
</template>

<script>
import cal from "@/library/_calculation";
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";

export default {
  props: {
    odds: {
      type: Array,
    },
    idx: {
      type: String,
    },
    pos: {
      type: String,
    },
    typ: {
      type: String,
    },
    cls: {
      type: String,
    },
    dataType: {
      type: String,
    },
  },
  data() {
    return {
      trigger: false,
      timer: null,
      old: null,
      dir: null,
      defStake: 0,
      newPage: true,
    };
  },
  computed: {
    betType() {
      return this.$store.state.layout.menu3;
    },
    id() {
      return this.odds[3];
    },
    value() {
      return this.odds[this.pos];
    },
    dv() {
      if (this.odds[parseInt(this.pos)]) {
        return parseFloat(this.odds[27]);
      } else {
        return 0;
      }
    },
    valueClass() {
      var result = [];
      if (this.cls) {
        result.push(this.cls);
      }
      if (this.trigger == true) {
        result.push("highlighted");
      }
      if (this.dir != null) {
        result.push(this.dir);
      }
      if (!this.allowBet) {
        result.push("nb");
      }
      if (this.value < 0) {
        result.push("text-red");
      }
      return result;
    },
    allowBet() {
      return this.odds[15] == 1;
    },
    pause() {
      return this.odds[14] == 1 || this.odds[21] == 1;
    },
    mixParlay() {
      return this.odds[13] == 1;
    },
  },
  watch: {
    dv(newVal, oldVal) {
      var nv = newVal;
      var ov = oldVal;
      if (nv == "" || nv == 0) {
        this.trigger = false;
        this.dir = null;
        return;
      } else {
        if (ov != nv) {
          this.trigger = true;
          if (nv < 0) {
            if (ov < nv) {
              this.dir = "down";
            } else {
              this.dir = "up";
            }
          } else {
            if (ov > nv) {
              this.dir = "down";
            } else {
              this.dir = "up";
            }
          }
          setTimeout(() => {
            this.trigger = false;
            this.dir = null;
          }, 10000);
        }
      }
      this.old = oldVal;
    },
    id(newVal, oldVal) {
      this.reset();
    },
    typ(newVal, oldVal) {
      this.reset();
    },
    betType(newVal, oldVal) {
      this.reset();
    },
  },
  mounted() {},
  destroyed() {},
  methods: {
    reset() {
      this.trigger = false;
      this.old = null;
      this.dir = null;
    },
    placeBet(e) {
      if (this.value) {
        $(".bet-value").removeClass("selected-odds");
        if (this.allowBet) {
          if (this.$store.state.layout.betting.defaultStake != "3") {
            this.defStake = this.$store.state.layout.betting.defaultStakeAmount;
          }
          if (this.betType != "parlay") {
            setTimeout(() => {
              $(e).addClass("selected-odds");
            }, 10);
            if (EventBus.betSingleMMO) {
              EventBus.betSingleMMO(this.odds, "MY", this.idx, this.value, this.betType, this.defStake, this.mixParlay, true, e, this.pos);
            }
          }
        }
      }
    },
  },
};
</script>
