# Vue 3 Project Setup Guide - Step by Step

## Step 1: Create New Vue 3 Project

### 1.1 Initialize Project
```bash
# Create the project
npm create vue@latest member-spa-vue3

# Navigate to project
cd member-spa-vue3

# Install dependencies
npm install

# Test the setup
npm run dev
```

### 1.2 Project Configuration Selection
When prompted, select these options:
```
✔ Project name: … member-spa-vue3
✔ Add TypeScript? … Yes
✔ Add JSX Support? … Yes
✔ Add Vue Router for Single Page Application development? … Yes
✔ Add Pinia for state management? … Yes
✔ Add Vitest for Unit Testing? … Yes
✔ Add an End-to-End Testing Solution? › Cypress
✔ Add ESLint for code quality? … Yes
✔ Add Prettier for code formatting? … Yes
```

## Step 2: Install Additional Dependencies

### 2.1 Install UI and Utilities
```bash
# UI Framework and styling
npm install @headlessui/vue @heroicons/vue
npm install sass

# HTTP client (replace vue-resource)
npm install axios

# Utilities (matching your existing stack)
npm install dayjs numeral js-cookie

# Vue ecosystem
npm install @vueuse/core
npm install @vuelidate/core @vuelidate/validators

# Notifications (replace vue-snotify)
npm install @kyvg/vue3-notification

# Development tools
npm install -D unplugin-auto-import unplugin-vue-components
npm install -D @vue/devtools
```

### 2.2 Update Vite Configuration
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core',
      ],
      dts: true,
    }),
    Components({
      dts: true,
      dirs: ['src/components'],
      extensions: ['vue'],
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 8080,
    host: true,
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`,
      },
    },
  },
})
```

## Step 3: Set Up Project Structure

### 3.1 Create Directory Structure
```bash
mkdir -p src/components/{ui,forms,layout,desktop,tournament}
mkdir -p src/composables
mkdir -p src/services
mkdir -p src/utils
mkdir -p src/types
mkdir -p src/styles
mkdir -p src/assets/{images,fonts,icons}
```

### 3.2 Copy Assets from Old Project
```bash
# Copy public assets
cp -r ../member-spa/public/* ./public/

# Copy source assets
cp -r ../member-spa/src/assets/* ./src/assets/

# Copy locales
cp -r ../member-spa/src/locales ./src/

# Copy config
cp ../member-spa/src/config.js ./src/config.js
```

## Step 4: Configure Core Infrastructure

### 4.1 Set Up Pinia Store
```javascript
// src/stores/user.js
import { defineStore } from 'pinia'
import { authService } from '@/services/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    balance: 0,
    account: {},
    rememberMe: null,
    isPublic: false,
    loading: false,
  }),

  getters: {
    isLoggedIn: (state) => !!state.account?.player_info?.session_token,
    
    playerInfo: (state) => state.account?.player_info || {},
    
    playerWallet: (state) => state.account?.player_wallet || {},
    
    currencyCode: (state) => state.account?.player_wallet?.currency_code || null,
    
    sessionToken: (state) => state.account?.player_info?.session_token || null,
    
    mmoMode: (state) => {
      const currencyCode = state.account?.player_wallet?.currency_code
      return currencyCode ? ['MMK', 'MMO'].includes(currencyCode) : false
    },
  },

  actions: {
    updateAccount(payload) {
      this.account = payload
      if (this.account.player_wallet) {
        this.balance = this.account.player_wallet.available_balance
      }
    },

    deleteAccount() {
      this.account = {}
      this.balance = 0
    },

    async login(credentials) {
      this.loading = true
      try {
        const response = await authService.login(credentials)
        this.updateAccount(response.data)
        return { success: true }
      } catch (error) {
        return { success: false, error: error.message }
      } finally {
        this.loading = false
      }
    },

    async logout() {
      try {
        await authService.logout()
      } finally {
        this.deleteAccount()
      }
    },
  },

  persist: {
    storage: localStorage,
    paths: ['account', 'rememberMe'],
  },
})
```

### 4.2 Set Up Services Layer
```javascript
// src/services/api.js
import axios from 'axios'
import { useUserStore } from '@/stores/user'
import config from '@/config'

const api = axios.create({
  baseURL: config.apiUrl || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    if (userStore.sessionToken) {
      config.headers.Authorization = `Bearer ${userStore.sessionToken}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const userStore = useUserStore()
      userStore.logout()
    }
    return Promise.reject(error)
  }
)

export default api
```

```javascript
// src/services/auth.js
import api from './api'

export const authService = {
  async login(credentials) {
    const response = await api.post('/auth/login', credentials)
    return response.data
  },

  async logout() {
    await api.post('/auth/logout')
  },

  async getBalance() {
    const response = await api.get('/user/balance')
    return response.data
  },

  async getProfile() {
    const response = await api.get('/user/profile')
    return response.data
  },
}
```

### 4.3 Set Up Router
```javascript
// src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// Import views
const HomeView = () => import('@/views/HomeView.vue')
const LoginView = () => import('@/views/LoginView.vue')
const DesktopView = () => import('@/views/desktop/DesktopView.vue')
const TournamentView = () => import('@/views/tournament/TournamentView.vue')

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomeView,
  },
  {
    path: '/login',
    name: 'login',
    component: LoginView,
  },
  {
    path: '/desktop',
    name: 'desktop',
    component: DesktopView,
    meta: { requiresAuth: true },
  },
  {
    path: '/tournament2',
    name: 'tournament',
    component: TournamentView,
    meta: { requiresAuth: true },
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// Navigation guard
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next({ name: 'login', query: { redirect: to.fullPath } })
  } else {
    next()
  }
})

export default router
```

## Step 5: Create Base UI Components

### 5.1 Base Button Component
```vue
<!-- src/components/ui/BaseButton.vue -->
<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    :type="type"
    @click="handleClick"
  >
    <span v-if="loading" class="loading-spinner" />
    <span v-if="!loading && icon" :class="iconClasses" />
    <span v-if="$slots.default" class="button-content">
      <slot />
    </span>
    <span v-else-if="title" class="button-content">{{ title }}</span>
  </button>
</template>

<script setup>
interface Props {
  variant?: 'primary' | 'secondary' | 'success' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  icon?: string
  title?: string
  type?: 'button' | 'submit' | 'reset'
  block?: boolean
}

interface Emits {
  (e: 'click', event: MouseEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  type: 'button',
  block: false,
})

const emit = defineEmits<Emits>()

const buttonClasses = computed(() => [
  'btn',
  `btn-${props.variant}`,
  `btn-${props.size}`,
  {
    'btn-block': props.block,
    'btn-disabled': props.disabled,
    'btn-loading': props.loading,
  },
])

const iconClasses = computed(() => [
  'btn-icon',
  props.icon,
])

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200 focus:ring-gray-500;
}

.btn-success {
  @apply text-white bg-green-600 hover:bg-green-700 focus:ring-green-500;
}

.btn-danger {
  @apply text-white bg-red-600 hover:bg-red-700 focus:ring-red-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-md {
  @apply px-4 py-2 text-sm;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.btn-block {
  @apply w-full;
}

.btn-disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-loading {
  @apply cursor-not-allowed;
}

.loading-spinner {
  @apply inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2;
}

.btn-icon {
  @apply w-4 h-4 mr-2;
}
</style>
```

### 5.2 Base Input Component
```vue
<!-- src/components/ui/BaseInput.vue -->
<template>
  <div class="form-group">
    <label v-if="label" :for="inputId" class="form-label">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    
    <div class="relative">
      <input
        :id="inputId"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="inputClasses"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      />
      
      <div v-if="error" class="error-icon">
        <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
      </div>
    </div>
    
    <div v-if="error" class="error-message">{{ error }}</div>
    <div v-else-if="help" class="help-text">{{ help }}</div>
  </div>
</template>

<script setup>
interface Props {
  modelValue?: string | number
  type?: 'text' | 'email' | 'password' | 'number' | 'tel'
  label?: string
  placeholder?: string
  error?: string
  help?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  size?: 'sm' | 'md' | 'lg'
}

interface Emits {
  (e: 'update:modelValue', value: string | number): void
  (e: 'blur', event: FocusEvent): void
  (e: 'focus', event: FocusEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  size: 'md',
})

const emit = defineEmits<Emits>()

const inputId = computed(() => `input-${Math.random().toString(36).substr(2, 9)}`)

const inputClasses = computed(() => [
  'form-input',
  `form-input-${props.size}`,
  {
    'form-input-error': props.error,
    'form-input-disabled': props.disabled,
  },
])

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = props.type === 'number' ? Number(target.value) : target.value
  emit('update:modelValue', value)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}
</script>

<style scoped>
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
  @apply w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
}

.form-input-sm {
  @apply px-3 py-1.5 text-sm;
}

.form-input-md {
  @apply px-3 py-2 text-base;
}

.form-input-lg {
  @apply px-4 py-3 text-lg;
}

.form-input-error {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}

.form-input-disabled {
  @apply bg-gray-100 cursor-not-allowed;
}

.error-icon {
  @apply absolute inset-y-0 right-0 pr-3 flex items-center;
}

.error-message {
  @apply mt-1 text-sm text-red-600;
}

.help-text {
  @apply mt-1 text-sm text-gray-500;
}
</style>
```

## Step 6: Create First Business Component

### 6.1 Migrate User Login Component
```vue
<!-- src/components/forms/LoginForm.vue -->
<template>
  <div class="login-form">
    <form @submit.prevent="handleSubmit">
      <div class="form-header">
        <h2 class="form-title">{{ $t('auth.login') }}</h2>
        <p class="form-subtitle">{{ $t('auth.loginSubtitle') }}</p>
      </div>

      <BaseInput
        v-model="form.username"
        :label="$t('auth.username')"
        :placeholder="$t('auth.usernamePlaceholder')"
        :error="errors.username"
        required
        @blur="validateUsername"
      />

      <BaseInput
        v-model="form.password"
        type="password"
        :label="$t('auth.password')"
        :placeholder="$t('auth.passwordPlaceholder')"
        :error="errors.password"
        required
        @blur="validatePassword"
      />

      <div class="form-options">
        <label class="remember-me">
          <input
            v-model="form.rememberMe"
            type="checkbox"
            class="form-checkbox"
          />
          <span>{{ $t('auth.rememberMe') }}</span>
        </label>

        <router-link to="/forgot-password" class="forgot-password">
          {{ $t('auth.forgotPassword') }}
        </router-link>
      </div>

      <BaseButton
        type="submit"
        variant="primary"
        size="lg"
        :loading="loading"
        :disabled="!isFormValid"
        block
      >
        {{ $t('auth.login') }}
      </BaseButton>

      <div v-if="loginError" class="error-message">
        {{ loginError }}
      </div>
    </form>
  </div>
</template>

<script setup>
import { useAuth } from '@/composables/useAuth'
import { useForm } from '@/composables/useForm'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseButton from '@/components/ui/BaseButton.vue'

interface LoginForm {
  username: string
  password: string
  rememberMe: boolean
}

const { login, loading } = useAuth()
const router = useRouter()

const form = reactive<LoginForm>({
  username: '',
  password: '',
  rememberMe: false,
})

const errors = reactive({
  username: '',
  password: '',
})

const loginError = ref('')

const validateUsername = () => {
  if (!form.username) {
    errors.username = 'Username is required'
  } else if (form.username.length < 3) {
    errors.username = 'Username must be at least 3 characters'
  } else {
    errors.username = ''
  }
}

const validatePassword = () => {
  if (!form.password) {
    errors.password = 'Password is required'
  } else if (form.password.length < 6) {
    errors.password = 'Password must be at least 6 characters'
  } else {
    errors.password = ''
  }
}

const isFormValid = computed(() => {
  return form.username && form.password && !errors.username && !errors.password
})

const handleSubmit = async () => {
  validateUsername()
  validatePassword()

  if (!isFormValid.value) return

  loginError.value = ''

  try {
    const result = await login(form)
    
    if (result.success) {
      const redirectTo = router.currentRoute.value.query.redirect as string
      router.push(redirectTo || '/desktop')
    } else {
      loginError.value = result.error || 'Login failed'
    }
  } catch (error) {
    loginError.value = 'An unexpected error occurred'
  }
}
</script>

<style scoped>
.login-form {
  @apply max-w-md mx-auto bg-white p-8 rounded-lg shadow-lg;
}

.form-header {
  @apply text-center mb-6;
}

.form-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.form-subtitle {
  @apply text-gray-600;
}

.form-options {
  @apply flex items-center justify-between mb-6;
}

.remember-me {
  @apply flex items-center text-sm text-gray-600 cursor-pointer;
}

.form-checkbox {
  @apply mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500;
}

.forgot-password {
  @apply text-sm text-blue-600 hover:text-blue-500;
}

.error-message {
  @apply mt-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm;
}
</style>
```

### 6.2 Create Auth Composable
```javascript
// src/composables/useAuth.js
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'

export function useAuth() {
  const userStore = useUserStore()
  const router = useRouter()

  const login = async (credentials) => {
    try {
      const result = await userStore.login(credentials)
      return result
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Login failed'
      }
    }
  }

  const logout = async () => {
    try {
      await userStore.logout()
      router.push('/login')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  return {
    // State
    user: computed(() => userStore.account),
    isAuthenticated: computed(() => userStore.isLoggedIn),
    loading: computed(() => userStore.loading),
    
    // Actions
    login,
    logout,
  }
}
```

## Step 7: Update Main App

### 7.1 Update Main.js
```javascript
// src/main.js
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

import App from './App.vue'
import router from './router'
import i18n from './i18n'

// Global styles
import './styles/main.scss'

const app = createApp(App)

// Configure Pinia
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

app.use(pinia)
app.use(router)
app.use(i18n)

app.mount('#app')
```

### 7.2 Update App.vue
```vue
<!-- src/App.vue -->
<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'

const userStore = useUserStore()
const { locale } = useI18n()

// Set initial language
onMounted(() => {
  const savedLanguage = localStorage.getItem('language') || 'en'
  locale.value = savedLanguage
})

// Watch for user changes
watch(
  () => userStore.account,
  (account) => {
    if (account?.player_info?.language) {
      locale.value = account.player_info.language
    }
  },
  { deep: true }
)
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}
</style>
```

## Step 8: Test the Setup

### 8.1 Run Development Server
```bash
npm run dev
```

### 8.2 Test Key Features
1. **Navigation**: Check that routing works
2. **Login**: Test the login form
3. **State Management**: Verify Pinia stores work
4. **Assets**: Check that images and styles load
5. **Build**: Run production build

```bash
# Test production build
npm run build
npm run preview
```

### 8.3 Run Tests
```bash
# Unit tests
npm run test:unit

# E2E tests
npm run test:e2e
```

## Next Steps

1. **Continue component migration**: Use the patterns established above
2. **Add more stores**: Create layout, cache, and betting stores
3. **Implement remaining views**: Desktop, tournament, etc.
4. **Add advanced features**: WebSocket, real-time updates
5. **Optimize performance**: Code splitting, lazy loading
6. **Add comprehensive testing**: More unit and E2E tests

This setup provides a solid foundation for migrating your entire Vue 2 application to Vue 3 with modern patterns and best practices.

---

*With this foundation in place, you can systematically migrate each component while maintaining code quality and modern Vue 3 patterns.* 