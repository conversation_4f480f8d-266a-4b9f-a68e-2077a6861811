<template lang="pug">
.hx-table.table-4d.hx-top-rounded.hx-mt-2(:class="source.marketId == 3 ? 'live' : 'non-live'" style="min-height: 46px; height: 46px; max-height: 46px;")
  .hx-cell.flex-fill
    .hx-row.h-100
      .d-flex.flex-row.align-items-center.h-100.w-100
        .pl-1
          img(:src="'/v1/images/icon-sport-svg/' + getImage(source.sportsId)" width="22")
        .d-flex.flex-row.align-items-baseline.align-item-margin
          .hx-header-title(:class="{ 'live' : source.marketId == 3 }") {{ source.marketType }}
          .hx-header-subtitle {{ source.sportsType }}
        .nav-item.nav-button.ml-auto
          a.nav-link(
            :href="'/info/rules?i=item-4-36'"
            target="_blank"
            onclick="event.stopPropagation();window.open(this.href,'info','top=10,height=600,width=1280,status=no,toolbar=no,menubar=no,location=no');return false;"
          )
            i.fas.fa-question-circle
</template>

<script>
import config from "@/config";
export default {
  props: {
    source: {
      type: Object
    }
  },
  computed: {
    language() {
      return this.$store.getters.language;
    }
  },
  methods: {
    getImage(e) {
      return config.getSportsImage(e);
    }
  }
}
</script>