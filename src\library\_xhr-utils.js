import config from "@/config";
import errors from "@/errors";
import Vue from "vue";

export default {
  loading: {
    prg: false,
    livetv: false
  },

  prg(args) {
    const url = config.prgUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "prg",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("min" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("max" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("bet_type" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("comm_type" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!args.min) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.max) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.bet_type) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.comm_type) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.prg = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.prg = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.login.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.prg = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },

  launchLiveTV(args) {
    const url = config.whiteLabel ? config.apiTvUrl() : config.liveTvUrl();
    // console.log(url);
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "launchLiveTV",
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("channel" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("match" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("sports" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!args.channel) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.match) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.sports) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.livetv = true;
        Vue.http.post(url, args).then(
          (res) => {
            this.loading.livetv = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.url;
                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.login.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          (err) => {
            this.loading.livetv = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  }
}; 