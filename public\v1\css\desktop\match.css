.flag-icon, .flag-icon-background {
	background-size: contain;
	background-position: 50%;
	background-repeat: no-repeat;
	-webkit-box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2);
	box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2);
}
.flag-icon {
	position: relative;
	display: inline-block;
	width: 21px;
	height: 16px;
	line-height: 16px;
	min-width: 21px;
}
.flag-icon:before {
	content: "\00a0"
}
.flag-icon.flag-icon-squared {
	width: 16px;
}
.flag-icon-austria-v {
	background-image: url(/images/result/austria.svg)
}
.flag-icon-belarus-v {
	background-image: url(/images/result/belarus.svg)
}
.flag-icon-belgium-v {
	background-image: url(/images/result/belgium.svg)
}
.flag-icon-croatia-v {
	background-image: url(/images/result/croatia.svg)
}
.flag-icon-czech-republic {
	background-image: url(/images/result/czech-republic.svg)
}
.flag-icon-denmark-v {
	background-image: url(/images/result/denmark.svg)
}
.flag-icon-england-v {
	background-image: url(/images/result/england.svg)
}
.flag-icon-finland-v {
	background-image: url(/images/result/finland.svg)
}
.flag-icon-france-v {
	background-image: url(/images/result/france.svg)
}
.flag-icon-germany-v {
	background-image: url(/images/result/germany.svg)
}
.flag-icon-hungary-v {
	background-image: url(/images/result/hungary.svg)
}
.ireland {
	width: 21px !important;
	background-image: url(/images/result/ireland.svg)
}
.flag-icon-italy-v {
	background-image: url(/images/result/italy.svg)
}
.flag-icon-netherlands-v {
	background-image: url(/images/result/netherlands.svg)
}
.flag-icon-poland-v {
	background-image: url(/images/result/poland.svg)
}
.flag-icon-portugal-v {
	background-image: url(/images/result/portugal.svg)
}
.flag-icon-russia-v {
	background-image: url(/images/result/russia.svg)
}
.flag-icon-scotland-v {
	background-image: url(/images/result/scotland.svg)
}
.flag-icon-spain-v {
	background-image: url(/images/result/spain.svg)
}
.flag-icon-sweden-v {
	background-image: url(/images/result/sweden.svg)
}
.flag-icon-switzerland-v {
	background-image: url(/images/result/switzerland.svg)
}
.flag-icon-turkey-v {
	background-image: url(/images/result/turkey.svg)
}
.flag-icon-ukraine-v {
	background-image: url(/images/result/ukraine.svg)
}
.flag-icon-wales-v {
	background-image: url(/images/result/wales.svg)
}
.flag-icon-iceland-v {
	background-image: url(/images/result/iceland.svg)
}
.flag-icon-norway-v {
	background-image: url(/images/result/norway.svg)
}
.flag-icon-slovenia-v {
	background-image: url(/images/result/slovenia.svg)
}
.block-title {
	padding: 12px 0;
	margin: 0 8px;
	font-size: 20px;
	font-weight: 600;
	border-bottom: 1px solid;
	color: #0f4f8c;
}
.block-small {
	padding: 0;
	margin: 0;
	border-bottom: 0;
}
.panel-content {
	padding: 4px 8px;
}
.table-group-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	margin-bottom: 16px;
}
.table-row {
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}
.table-heading {
	font-weight: 500;
}
.table-item {
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
	text-align: center;
	padding: 2px;
	font-size: 14px;
	border-bottom: 1px solid #4f7eab2e;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	   text-overflow: ellipsis;
	line-height: 30px;
}
.table-row:last-child .table-item {
	border-bottom: 0;
}
.table-group {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	text-align: left;
	width: 120px;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	   text-overflow: ellipsis;
	white-space: nowrap;
}
.text-highlight {
	font-weight: 600;
}
.tour-wrapper {
	font-family: "Roboto Condensed", sans-serif;
	max-width: 100%;
	height: auto;
}
.tour-wrapper .preloader {
	margin-top: 200px;
}
.info-wrapper.tour-wrapper {
	min-width: 480px;
	height: auto;
	display: block;
}
.info-wrapper.tour-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link {
	background: #309dcf;
	color: #ffffff;
}
.info-wrapper.tour-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link.active {
	background: #ffffff;
	color: #309dcf;
}
.tour-wrapper .bg-light {
	background: #ffffff !important;
}
.tour-wrapper .bg-dark {
	background: #00000000 !important;
}
.tour-wrapper .page-logo img {
	margin-top: 5px;
	height: 80px;
}
.tour-wrapper .page-logo {
	display: block;
	padding: 0 10px;
}
.tour-wrapper .h1 {
	font-family: "Oswald";
	font-weight: 500;
	font-size: 32px;
	text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.5);
}
.tour-wrapper .info-title {
	height: auto;
}
.info-tablewrap.tour-wrapper {
	height: auto;
}
.tournament {
}
.wbet-bg {
	background: #2199aa url(/images/result/bg.jpg?v=muQbdTxYGU) top center no-repeat;
	background-attachment: fixed;
	background-size: cover;
}
.tournament-bracket {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	    flex-direction: column;
	padding: 1rem;
}
.tournament-bracket-round {
	display: block;
	margin-left: -3px;
	-webkit-box-flex: 1;
	-ms-flex: 1;
	    flex: 1;
}
.tournament-bracket-round-title {
	color: #000000;
	font-size: 20px;
	font-weight: 600;
	text-align: center;
	margin-bottom: 5px;
	font-family: "Oswald";
	text-transform: uppercase;
}
.tournament-bracket-list {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	    flex-direction: column;
	-ms-flex-flow: row wrap;
	    flex-flow: row wrap;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	    justify-content: center;
	height: 100%;
	min-height: 100%;
	padding-bottom: 32px;
	margin-bottom: 32px;
	padding: 0;
}
.tournament-bracket-round:last-child .tournament-bracket-list {
	border: 0;
}
.tournament-bracket-item {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-flex: 0;
	-ms-flex: 0 1 auto;
	    flex: 0 1 auto;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	    justify-content: center;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	    flex-direction: column;
	-webkit-box-align: start;
	-ms-flex-align: start;
	    align-items: flex-start;
	position: relative;
	padding: 2% 0;
	width: 48%;
}
.tournament-bracket-item:nth-child(odd) {
	margin-right: 2%;
}
.tournament-bracket-item:nth-child(even) {
	margin-left: 2%;
}
.tournament-bracket-item::after {
}
.tournament-bracket-match {
	width: 100%;
	padding: 0;
	border: 1px solid #ffffff88;
	background: rgba(0, 0, 0, 0.5);
	outline: none;
	border-radius: 0.5rem;
	-webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
	        box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}
.tournament-bracket-match::before, .tournament-bracket-match::after {
}
.tournament-bracket-round:first-child .tournament-bracket-match::before, .tournament-bracket-round:first-child .tournament-bracket-match::after {
	display: none;
}
.tournament-bracket-content {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	margin-bottom: 6px;
}
.tournament-bracket-content::after {
	content: ':';
	width: 16px;
	text-align: center;
	padding: 3px 2px;
	color: #efdd00;
}
.tournament-bracket-content .tournament-bracket-team:first-child {
	-webkit-box-ordinal-group: 1;
	-ms-flex-order: 0;
	-ms-order: 0;
	order: 0;
	text-align: right;
}
.tournament-bracket-content .tournament-bracket-team:first-child .tournament-bracket-country {
	-webkit-box-ordinal-group: 3;
	-ms-flex-order: 2;
	-ms-order: 2;
	order: 2;
	-webkit-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end;
}
.tournament-bracket-content .tournament-bracket-team:first-child .tournament-bracket-score {
	-webkit-box-ordinal-group: 1;
	-ms-flex-order: 0;
	-ms-order: 0;
	order: 0;
}
.tournament-bracket-content .tournament-bracket-team:last-child {
	width: 50%;
	-webkit-box-ordinal-group: 3;
	-ms-flex-order: 2;
	-ms-order: 2;
	order: 2;
	text-align: left;
}
.tournament-bracket-content .tournament-bracket-team:last-child .tournament-bracket-code {
	-webkit-box-ordinal-group: 2;
	-ms-flex-order: 1;
	-ms-order: 1;
	order: 1;
}
.tournament-bracket-table {
	width: 100%;
	display: block;
	font-family: "Oswald";
}
.tournament-bracket-caption {
	font-size: 12px;
	color: #fff;
	font-weight: 400;
	padding: 6px 0;
	text-align: center;
	text-transform: uppercase;
	background: rgba(255, 255, 255, 0.25);
	margin-bottom: 6px;
	border-top-left-radius: 0.5rem;
	border-top-right-radius: 0.5rem;
}
.tournament-bracket-team {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: reverse;
	-ms-flex-direction: row-reverse;
	    flex-direction: row-reverse;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	    justify-content: space-between;
	white-space: nowrap;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	   text-overflow: ellipsis;
}
.tournament-bracket-country {
	font-size: 12px;
	font-weight: 300;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	margin-top: 3px;
	-webkit-box-align: center;
	-ms-flex-align: center;
	    align-items: center;
	white-space: nowrap;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	   text-overflow: ellipsis;
}
.tournament-bracket-code {
	padding: 0 8px;
	color: #ffffff;
	font-weight: 300;
	text-transform: uppercase;
	border: 0;
	text-decoration: none;
	cursor: help;
	white-space: nowrap;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	   text-overflow: ellipsis;
}
.tournament-bracket-score {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	    align-items: center;
}
.tournament-bracket-team:first-child .tournament-bracket-score {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: reverse;
	-ms-flex-direction: row-reverse;
	    flex-direction: row-reverse;
	padding-left: 12px;
}
.tournament-bracket-team:last-child .tournament-bracket-score {
	padding-right: 12px;
}
.tournament-bracket-number {
	display: inline-block;
	padding: 3px 6px 3px;
	border-bottom: 1px solid transparent;
	font-size: 16px;
	background-color: #eeeff4;
	border-color: #eeeff4;
}
.tournament-bracket-number.red-card-score {
	background: red;
	color: white;
	margin: 0 4px;
}
.tournament-bracket-team-winner .tournament-bracket-number {
	border-color: #f9d040;
}
.tournament-bracket__medal {
	padding: 0 8px;
}
.tournament-bracket__medal--gold {
	color: #FFD700;
}
.tournament-bracket__medal--silver {
	color: #C0C0C0;
}
.tournament-bracket__medal--bronze {
	color: #CD7F32;
}
@media (max-width: 481px) {
	.tournament-bracket-list {
		padding-bottom: 16px;
		margin-bottom: 16px;
	}
	.tournament-bracket-item {
		width: 100%;
	}
	.tournament-bracket-item:nth-child(odd), .tournament-bracket-item:nth-child(even) {
		margin-left: 0;
		margin-right: 0;
	}
	.tournament-bracket-match {
		padding: 12px 8px;
	}
	.tournament-bracket-country {
		margin-top: 0;
	}
	.tournament-bracket-code {
		padding: 0 4px;
	}
}
@media (min-width: 481px) {
	.tournament-bracket-content .tournament-bracket-team:first-child .tournament-bracket-country {
		-webkit-box-ordinal-group: 1;
		-ms-flex-order: 0;
		-ms-order: 0;
		order: 0;
	}
	.tournament-bracket-content .tournament-bracket-team:first-child .tournament-bracket-score {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		-ms-order: 2;
		order: 2;
	}
	.tournament-bracket-team {
		-webkit-box-orient: vertical;
		-webkit-box-direction: reverse;
		-ms-flex-direction: column-reverse;
		flex-direction: column-reverse;
	}
}
@media (min-width: 880px) {
	.tournament-bracket {
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-ms-flex-direction: row;
		    flex-direction: row;
	}
	.tournament-bracket-list {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: 0;
	}
	.tournament-bracket-item {
		padding: 8px 16px;
		width: 100%;
	}
	.tournament-bracket-item:nth-child(odd), .tournament-bracket-item:nth-child(even) {
		margin: 0;
	}
	.tournament-bracket-item::after {
		position: absolute;
		right: 0;
		content: '';
		display: block;
		width: 16px;
		height: 45%;
		border-right: 1px solid #ffffff88;
	}
	.tournament-bracket-item:nth-child(odd)::after {
		top: 50%;
		border-top: 1px solid #ffffff88;
		-webkit-transform: translateY(-1px);
		-ms-transform: translateY(-1px);
		    transform: translateY(-1px);
	}
	.tournament-bracket-rounded .tournament-bracket-item:nth-child(odd)::after {
		border-top-right-radius: 10px;
	}
	.tournament-bracket-item:nth-child(even)::after {
		bottom: 50%;
		border-bottom: 1px solid #ffffff88;
		-webkit-transform: translateY(1px);
		-ms-transform: translateY(1px);
		    transform: translateY(1px);
	}
	.tournament-bracket-rounded .tournament-bracket-item:nth-child(even)::after {
		border-bottom-right-radius: 10px;
	}
	.tournament-bracket-round:first-child .tournament-bracket-item {
		padding-left: 0;
	}
	.tournament-bracket-round:last-child .tournament-bracket-item {
		padding-right: 0;
	}
	.tournament-bracket-round:last-child .tournament-bracket-item::after {
		display: none;
	}
	.tournament-bracket-match::before, .tournament-bracket-match::after {
		position: absolute;
		left: 2px;
		z-index: 1;
		content: '';
		display: block;
		width: 16px;
		height: 10%;
		border-left: 1px solid #ffffff88;
	}
	.tournament-bracket-match::before {
		bottom: 50%;
		-webkit-transform: translate(0, 1px);
		-ms-transform: translate(0, 1px);
		    transform: translate(0, 1px);
	}
	.tournament-bracket-rounded .tournament-bracket-match::before {
		border-bottom-left-radius: 10px;
	}
	.tournament-bracket-match::after {
		top: 50%;
		border-top: 1px solid #ffffff88;
		-webkit-transform: translate(0, -1px);
		-ms-transform: translate(0, -1px);
		    transform: translate(0, -1px);
	}
	.tournament-bracket-rounded .tournament-bracket-match::after {
		border-top-left-radius: 10px;
	}
	.tournament-bracket-content::after {
		-webkit-box-ordinal-group: 2;
		-ms-flex-order: 1;
		    order: 1;
	}
	.tournament-bracket-content .tournament-bracket-team:last-child .tournament-bracket-code {
		-webkit-box-ordinal-group: 2;
		-ms-flex-order: 1;
		-ms-order: 1;
		order: 1;
	}
	.tournament-bracket-content .tournament-bracket-team:last-child .tournament-bracket-country {
		-webkit-box-pack: start;
		-ms-flex-pack: start;
		justify-content: flex-start;
	}
	.tournament-bracket-team {
		-webkit-box-orient: vertical;
		-webkit-box-direction: reverse;
		-ms-flex-direction: column-reverse;
		flex-direction: column-reverse;
	}
}
@media (min-width: 992px) {
	.tournament-bracket-item {
		padding: 7px 24px;
	}
	.tournament-bracket-item::after {
		width: 24px;
	}
	.tournament-bracket-match::before, .tournament-bracket-match::after {
		width: 24px;
	}
	.tournament-bracket-match::before {
		-webkit-transform: translate(0, 1px);
		-ms-transform: translate(0, 1px);
		    transform: translate(0, 1px);
	}
	.tournament-bracket-match::after {
		-webkit-transform: translate(0, -1px);
		-ms-transform: translate(0, -1px);
		    transform: translate(0, -1px);
	}
}
@media (min-width: 880px) and (max-width: 1200px) {
	.tournament-bracket-content .tournament-bracket-team:first-child {
		-webkit-box-align: end;
		-ms-flex-align: end;
		    align-items: flex-end;
	}
	.tournament-bracket-content .tournament-bracket-team:first-child .tournament-bracket-country {
		-webkit-box-orient: vertical;
		-webkit-box-direction: reverse;
		-ms-flex-direction: column-reverse;
		    flex-direction: column-reverse;
		-webkit-box-align: end;
		-ms-flex-align: end;
		    align-items: flex-end;
	}
	.tournament-bracket-content .tournament-bracket-team:last-child {
		-webkit-box-align: start;
		-ms-flex-align: start;
		    align-items: flex-start;
	}
	.tournament-bracket-content .tournament-bracket-team:last-child .tournament-bracket-country {
		-webkit-box-align: start;
		-ms-flex-align: start;
		    align-items: flex-start;
	}
	.tournament-bracket-country {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-ms-flex-direction: column;
		    flex-direction: column;
	}
	.tournament-bracket-country .tournament-bracket-code {
		margin-top: 3px;
	}
	.tournament-bracket-code {
		padding: 0;
	}
}
.match-result {
	position: relative;
	border-radius: 8px 8px 8px 8px;
	overflow: hidden;
	margin: 8px 0;
	background: #fff;
}
.match-number {
	width: 28px;
	position: absolute;
}
.match-number .text-match {
	z-index: 1;
	font-size: 12px;
	color: #ffffff;
	position: relative;
	font-weight: bold;
	top: 5px;
	left: 2px;
	text-align: center;
	line-height: 16px;
}
.match-number::after {
	content: "";
	position: absolute;
	top: 0;
	left: -1px;
	border-style: solid solid solid solid;
	border-width: 50px 55px 0px 0px;
	border-color: #0f4f8c transparent transparent transparent;
}
.team-wrap {
	font-size: 14px;
	line-height: 18px;
	padding: 8px 0px;
	color: #000;
	min-height: 65px;
}
.team-name {
	padding: 0px;
	overflow: hidden;
	width: 120px;
}
.team-score {
	font-size: 18px;
	font-weight: bold;
	padding: 0 12px;
	position: absolute;
	color: #0f4f8c;
}
.team-score.home {
	margin-left: -80px;
}
.team-score.away {
	margin-left: 80px;
}
.team-vs {
	line-height: 8px;
	padding: 0 4px;
	white-space: nowrap;
}
.team-text {
	padding: 4px 10px 0 10px;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	   text-overflow: ellipsis;
	white-space: nowrap;
	width: 100%;
	text-transform: capitalize;
}
.team-date {
	font-size: 14px;
	font-weight: bold;
	line-height: 24px;
}
.team-time {
	padding: 2px;
}
.team-status {
	padding: 2px;
}