<template lang="pug">
button.btn(type="button" @click="handleClick" :disabled="loading" :class="css")
  span(v-if="!loading") {{ text }}
  i(v-if="img != '' && !loading" :class="img")
  .spinner-grow.spinner-grow-sm.text-white(v-if="loading")
</template>

<script>
export default {
  props: {
    text: {
      type: String,
      default: "OK"
    },
    css: {
      type: String,
      default: ""
    },
    loading: {
      type: Boolean,
      default: false
    },
    img: {
      type: String,
      default: ""
    }
  },
  methods: {
    handleClick() {
      this.$emit("click");
    }
  }
};
</script>
