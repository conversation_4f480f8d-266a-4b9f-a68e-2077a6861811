<template lang="pug">
.bg
  .side-row.xb.special-event
    .heading-collapse.collapsed(
      :id="'heading-sm-' + menuId"
      data-toggle="collapse"
      :data-target="'#collapse-sm-' + menuId"
      aria-expanded="false"
      :aria-controls="'collapse-sm-' + menuId"
      @click="setMenu0('event', false, menuId, getEventDate(startDate))"
      )
      .group(:title="title" :class="{ 'selected' : menu0 == 'event' && menuY == menuId }")
        .d-flex.justify-content-center.align-items-center
          .live-icon
            img(:src="icon" width="20px" height="20px")
          .flex-fill.active-none
            span {{ title }}
          .arrow-up.active-none
            i.far.fa-chevron-up
  .collapse.xb(:id="'collapse-sm-' + menuId" aria-labelledby="'heading-sm-' + menuId" data-parent="#accordion-sports")
    .group.sub.group-mmo
      ul.subgroup
        li.small
          a(
            href="javascript:void(0);"
            @click="setMenu0('event', false, menuId, 'early')"
          )
            .d-flex(:class="{ 'active' : menu0 == 'event' && menuY == menuId && menu1 == 'early' && menu3 == 'hdpou' }")
              .subtitle.flex-fill
                span {{ $t("ui.early") }}
              .game-number
        li.small
          a(
            href="javascript:void(0);"
            @click="setMenu0('event', false, menuId, 'today')"
          )
            .d-flex(:class="{ 'active' : menu0 == 'event' && menuY == menuId && menu1 == 'today' && menu3 == 'hdpou' }")
              .subtitle.flex-fill
                span {{ $t("ui.today") }}
              .game-number
        li.small
          a(
            href="javascript:void(0);"
            @click="setMenu0('event', false, menuId, 'today', 'orz')"
          )
            .d-flex(:class="{ 'active' : menu0 == 'event' && menuY == menuId && menu3 == 'orz'}")
              .subtitle.flex-fill
                span {{ $t("ui.outright") }}
              .game-number
</template>

<script>
export default {
  name: 'MenuSpecialEvent',
  props: {
    startDate: {
      type: String,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      required: true
    },
    menuId: {
      type: String,
      required: true
    },
    menu0: {
      type: String,
      required: true
    },
    menu1: {
      type: String,
      required: true
    },
    menu3: {
      type: String,
      required: true
    },
    menuY: {
      type: String,
      required: true
    }
  },
  methods: {
    setMenu0(event, x, y, z, bt) {
      this.$emit('setMenu0', event, x, y, z, bt);
    },
    getEventDate(date) {
      const d1 = new Date();
      const d2 = new Date(date);
      return d1 >= d2 ? 'today' : 'early';
    }
  }
}
</script> 