<template lang="pug">
  .d-flex.flex-row.filter-block
    .filter-single
      .d-flex.flex-row(@click="handleSelection(2)")
        .filter-date(:class="{ 'active': 2 == parseInt(parlayMode) }")
          .filter-date-title {{ $t("ui.today") }}
          .filter-date-body {{ $dayjs.utc().tz("Asia/Kuala_Lumpur").format("M/DD") }}
    .filter-single
      .d-flex.flex-row(@click="handleSelection(1)")
        .filter-date(:class="{ 'active': 1 == parseInt(parlayMode) }")
          .filter-date-title {{ $t("ui.early") }}
          .filter-date-body &ge; {{ $dayjs.utc().tz("Asia/Kuala_Lumpur").add(1, 'day').format("M/DD") }}
    .filter-single
      .d-flex.flex-row(@click="handleSelection(6)")
        .filter-date(:class="{ 'active': 6 == parseInt(parlayMode) }")
          .filter-date-title {{ $t("ui.all") }}
          .filter-date-body */*
</template>

<script>
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";

export default {
  computed: {
    parlayMode() {
      return this.$store.state.cache.parlayMode;
    }
  },
  methods: {
    handleSelection(e) {
      this.$store.dispatch("cache/setParlayMode", e);
      EventBus.$emit("INVALIDATE");
    }
  }
};
</script>