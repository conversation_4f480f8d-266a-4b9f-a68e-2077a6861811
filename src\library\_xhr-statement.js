import config from "@/config";
import errors from "@/errors";
import Vue from "vue";

export default {
  loading: {
    getBetParlay: false,
    getBetParlayMatch: false,
    getDailyStatement: false,
    getPlatformWinlose: false,
    getGameResult: false,
    getGameResultSummary: false,
    getParlayDetails: false,
    getParlayResults: false,
    getCancelledBetList: false,
    getEsportsDetails: false,
    getRefundList: false
  },

  getBetParlayBet(args) {
    const url = config.reportBetSystemParlayBetUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getBetPagetBetParlayMatchrlay"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.getBetParlayMatch = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getBetParlayMatch = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.statement.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getBetParlayMatch = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getBetParlayMatch(args) {
    const url = config.reportBetSystemParlayMatchUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getBetPagetBetParlayMatchrlay"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.getBetParlayMatch = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getBetParlayMatch = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.statement.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getBetParlayMatch = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getBetParlay(args) {
    const url = config.reportBetSystemParlayUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getBetParlay"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.getBetParlay = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getBetParlay = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.statement.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getBetParlay = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getDailyStatement(args) {
    const url = config.dailyStatementUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getDailyStatement"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading.getDailyStatement == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.getDailyStatement = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getDailyStatement = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.statement.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getDailyStatement = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getPlatformWinlose(args) {
    const url = config.platformWinloseUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getPlatformWinlose"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("start_date" in args)) {
        feedback.status = errors.statement.startDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("end_date" in args)) {
        feedback.status = errors.statement.endDateRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.start_date) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.end_date) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading.getPlatformWinlose == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.getPlatformWinlose = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getPlatformWinlose = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.statement.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getPlatformWinlose = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getSettlement(args) {
    const url = config.settlementUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getSettlement"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("working_date" in args)) {
        feedback.status = errors.statement.workingDateRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.working_date) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      // if (this.loading.getSettlement == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.getSettlement = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getSettlement = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.statement.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getSettlement = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getGameResult(args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getGameResult"
    };
    // console.log("getGameResult-XHR", args);
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || !args.account_id) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || !args.session_token) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("start_date" in args) || !args.start_date) {
        feedback.status = errors.statement.startDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("end_date" in args) || !args.end_date) {
        feedback.status = errors.statement.endDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("page_number" in args) || !args.page_number) {
        feedback.status = errors.statement.pageNumberRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("page_size" in args) || !args.page_size) {
        feedback.status = errors.statement.pageSizeRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("category_type" in args) || !args.category_type) {
        feedback.status = errors.statement.sportsTypeRequired;
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        var url = "";

        if (args.category_type == "slots") url = config.gameResultSlotsUrl();
        else if (args.category_type == "livecasino") url = config.gameResultLiveCasinoUrl();
        else if (args.category_type == "lottery") url = config.gameResultLotteryUrl();
        else if (args.category_type == "esports2") url = config.gameResultEsports2Url();
        else if (args.category_type == "tournament") url = config.gameResultTournamentUrl();
        else if (args.category_type == "adjustment") url = config.gameResultAdjustmentUrl();
        else url = config.gameResultSportsbookUrl();

        this.loading.getGameResult = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getGameResult = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  if (res.data.hasOwnProperty("names")) {
                    feedback.names = res.data.names;
                  }
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getGameResult = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getGameResultSummary(args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getGameResultSummary"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("start_date" in args)) {
        feedback.status = errors.statement.startDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("end_date" in args)) {
        feedback.status = errors.statement.endDateRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.start_date) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.end_date) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading.getGameResult == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        var url = "";
        url = config.gameResultSummaryUrl();

        this.loading.getGameResultSummary = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getGameResultSummary = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;

                  if (res.data.hasOwnProperty("names")) {
                    feedback.names = res.data.names;
                  }

                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getGameResultSummary = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getEsportsDetails(args) {
    // console.log(args);
    const url = config.gameResultEsports2DetailUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "getEsportsDetails"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.bet_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      this.loading.getEsportsDetails = true;
      if (canRequest == true) {
        Vue.http.post(url, args).then(
          res => {
            this.loading.getEsportsDetails = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              // console.log(res);
              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;

                  if (res.data.hasOwnProperty("names")) {
                    feedback.names = res.data.names;
                  }

                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.request.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getEsportsDetails = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getParlayDetails(args) {
    // console.log(args);
    const url = config.statementParlayDetailsUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "getParlayDetails"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.bet_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      this.loading.getParlayDetails = true;
      if (canRequest == true) {
        Vue.http.post(url, args).then(
          res => {
            this.loading.getParlayDetails = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              // console.log(res);
              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;

                  if (res.data.hasOwnProperty("names")) {
                    feedback.names = res.data.names;
                  }

                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.request.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getParlayDetails = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getCancelledBetList(args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getCancelledBetList"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("working_date" in args)) {
        feedback.status = errors.result.workingDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("page_number" in args)) {
        feedback.status = errors.statement.pageNumberRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("page_size" in args)) {
        feedback.status = errors.statement.pageSizeRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.working_date) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.page_number) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.page_size) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        var url = config.cancelledBetListUrl();

        if (args.mode == "lottery") {
          url = config.cancelledBetListLotteryUrl();
        }

        this.loading.getCancelledBetList = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getCancelledBetList = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;

                  if (res.data.hasOwnProperty("names")) {
                    feedback.names = res.data.names;
                  }

                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getCancelledBetList = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  getRefundList(args) {
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "getRefundList"
    };
    // console.log("getGameResult-XHR", args);
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args) || !args.account_id) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args) || !args.session_token) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("start_date" in args) || !args.start_date) {
        feedback.status = errors.statement.startDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("end_date" in args) || !args.end_date) {
        feedback.status = errors.statement.endDateRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("page_number" in args) || !args.page_number) {
        feedback.status = errors.statement.pageNumberRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("page_size" in args) || !args.page_size) {
        feedback.status = errors.statement.pageSizeRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("category_type" in args) || !args.category_type) {
        feedback.status = errors.statement.sportsTypeRequired;
        reject(feedback);
        canRequest = false;
      }

      if (canRequest == true) {
        var url = config.gameResultTournamentRefundUrl();

        this.loading.getRefundList = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.getRefundList = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = res.data.value;
                  if (res.data.hasOwnProperty("names")) {
                    feedback.names = res.data.names;
                  }
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.message.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.getRefundList = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  }
};
