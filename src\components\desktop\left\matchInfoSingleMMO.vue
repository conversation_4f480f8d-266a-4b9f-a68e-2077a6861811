<template lang="pug">
.match-info-parlay
  .team01.text-center(:class="{ 'red': betslip.giving == 1 && isBallDisplay }") {{ betslip.homeName }}
  .text-vs.text-center(v-if="betslip.betType != 'OR'") - VS -
  .team02.text-center(:class="{ 'red': betslip.giving == 0 && isBallDisplay }" v-if="betslip.betType != 'OR'")  {{ betslip.awayName }}
  .league.text-center {{ betslip.leagueName }}
</template>

<script>
import naming from "@/library/_name";

export default {
  props: {
    betslip: {
      type: Object
    }
  },
  computed: {
    isBallDisplay() {
      var result = naming.ballDisplay2(this.betslip.ballOrigin, this.betslip.giving, this.betslip.homeAway, this.betslip.betType, this);
      if (["HDP", "HDPH"].includes(this.betslip.betType)) {
        return result != null && result != "0";
      } else {
        return false;
      }
    }
  }
};
</script>