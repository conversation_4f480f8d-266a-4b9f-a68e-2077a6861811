import config from "@/config";
import errors from "@/errors";
import service from "@/library/_xhr-ext";

export default {
  data() {
    return {
      schedule: {
        LiveTV: [],
        LiveMatch: [],
        Events: [],
        Brackets: [],
        Standing: [],
        Result: [],
        NgResult: [],
        EFResult: []
      },
      loading: {
        LiveTV: false,
        LiveMatch: false,
        Events: false,
        Brackets: false,
        Standing: false,
        Result: false,
        NgResult: false,
        EFResult: false
      },
      feedback: {
        success: false,
        status: errors.session.invalidSession
      }
    };
  },
  methods: {
    getLiveMatchList() {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken
      };
      this.loading.LiveMatch = true;
      service.getLiveList(config.liveMatchListUrl(), json).then(
        result => {
          this.loading.LiveMatch = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              var data = result.data;
              var group = data.reduce((r, a) => {
                r[a.sports_type] = [...(r[a.sports_type] || []), a];
                return r;
              }, {});
              var gk = Object.keys(group);
              for (var i = 0; i < gk.length; i++) {
                group[gk[i]].sort((a, b) => {
                  var keyA = new Date(a.match_time),
                    keyB = new Date(b.match_time);
                  if (keyA < keyB) return -1;
                  if (keyA > keyB) return 1;
                });
              }
              this.schedule.LiveMatch = group;
              // console.log(this.schedule.LiveMatch);
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        err => {
          this.loading.LiveMatch = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getLiveTVList() {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken
      };
      this.loading.LiveTV = true;
      service.getLiveList(config.liveTvListUrl(), json).then(
        result => {
          this.loading.LiveTV = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              var data = result.data;
              var group = data.reduce((r, a) => {
                r[a.sports_type] = [...(r[a.sports_type] || []), a];
                return r;
              }, {});
              var gk = Object.keys(group);
              for (var i = 0; i < gk.length; i++) {
                group[gk[i]].sort((a, b) => {
                  var keyA = new Date(a.match_time),
                    keyB = new Date(b.match_time);
                  if (keyA < keyB) return -1;
                  if (keyA > keyB) return 1;
                });
              }
              this.schedule.LiveTV = group;
              // console.log(this.schedule.LiveTV);
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        err => {
          this.loading.LiveTV = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },

    getEvents() {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken
      };
      this.loading.Events = true;
      service.getLiveList(config.eventListUrl(), json).then(
        result => {
          this.loading.Events = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.schedule.Events = result.data;
              // console.log(this.schedule.Events);
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        err => {
          this.loading.Events = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },

    getStanding(e) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        event_id: e.toString()
      };
      this.loading.Standing = true;
      service.getLiveList(config.standingListUrl(), json).then(
        result => {
          this.loading.Standing = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              var data = result.data;
              var group = data.reduce((r, a) => {
                r[a.group_id] = [...(r[a.group_id] || []), a];
                return r;
              }, {});
              this.schedule.Standing = group;
              // console.log(this.schedule.Standing);
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        err => {
          this.loading.Standing = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },

    getResult(e) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        event_id: e.toString()
      };
      this.loading.Result = true;
      service.getLiveList(config.standingResultUrl(), json).then(
        result => {
          this.loading.Result = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              var data = result.data;
              var group = data.reduce((r, a) => {
                r[a.group_id] = [...(r[a.group_id] || []), a];
                return r;
              }, {});
              var gk = Object.keys(group);
              for (var i = 0; i < gk.length; i++) {
                group[gk[i]].sort((a, b) => {
                  var keyA = parseInt(a.match_number),
                    keyB = parseInt(b.match_number);
                  if (keyA < keyB) return -1;
                  if (keyA > keyB) return 1;
                });
              }
              this.schedule.Result = group;
              // console.log(this.schedule.Result);
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        err => {
          this.loading.Result = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },

    getNgResult(e) {
      return new Promise(resolve => {
        var json = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          league_id: e.toString()
        };
        this.loading.NgResult = true;
        service.getLiveList(config.ngResultUrl(), json).then(
          result => {
            this.loading.NgResult = false;
            if (result) {
              this.feedback.status = result.status;
              if (result.success == true) {
                this.schedule.NgResult = result.data;
              } else {
                this.$helpers.handleFeedback(this.feedback.status);
              }
            }
            resolve();
          },
          err => {
            this.loading.NgResult = false;
            this.feedback.success = false;
            this.feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);
            resolve();
          }
        );
      });
    },

    getEFResult(e) {
      return new Promise(resolve => {
        var json = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          sports_type: e.toString()
        };
        this.loading.EFResult = true;
        service.getLiveList(config.efResultUrl(), json).then(
          result => {
            this.loading.EFResult = false;
            if (result) {
              this.feedback.status = result.status;
              if (result.success == true) {
                this.schedule.EFResult = result.data;
              } else {
                this.$helpers.handleFeedback(this.feedback.status);
              }
            }
            resolve();
          },
          err => {
            this.loading.EFResult = false;
            this.feedback.success = false;
            this.feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);
            resolve();
          }
        );
      });
    },

    getBrackets(e) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        event_id: e.toString(),
        round_level: "0"
      };
      this.loading.Brackets = true;
      service.getLiveList(config.bracketListUrl(), json).then(
        result => {
          this.loading.Brackets = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              var data = result.data;
              var group = data.reduce((r, a) => {
                r[a.round_level] = [...(r[a.round_level] || []), a];
                return r;
              }, {});
              this.schedule.Brackets = group;
              // console.log(this.schedule.Brackets);
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        err => {
          this.loading.Brackets = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    }
  }
};