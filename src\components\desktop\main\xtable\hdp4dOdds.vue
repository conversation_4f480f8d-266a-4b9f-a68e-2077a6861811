<template lang="pug">
  .hx-main.hdp4dOdds
    .hx-table.hx-match.hx-morebet(
      :id="'morebet_' + id"
      :aria-labelledby="'morehead_' + id"
      data-parent="#hdpou"
      :class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }"
      )
      morePanel(
        ref="morePanel"
        :uid="id"
        :details="details"
        :child1Ids="details['child1']"
        :child2Ids="details['child2']"
        :matchId="matchId"
        :leagueId="leagueId"
        :marketType="marketType"
        :sportsType="sportsType"
        :betType="betType"
        :layoutIndex="layoutIndex"
      )
</template>

<script>
import court from "@/components/desktop/court";
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";
// import mixinFavorite from "@/library/mixinFavorite";
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

export default {
  components: {
    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    morePanel: () => import("@/components/desktop/main/xheader/morePanel"),
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem"),
    court
  },
  mixins: [mixinHDPOUOdds],
  data() {
    return {
      show: false,
      timer: null,
    };
  },
  mounted() {
    // setTimeout(() => {
    //   this.show = true;
    // }, (this.index % 10) * config.showInterval);
  },
  methods: {
  }
};
</script>