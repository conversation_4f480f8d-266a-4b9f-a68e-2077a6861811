<template lang="pug">
div
  ul.nav.nav-tabs(role='tablist')
    li.nav-item
      a#tab-betlist.nav-link.active(
        data-toggle='tab'
        href='#betlist'
        role='tab'
        aria-controls='betlist'
        aria-selected='true'
        )
        | {{ $t("ui.bet_list") }}
    li.nav-item
      a#tab-betreject.nav-link(
        @click="getCancelledBetList()"
        data-toggle='tab'
        href='#betreject'
        role='tab'
        aria-controls='betreject'
        aria-selected='false'
        )
        | {{ $t("ui.bet_reject") }}
  .tab-content
    #betlist.tab-pane.show.active(role='tabpanel', aria-labelledby='tab-betlist')
      table.table-info(width='100%' id="statement-accordion")
        tbody(v-if="ploading")
          tr
            .empty.match-info.text-center.p-4
              i.fad.fa-spinner.fa-spin
        tbody(v-else)
          tr
            th.text-center(scope='col', width='5%') {{ $t("ui.no/") }}
            th.text-left(scope='col', width='20%') {{ $t("ui.trans_time") }}
            th.text-left(scope='col', width='22%') {{ $t("ui.event") }}
            th.text-left(scope='col', width='27%') {{ $t("ui.betting") }}
            th.text-right(scope='col', width='8%') {{ $t("ui.stake") }}
            th.text-right(scope='col', width='10%') {{ $t("ui.win") }} / {{ $t("ui.loss") }}
            th.text-left(scope='col', width='8%') {{ $t("ui.status") }}
          tr.grey(v-if="gameResultList.length == 0")
            td(colspan="7").text-center
              span {{ $t('message.no_information_available') }}
          tr(v-for="(item, index) in gameResultList" :class="{ grey: index % 2 === 0 }")
            td.text-center(valign='top') {{ ((currentPage - 1) * $store.getters.pageSize + index + 1) }}
            td.text-left(valign='top')
              div {{ $t("ui.ref_no") }}: {{ item.ticket_id }} / {{ item.result_id }}
              div {{ $dayjs(item.bet_time).format("MM/DD/YYYY hh:mm:ss A") }}
            td.text-left(valign='top')
              .bet-info
                .bet-type.blue {{ $t("m.D_" + item.bet_type) }}
                .bet-detail.blue
                  .name.red {{ item.comp_name }} - {{ item.num_type }}
                  .oddsdetail
                    .d-flex.justify-content-start
                      .selector-name {{ $dayjs(item.match_date).format("MM/DD/YYYY") }}
            td.text-left(valign='top')
              .bet-info
                .font-weight-bold {{ item.number }}
                .bet-detail.blue
                  .small(v-if="parseFloat(item.big) > 0")
                    span {{ item.num_type == "4D" ? $t("m.D_BIG") : "AE" }}
                    span  x {{ $numeral(item.big).format("0,0.00") }}
                  .small(v-if="parseFloat(item.small) > 0")
                    span {{ $t("m.D_SMALL") }}
                    span  x {{ $numeral(item.small).format("0,0.00") }}
                  .small(v-if="parseFloat(item.sa) > 0")
                    span {{ item.num_type == "4D" ? "SA" : "3A" }}
                    span  x {{ $numeral(item.sa).format("0,0.00") }}
                  .small(v-if="parseFloat(item.sb) > 0")
                    span B
                    span  x {{ $numeral(item.sb).format("0,0.00") }}
                  .small(v-if="parseFloat(item.sc) > 0")
                    span C
                    span  x {{ $numeral(item.sc).format("0,0.00") }}
                  .small(v-if="parseFloat(item.sd) > 0")
                    span D
                    span  x {{ $numeral(item.sd).format("0,0.00") }}
                  .small(v-if="parseFloat(item.se) > 0")
                    span E
                    span  x {{ $numeral(item.se).format("0,0.00") }}
                  .small(v-if="parseFloat(item.aa) > 0")
                    span AA
                    span  x {{ $numeral(item.aa).format("0,0.00") }}
                  .small(v-if="parseFloat(item.abc) > 0")
                    span 3C
                    span  x {{ $numeral(item.abc).format("0,0.00") }}
            td.text-right(valign='top')
              div {{ $numeral(item.bet_member).format("0,0.00") }}
            td.text-right(valign='top')
              div
                span(:class="{ red: parseFloat(item.winlose) < 0 }") {{ $numeral(item.winlose).format("0,0.00") }}
              div {{  $numeral(item.member_comm).format("0,0.00") }}
            td.text-left(valign='top')
              div {{ parseFloat(item.winlose) != 0 ? (parseFloat(item.winlose) < 0 ? $t("ui.lost") : $t("ui.won")) : $t("ui.draw") }}
      table.table-total(width='100%' v-if="isTotal")
        tbody
          tr
            td.text-right(width='81%') {{ $t("ui.subtotal") }} ({{ parseFloat(gameResultSummary.winlose) < 0 ? $t("ui.lost") : $t("ui.won") }})
            td.text-right(width='10%')
              span(
                :class="{ red: parseFloat(gameResultSummary.winlose) < 0 }"
                ) {{ $numeral(gameResultSummary.winlose).format("0,0.00") }}
            td.text-right(width='8%')  
          tr
            td.text-right {{ $t("ui.subtotal") }} ({{ $t("ui.commission") }})
            td.text-right {{ $numeral(gameResultSummary.member_comm).format("0,0.00") }}
            td  
          tr
            td.text-right {{ $t("ui.total") }}
            td.text-right
              span(
                :class="{ red: total < 0 }"
                ) {{ $numeral(total).format("0,0.00") }}
            td
      .mt-2
        v-pagination(
          v-model="currentPage"
          :page-count="gameResultTotalPages"
          :classes="bootstrapPaginationClasses"
          :labels="paginationAnchorTexts"
          @input="changedPage($event, 'game_result')"
          v-if="gameResultTotalPages"
        )

    #betreject.tab-pane(role='tabpanel', aria-labelledby='tab-betreject' )
      table.table-info(width='100%')
        tbody
          tr
            th.text-center(scope='col', width='5%') {{ $t("ui.no/") }}
            th.text-left(scope='col', width='20%') {{ $t("ui.trans_time") }}
            th.text-left(scope='col', width='22%') {{ $t("ui.event") }}
            th.text-left(scope='col', width='27%') {{ $t("ui.betting") }}
            th.text-right(scope='col', width='18%') {{ $t("ui.stake") }}
            th.text-left(scope='col', width='8%') {{ $t("ui.status") }}
          tr.grey(v-if="cancelledBetList == 'undefined' || cancelledBetList.length <= 0")
            td(colspan="6").text-center
              span {{ $t('message.no_information_available') }}
          tr(v-for="(item, index) in cancelledBetList" :class="{ grey: index % 2 === 0 }")
            td.text-center(valign='top') {{ ((currentPage2 - 1) * $store.getters.pageSize + index + 1) }}
            td.text-left(valign='top')
              div {{ $t("ui.ref_no") }}: {{ item.ticket_id }} / {{ item.bet_id }}
              div {{ $dayjs(item.bet_time).format("MM/DD/YYYY hh:mm:ss A") }}
            td.text-left(valign='top')
              .bet-info
                .bet-type.blue {{ $t("m.D_" + item.bet_type) }}
                .bet-detail.blue
                  .name.red {{ item.comp_name }} - {{ item.num_type }}
                  .oddsdetail
                    .d-flex.justify-content-start
                      .selector-name {{ $dayjs(item.match_date).format("MM/DD/YYYY") }}
            td.text-left(valign='top')
              .bet-info
                .font-weight-bold {{ item.number }}
                .bet-detail.blue
                  .small(v-if="parseFloat(item.big) > 0")
                    span {{ item.num_type == "4D" ? $t("m.D_BIG") : "AE" }}
                    span  x {{ $numeral(item.big).format("0,0.00") }}
                  .small(v-if="parseFloat(item.small) > 0")
                    span {{ $t("m.D_SMALL") }}
                    span  x {{ $numeral(item.small).format("0,0.00") }}
                  .small(v-if="parseFloat(item.sa) > 0")
                    span {{ item.num_type == "4D" ? "SA" : "3A" }}
                    span  x {{ $numeral(item.sa).format("0,0.00") }}
                  .small(v-if="parseFloat(item.sb) > 0")
                    span B
                    span  x {{ $numeral(item.sb).format("0,0.00") }}
                  .small(v-if="parseFloat(item.sc) > 0")
                    span C
                    span  x {{ $numeral(item.sc).format("0,0.00") }}
                  .small(v-if="parseFloat(item.sd) > 0")
                    span D
                    span  x {{ $numeral(item.sd).format("0,0.00") }}
                  .small(v-if="parseFloat(item.se) > 0")
                    span E
                    span  x {{ $numeral(item.se).format("0,0.00") }}
                  .small(v-if="parseFloat(item.aa) > 0")
                    span AA
                    span  x {{ $numeral(item.aa).format("0,0.00") }}
                  .small(v-if="parseFloat(item.abc) > 0")
                    span 3C
                    span  x {{ $numeral(item.abc).format("0,0.00") }}
            td.text-right(valign='top')
              | {{ $numeral(item.bet_member).format("0,0.00") }}
            td.text-left(valign='top')
              div {{ (item.bet_status_code == 1) ? $t('ui.reject') : $t('ui.void') }}
      .mt-2
        v-pagination(
          v-model="currentPage2"
          :page-count="cancelledBetTotalPage"
          :classes="bootstrapPaginationClasses"
          :labels="paginationAnchorTexts"
          @input="changedPage($event, 'cancelled_bet')"
          v-if="cancelledBetTotalPage"
        )
</template>
<script>
import vPagination from "vue-plain-pagination";
import config from "@/config";
import naming from "@/library/_name";
import service from "@/library/_xhr-statement";

export default {
  components: { vPagination },
  props: {
    gameResultList: {
      type: Array,
      default: []
    },
    gameResultSummary: {
      type: Object,
      default: {}
    },
    gameResultTotalPages: {
      type: Number
    },
    ploading: {
      type: Boolean,
      default: false
    },
    cancelledBetList: {
      type: Array,
      default: []
    },
    cancelledBetTotalPage: {
      type: Number
    }
  },
  data() {
    return {
      loading: false,
      parlayItems: [],
      selectedBetType: "",
      selectedMatch: 0,
      currentPage: 1,
      currentPage2: 1,
      bootstrapPaginationClasses: {
        ul: "pagination justify-content-center",
        li: "page-item",
        liActive: "active",
        liDisable: "disabled",
        button: "page-link",
        buttonActive: "active",
        buttonDisable: "disable"
      },
      paginationAnchorTexts: {
        first: "<i class='fas fa-angle-double-left'></i>",
        prev: "<i class='fas fa-angle-left'></i>",
        next: "<i class='fas fa-angle-right'></i>",
        last: "<i class='fas fa-angle-double-right'></i>"
      }
    };
  },
  computed: {
    language() {
      return this.$store.getters.language;
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
    isTotal() {
      if (this.gameResultSummary != null) {
        return Object.keys(this.gameResultSummary).length > 0;
      } else {
        return false;
      }
    },
    total() {
      if (this.gameResultSummary != null) {
        return parseFloat(this.gameResultSummary.winlose) + parseFloat(this.gameResultSummary.member_comm);
      } else {
        return 0;
      }
    }
  },
  mounted() {
    this.currentPage = this.currentGameResultPage;
    this.changedPage(1, "game_result");
  },
  methods: {
    getCancelledBetList() {
      this.$emit("getCancelledBet", "lottery");
    },
    changedPage(pageNo, type) {
      if (type == "game_result") this.currentPage = pageNo;
      else this.currentPage2 = pageNo;
      this.$emit("changedPage", pageNo, type);
    }
  }
};
</script>
