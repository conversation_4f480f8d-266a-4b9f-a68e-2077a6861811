export default {
	validateWord(evt) {
		var elem = evt.target;
		var currentValue = $(elem).val();
		if (currentValue == "") {
			return true;
		}
		if (!currentValue.match(/^\d+(\.\d+)?$/)) {
			return false;
		}
		return true;
	},
	validate(evt) {
		evt = evt ? evt : window.event;
		// var charCode = evt.which ? evt.which : evt.keyCode;
		// console.log(charCode);
		// if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
		//   evt.preventDefault();
		// } else {
		//   return true;
		// }

		var allowFloat = false;
		var allowNegative = false;
		var inputCode = evt.which ? evt.which : evt.keyCode;
		// var elem = this.$refs.stake;
		var elem = evt.target;
		var currentValue = $(elem).val();
		// console.log(currentValue);

		if (inputCode > 0 && (inputCode < 48 || inputCode > 57)) {
			// Checks the if the character code is not a digit
			if (allowFloat == true && inputCode == 46) {
				// Conditions for a period (decimal point)
				//Disallows a period before a negative
				if (allowNegative == true && getCaret(this) == 0 && currentValue.charAt(0) == "-") evt.preventDefault();

				//Disallows more than one decimal point.
				if (currentValue.match(/[.]/)) evt.preventDefault();
			} else if (allowNegative == true && inputCode == 45) {
				// Conditions for a decimal point
				if (currentValue.charAt(0) == "-") evt.preventDefault();

				if (getCaret(elem) != 0) evt.preventDefault();
			} else if (inputCode == 8 || inputCode == 67 || inputCode == 86)
				// Allows backspace , ctrl+c ,ctrl+v (copy & paste)
				return true;
			// Disallow non-numeric
			else evt.preventDefault();
		} else if (inputCode > 0 && (inputCode >= 48 && inputCode <= 57)) {
			// Disallows numbers before a negative.
			if (allowNegative == true && currentValue.charAt(0) == "-" && getCaret(this) == 0) evt.preventDefault();
		}
	}
};