<template lang="pug">
  .hx-table.hx-top-rounded.hx-mt-2(:class="source.marketId == 3 ? 'live' : 'non-live'")
    .hx-cell.flex-fill
      .hx-row.h-100
        .d-flex.flex-row.align-items-center.h-100
          .pl-1
            img(:src="'/v1/images/icon-sport-svg/' + getImage(source.sportsId)" width="22")
          .d-flex.flex-row.align-items-baseline.align-item-margin
            .hx-header-title(:class="{ 'live' : source.marketId == 3 }") {{ source.marketType }}
            .hx-header-subtitle {{ source.sportsType }}
    .hx-cell.w-549
      .hx-row.h-100
        .hx-col.w-61.h-46(v-for="item in htft")
          .hx.text-center.h-100.hx-flex-c {{ $t("m.BT_" + item) }}
    .hx-cell.w-40
      .hx-row
</template>

<script>
import config from "@/config";
export default {
  props: {
    source: {
      type: Object
    },
  },
  data() {
    return {
      htft: ['HH', 'HD', 'HA', 'DH', 'DD', 'DA', 'AH', 'AD', 'AA']
    }
  },
  methods: {
    getImage(e) {
      return config.getSportsImage(e);
    }
  }
}
</script>