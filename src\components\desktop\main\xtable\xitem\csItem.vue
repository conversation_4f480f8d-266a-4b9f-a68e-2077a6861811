<template lang="pug">
  .hx.h-100.hx-flex-c
    .hxs.w-100(v-if="details[betType] != null && details[betType][item] != null && details[betType][item][6] != null && details[betType][item][6] != '' && details[betType][item][5] != '' && details[betType][item][5] != 0")
      oddsItem(:odds="details[betType][item]" idx=5 :typ="oddsType" dataType="3")
    .hxs.text-center.w-100(v-else) -
</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";

export default {
  components: {
    oddsItem
  },
  props: {
    details: {
      type: Object
    },
    oddsType: {
      type: String
    },
    item: {
      type: String
    },
    i: {
      type: String
    },
    betType: {
      type: String
    }
  },
  // updated: function() {
  //   this.$nextTick(function() {
  //     console.log("cs", new Date(), this.oddsType, this.item);
  //   });
  // }
};
</script>
