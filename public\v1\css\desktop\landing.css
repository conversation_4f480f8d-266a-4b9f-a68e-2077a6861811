.video-wrap {
  text-align: center;
}
.video-wrap {
  margin-bottom: 50px;
}
.video-wrap iframe {
  width: 620px;
  height: 350px;
}
#landing {
  position: relative;
}
/* .landing {
	position: relative;
	overflow-x: hidden;
	font-family: "La<PERSON>", "<PERSON>homa", sans-serif;
	height: auto;
	width: 100%;
} */

body.landing {
  background: rgb(2, 22, 40);
}
.landing {
  position: relative;
  /* overflow-x: hidden; */
  font-family: "Lato", "Tahoma", sans-serif;
  height: auto;
  width: 100%;
}
.landing .resource {
  display: none;
  width: 0;
  height: 0;
}
.landing.white-label {
  height: 100%;
  width: 100%;
  z-index: 0;
}
.landing .preloader .logo {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
.landing .preloader .logo img {
  width: 100px;
  opacity: 0.8;
  display: block;
}
.landing .preloader .logo span {
  color: #ffffffcc;
  display: block;
  font-size: 14px;
  line-height: 18px;
}
.landing .top-wrapper {
  position: absolute;
  width: 100%;
  height: 70px;
  z-index: 100;
  transition: all 0.3s ease-in-out;
}
.landing .top {
  padding: 30px 0px;
  position: absolute;
  width: 85%;
  max-width: 1600px;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  z-index: 100;
  top: 0;
  display: flex;
  justify-content: space-between;
}
.landing .top .login-wrapper {
  padding: 10px;
  background-color: #0c4780;
  height: 60px;
  border-radius: 5px;
  display: none;
}
.landing .top .input-group-text {
  background-color: #fff;
  border: none;
}
.landing .top .form-control {
  border: none;
  /* padding-left: 4px; */
  background-color: #fff !important;
  /* font-family: Lato, Tahoma, sans-serif; */
  height: 100%;
  font: 400 13.3333px Arial;
}
.landing .top .login-field {
  margin: 0px 5px;
  max-width: 200px;
  height: 38px;
}
.landing .top .login-field .input-group {
  height: 100%;
}
.landing .top .input-group-text {
  font-size: 0.8rem;
  color: #8397ab;
}
.landing .top .input-group > .form-control:not(:last-child),
.input-group > .custom-select:not(:last-child) {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.landing .top-wrapper .top .invalid-feedback {
  font-size: 10px;
  margin-top: 0px;
  color: #ffffff;
  position: absolute;
  bottom: -12px;
  background-color: #b10600;
  padding: 0px 10px;
  z-index: 100;
  text-align: center;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.lang-dropdown-wrap-blue {
  display: none;
  width: 150px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  border: 1px solid rgba(0, 0, 0, 0.15);
  padding: 5px 10px;
  border-radius: 0.25rem;
  background-color: #03325f;
  position: absolute;
  top: 60px;
  right: -10px;
  box-sizing: border-box;
  z-index: 10;
}

.lang-wrap #lang-dropdown-list li span {
  margin-left: 5px;
}

.lang-wrap #lang-dropdown-list li a {
  width: 100%;
  color: #fff;
  text-decoration: none;
  white-space: nowrap;
  font-size: 13px;
  min-width: 148px;
  text-overflow: ellipsis;
  overflow: hidden;
}

@media (min-width: 960px) {
  .landing .top {
    width: 100%;
  }
  .landing .top-wrapper.small-top .top {
    padding: 5px 50px;
    align-items: center;
  }
}
@media (min-width: 768px) {
  .landing .top {
    width: 90%;
  }
  .landing .top-wrapper.small-top {
    background-color: #0c4780;
    position: fixed;
  }
  .landing .top-wrapper.small-top .login-wrapper {
    background-color: transparent;
  }
  .landing .top-wrapper.small-top .top {
    padding: 5px 0px;
    align-items: center;
  }
  .landing .top-wrapper.small-top .top .logo img {
    max-width: 150px;
  }
  .landing .top .login-wrapper {
    display: flex;
  }
}

.landing .top.fixed-top {
  position: fixed;
}
.landing .logo {
  float: left;
  width: 25%;
  min-width: 150px;
}
.landing .logo img {
  width: 100%;
  max-width: 280px;
}
.landing .login-btn {
  font-family: "Lato", sans-serif;
  float: right;
  color: #042554;
  border: none;
  font-size: 14px;
  padding: 5px;
  border-radius: 0.25rem;
  font-weight: bolder;
  text-transform: uppercase;
  border: 2px solid #f0c103;
  background-color: #f0c103;
  background-image: -o-linear-gradient(45deg, #f0da03 50%, transparent 50%);
  background-image: linear-gradient(45deg, #f0da03 50%, transparent 50%);
  background-position: 100%;
  background-size: 400%;
  -webkit-transition: background 300ms ease-in-out;
  -o-transition: background 300ms ease-in-out;
  transition: background 300ms ease-in-out;
  -webkit-box-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 1px 1px 3px rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 1px 1px 3px rgba(0, 0, 0, 0.5);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3), -1px -1px 2px rgba(255, 255, 255, 0.3);
  line-height: 20px;
  letter-spacing: 1px;
  height: 38px;
  width: 120px;
  overflow: hidden;
  white-space: nowrap;
  text-align: center;
  text-overflow: ellipsis;
}
.landing .join-btn {
  font-family: "Lato", sans-serif;
  color: #2f2f2f;
  border: 2px solid #f0c103;
  background-color: #f0c103;
  background-image: -o-linear-gradient(45deg, #f0da03 50%, transparent 50%);
  background-image: linear-gradient(45deg, #f0da03 50%, transparent 50%);
  background-position: 100%;
  background-size: 400%;
  -webkit-transition: background 300ms ease-in-out;
  -o-transition: background 300ms ease-in-out;
  transition: background 300ms ease-in-out;
  padding: 10px 25px;
  border-radius: 5px;
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.9em;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}
.landing .login-btn:hover,
.landing .join-btn:hover {
  border: 2px solid #f0da03;
  background-position: 0;
}
.slider-wrap {
  position: relative;
}
.bg-landing {
  margin: 0 auto;
  /* background-image: url(https://r.myw0011001.com/images/landing2/slider_bg.jpg); */
  background-size: cover;
  position: relative;
  background-repeat: no-repeat;
  background-position: top center;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.fadeInSlider {
  animation: fadeInSlider 1s alternate forwards;
  -webkit-animation: fadeInSlider 1s alternate forwards;
}
@keyframes fadeInSlider {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-webkit-keyframes fadeInSlider {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.carousel-inner {
  z-index: 10;
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0px 30px;
}
.carousel-item {
  text-align: center;
  padding-bottom: 90px;
  padding-top: 80px;
}
.carousel-item.sport {
}
.carousel-item .banner-text {
  color: #fff;
  display: block;
  text-align: center;
  width: 100%;
  position: absolute;
  left: 0;
  top: 80%;
  text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.9);
  padding: 0px;
}
.carousel-item .banner-text h1 {
  font-size: 1.1em;
  text-transform: uppercase;
  font-family: "Oswald", sans-serif;
}
.carousel-item .banner-text p {
  display: none;
}
.carousel-item .banner-image {
  color: #fff;
  display: block;
  width: 100%;
  position: relative;
  margin: 0 auto;
}
.carousel-item .banner-image .animate-image {
  position: absolute;
  right: 0;
  top: 0;
  opacity: 0;
}
.carousel-item.active .banner-image .animate-image {
  -webkit-animation: slideUp 1s alternate forwards;
  animation: slideUp 1s alternate forwards;
}
.carousel-control-prev,
.carousel-control-next {
  z-index: 10;
}
@-webkit-keyframes slideUp {
  0% {
    top: 50px;
    opacity: 0;
  }
  100% {
    top: 0px;
    opacity: 1;
  }
}
@keyframes slideUp {
  0% {
    top: 50px;
    opacity: 0;
  }
  100% {
    top: 0px;
    opacity: 1;
  }
}
#main-carousel .carousel-inner .carousel-item:after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 400px;
}
#main-carousel .carousel-indicators {
  top: 700px;
  bottom: initial;
  display: none;
}
#main-carousel .carousel-indicators li {
  width: 15px;
  height: 15px;
  border-radius: 50%;
}
.lobby-wrap {
  width: 100%;
  margin: 0 auto;
  min-height: 500px;
  position: relative;
  top: -150px;
  margin-bottom: -150px;
}
.lobby-wrap:before {
  background-image: url(https://r.myw0011001.com/images/landing2/line1.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: bottom;
  width: 100%;
  height: 150px;
  position: absolute;
  top: -140px;
  content: "";
}
.inner-wrap {
  background-color: #031b32;
  background-image: url(https://r.myw0011001.com/images/landing2/lobby_bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
  text-align: center;
  color: #fff;
}
.lobbyinner {
  max-width: 900px;
  width: 90%;
  margin: 0 auto;
  padding-top: 150px;
  padding-bottom: 50px;
}
.lobbyinner .lobby-item {
  background-color: transparent;
  width: 100%;
  height: 200px;
  -webkit-perspective: 1000px;
  perspective: 1000px;
  margin: 15px auto;
}
.lobbyinner .lobby-item .card-flip {
  position: relative;
  -webkit-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.45);
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.45);
  width: 100%;
  height: 100%;
  text-align: center;
  -webkit-transition: -webkit-transform 0.4s;
  transition: -webkit-transform 0.4s;
  -o-transition: transform 0.4s;
  transition: transform 0.4s;
  transition: transform 0.4s, -webkit-transform 0.4s;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  border: none;
  border-radius: 10px;
}
.lobbyinner .lobby-item .front,
.lobbyinner .lobby-item .back {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  overflow: hidden;
  border-radius: 8px;
}
.lobbyinner .lobby-item .front {
  background-color: rgba(15, 79, 140, 1);
}
.lobbyinner .lobby-item .front .image-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 8px;
}
.lobbyinner .lobby-item .front .image-wrap img {
  position: relative;
  width: 150%;
  left: -25%;
  right: -25%;
  top: 50%;
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}
.lobbyinner .lobby-item .front .info {
  position: absolute;
  width: 110%;
  left: -5%;
  right: -5%;
  bottom: 0px;
  padding: 10px;
  color: #fff;
  text-align: center;
  background-color: rgba(15, 79, 140, 1);
}
.lobbyinner .lobby-item .front .info h1 {
  color: #ffffff;
  font-weight: bold;
  font-size: 1em;
  margin: 0px;
  text-shadow: 1px 1px 1px #000;
  font-family: "Oswald", sans-serif;
}
.lobbyinner .lobby-item .back {
  -webkit-transform: rotateY(180deg);
  transform: rotateY(180deg);
  background: #f0f3f5;
  color: #000;
  padding: 5px;
  display: none;
}
.lobbyinner .lobby-item .back h3 {
  font-size: 1em;
  color: #0f4f8c;
  font-weight: 700;
  margin: 5px auto;
  font-family: "Oswald", sans-serif;
}
.lobbyinner .lobby-item .back p {
  font-size: 0.7em;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.lobbyinner .lobby-item .back .card-footer {
  position: absolute;
  bottom: 0px;
  padding: 0px;
  width: 100%;
  left: 50%;
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
}
.lobbyinner .lobby-item .back .btn {
  background-color: #0f4f8c;
  color: #fff;
  padding: 7px 15px;
  border: none;
  border-radius: 5px;
  margin: 10px;
  font-weight: 700;
  position: relative;
  font-size: 0.8em;
}
.lobbyinner .lobby-item .back .btn:hover {
  opacity: 0.8;
}
.join-us {
  max-width: 1000px;
  margin: 0 auto;
  padding-bottom: 150px;
}
.join-us h1 {
  font-size: 1.2em;
}
.join-us p {
  font-size: 0.9em;
}
.feature-wrap {
  background-color: #d8e2e8;
  position: relative;
  padding: 0px 0px 50px 0px;
}
.feature-wrap:before {
  display: block;
  content: "";
  background-image: url(https://r.myw0011001.com/images/landing2/line2.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: bottom;
  position: absolute;
  width: 120%;
  height: 150px;
  top: -140px;
}
.feature-wrap:after {
  display: block;
  content: "";
  background-image: url(https://r.myw0011001.com/images/landing2/line2.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: bottom;
  position: absolute;
  width: 120%;
  height: 150px;
  bottom: -140px;
  z-index: 1;
  -ms-transform: scaleY(-1);
  transform: scaleY(-1);
  -webkit-transform: scaleY(-1);
}
.featureinner {
  max-width: 1340px;
  width: 90%;
  margin: 0 auto;
}
.feature-item {
  margin: 30px 0px;
}
.feature-item .icon {
  width: 60px;
  display: inline-block;
  vertical-align: middle;
}
.feature-item .icon img {
  width: 90%;
}
.feature-item .feature-content {
  width: calc(100% - 70px);
  padding-left: 15px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
}
.feature-item .feature-content h1 {
  font-size: 0.9em;
  margin: 0;
}
.feature-item .feature-content p {
  font-size: 0.7em;
  margin: 0;
  line-height: normal;
  color: #000;
}
.landing .footer {
  text-align: center;
  background-color: rgb(2, 22, 40);
  padding: 15px;
  font-size: 0.8rem;
  color: #fff;
  position: relative;
  padding-top: 150px;
}
.landing .footer .license,
.landing .footer .tnc {
  text-align: center;
  max-width: 1340px;
  margin: 15px auto;
}
.landing .footer .tnc {
  padding: 20px 18px;
  border-top: 1px rgba(255, 255, 255, 0.2) solid;
}
.landing .license .image-wrap {
  display: inline-block;
  width: 100px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px;
  border: 1px rgba(255, 255, 255, 0.2) solid;
  margin: 5px;
}
.landing .license .image-wrap img {
  width: 80%;
}
.landing .license .image-wrap .col-2 {
  padding: 0px;
}
.landing .footer .tnc .icon {
  width: 100%;
  display: inline-block;
  vertical-align: top;
  text-align: left;
  margin-bottom: 10px;
}
.landing .footer .tnc .icon img {
  width: 100%;
  max-width: 50px;
}
.landing .footer .tnc .tnc-content {
  width: 100%;
  display: inline-block;
  text-align: left;
  vertical-align: top;
}
.landing .footer .tnc .tnc-content h4 {
  font-size: 1.2em;
  color: #ff583e;
}
.deco1 {
  position: absolute;
  width: 200px;
  top: -80px;
  right: -100px;
  display: none;
}
.deco2 {
  position: absolute;
  width: 400px;
  bottom: -100px;
  left: -100px;
  z-index: 10;
  display: none;
}
.deco1 img,
.deco2 img {
  width: 100%;
}
.desktop {
  display: none;
}
.mobile {
  display: block;
}
.login-modal {
  font-size: 16px;
  padding-right: 0 !important;
}
body {
  padding-right: 0 !important;
}
.login-modal.modal .modal-dialog {
  top: 50%;
  -ms-transform: translateY(-60%);
  transform: translateY(-60%);
  -webkit-transform: translateY(-60%);
}
.login-modal .modal-content {
  -webkit-box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.5);
  box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.5);
  font-family: "Lato", sans-serif;
  font-family: 500;
}
.login-modal .banner {
  width: 100%;
  height: auto;
  background-color: #ffcd00;
  border-radius: 0.3rem;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  position: relative;
}
.login-modal .banner .text {
  text-align: center;
  padding: 10px;
}
.login-modal .banner .text h1 {
  font-size: 1em;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 0px;
  font-family: "Oswald", sans-serif;
}
.login-modal .banner .text p {
  display: none;
  font-size: 14px;
}
#login-modal.login-modal .modal-body {
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 30px 20px;
  background-color: #fff;
  border-radius: 0.3rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.login-modal .modal-body .form-group {
  position: absolute;
  top: 50%;
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}
.login-modal .modal-body .login-field {
  margin-bottom: 15px;
}
.login-modal .modal-body .login-field label {
  display: none;
}
.login-modal .modal-body .login-field input {
  font-size: 16px;
}
.login-modal .modal-body .login-field .input-group > .form-control:not(:last-child) {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.login-modal .modal-body .login-btn {
  background-color: #0f4f8c;
  font-size: 16px;
  padding: 7px 2px;
  color: #fff;
  border: 2px #0f4f8c solid;
  background-image: -o-linear-gradient(45deg, #093968 50%, transparent 50%);
  background-image: linear-gradient(45deg, #093968 50%, transparent 50%);
  background-position: 100%;
  background-size: 400%;
  -webkit-transition: background 300ms ease-in-out;
  -o-transition: background 300ms ease-in-out;
  transition: background 300ms ease-in-out;
  float: none;
  width: 120px;
}
.login-modal .modal-body .login-btn:hover {
  border: 2px #0f4f8c solid;
  background-position: 0;
}
.langpicker {
  display: none;
}
#lang-dropdown-list {
  padding: 5px;
  margin-bottom: 0px;
}
#lang-dropdown-list img,
.btn-select img {
  width: 26px;
  padding: 0;
}
#lang-dropdown-list li {
  list-style: none;
  padding-top: 5px;
  padding-bottom: 5px;
  cursor: pointer;
}
#lang-dropdown-list li span,
.btn-select li span {
  margin-left: 15px;
}
.lang-dropdown-wrap {
  display: none;
  width: 100%;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  border: 1px solid rgba(0, 0, 0, 0.15);
  padding: 5px 10px;
  border-radius: 5px;
  background-color: #fff;
  position: absolute;
  top: 50px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 10;
}
.open {
  display: show !important;
}
.btn-select {
  /* display: inline-block;
  border-radius: 0.25rem;
  border: 1px solid #ced4da;
  background-color: #fff;
  padding: 0 10px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #000;
  width: 100%;
  height: 40px;
  line-height: 1;
  position: relative;
  font-size: 16px;
  cursor: pointer; */
  display: inline-block;
  border-radius: 100%;
  border: 1px solid #3b6996;
  background-color: #03325f;
  padding: 5px;
  box-sizing: border-box;
  color: #000;
  width: 40px;
  height: 40px;
  line-height: 40px;
  position: relative;
  font-size: 16px;
  margin-left: 5px;
  cursor: pointer;
}
.lang-select {
  width: 100%;
  position: relative;
  height: 40px;
}
.btn-select:after {
  /* font-family: "Font Awesome 5 Pro";
  font-weight: 900;
  content: "\f078";
  right: 10px;
  position: absolute;
  top: 50%;
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  color: rgba(0, 0, 0, 0.5);
  font-size: 16px; */
  /* content: "";
  width: 10px;
  height: 10px;
  display: block;
  background-image: url(/images/caret-down-fill.svg);
  background-size: 100%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 5px; */
}
.btn-select li {
  /* list-style: none;
  float: left;
  padding-bottom: 0px;
  font-size: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100%; */
  list-style: none;
  float: left;
  padding-bottom: 0px;
  font-size: 16px;
}
.btn-select img {
  width: 36px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
@media (min-width: 375px) {
  .carousel-item .banner-text {
    top: 85%;
  }
  .lobbyinner {
    max-width: 100%;
  }
  .lobbyinner .lobby-item {
    height: 250px;
  }
  .lobbyinner .lobby-item .front .image-wrap img {
    width: 130%;
    left: -15%;
    right: -15%;
  }
  .lobbyinner {
    max-width: 1000px;
  }
}
@media (min-width: 576px) {
  .landing {
    font-size: 18px;
  }
  .carousel-item .banner-image {
    max-width: 450px;
  }
  .carousel-item .banner-text h1 {
    font-size: 1.3em;
  }
  .lobbyinner .lobby-item .front .image-wrap img {
    width: 130%;
    left: -15%;
    right: -15%;
    -ms-transform: translateY(-40%);
    transform: translateY(-40%);
    -webkit-transform: translateY(-40%);
  }
  .lobbyinner {
    max-width: 500px;
  }
}
@media (min-width: 768px) {
  .desktop {
    display: block;
  }
  .mobile {
    display: none;
  }
  .landing {
    font-size: 20px;
  }
  .carousel-inner {
    width: 100%;
    height: 750px;
  }
  .landing .carousel-item {
    height: 750px;
  }
  .carousel-item .banner-text {
    padding: 0px 30px;
  }
  .carousel-item .banner-text h1 {
    font-size: 1.4em;
  }
  .carousel-item .banner-text p {
    display: block;
  }
  .carousel-item .banner-image {
    max-width: 550px;
  }
  .lobby-wrap {
    top: -300px;
    margin-bottom: -300px;
  }
  .lobbyinner {
    max-width: 700px;
    padding-top: 300px;
  }
  .lobbyinner .lobby-item {
    height: 300px;
  }
  .lobbyinner .lobby-item.sport {
    -ms-transform: scale(1.1);
    transform: scale(1.1);
    -webkit-transform: scale(1.1);
    -webkit-font-smoothing: subpixel-antialiased;
  }
  .lobbyinner .lobby-item .front .image-wrap img {
    width: 120%;
    left: -10%;
    right: -10%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
  }
  .landing .join-btn {
    font-size: 0.7em;
    padding: 15px 30px;
  }
  .login-modal .modal-content {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    flex-direction: row;
    -ms-flex-direction: row;
  }
  .login-modal .banner {
    width: 35%;
    height: 450px;
    border-radius: 0.3rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .login-modal .banner .text {
    position: absolute;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    text-align: right;
    padding: 20px;
  }
  .login-modal .banner .text h1 {
    font-size: 1.5em;
  }
  .login-modal .banner .text p {
    display: block;
  }
  #login-modal.login-modal .modal-body {
    width: 65%;
    padding: 60px 20px;
  }
  .login-modal .modal-body .login-field label {
    display: block;
  }
}
@media (min-width: 960px) {
  .landing {
    font-size: 22px;
  }
  .carousel-item .banner-image {
    max-width: 600px;
  }
  .lobbyinner {
    max-width: 900px;
  }
  .lobby-wrap {
    top: -300px;
    margin-bottom: -300px;
  }
  .lobby-wrap:before {
    height: 200px;
    top: -190px;
  }
  .lobbyinner {
    padding-top: 320px;
    width: 80%;
  }
  .lobbyinner .lobby-item {
    height: 400px;
  }
  .lobbyinner .lobby-item .front .info {
    bottom: 0px;
    font-size: 1em;
    padding: 15px;
  }
  .lobbyinner .lobby-item .back {
    padding: 30px;
  }
  .lobbyinner .lobby-item .back h3 {
    margin: 15px auto;
  }
  .feature-wrap {
    padding: 50px 0px 100px 0px;
  }
  .feature-item .icon {
    width: 15%;
  }
  .feature-item .feature-content {
    width: calc(100% - 20%);
  }
  .landing .footer .tnc .tnc-content {
    width: calc(100% - 60px);
    padding-left: 10px;
  }
  .landing .footer .tnc .icon {
    width: 50px;
  }
}
@media (min-width: 1024px) {
  .landing .top {
    padding: 30px 50px;
  }
  .landing .login-btn {
    /* padding: 7px 30px; */
  }
  .carousel-item .banner-text {
    top: 90%;
  }
  .carousel-inner {
    height: 750px;
  }
  .landing .carousel-item {
    height: 750px;
  }
}
@media (min-width: 1340px) {
  .carousel-inner {
    width: 85%;
  }
  .carousel-item {
    padding-top: 50px;
  }
  .carousel-item .banner-text {
    width: 50%;
    display: inline-block;
    text-align: left;
    top: 50%;
    left: 0px;
    right: auto;
    bottom: auto;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
  }
  .carousel-item .banner-text h1 {
    font-size: 1.5em;
  }
  .carousel-item .banner-image {
    width: 52%;
    max-width: 100%;
    position: relative;
    float: right;
    display: inline-block;
  }
  .lobby-wrap {
    top: -180px;
    margin-bottom: -180px;
  }
  .lobby-wrap:before {
    height: 280px;
    top: -270px;
  }
  .lobbyinner {
    max-width: 1000px;
    padding-top: 30px;
  }
  .lobbyinner .lobby-item {
    height: 500px;
  }
  .lobbyinner .lobby-item:hover .card-flip {
    -webkit-transform: rotateY(180deg);
    transform: rotateY(180deg);
  }
  .lobbyinner .lobby-item .back {
    display: block;
  }
  .lobbyinner .lobby-item .back .btn {
    padding: 10px 30px;
  }
  .modal-dialog {
    max-width: 600px;
  }
}
@media (min-width: 1460px) {
  .landing {
    font-size: 24px;
  }
  .carousel-item .banner-text h1 {
    font-size: 1.8em;
  }
  .lobby-wrap {
    top: -50px;
    margin-bottom: -50px;
  }
}
@media (min-width: 1920px) {
  .deco1,
  .deco2 {
    display: block;
  }
}
@-webkit-keyframes fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.fadeIn {
  -webkit-animation: fade 1s alternate;
  animation: fade 1s alternate;
}