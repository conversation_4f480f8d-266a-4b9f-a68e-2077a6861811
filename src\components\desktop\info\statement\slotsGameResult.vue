<template lang="pug">
div
  table.table-info(width='100%')
    tbody
      tr
        th.text-center(scope='col', width='6%') {{ $t("ui.no/") }}
        th.text-left(scope='col', width='20%') {{ $t("ui.trans_time") }}
        th.text-left(scope='col', width='30%') {{ $t("ui.event") }}
        th.text-right(scope='col', width='10%') {{ $t("ui.stake") }}
        th.text-right(scope='col', width='12%') {{ $t("ui.win") }} / {{ $t("ui.loss") }}
        th(scope='col', width='12%') {{ $t("ui.status") }}
      tr.grey(v-if="gameResultList.length == 0")
        td(colspan="7").text-center
          span {{ $t('message.no_information_available') }}
      tr(v-for="(item, index) in gameResultList" :class="{ grey: index % 2 === 0 }")
        td.text-center(valign='top') {{index + 1}}
        td.text-left(valign='top')
          div {{ $t("ui.ref_no") }}: {{ item.result_id }}
          div {{ $dayjs(item.bet_time).format("MM/DD/YYYY hh:mm:ss A") }}
        td.text-left(valign='top')
          .bet-info
            .bet-type.blue {{ item.game_name }}
            .bet-detail
              .name {{ item.bet_id }}
        td.text-right(valign='top') {{ $numeral(item.turnover).format("0,0.00") }}
        td.text-right(valign='top')
          div
            span(
              :class="{ red: parseFloat(item.winlose) < 0 }"
              ) {{ $numeral(item.winlose).format("0,0.00") }}
          div {{ item.comm ? parseFloat(item.comm).toFixed(2) : '0.00' }}
        td.text-left(valign='top')
          div {{ parseFloat(item.winlose) != 0 ? (parseFloat(item.winlose) < 0 ? $t("ui.lost") : $t("ui.won")) : $t("ui.draw") }}
          template(v-if="item.game_provider == 'AR'")
            a.icon-info(v-if="!loading.arcardia" href="javascript:void(0);" title='Result' @click="getResultAR(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'PGS'")
            a.icon-info(v-if="!loading.pgsoft" href="javascript:void(0);" title='Result' @click="getResultPGS(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'PP'")
            a.icon-info(v-if="!loading.pragmatic" href="javascript:void(0);" title='Result' @click="getResultPragmatic(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'JK'")
            a.icon-info(v-if="!loading.joker" href="javascript:void(0);" title='Result' @click="getResultJoker(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'SP'")
            a.icon-info(v-if="!loading.spp" href="javascript:void(0);" title='Result' @click="getResultSimplePlay(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'JILI'")
            a.icon-info(v-if="!loading.jili" href="javascript:void(0);" title='Result' @click="getResultJili(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'UU'")
            a.icon-info(v-if="!loading.uusl" href="javascript:void(0);" title='Result' @click="getResultUusl(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'NEXT'")
            a.icon-info(v-if="!loading.uusl" href="javascript:void(0);" title='Result' @click="getResultNextSpin(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'WOW'")
            a.icon-info(v-if="!loading.wow" href="javascript:void(0);" title='Result' @click="getResultWowGaming(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'DB'")
            a.icon-info(v-if="!loading.uusl" href="javascript:void(0);" title='Result' @click="getResultDbHashSpin(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'OSG'")
            a.icon-info(v-if="!loading.wow" href="javascript:void(0);" title='Result' @click="getResultLive22Gaming(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'WF'")
            a.icon-info(v-if="!loading.wow" href="javascript:void(0);" title='Result' @click="getResultWfGaming(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'EPW'")
            a.icon-info(v-if="!loading.wow" href="javascript:void(0);" title='Result' @click="getResultEpwGaming(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

          template(v-if="item.game_provider == 'BI'")
            a.icon-info(v-if="!loading.aio" href="javascript:void(0);" title='Result' @click="getResultAioGaming(item.bet_id)")
              i.fad.fa-poll
            a.icon-info(v-else href="javascript:void(0);")
              i.fa.fa-spin.fa-spinner

  table.table-total(width='100%' v-if="isTotal")
    tbody
      tr
        td.text-right(valign='top' width='66%') {{ $t("ui.subtotal") }} ({{ parseFloat(gameResultSummary.winlose) < 0 ? $t("ui.lost") : $t("ui.won") }})
        td.text-right(valign='top' width='12%')
          span(
            :class="{ red: parseFloat(gameResultSummary.winlose) < 0 }"
            ) {{ $numeral(gameResultSummary.winlose).format("0,0.00") }}
        td.text-right(valign='top' width='12%')  
      tr
        td.text-right(valign='top') {{ $t("ui.subtotal") }} ({{ $t("ui.commission") }})
        td.text-right(valign='top') {{ $numeral(gameResultSummary.comm).format("0,0.00") }}
        td  
      tr
        td.text-right(valign='top') {{ $t("ui.total") }}
        td.text-right(valign='top')
          span(
            :class="{ red: total < 0 }"
            ) {{ $numeral(total).format("0,0.00") }}
        td
  .mt-2
    v-pagination(
      v-model="currentPage"
      :page-count="gameResultTotalPages"
      :classes="bootstrapPaginationClasses"
      :labels="paginationAnchorTexts"
      @input="changedPage($event)"
      v-if="gameResultTotalPages"
    )
</template>
<script>
import config from "@/config";
import errors from "@/errors";
import vPagination from "vue-plain-pagination";

export default {
  components: { vPagination },
  props: {
    currentGameResultPage: {
      type: Number,
    },
    gameResultList: {
      type: Array,
      default: [],
    },
    gameResultSummary: {
      type: Object,
      default: {},
    },
    currSportType: {
      type: String,
      default: "",
    },
    gameResultTotalPages: {
      type: Number,
    },
  },
  data() {
    return {
      currentPage: 1,
      bootstrapPaginationClasses: {
        ul: "pagination justify-content-center",
        li: "page-item",
        liActive: "active",
        liDisable: "disabled",
        button: "page-link",
        buttonActive: "active",
        buttonDisable: "disable",
      },
      paginationAnchorTexts: {
        first: "<i class='fas fa-angle-double-left'></i>",
        prev: "<i class='fas fa-angle-left'></i>",
        next: "<i class='fas fa-angle-right'></i>",
        last: "<i class='fas fa-angle-double-right'></i>",
      },
      loading: {
        arcardia: false,
        pgsoft: false,
        pragmatic: false,
        joker: false,
        spp: false,
        jili: false,
        uusl: false,
        next: false,
        dbhash: false,
        wow: false,
        live22: false,
        wf: false,
        epw: false,
        aio: false
      },
    };
  },
  computed: {
    newFeatures() {
      return config.newFeatures;
    },
    currency_code() {
      return this.$store.getters.currencyCode;
    },
    account_id() {
      return this.$store.getters.accountId;
    },
    session_token() {
      return this.$store.getters.sessionToken;
    },
    isTotal() {
      if (this.gameResultSummary != null) {
        return Object.keys(this.gameResultSummary).length > 0;
      } else {
        return false;
      }
    },
    total() {
      if (this.gameResultSummary != null) {
        return parseFloat(this.gameResultSummary.winlose) + parseFloat(this.gameResultSummary.comm);
      } else {
        return 0;
      }
    },
  },
  mounted() {
    this.currentPage = this.currentGameResultPage;
    this.changedPage(1, "game_result");
  },
  methods: {
    // getResult(e) {
    //   var result = config.r2ResultUrl() + e;
    //   if (e.includes("LOSG_") || e.includes("LCB_")) {
    //     var result = config.r3ResultUrl() + e;
    //   }

    //   // console.log(e, result);
    //   return result;
    // },
    getResultAR(e) {
      if (this.loading.arcardia) return;
      var id = e.replace("AR_", "");
      // console.log(id);
      var url = "";
      const args = {
        cert: config.arcadeCert,
        transId: id.toString(),
      };
      this.loading.arcardia = true;
      this.$http.post(config.arcadePath, args).then(
        (res) => {
          this.loading.arcardia = false;
          if (res.data) {
            url = res.data.url;
            if (url) {
              window.open(url, "resultar", "top=10,height=600,width=400,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.arcardia = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultPGS(e) {
      if (this.loading.pgsoft) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        language: "en",
      };
      this.loading.pgsoft = true;
      this.$http.post(config.pgsPath, args).then(
        (res) => {
          this.loading.pgsoft = false;
          if (res.data) {
            url = res.data.history_url;
            if (url) {
              window.open(url, "resultpgs", "top=10,height=414,width=737,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.pgsoft = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultPragmatic(e) {
      if (this.loading.pragmatic) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        userid: this.account_id,
        language: "en",
      };
      this.loading.pragmatic = true;
      this.$http.post(config.pragmaticPath, args).then(
        (res) => {
          this.loading.pragmatic = false;
          if (res.data) {
            url = res.data.history_url;
            if (url) {
              window.open(url, "resultppg", "top=10,height=520,width=800,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.pragmatic = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultJoker(e) {
      if (this.loading.joker) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        userid: this.account_id,
        language: "en",
      };
      this.loading.joker = true;
      this.$http.post(config.jokerPath, args).then(
        (res) => {
          this.loading.joker = false;
          if (res.data) {
            url = res.data.history_url;
            if (url) {
              window.open(url, "resultjkr", "top=10,height=520,width=800,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.joker = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultSimplePlay(e) {
      if (this.loading.spp) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        // userid: this.account_id,
        // language: "en"
      };
      this.loading.spp = true;
      this.$http.post(config.sppPath, args).then(
        (res) => {
          this.loading.spp = false;
          if (res.data) {
            url = res.data.history_url;
            if (url) {
              window.open(url, "resultspp", "top=10,height=598,width=920,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.spp = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultJili(e) {
      if (this.loading.jili) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        // userid: this.account_id,
        language: this.$store.getters.language,
      };
      this.loading.jili = true;
      this.$http.post(config.jiliPath, args).then(
        (res) => {
          this.loading.jili = false;
          if (res.data) {
            url = res.data.detail_url;
            if (url) {
              window.open(url, "resultjili", "top=10,height=598,width=920,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.jili = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultUusl(e) {
      if (this.loading.uusl) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        language: this.$store.getters.language,
      };
      this.loading.uusl = true;
      this.$http.post(config.uuslPath, args).then(
        (res) => {
          this.loading.uusl = false;
          if (res.data) {
            url = res.data.detail_url;
            if (url) {
              window.open(url, "resultuusl", "top=10,height=320,width=600,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.uusl = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultNextSpin(e) {
      if (this.loading.next) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        language: this.$store.getters.language,
      };
      this.loading.next = true;
      this.$http.post(config.nextPath, args).then(
        (res) => {
          this.loading.next = false;
          if (res.data) {
            url = res.data.detail_url;
            if (url) {
              window.open(url, "resultnext", "top=10,height=640,width=600,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.next = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultDbHashSpin(e) {
      // if (this.loading.dbhash) return;
      var url = config.dbhashPath + "?result_id=" + e.toString() + "&auth_token=" + this.session_token + "&login_id=" + this.account_id;
      window.open(url, "resultdbhash", "top=10,height=600,width=320,status=no,toolbar=no,menubar=no,location=no");
    },
    getResultWowGaming(e) {
      if (this.loading.wow) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        userid: this.account_id,
      };
      this.loading.wow = true;
      this.$http.post(config.wowPath, args).then(
        (res) => {
          this.loading.wow = false;
          if (res.data) {
            url = res.data.detail_url;
            if (url) {
              window.open(url, "resultwow", "top=10,height=640,width=600,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.wow = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultLive22Gaming(e) {
      if (this.loading.live22) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        currency: this.currency_code,
      };
      this.loading.live22 = true;
      this.$http.post(config.live22Path, args).then(
        (res) => {
          this.loading.live22 = false;
          if (res.data) {
            url = res.data.detail_url;
            if (url) {
              window.open(url, "resultlive22", "top=10,height=400,width=640,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.live22 = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultWfGaming(e) {
      if (this.loading.wf) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        currency: this.currency_code,
      };
      this.loading.wf = true;
      this.$http.post(config.wfPath, args).then(
        (res) => {
          this.loading.wf = false;
          if (res.data) {
            url = res.data.detail_url;
            if (url) {
              window.open(url, "resultwf", "top=10,height=400,width=640,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.wf = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultEpwGaming(e) {
      if (this.loading.epw) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        currency: this.currency_code,
      };
      this.loading.epw = true;
      this.$http.post(config.epwPath, args).then(
        (res) => {
          this.loading.epw = false;
          if (res.data) {
            url = res.data.detail_url;
            if (url) {
              window.open(url, "resultepw", "top=10,height=400,width=640,status=no,toolbar=no,menubar=no,location=no");
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.epw = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    getResultAioGaming(e) {
      if (this.loading.aio) return;
      var id = e;
      var url = "";
      const args = {
        resultid: id.toString(),
        userid: this.account_id,
        currency: this.currency_code,
      };
      this.loading.aio = true;
      this.$http.post(config.aioPath, args).then(
        (res) => {
          this.loading.aio = false;
          if (res.data) {
            url = res.data.detail_url;
            if (url) {
              const newWindow = window.open("", "resultaio", "top=10,height=240,width=640,status=no,toolbar=no,menubar=no,location=no");
              if (newWindow) {
                newWindow.document.head.innerHTML = `
                   <title>Game Result</title>
                   <link rel="stylesheet" href="/v1/css/bootstrap.min.css">
                   <link rel="stylesheet" href="/v1/css/app.css">
                   <link rel="stylesheet" href="/v1/css/desktop/info.css">
                   <style>
                     body { 
                       margin: 20px; 
                       background: #F4F7FC;
                     }
                   </style>
                 `;
                newWindow.document.body.innerHTML = `<div class="result-container">${url}</div>`;
              }
            } else {
              this.$swal("Error", res.data.errorMessage, "error");
            }
          } else {
            this.$swal("Error", res.data.errorMessage, "error");
          }
        },
        (err) => {
          this.loading.aio = false;
          this.$swal("Error", err, "error");
        }
      );
    },
    changedPage: function (pageNo) {
      this.currentPage = pageNo;
      this.$emit("changedPage", pageNo, "game_result");
    },
  },
};
</script>
