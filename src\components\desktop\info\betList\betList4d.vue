<template lang="pug">
.info-tablewrap.magicZ
  ul.nav.nav-tabs(role='tablist')
    li.nav-item
      a.nav-link(href="javascript:void(0);" :class="{ 'active' : mode == 0 }" @click="setMode(0)") {{ $t("ui.accepted") }}
    li.nav-item
      a.nav-link(href="javascript:void(0);" :class="{ 'active' : mode == 2 }" @click="setMode(2)") {{ $t("ui.void") }}
  .tab-content
    betList4dAccepted(v-if="mode == 0" @loading="handleLoading")
    betList4dVoid(v-if="mode == 2" @loading="handleLoading")
</template>


<script>
import betList4dAccepted from "@/components/desktop/info/betList/betList4dAccepted";
import betList4dVoid from "@/components/desktop/info/betList/betList4dVoid";

import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {
    betList4dAccepted,
    betList4dVoid
  },
  data() {
    return {
      mode: 0
    };
  },
  methods: {
    handleLoading(e) {
      this.$emit("loading", e);
    },
    setMode(e) {
      this.mode = e;
    },
    refreshList() {
      EventBus.$emit("BETLIST_REFRESH");
    }
  }
};
</script>