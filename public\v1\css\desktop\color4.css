body.color4 {
	background: #E3E3E3;
}
.color4 header,.color4 header .topbar {
	background: #479bd8;
}
.color4 header .toolbar {
	background-color: #3C9DD5;
	border-top: 0;
	border-bottom: 8px solid #E3E3E3;
	top: 0;
}
.color4 header .toolbar.active.white-label .new-timezone {
	min-width: 256px;
}
.color4 header .toolbar.active.white-label .menu {
	min-width: 256px;
}
.color4 header .toolbar.active .container .logo {
	padding-top: 1px;
	display: none !important;
}
.color4 .content .right .z-side .card .card-header {
	color: #fff;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	background: url(/images/color4/tab-bg.png) no-repeat;
    background-size: cover;
}
.color4 .content .right .text-icon.selected {
}
.color4 .content .right .z-side .card .card-header .text {
	border-right: 1px solid #00000022;
}
.color4 .frame-header {
	background: #2E78A2;
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
	border-left: 1px solid rgba(255, 255, 255, 0.2);
	border-right: 1px solid rgba(255, 255, 255, 0.2);
}
.color4 .frame-wrapper {
}
.color4 header .toolbar .show-user {
	display: none !important;
}
.color4 header .toolbar .menu ul.nav li.nav-item a.nav-link {
	height: 32px;
    border-radius: 3px;
    margin: 1px;
	background: #2E78A2;
	border: 1px solid #7abae7;
	-webkit-box-shadow: 0 -1px 0 #316b94cc inset, -1px -1px 0 #316b9444 inset;
	        box-shadow: 0 -1px 0 #316b94cc inset, -1px -1px 0 #316b9444 inset;
	color: #ffffff;
}
.color4 header .toolbar .menu ul.nav li.nav-item a.nav-link:hover {
	color: #f5db8c;
}
.color4 .dropdown-panel {
	background: #fff;
    box-shadow: none;
    border-radius: 0 0 3px 3px !important;
}
.color4 .dropdown-li {
	border-left: 0;
	border-right: 0;
}
.color4 .dropdown-li:last-child {
	border-bottom: 0;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0;
}
.color4 .dropdown-li .caption {
	font-size: 10px;
	color: #545454;
}
.color4 .dropdown-li .value .unit {
	font-size: 10px;
	color: #545454;
}
.color4 .dropdown-li .value {
	font-size: 12px;
}
.color4 .user-info-wrapper {
	border-radius: 3px;
	margin-bottom: 4px;
	font-size: 11px;
	font-family: "Lato", sans-serif;
}
.color4 .profile .dropdown-li {
	padding: 2px 2px;
	border-top: 1px solid rgba(0, 0, 0, 0.05);
	border-bottom: 0;
}
.color4 .user-info-wrapper {
	background-color: #2E78A2;
	border: 1px solid #4aa2e0;
	color: #fff;
}
.color4 .user-info-wrapper .user {
	color: #fff;
}
.color4 .user-info-wrapper .balance-drop {
	border-top: 1px solid rgba(255, 255, 255, 0.2);
	background-color: #eeeff4;
	color: #3480b7;
}
.color4 .user-info-wrapper .details {
	background-color: #378BBB;
	color: #fff;
	border-top: 1px solid rgba(255, 255, 255, .1);
}
.color4 .user-info-wrapper .details .balance-text {
	cursor: pointer;
	padding: 0.5rem;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	    -ms-flex-pack: justify;
	        justify-content: space-between;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	text-align: left;
	position: relative;
}
.color4 .user-info-wrapper .details .balance-text::after {
    content: "";
    height: 19px;
    border-right: 1px solid rgba(255, 255, 255, .1);
    padding-right: 16px;
}
.color4 .user-info-wrapper .details .balance .caption {
	font-size: 9px;
	color: #fff;
}
.color4 .user-info-wrapper .details .balance {
	padding: 0.5rem;
	-webkit-box-flex: 1;
	    -ms-flex-positive: 1;
	        flex-grow: 1;
	text-align: right;
	background-color: #378BBB;
	color: #F6C344;
	font-weight: bold;
}
.color4 .content .main .header-wrap {
	background: #d0e3f1;
}
.color4 .content .main .filter-area {
}
.color4 .content .main .filter-area .filter-item .filter-icon {
	color: #e7fffa;
}
.color4 .content .main .filter-area .filter-item .filter-icon.page-button:hover {
	background-color: #479bd8;
}
.color4 .page-button {
	border: 1px solid rgba(255, 255, 255, 0.2);
}
.color4 .x-accordion.accordion .bg {
	background: #378BBB;
}
.color4 .language-selector .dropdown-menu {
	background: #3480b7 !important;
	border: 1px solid rgba(0, 0, 0, 0.3) !important;
	-webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset !important;
	        box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset !important;
}
.color4 .x-btn-default,.color4 .x-btn {
	border: 1px solid rgba(0, 0, 0, 0.5);
	-webkit-box-shadow: 0 1px 0 #ffffff3d inset, 1px 1px 0 #ffffff2e inset;
	        box-shadow: 0 1px 0 #ffffff3d inset, 1px 1px 0 #ffffff2e inset;
}
.color4 .x-btn-default:hover {
	-webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 1px 1px 0 rgba(255, 255, 255, 0.2) inset, -1px -1px 1px rgba(0, 0, 0, 0.2) inset, 0 0 3px rgba(0, 0, 0, 0.2);
	        box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 1px 1px 0 rgba(255, 255, 255, 0.2) inset, -1px -1px 1px rgba(0, 0, 0, 0.2) inset, 0 0 3px rgba(0, 0, 0, 0.2);
}
.color4 .content .left .nav-header {
	color: #3480b7;
}
.color4 .content .left.active .nav-header.nav-right {
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.color4 .content .left .nav-header .collapsed {
	background: #3480b7;
}
.color4 .content .left .group.selected {
	background: url(/images/color4/tab-bg.png) no-repeat;
    background-size: auto 100%;
	color: #fff;
}
.color4 .content .left .side-row {
    background: url(/images/color4/tab-bg.png) no-repeat;
    background-size: cover;
}
.color4 .content .left.active .collapse.show .group {
	background: #3480b7;
}
.color4 .content .left.active #collapse-allsports.collapse.show #market-group {
	border-left: 1px solid #4aa2e0;
    border-right: 1px solid #4aa2e0;
}
.color4 .content .left.active .group.changed.selected {
	background: #186caa !important;
}
.color4 .content .left.active .heading-collapse[aria-expanded="false"] .group.changed:hover {
	background: #186caa !important;
}
.color4 .content .left .group {
	color: #fff;
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.color4 .content .left .tb {
	border-top: 1px solid #4aa2e0;
	border-radius: 3px 3px 0 0;
}
.color4 .content .left .xb {
	border-left: 1px solid #4aa2e0;
	border-right: 1px solid #4aa2e0;
}
.color4 .content .left .xb a:hover {
	text-decoration: none;
}
.color4 .content .left .xb.tb {
	border-radius: 5px;
}
.color4 .content .left ul.subgroup li.small {
	border-bottom: 1px solid #3b90cc;
}
.color4 .content .left ul.subgroup li.small:last-child {
	border-bottom: 0;
}
.color4 .content .left .group .sport-type {
	background: #2f83bf;
}
.color4 .content .left .group .sport-type a {
	color: #e8f9ff;
}
.color4 .content .left .group.selected,.color4 .content .left .group .sport-type.active {
}
.color4 .content .left .group.changed.selected, .color4 .content .left .group .sport-type.active {
    background: #2E78A2;
}
.color4 .content .main .filter-block {
}
.color4 .round-alert {
	background: #00000044;
	border: 1px solid #186caa;
}
.color4 .text-info {
	color: #186caa !important;
}
.color4 .content .main .filter-area .filter-item .dropdown .dropdown-menu a.dropdown-item,.color4 .content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item .filter-icon {
	color: #186caa;
}
.color4 .content .main .filter-area .filter-item .dropdown .dropdown-menu a:hover.dropdown-item {
	background-color: #479bd8;
	color: #fff;
}
.color4 .x-accordion.accordion .bg:last-child {
	border-bottom: 1px solid #4aa2e0;
}
.color4 .modal .modal-dialog .modal-content .modal-header {
	background: #479bd8 !important;
}
.color4 .modal .modal-dialog .modal-content .modal-body #select-league .card .card-header {
	background: #d0e3f1;
	color: #235273;
}
.color4 .modal .modal-dialog .modal-content .modal-footer .btn-primary {
	background: #2E78A2;
}
.color4 .table-info tr th,.color4 .table-betresult tr th {
	background: #186caa;
}
.color4 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link.active {
	background: #3480b7;
}
.color4 header .topbar .info-content .nav-info a:hover,.color4 header .topbar .nav-info a.active {
	background-color: #186caa;
	border-left: 1px #3480b7 solid;
	border-right: 1px #3480b7 solid;
	text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
}
.color4 .info-wrapper .info-tablewrap .nav-tabs {
	border-bottom: 1px solid #479bd8;
}
.color4 .info-wrapper .info-tablewrap .nav-tabs li.nav-item {
	background: rgba(0, 0, 0, 0.05);
	border-top: 1px solid #479bd8;
	border-left: 1px solid #479bd8;
	border-right: 1px solid #479bd8;
}
.color4 .result-selection .form-control.datepicker {
	background: #2E78A2;
}
.color4 .accordion-rules .card .card-header {
	padding: 8px;
	background: #3480b7;
	color: #efdd00;
	border-radius: 0 0 0 0;
}
.color4 .info-tablewrap .setting-right .btn-result.active {
	color: #fff;
	border: 1px solid #ffffff;
	border-radius: 0 0 0 0;
}
.color4 .modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap:hover input ~ .checkmark {
	background-color: #186caa;
	border: 1px solid #3480b7;
}
.color4 .hx-league {
	background: #ECECEC;
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.color4 .hx-league.live {
	/* background: #d0e3f1; */
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.color4 .hx-table {
	background: url(/images/color4/market-head-bg.png);
    background-size: auto 100%;
	border-bottom: 1px solid #c4c4c4;
}
.color4 .hx-cell {
	border-left: 1px solid #52aae9;
}
.color4 .hx-row {
	border-top: 1px solid #52aae9;
}
.color4 .hx-col {
	border-left: 1px solid #52aae9;
}
.color4 .hx-more {
	background: #479bd8;
}
.color4 .morebet-wrapper .body-bet .nav-tabs li.nav-item a.nav-link.active {
	background: #479bd8;
	border-bottom: 2px solid #3d8dc7;
}
.color4 .morebet-wrapper .body-bet .tab-content .tab-pane .card .card-header {
	background: #3d8dc782;
}
.color4 .morebet-wrapper .body-bet .nav-tabs.live-tab li.nav-item a.nav-link.active {
	background: #85360b;
	border-bottom: 2px solid #85360b;
}
.color4 .morebet-wrapper .body-bet .tab-content .tab-pane .card .card-header.live {
	background: #85360b;
}
.color4 .profile .dropdown-li a {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	padding: 6px;
	margin: 2px;
	color: #a5cae5;
	border: 1px solid #30546e;
	-webkit-box-shadow: 0 1px 0 #519fd6cc inset, 1px 1px 0 #519fd644 inset;
	        box-shadow: 0 1px 0 #519fd6cc inset, 1px 1px 0 #519fd644 inset;
	border-radius: 3px;
	text-align: center;
	text-decoration: none;
	line-height: 1;
}
.color4 .profile .dropdown-li a i {
	margin-right: 4px;
	font-size: 12px;
}
.color4 .profile .dropdown-li a:hover {
	color: #ffc107;
}
.color4 .content .left ul.subgroup input[type="checkbox"] {
	position: relative;
	width: 15px !important;
	height: 15px !important;
	color: #30546e;
	border: 1px solid #fff;
	border-radius: 3px;
	-webkit-appearance: none;
	   -moz-appearance: none;
	        appearance: none;
	outline: 0;
	cursor: pointer;
	-webkit-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	-o-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
}
.color4 .content .left ul.subgroup input[type="checkbox"]::before {
	position: absolute;
	content: "";
	display: block;
	top: 0;
	left: 4px;
	width: 5px !important;
	height: 10px !important;
	border-style: solid;
	border-color: #fff;
	border-width: 0 2px 2px 0;
	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	        transform: rotate(45deg);
	opacity: 0;
}
.color4 .content .left ul.subgroup input[type="checkbox"]:checked {
	color: #fff;
	border-color: #30546e;
	background: #30546e;
}
.color4 .content .left ul.subgroup input[type="checkbox"]:checked::before {
	opacity: 1;
}
.color4 .content .left ul.subgroup input[type="checkbox"]:checked ~ label::before {
	-webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
	        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}
.color4 .hx-table.hx-match.live .morebet-wrapper {
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	border-bottom: 1px solid #b2c7d6;
	border-top: 1px solid #b2c7d6;
	border-collapse: collapse;
}
.color4 .content .main .single-match {
	background: #3480b7;
	color: #ffffffcc;
	border: 1px solid rgba(255, 255, 255, 0.2);
}
.color4 .content .main .single-match .action-block {
	border-right: 1px solid rgba(255, 255, 255, 0.2);
}
.color4 .content .main .single-match .content img {
	-webkit-filter: hue-rotate(10deg);
	        filter: hue-rotate(10deg);
}
.color4 .content .left .nav-header [aria-expanded="true"] {
	color: #F6C344;
	background: #2E78A2;
}
.color4 .content .left .nav-header [aria-expanded="false"] {
	color: #FFFFFF;
	background: #378BBB;
}
.color4 .luckybox .btn-2 {
	background: transparent linear-gradient(90deg, #2E78A2 0%, #3C9DD5 100%) 0% 0% no-repeat padding-box;
}
.color4 .mini-game-container .carousel-control-prev-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23378BBB' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
}
.color4 .mini-game-container .carousel-control-next-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23378BBB' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
}
.color4 .mini-game-container .carousel-indicators .active {
	background-color: #378BBB;
}
.color4 .luckyslider-label {
    color: #000;
}
.color4 .luckybox .btn-1 {
	background-color: rgba(0, 0, 0, 0.3);
	color: #fff;
}
.color4 header .toolbar .menu .nav {
    justify-content: flex-end;
}
.color4 .right .text-icon.selected i, .color4 .right .text-icon.selected svg {
    color: #fff;
}
.color4 .right .text-icon.selected {
    color: #fff;
}
.color4 .luckybox {
    color: #000;
}
.color4 .content .main .filter-area .filter-item {
    color: #505050;
}
.color4 .content .main .filter-area .filter-item .filter-icon {
    color: #505050;
}
.color4 #new-searchbar.searchbar .input-group .input-group-prepend .input-group-text {
	color: #505050;
}
.color4 #new-searchbar.searchbar .input-group .form-control {
	color: #505050;
}
.color4 #new-searchbar.filter-item.searchbar .input-group .form-control::-webkit-input-placeholder {
	color: #505050 !important;
}
.color4 #new-searchbar.filter-item.searchbar .input-group .form-control:-ms-input-placeholder {
	color: #505050 !important;
}
.color4 #new-searchbar.filter-item.searchbar .input-group .form-control:-moz-placeholder {
	color: #505050 !important;
}
.color4 .content .left .content-bet .nav li.nav-item a.nav-link.active {
    background: #f9d040;
}
.color4 .content .left .content-bet .nav li.nav-item a.nav-link {
    background: #eeeff4;
    color: #333;
}
.color4 .frame-wrapper {
    background: #fff;
}
.color4 .luckybox .switch-wrap .switch .slider {
    background-color: #AEAEAE;
}
.color4 .mini-bg1 {
    background: #fff;
}
.color4 .morebet-wrapper .body-bet .tab-content .tab-pane .card .card-header.live {
    background: #F4D1C5;
}
.color4 .morebet-wrapper .body-bet .nav-tabs.live-tab li.nav-item a.nav-link.active {
    background: #C85F3F;
    border-bottom: 2px solid #C85F3F;
}
.color4 .hx-table.alternate.hx-match {
    background: #F6F6F6;
}
.color4 .content .main .filter-block .filter-single .filter-date {
    border: 1px solid rgba(0, 0, 0, 0.1);
}
.color4 .content .main .filter-block .filter-single .filter-date .filter-date-title {
    background-color: #378BBB;
}
.color4 .content .main .filter-block .filter-single .filter-date.active {
	color: #000;
}
.color4 .content .main .filter-block .filter-single .filter-date.active .filter-date-body {
	color: #000;
}
.color4 .content .main .filter-block .filter-single .filter-date.active .filter-date-title {
    background-color: #F6C344;
}
.color4 .content .main .filter-block .filter-single .filter-date .filter-date-body {
	color: #3E3E3E;
}
.color4 .content .left.active .nav-header .collapsed, .content .left.active .nav-header [aria-expanded="true"], 
.color4 .content .left.active .nav-header [aria-expanded="false"] {
    background: transparent;
}
.color4 .content .left.active .nav-header.nav-left, .color4 .content .left.active .nav-header.nav-right {
	background: #378BBB;
}
.color4 .content .left ul.subgroup li.hot { 
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.color4.europeview .content .main .single-match {
	background: #378BBB;
    border: 1px solid #378BBB;
}
.color4.europeview .content .main .single-match .action-block {
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}
.color4.europeview .hx-table.hx-match .bet-value {
	background-color: #ECECEC;
}
.color4 .x-side {
	background: #E3E3E3;
}
.color4 .x-side::before {
	border-left: 12px solid #E3E3E3;
}
.color4 .x-side::after {
	border-right: 8px solid #E3E3E3;
}
.color4 .luckybox .btn-1:hover {
	background: #378BBB;
}
.color4 .luckydelete {
	background: rgba(0, 0, 0, 0.3);
}
.color4 .luckydelete:hover {
	background: #378BBB;
}
.color4 .luckyodds:last-child {
	color: #0A4782;
}
.color4 .luckyeven {
	background: rgba(0,0,0,.05);
}
.color4 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link {
	color: #000;
}
.color4 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link.active {
    color: #F6C344;
}
.color4 .info-tablewrap .setting-right .btn-result.active {
	background: #2E78A2 !important;
}
.color4 .result-selection .btn-result {
	border: 1px solid #479bd8;
}
.color4 .result-selection .btn-result:hover, .result-selection .btn-result.active {
	background: #2E78A2 !important;
	border: 1px solid #479bd8;
}
.color4 .info-tablewrap .pagination .page-item .page-link {
	border: 1px solid #479bd8;
	color: #479bd8;
}
.color4 .info-tablewrap .pagination .page-item .page-link.active {
	background: #2E78A2;
	color: #fff;
}
.color4 .info-tablewrap .setting-right .custom-control-input:checked ~ .custom-control-label::before {
    background: #2E78A2;
    border: 1px solid #479bd8;
}
.color4 .modal .modal-dialog .modal-content .modal-footer .btn-primary {
    background: #2E78A2;
}
.color4 .result-selection .dropdown-menu .dropdown-item:hover, 
.color4 .result-selection .dropdown-menu .dropdown-item:focus, 
.color4 .result-selection .dropdown-menu .dropdown-item.active {
	background: #9f9f9f;
}
.color4 .tournament-top, 
.color4 .tournament-top-plus {
	background: #3C9DD5;
}
.color4 .tournament-btn-search {
	background-color: #2f83bf;
}
.color4 .tournament-btn-search:hover, 
.color4 .tournament-btn-search:focus {
	background-color: #2f83bf;
}
.color4 .tournament-user {
	background: transparent linear-gradient(90deg, #2E78A2 0%, #3C9DD5 100%) 0% 0% no-repeat padding-box;
}
.color4 .tournament-pagination ul li .page-link.disable,
.color4 .tournament-pagination ul li .page-link,
.color4 .tournament-pagination ul li .page-link:hover {
	border: 1px solid #479bd8;
}
.color4 .tournament-pagination ul li .page-link i,
.color4 .tournament-pagination ul li .page-link {
	color: #479bd8;
}
.color4 .tournament-pagination ul li .page-link.active {
	background-color: #2f83bf;
	border: 1px solid #479bd8;
	color: #fff;
}
.color4 .tournament-pagination ul li .page-link:hover {
	color: #000;
}
.color4 .tournament-btn {
	background: url('../../img/tn/bg-match-color4.png') top center no-repeat;
}
.color4 .tournament-pool-fee {
	background: rgba(0,0,0,0.5);
	border: 1px rgba(255,255,255,0.2) solid;
}
.color4 .tournament-pool-result {
    background-color: #2f83bf;	
}
.color4 .tournament-page-wrapper ul li.nav-item .nav-link {
	color: #2f83bf;
}
.color4 .select-day-top {
	color: #000;
}
.color4 .select-day-bottom {
	color: #479bd8;
}
.color4 .select-day.today .select-day-bottom {
	color: #F6C344;
}
.color4 .select-league-title {
	color: #000;
}
.color4 .select-league-teams {
	color: #000;
}
.color4 .select-league-time {
	color: #000;
}
.color4 .select-league .date-check input:checked ~ .checkmark-date {
	background-color: #2f83bf;
	border: 1px solid #2f83bf;
}
.color4 .room-bottom {
	color: #000;
}
.color4 .room-rate-select select option, 
.color4 .room-limit-select select option,
.color4 .tournament-search select option {
	background-color: #2f83bf;
}
.color4 .room-rate-select select,
.color4 .room-limit-select select {
	color: #000;
}
.color4 .tournament-point {
	background-color: #3C9DD5;
}
.color4 .tournament-menu ul li.active {
	background-color: #F6C344;
}
.color4 .tournament-menu ul li.active a {
	color: #000;
}
.color4 .bet-info .bet-type.blue {
	color: #479bd8;
}
.color4 .tournament-mybet-inner {
	background: #D1D9E1;
}
.color4 .tournament-details-icon {
	filter: brightness(0%);
    opacity: 0.5;
}
.color4 .player-prize-icon {
	filter: brightness(0%);
    opacity: 0.5;
}
.color4 .tournament-details-bottom {
	color: #479bd8;
}
.color4 .tournament-betslip-room {
	background: #2E78A2;
}
.color4 .tournament-betslip {
    background: #D1D9E1;
}
.color4 .alert-tournament {
	background-color: #fff;
	color: #000;
}
.color4 .tournament-odds {
	background-color: #fff;
}
.color4 .tournament-odds::before {
	filter: brightness(0) invert(1);
}
.color4 .tournament-odds::after {
	filter: brightness(0) invert(1);
}
.color4 .tournament-betslip-matches {
	color: #000;
}
.color4 .tournament-odds-text {
	color: #000;
}
.color4 .tournament-betslip-matches .tn-team {
	color: #000;
}
.color4 .tournament-betslip-matches .tn-vs {
	color: #333;
}
.color4 .tournament-betslip-header-title {
	background: #378BBB;
}
.color4 .player-betslip-top {
	background-color: #3C9DD5;
}
.color4 .player-prize-bottom {
	color: #479bd8;
}
.color4 .player-betslip-room {
    color: #000;
}
.color4 .player-betslip-content .tournament-mybet-single .tournament-mybet-date {
    color: #479bd8;
}
.color4 .player-betslip-content .tournament-mybet-single .tournament-mybet-small span {
	color: #479bd8;
}
.color4 .tournament-mybet-status {
	color: #50AE1B;
}
.color4 .tournament-betslip-wrapper ul li.nav-item .nav-link {
	background-color: #2E78A2;
}
.color4 .hl-select-date {
	background-color: #3480b7;
}
.color4 .hl-filter-bar .dropdown-menu {
	background: #479bd8;
}
.color4 .hl-filter-bar .dropdown-menu .dropdown-item:hover {
	background: #3480b7;	
}
.color4 .hl-filter-bar .dropdown-menu .dropdown-item.active,
.color4 .hl-filter-bar .dropdown-menu .dropdown-item:active,
.color4 .hl-filter-bar .dropdown-menu .dropdown-item.active:hover {
	background-color: #2E78A2;
}
.color4 .hl-league-titlebar {
	background: #186caa;
}
.color4 .tournament-betslip-matches .text-center {
	color: #000 !important;
}
.color4 .tournament-table-entry .table-tournament {
	color: #000;
}
.color4 .tournament-stake-field .input-group-text {
	background: #2f83bf;
}
.color4 .swal2-confirm {
	background: #3480b7 !important;
    border: 1px solid #479bd8 !important;
}

.color4 .tournament-betslip-league span {
	color: #000;
}
.color4 .tournament-odds2 .tournament-odds-text {
	color: #fff;
}