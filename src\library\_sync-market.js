export default {
  data: {},
  decode(r, t, c, ot, parlay, bypass, bytime, bymarket) {
    const leagueIndex = 0;
    const matchIndex = 1;
    const childIndex = 2;
    const matchHeader = 3;

    var m = {};

    m["options"] = {
      bymarket: bymarket,
      bytime: bytime,
      t: t,
      bypass: bypass
    };

    if (r.length <= 0) {
      return;
    }

    if (Object.keys(r[leagueIndex]).length > 0) {
      m["league"] = {};
      for (var n in r[leagueIndex]) {
        if (typeof r[leagueIndex][n] != "function") m["league"][r[leagueIndex][n][0]] = r[leagueIndex][n];
      }
    }

    m["ln"] = r[leagueIndex];

    if (Object.keys(r[matchIndex]).length > 0) {
      m["match"] = {};
      for (var n in r[matchIndex]) {
        if (typeof r[matchIndex][n] != "function") m["match"][r[matchIndex][n][0]] = r[matchIndex][n];
      }
    }

    if (Object.keys(r[childIndex]).length > 0) {
      m["child"] = {};
      m["parent"] = {};
      for (var n in r[childIndex]) {
        var rcn = r[childIndex][n];
        if (typeof rcn != "function") {
          var mm = {};
          for (var mmi in rcn) {
            if (mm[rcn[mmi][0]] == null) {
              mm[rcn[mmi][0]] = [];
            }
            mm[rcn[mmi][0]].push([rcn[mmi][1], rcn[mmi][2]]);
            if (m["match"][rcn[mmi][0]][4] == 3) {
              // check if is child type is live market
              if (m["match"][rcn[mmi][1]]) {
                m["match"][rcn[mmi][1]][12] = m["match"][rcn[mmi][0]][12]; // copy running time
              }
            }
          }

          for (var jj in rcn) {
            m["parent"][rcn[jj][1]] = rcn[jj][0];
          }

          for (var nn in mm) {
            m["child"][nn] = mm[nn];
          }
        }
      }
    }

    if (Object.keys(r[matchHeader]).length > 0) {
      m["head"] = {};
      for (var mhi in r[matchHeader]) {
        if (m["head"][r[matchHeader][mhi][0]] == null) {
          m["head"][r[matchHeader][mhi][0]] = [];
        }
        m["head"][r[matchHeader][mhi][0]].push(r[matchHeader][mhi]);
      }

      if (m["head"] != null) {
        var mk = {};
        for (var mhi in r[matchHeader]) {
          if (mk[r[matchHeader][mhi][2]] == null) {
            mk[r[matchHeader][mhi][2]] = [];
          }
          mk[r[matchHeader][mhi][2]].push(r[matchHeader][mhi]);
        }
        if (mk[1] != null) {
          m["early"] = {};
          for (var mei in mk[1]) {
            m["early"][mk[1][mei][0]] = mk[1][mei][2];
          }
        }
        if (mk[2] != null) {
          m["today"] = {};
          for (var mei in mk[2]) {
            m["today"][mk[2][mei][0]] = mk[2][mei][2];
          }
        }
        if (mk[3] != null) {
          m["live"] = {};
          for (var mei in mk[3]) {
            m["live"][mk[3][mei][0]] = mk[3][mei][2];
          }
        }
      }
    }

    return m;
  },
  grouper(grp, jjpp, ch, bytime) {
    for (var n in jjpp) {
      for (var k in jjpp[n]) {
        if (grp[n] == null) {
          grp[n] = {};
        }
        for (var j in jjpp[n][k]) {
          if (grp[n][k] == null) {
            grp[n][k] = {};
          }
          grp[n][k][j] = this.group(k, jjpp[n][k][j], ch, bytime);
        }
      }
    }
  },
  grouping(jjpp, ch, bytime) {
    var grp = {};
    for (var n in jjpp) {
      for (var k in jjpp[n]) {
        if (grp[n] == null) {
          grp[n] = {};
        }
        for (var j in jjpp[n][k]) {
          if (grp[n][k] == null) {
            grp[n][k] = {};
          }
          grp[n][k][j] = this.group(k, jjpp[n][k][j], ch, bytime);
        }
      }
    }

    return grp;
  },
  group(e, li, ch, bytime) {
    var array = li;
    var result = [];

    if (bytime == true) {
      array.sort((a, b) => {
        // sort match time
        if (new Date(a[9]) < new Date(b[9])) return -1;
        if (new Date(a[9]) > new Date(b[9])) return 1;

        // sort league
        if (a[0] < b[0]) return -1;
        if (a[0] > b[0]) return 1;

        // sort match weight
        if (a[3] < b[3]) return -1;
        if (a[3] > b[3]) return 1;

        return 0;
      });
    } else {
      if (e == 3) {
        // live weight
        array.sort((a, b) => {
          // sort match weight
          if (a[3] < b[3]) return -1;
          if (a[3] > b[3]) return 1;

          // sort league
          if (a[0] < b[0]) return -1;
          if (a[0] > b[0]) return 1;
          return 0;
        });
      } else {
        // normal weight
        array.sort((a, b) => {
          // sort league weight
          if (a[1] < b[1]) return -1;
          if (a[1] > b[1]) return 1;

          // sort league
          if (a[0] < b[0]) return -1;
          if (a[0] > b[0]) return 1;

          // sort match weight
          if (a[3] < b[3]) return -1;
          if (a[3] > b[3]) return 1;
          return 0;
        });
      }
    }

    var temp = [];
    var difference;
    for (var i = 0; i < array.length; i += 1) {
      if (difference !== array[i][0]) {
        if (difference !== undefined) {
          result.push(temp);
          temp = [];
        }
        difference = array[i][0];
      }
      temp.push(array[i]);
    }

    if (temp.length) {
      result.push(temp);
    }
    return result;
  }
};
