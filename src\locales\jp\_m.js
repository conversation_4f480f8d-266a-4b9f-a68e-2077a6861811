export default {
  BT_O: "オーバー",
  BT_U: "アンダー",

  BT_BOTH: "両方",
  BT_ONE: "一方",
  BT_HDP: "ハンディキャップ",
  BT_OU: "オーバー/アンダー",
  BT_ML: "マネーライン",
  BT_1X2: "1X2",
  BT_1X2HDP: "3ウェイハンディキャップ",
  BT_CS: "正確なスコア",
  BT_OE: "奇数/偶数",
  BT_DC: "ダブルチャンス",
  BT_HTFT: "ハーフタイム/フルタイム",
  BT_FGLG: "最初のゴール/最後のゴール",
  BT_TG: "総得点",
  BT_OR: "アウトライト",
  BT_ORZ: "アウトライト",
  BT_ODD: "奇数",
  BT_EVEN: "偶数",

  BT_ANB: "アウェイノーベット",
  BT_BS: "両方/一方/どちらも得点しない",
  BT_CL: "クリーンシート",
  BT_DNB: "引き分けノーベット",
  BT_DNBH: "前半引き分けノーベット",
  BT_HNB: "ホームノーベット",
  BT_TWTN: "無得点勝利",

  BS_1X2OU: "1X2 + OU",
  BT_1X2OU: "1X2 + OU",
  BT_1O: "1オーバー",
  BT_1U: "1アンダー",
  BT_2O: "2オーバー",
  BT_2U: "2アンダー",
  BT_XO: "XO",
  BT_XU: "XU",

  BS_DCOU: "ダブルチャンス + OU",
  BT_DCOU: "ダブルチャンス + OU",
  BT_HDO: "HDO",
  BT_HDU: "HDU",
  BT_HAO: "HAO",
  BT_HAU: "HAU",
  BT_DAO: "DAO",
  BT_DAU: "DAU",

  BS_OUOE: "OU + OE",
  BT_OUOE: "OU + OE",
  BK_OO: "OO",
  BK_OE: "OE",
  BK_UO: "UO",
  BK_UE: "UE",

  BS_HTFTOE: "HTFT + OE",
  BT_HTFTOE: "HTFT + OE",
  BK_EO: "EO",
  BK_EE: "EE",

  BS_WM: "勝ち差",
  BT_WM: "勝ち差",

  BS_ETG: "正確な総得点",
  BT_ETG: "正確な総得点",
  BS_EHTG: "ホームの正確な総得点",
  BT_EHTG: "ホームの正確な総得点",
  BS_EATG: "アウェイの正確な総得点",
  BT_EATG: "アウェイの正確な総得点",
  BS_ETGH: "前半正確な総得点",
  BT_ETGH: "前半正確な総得点",
  BS_EHTGH: "前半ホームの正確な総得点",
  BT_EHTGH: "前半ホームの正確な総得点",
  BS_EATGH: "前半アウェイの正確な総得点",
  BT_EATGH: "前半アウェイの正確な総得点",
  BT_0: "0",
  BT_1: "1",
  BT_2: "2",
  BT_3: "3",
  BT_4: "4",
  BT_5: "5",
  BT_6Up: "6+",
  BT_3Up: "3+",


  BT_HDPH: "前半ハンディキャップ",
  BT_OUH: "前半オーバー/アンダー",
  BT_MLH: "前半マネーライン",
  BT_1X2H: "前半1X2",
  BT_1X2HDPH: "前半3ウェイハンディキャップ",
  BT_CSH: "前半正確なスコア",
  BT_OEH: "前半奇数/偶数",
  BT_DCH: "前半ダブルチャンス",
  BT_FGLGH: "前半最初のゴール/最後のゴール",
  BT_TGH: "前半総得点",

  TB_HDP: "フルタイムハンディキャップ",
  TB_OU: "フルタイムオーバー/アンダー",
  TB_1X2: "フルタイム1X2",
  TB_OE: "フルタイム奇数/偶数",
  TB_HDPH: "前半ハンディキャップ",
  TB_OUH: "前半オーバー/アンダー",
  TB_1X2H: "前半1X2",
  TB_OEH: "前半奇数/偶数",

  BT_HDPOU: "ハンディキャップ & オーバー/アンダー",
  BT_PARLAY: "ミックスパーレー",
  BT_FT: "フルタイム",
  BT_1H: "前半",
  BT_2H: "後半",

  BT_FG: "最初のゴール",
  BT_LG: "最後のゴール",
  BT_HFG: "ホームファースト",
  BT_AFG: "アウェイファースト",
  BT_HLG: "ホームラスト",
  BT_ALG: "アウェイラスト",
  BT_NG: "ノーゴール",
  BT_HY: "ホーム はい",
  BT_HN: "ホーム いいえ",
  BT_AY: "アウェイ はい",
  BT_AN: "アウェイ いいえ",
  BT_H: "ホーム",
  BT_A: "アウェイ",
  BT_D: "引き分け",
  BT_HD_DC: "HD",
  BT_HA_DC: "HA",
  BT_DA_DC: "DA",
  BT_HH: "HH",
  BT_HD: "HD",
  BT_HA: "HA",
  BT_DH: "DH",
  BT_DD: "DD",
  BT_DA: "DA",
  BT_AH: "AH",
  BT_AD: "AD",
  BT_AA: "AA",

  BT_Y: "はい",
  BT_N: "いいえ",

  BT_LIVE: "ライブ",
  BT_Live: "ライブ",
  BT_live: "ライブ",
  BT_HT: "ハーフタイム",
  BT_DELAYED: "遅延",
  BT_PEN: "ペナルティ",

  BT_FT1: "フルタイム1",
  BT_FT2: "フルタイム2",
  BT_FTX: "フルタイムX",

  TC_FT1: "1",
  TC_FT2: "2",
  TC_FTX: "X",

  BT_HT1: "前半1",
  BT_HT2: "前半2",
  BT_HTX: "前半X",

  F5_INN_HDP: "F5回 ハンディキャップ",
  F5_INN_OU: "F5回 オーバー/アンダー",

  BS_HDP: "フルタイムハンディキャップ",
  BS_HDPH: "前半ハンディキャップ",
  BS_OU: "フルタイムオーバー/アンダー",
  BS_OUH: "前半オーバー/アンダー",
  BS_OE: "フルタイム奇数/偶数",
  BS_OEH: "前半奇数/偶数",
  BS_1X2: "フルタイム1X2",
  BS_1X2H: "前半1X2",
  BS_ML: "フルタイムマネーライン",
  BS_MLH: "前半マネーライン",
  BS_1X2HDP: "フルタイム3ウェイハンディキャップ",
  BS_1X2HDPH: "前半3ウェイハンディキャップ",
  BS_CS: "フルタイム正確なスコア",
  BS_CSH: "前半正確なスコア",
  BS_DC: "フルタイムダブルチャンス",
  BS_DCH: "前半ダブルチャンス",
  BS_FGLG: "フルタイム最初/最後のゴール",
  BS_FGLGH: "前半最初/最後のゴール",
  BS_TG: "フルタイム総得点",
  BS_TGH: "前半総得点",
  BS_ANB: "Away No Bet",
  BS_HTFT: "ハーフタイム/フルタイム",
  BS_BS: "両方/一方/どちらも得点しない",
  BS_CL: "クリーンシート",
  BS_DNB: "引き分けノーベット",
  BS_DNBH: "前半引き分けノーベット",
  BS_HNB: "ホームノーベット",
  BS_TWTN: "無得点勝利",
  BS_OR: "アウトライト",
  BS_ORZ: "アウトライト",

  LOT_BS: "B/S",
  LOT_BIG: "B",
  LOT_SMALL: "S",
  LOT_OE: "O/E",
  LOT_ODD: "O",
  LOT_EVEN: "E",
  LOT_OU: "O/U",
  LOT_OVER: "O",
  LOT_UNDER: "U",
  LOT_1: "H",
  LOT_0: "A",
  LOT3_OVER: "O",
  LOT3_UNDER: "E",

  D_1: "シングルナンバー",
  D_2: "ナンバー & リバース",
  D_3: "ボックス",
  D_4: "アイボックス",
  D_5: "最後の3桁ボックス",
  D_6: "最後の2桁ボックス",

  D_BIG: "ビッグ",
  D_SMALL: "スモール",
  D_AA: "AA",
  D_SA: "A",
  D_SB: "B",
  D_SC: "C",
  D_SD: "D",
  D_SE: "E",
  D_ABC: "ABC",

  GH_1X2: "勝利",
  GH_1X20: "勝利",
  GH_1X2HDP: "着順/見せる",
  GH_1X2HDP2: "着順",
  GH_1X2HDP4: "見せる",
  GH_OVER: "オーバー (4,5,6)",
  GH_UNDER: "アンダー (1,2,3)",
  GH_ODD: "奇数 (1,3,5)",
  GH_EVEN: "偶数 (2,4,6)",
  GH_OU1: "オーバー (4,5,6)",
  GH_OU2: "アンダー (1,2,3)",
  GH_OE1: "奇数 (1,3,5)",
  GH_OE2: "偶数 (2,4,6)",

  ROOM_A: "オープン",
  ROOM_0: "アクティブ",
  ROOM_1: "閉鎖",
  ROOM_2: "キャンセル",
  ROOM_3: "終了",
  ROOM_4: "ベット停止",
  ROOM_10: "すべて",

  ROOM_T0: "すべて",
  ROOM_T1: "パブリックルーム",
  ROOM_T2: "プライベートルーム",
  ROOM_T3: "フリールーム",

  joinRoom1: "パブリックルームに参加",
  joinRoom2: "プライベートルームに参加",
  joinRoom3: "フリールームに参加",
  createRoom1: "パブリックルームを作成",
  createRoom2: "プライベートルームを作成",
  createRoom3: "フリールームを作成",
  Champion: "チャンピオン",
  FirstRunnerUp: "ファーストランナーアップ",
  SecondRunnerUp: "セカンドランナーアップ",


  BT_OEHM: "ホーム奇数/偶数",
  BT_OEHMH: "前半ホーム奇数/偶数",
  BT_OEAW: "アウェイ奇数/偶数",
  BT_OEAWH: "前半アウェイ奇数/偶数",

  BS_OEHM: "フルタイムホーム奇数/偶数",
  BS_OEHMH: "前半ホーム奇数/偶数",
  BS_OEAW: "フルタイムアウェイ奇数/偶数",
  BS_OEAWH: "前半アウェイ奇数/偶数",

};