<template lang="pug">
  .hx-row.hx-rows.h-100
    .hx-col.hx-cols.w-103(v-if="details[item] != null && details[item][0] && details[item][0][5] != 0 && details[item][0][5] != ''")
      .hx.hx-flex-c.h-100
        .hxs.w-60
          oddsItem(:odds="details[item][0]" idx=5 :typ="oddsType" dataType="2")
    .hx-col.hx-cols.w-103(v-if="details[item] != null && details[item][0] && details[item][0][7] != 0 && details[item][0][7] != ''")
      .hx.hx-flex-c.h-100
        .hxs.w-60
          oddsItem(:odds="details[item][0]" idx=7 :typ="oddsType" dataType="2")
</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";

export default {
  components: {
    oddsItem
  },
  props: {
    details: {
      type: Object
    },
    oddsType: {
      type: String
    },
    item: {
      type: String
    },
    i: {
      type: String
    },
    betType: {
      type: String
    }
  },
  // updated: function() {
  //   this.$nextTick(function() {
  //     console.log("oe", new Date(), this.oddsType, this.item);
  //   });
  // }
};
</script>
