<template lang="pug">
.wrapper.h-100v.p-3
  .preloader.d-flex.align-items-center.justify-content-center(v-if="error")
    .game-warning
      i.fad.fa-exclamation-triangle.mr-1
      span {{ error }}
  .lds-overlay(v-else)
    .lds-roller
      div
      div
      div
      div
      div
      div
      div
      div
</template>

<script>
import config from "@/config";
import xhr from "@/library/_xhr-game.js";
import mixinTypes from "@/library/mixinTypes";

export default {
  data() {
    return {
      url: "",
      error: ""
    };
  },
  computed: {
    whiteLabel() {
      return this.$store.getters.whiteLabel.mode;
    }
  },

  mounted() {
    $("html").addClass("minimal");
    $("body").addClass("minimal");
  },
  created() {
    $("html").addClass("minimal");
    $("body").addClass("minimal");

    if (this.whiteLabel) return;

    this.launchGame();
  },
  methods: {
    launchGame() {
      var q = this.$route.query;
      var game = "830009";
      if (q.game) {
        game = q.game;
      }

      $("html").addClass("minimal");
      $("body").addClass("minimal");
      var args = {
        player_id: this.$store.getters.accountId,
        password: this.$store.getters.sessionToken,
        display_name: this.$store.getters.accountId,
        game_id: game,
        clienttype: 0,
        currency: this.$store.getters.currencyCode,
        lang: this.$store.getters.language == "cn" ? "zh-cn" : "en"
      };

      xhr.launchL22(args).then(
        res => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  window.location.replace(this.url);
                }, 100);
              } else {
                switch (res.status) {
                case "lotteryDisabled":
                case "currencyNotSupported":
                case "liveCasinoDisabled":
                case "EsportsDisabled":
                case "MiniGameDisabled":
                  this.error = this.$t("error." + res.status);
                  break;
                case "account_status_suspend":
                  this.error = this.$t("error.SUSPEND");
                  break;
                default:
                  this.error = this.$t("error.game_maintenance");
                  break;
                }
              }
            } else {
              switch (res.status) {
              case "lotteryDisabled":
              case "currencyNotSupported":
              case "liveCasinoDisabled":
              case "EsportsDisabled":
              case "MiniGameDisabled":
                this.error = this.$t("error." + res.status);
                break;
              case "account_status_suspend":
                this.error = this.$t("error.SUSPEND");
                break;
              default:
                this.error = this.$t("error.game_maintenance");
                break;
              }
            }
          }
        },
        err => {
          this.loading = false;
          switch (err.status) {
          case "lotteryDisabled":
          case "currencyNotSupported":
          case "liveCasinoDisabled":
          case "EsportsDisabled":
          case "MiniGameDisabled":
            this.error = this.$t("error." + err.status);
            break;
          case "account_status_suspend":
            this.error = this.$t("error.SUSPEND");
            break;
          default:
            this.error = this.$t("error.game_maintenance");
            break;
          }
        }
      );
    }
  }
};
</script>
