html {
  scroll-behavior: smooth;
}
body {
  font-family: "Tahoma", -apple-system, system-ui, BlinkMacSystemFont, "Helvetica Neue", sans-serif;
  background: #C0CED9;
}
html,
body,
.full-auto {
  display: block;
  width: 100%;
  height: auto;
  min-height: 100%;
  scroll-behavior: smooth;
}
html,
body {
  min-width: 1340px;
}
.full-auto {
  min-width: 1024px;
}
.collapse {
  -webkit-transform: none !important;
  -ms-transform: none !important;
  transform: none !important;
}
#hdpou {
  font-family: "Tahoma", -apple-system, system-ui, BlinkMacSystemFont, "Helvetica Neue", sans-serif;
}
.content .main .sport .wrapper .card .card-header {
  font-family: "Tahoma", -apple-system, system-ui, BlinkMacSystemFont, "Helvetica Neue", sans-serif;
}
.ball {
  font-weight: 400;
  font-size: 11px;
  text-align: left;
}
.content .main .sport .wrapper .card .card-header.child i {
  font-size: 14px;
  vertical-align: 0;
}
.content .main .sport .wrapper .card .card-header.child span {
  font-size: 12px;
}
:focus {
  outline: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}
*:not(input) {
  -webkit-touch-callout: initial;
  -webkit-user-select: initial;
  -moz-user-select: initial;
  -ms-user-select: initial;
  user-select: initial;
  -webkit-user-drag: initial;
}
input {
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
  -o-user-select: auto !important;
  user-select: auto !important;
}
.collapsed .arrow-up i {
  -webkit-transform: rotate(-180deg);
  -ms-transform: rotate(-180deg);
  transform: rotate(-180deg);
}
.collapsed .fa-chevron-up {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.fa-chevron-up {
  -webkit-transition: 0.3s -webkit-transform ease-in-out;
  transition: 0.3s -webkit-transform ease-in-out;
  -o-transition: 0.3s transform ease-in-out;
  transition: 0.3s transform ease-in-out;
  transition: 0.3s transform ease-in-out, 0.3s -webkit-transform ease-in-out;
}
.icon-special {
  font-size: 100px;
  color: #bb4038;
}
.special-wrap .header-special {
  font-size: 60px;
}
.special-wrap .body-special {
  font-size: 20px;
  font-style: italic;
}
.head-notice {
  font-size: 70px;
}
.longdash {
  padding: 0.1em 0 0 0;
  width: 2em;
  margin: 20px;
  background: #000;
}
.maintenance-logo img {
  max-width: 150px;
}
#new-switch.switch-wrap {
  margin-left: 4px;
}
#new-switch.switch-wrap .switch {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 16px;
  margin-bottom: 0;
  top: 0;
}
#new-switch.switch-wrap .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
#new-switch.switch-wrap .switch input:checked + .slider {
  background-color: #4ed164;
}
#new-switch.switch-wrap .switch input:checked + .slider:before {
  -webkit-transform: translateX(14px);
  -ms-transform: translateX(14px);
  transform: translateX(14px);
}
#new-switch.switch-wrap .switch input:focus + .slider {
  -webkit-box-shadow: 0 0 1px #4ed164;
  box-shadow: 0 0 1px #4ed164;
}
#new-switch.switch-wrap .switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: grey;
  -webkit-transition: 0.4s;
  -o-transition: 0.4s;
  transition: 0.4s;
}
#new-switch.switch-wrap .switch .slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 2px;
  bottom: 1px;
  background-color: #fff;
  -webkit-transition: 0.4s;
  -o-transition: 0.4s;
  transition: 0.4s;
}
#new-switch.switch-wrap .switch .slider.round {
  border-radius: 10px 10px 10px 10px;
}
#new-switch.switch-wrap .switch .slider.round:before {
  border-radius: 50%;
}
#new-searchbar.searchbar .input-group .input-group-prepend .input-group-text {
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0;
  width: 24px;
  color: #014273;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

#new-searchbar.searchbar .input-group .input-group-prepend .input-group-text .fa-bolt {
  color: #f9d040;
  margin-left: 2px;
}
#new-searchbar.searchbar .input-group .form-control {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid transparent;
  color: #014273;
  font-size: 12px;
  padding: 0 4px;
  margin: 4px 0;
  line-height: 22px;
  height: 25px;
  border-radius: 3px;
}
#new-searchbar.filter-item.searchbar .input-group .form-control::-webkit-input-placeholder {
  color: #014273 !important;
}
#new-searchbar.filter-item.searchbar .input-group .form-control:-ms-input-placeholder {
  color: #014273 !important;
}
#new-searchbar.filter-item.searchbar .input-group .form-control:-moz-placeholder {
  color: #014273 !important;
}
.icon-dialog {
  font-size: 40px;
}
.icon-user {
  font-size: 35px;
  color: #d1dfe8;
  margin-top: 5px;
  cursor: pointer;
}
.icon-user:hover {
  color: #efdd00;
}
#wrapper-table {
  position: relative;
  font-size: 12px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
#wrapper-table .card {
  z-index: 1;
}
.carousel-item {
  text-align: center;
}
.filter-area,
.content .left .group,
.content .right .z-side,
.content .right .z-side .card,
#accordion-sports,
#live-show .card,
.topbar,
.toolbar,
#market-tab .tab-pane {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  will-change: transform;
}
.full-height {
  position: relative;
  height: 100vh !important;
}
.full-topbar {
  position: relative;
  min-height: calc(100vh - 45px) !important;
}
.pt-7px {
  padding-top: 7px;
}
.btn-dropdown {
  border-radius: 0;
}
.live-icon {
  width: 20px;
  text-align: center;
}
.btn-edit {
  margin-top: -2px;
}
.mr-3px {
  margin-right: 3px;
}
#heading-favorites i {
  font-size: 16px;
}
#heading-live i {
  font-size: 18px;
}
#heading-betslip > div {
  padding: 0 3px;
}
#heading-mybet > div {
  padding: 0 3px;
}
.collapsing {
  -o-transition: none !important;
  transition: none !important;
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -ms-transition: none !important;
}
.bet-list-scroll,
.new-betslipwrap {
  overflow: auto;
  /* max-height: 60vh; */
  max-height: calc(100vh - 360px);
  min-height: 180px;
  height: auto;
  outline: 0;
}
.betlist-content {
  margin-bottom: 5px;
}
.bet-parlay-scroll {
  overflow: auto;
  /* max-height: 60vh;*/
  max-height: calc(100vh - 360px);
  min-height: 180px;
  height: auto;
  outline: 0;
}
.ticket-status {
  text-transform: uppercase !important;
  font-size: 11px;
  line-height: 1;
}
.mb-1px {
  margin-bottom: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 1px 1px 0 1px;
}
.bg-blue .fa-star.selected {
  color: #ffe105;
}
.bg-blue .fas.fa-star {
  width: 16px;
}
.pq-1 {
  padding: 1px !important;
}
.br-0 {
  border-radius: 0 !important;
}
.w-16px {
  width: 16px;
}
.checkmarkbox {
  display: block;
  position: relative;
  padding-left: 25px;
  margin-bottom: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.checkmarkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.checkmarkbox input:checked ~ .checkmark {
  background-color: #1e66aa;
  border: 1px solid #0f4f8c;
}
.checkmarkbox input:checked ~ .checkmark:after {
  display: block;
}
.checkmarkbox .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 18px;
  width: 18px;
  background-color: #c3e5ff;
  border: 1px solid #0f4f8c;
  border-radius: calc(0.25rem - 1px);
}
.checkmarkbox .checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid #ffffff;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.checkmarkbox:hover input ~ .checkmark {
  background-color: #276FA8;
  border: 1px solid #1e66aa;
}
.checkmarkbox span {
  position: relative;
  display: block;
  font-size: 13px;
  color: #333;
  top: -1px;
  text-transform: uppercase;
}
.checkmarkbox span.blue {
  color: #0f4f8c;
}
.checkmarkbox.cmb-sm input:checked ~ .checkmark {
  background-color: #0f4f8c;
  border: 1px solid #0f4f8c;
}
.checkmarkbox.cmb-sm .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: #c3e5ff;
  border: 1px solid #0f4f8c;
  border-radius: 3px;
}
.checkmarkbox.cmb-sm .checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 4px;
  top: 0px;
  width: 6px;
  height: 11px;
  border: solid #1d8af0;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.checkmarkbox.cmb-sm {
  padding-left: 22px;
  font-size: 12px;
}
.checkmarkbox.cmb-sm span {
  text-transform: initial;
  font-size: 11px;
  top: 0;
}
.mini-casino {
  border: 0;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 698px;
}
.mini-slots {
  border: 0;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 200px;
}
.league-info {
  color: #264e74;
  font-size: 14px;
  display: inline-block;
  line-height: 1;
  padding-top: 1px;
}
.card-live .league-info {
  color: #85360b;
}
.league-info i {
  text-shadow: 0 0 2px white;
}
.animated.fast {
  -webkit-transition-duration: 250ms;
  -o-transition-duration: 250ms;
  transition-duration: 250ms;
}
.z-side {
  position: -webkit-sticky;
  position: sticky;
  top: 110px;
  left: 0;
  right: 0;
  width: 100%;
  height: calc(100vh - 110px);
}
.container.active .z-side {
  top: 44px;
  height: calc(100vh - 44px);
}
.scroll-sports {
  position: relative;
}
.bet-list-scroll {
  position: relative;
}
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
.content {
  z-index: 0;
  position: relative;
  /* justify-content: center; */
}
.content .right {
  z-index: 1;
  position: relative;
  max-width: 442px;
  min-width: 256px;
  min-height: 100vh;
}
.content .left .accordion {
  width: 100%;
}
.content .right.expanded {
  max-width: 612px;
}
.content .right .accordion {
  width: 100%;
}
.content .right.active {
  width: 50px;
}
.content .right.active2 {
  width: 514px;
}
.content .main {
  margin: 0 7px;
  width: 860px;
  min-width: 860px;
  max-width: 920px;
  display: inline-block;
  position: relative;
  z-index: 2000;
}

@media (min-width: 1920px) {
  .content .main {
    width: 1190px;
    min-width: 1190px;
    max-width: 1190px;
  }
}

@media (min-width: 2048px) {
  .content .main {
    width: 2000px;
    max-width: 2000px;
  }
}

.content .main.active {
  margin-left: 64px;
}
.content .main.active.active3 {
  margin-left: 64px;
}
.content .left {
  min-width: 220px;
  max-width: 220px;
  width: 220px;
  z-index: 2001;
}
.content .left.active {
  width: 50px;
  min-width: 50px;
}
header {
  max-width: 100vw !important;
  margin: 0 auto !important;
  padding: 0;
  width: 80%;
  min-width: 1340px !important;
}
.container {
  max-width: 1536px !important;

  /* min-width: 1874px;
  max-width: 1886px !important; */
  margin: 0 auto !important;
  padding: 7px 0 0 0;
  width: 98%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

@media (min-width: 1920px) {
  .container {
    min-width: 1874px;
    max-width: 1886px !important;
  }
}

@media (min-width: 2048px) {
  .container{
    min-width: 2078px;
    max-width: 2078px !important;
  }
}

.left #market-tab .tab-pane {
  height: calc(100vh - 310px - 45px);
}
.left #market-tab .tab-pane.mmo {
  height: calc(100vh - 350px - 45px);
}
.left #market-tab.higher .tab-pane {
  height: calc(100vh - 244px - 45px);
}
.left #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 284px - 45px);
}

.left.active #market-tab .tab-pane {
  height: calc(100vh - 353px - 45px);
}
.left.active #market-tab .tab-pane.mmo {
  height: calc(100vh - 393px - 45px);
}
.left.active #market-tab.higher .tab-pane {
  height: calc(100vh - 287px - 45px);
}
.left.active #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 327px - 45px);
}

.left.extend #market-tab .tab-pane {
  height: calc(100vh - 390px - 45px);
}
.left.extend #market-tab .tab-pane.mmo {
  height: calc(100vh - 430px - 45px);
}
.left.extend #market-tab.higher .tab-pane {
  height: calc(100vh - 324px - 45px);
}
.left.extend #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 364px - 45px);
}

.left.extend.active #market-tab .tab-pane {
  height: calc(100vh - 433px - 45px);
}
.left.extend.active #market-tab .tab-pane.mmo {
  height: calc(100vh - 473px - 45px);
}
.left.extend.active #market-tab.higher .tab-pane {
  height: calc(100vh - 367px - 45px);
}
.left.extend.active #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 407px - 45px);
}

.left.extend1 #market-tab .tab-pane {
  height: calc(100vh - 430px - 45px);
}
.left.extend1 #market-tab .tab-pane.mmo {
  height: calc(100vh - 470px - 45px);
}
.left.extend1 #market-tab.higher .tab-pane {
  height: calc(100vh - 364px - 45px);
}
.left.extend1 #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 404px - 45px);
}

.left.extend1.active #market-tab .tab-pane {
  height: calc(100vh - 353px - 45px);
}
.left.extend1.active #market-tab .tab-pane.mmo {
  height: calc(100vh - 393px - 45px);
}
.left.extend1.active #market-tab.higher .tab-pane {
  height: calc(100vh - 287px - 45px);
}
.left.extend1.active #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 327px - 45px);
}

@media (max-width: 1600px) {
  .container,
  header .topbar .container,
  header .toolbar .container {
    width: 98%;
  }
}
@media (max-width: 1366px) {
  .container,
  header .topbar .container,
  header .toolbar .container {
    width: 100%;
  }
}
.container.viewport {
  min-height: 100vh;
  height: auto;
  padding-top: 110px;
  position: relative;
}
.container.viewport.active {
  padding-top: 44px;
}
.line-height-1 {
  line-height: 1;
}
#event {
  width: 100%;
  height: 0;
  min-width: 1340px;
}
.overlay {
  position: fixed;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  top: 20px;
  right: 20px;
  max-height: 400px;
  width: 260px;
  -webkit-perspective: 400px;
  perspective: 400px;
  z-index: 10000;
}
.x-btn {
  padding: 8px 12px;
  margin: 8px 4px 4px 4px;
  font-family: "Lato", sans-serif;
  border-radius: 3px;
  cursor: pointer;
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  line-height: 1;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
  background-color: #ffc107dd;
  border: 1px solid #201908cc;
  -webkit-box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset;
  box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset;
  color: #2d250bcc;
}
.x-btn:hover {
  -webkit-box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset, -1px -1px 1px #897123cc inset, 0 0 3px rgba(0, 0, 0, 0.5);
  box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset, -1px -1px 1px #897123cc inset, 0 0 3px rgba(0, 0, 0, 0.5);
  background-color: #ffc107;
  color: #2d250b;
}
.x-btn i {
  margin-right: 4px;
}
.x-btn-xs {
  padding: 2px 3px;
  margin: 0 0 0 4px;
}
.x-btn-xs i {
  padding: 0;
  border: 0;
  margin: 0;
  line-height: 1;
  font-size: 10px;
}
.x-btn-default {
  background-color: #DCE6EF;
  border: 1px solid #051c33cc;
  -webkit-box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset;
  box-shadow: 0 1px 0 #2d6fb2cc inset, 1px 1px 0 #2d6fb244 inset;
  color: #014273;
}
.x-btn-default:hover {
  -webkit-box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset, -1px -1px 1px #897123cc inset, 0 0 3px rgba(0, 0, 0, 0.5);
  box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset, -1px -1px 1px #897123cc inset, 0 0 3px rgba(0, 0, 0, 0.5);
  background-color: rgba(255, 255, 255, 0.2);
  color: #014273;
}
.x-btn-sm {
  margin: 0;
  padding: 4px 8px;
}
.y-btn {
  border: 0;
  margin: 0;
  padding: 0;
  cursor: pointer;
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  line-height: 1;
  background: transparent;
  color: #C4E4FF;
  margin-top: -1px;
}
.y-btn:hover {
  color: #ffffff;
}
.x-accordion.accordion {
  padding-right: 0 !important;
  margin-right: 0 !important;
  z-index: 1040;
  border-radius: 3px 3px 0 0;
  padding: 0;
  font-size: 12px;
  font-weight: 400;
  font-family: "Roboto", "Tahoma", sans-serif;
  margin-bottom: 7px;
}
.x-accordion.accordion .bg {
  background: #276FA8;
  margin-bottom: 4px;
  border-radius: 5px;
}
.x-accordion.accordion .bg:last-child {
  border-bottom: 1px solid #395f83;
  border-radius: 5px;
}
.x-accordion.accordion .bg:first-child {
}
.x-accordion.accordion .bg:first-child .group {
  border-top: 1px solid #4f7eab;
}
.content .left .vgames .group, .content .left .side-row, .content .left .side-row .group {
  border-top: 0;
  border-radius: 5px
}

.x-accordion.accordion .bg.bg-mmo {
  background: #113f50;
}
.x-accordion.accordion .bg.bg-mmo .group-mmo {
  background: #0d5a64;
}
.x-accordion.accordion .bg.bg-mmo .group-mmo ul.subgroup li.small {
  border-bottom: 1px solid #19646e;
}
.x-accordion.accordion .bg.bg-mmo:last-child {
  border-bottom: 1px solid #39837d;
}
.x-accordion.accordion .bg.bg-mmo:first-child {
  border-radius: 4px 4px 0 0;
}
.x-accordion.accordion .bg.bg-mmo:first-child .group {
  border-top: 1px solid #4faba3;
}

.productx {
  width: 100%;
  height: 100%;
  position: relative;
}
.productx .suit-image {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  cursor: pointer;
  -webkit-filter: brightness(95%);
  filter: brightness(95%);
}
.productx .suit-image img {
  -webkit-filter: brightness(95%);
  filter: brightness(95%);
}
.productx:hover .suit-image img {
  -webkit-filter: brightness(100%);
  filter: brightness(100%);
}
.productx .play {
  cursor: pointer;
  opacity: 0;
}
.productx .play img {
  position: absolute;
  width: 50px;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
}
.productx:hover .play {
  background-color: rgba(204, 207, 217, 0.8);
  opacity: 1;
}
.action-panel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}
.action-panel a {
  text-decoration: none;
}
.number-list {
  font-family: "Oswald", "Tahoma", sans-serif;
}
.cursor-pointer {
  cursor: pointer;
  width: 100%;
  border: 1px solid red;
}
.pointer {
  cursor: pointer;
}
.dropdown-menu.contact-list a {
  text-decoration: none !important;
}
.dropdown-menu.contact-list {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  font-family: "Lato", "Tahoma", sans-serif;
  font-size: 12px;
  -webkit-box-shadow: inset 0px 7px 5px -5px rgba(0, 0, 0, 0.5);
  box-shadow: inset 0px 7px 5px -5px rgba(0, 0, 0, 0.5);
}
.dropdown-menu.contact-list .contact-1 {
  line-height: 18px;
}
.dropdown-menu.contact-list .contact-2 {
  font-size: 14px;
  font-weight: 600;
}
.game-warning {
  color: #ffffffcc;
  font-family: "Oswald", "Tahoma", sans-serif;
  font-size: 1rem;
}
.bg-soccer {
  background: #0d3a64 url(/images/landing2/slider_bg.jpg);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-blend-mode: overlay;
  width: 100%;
  height: 100%;
}
.embedded iframe {
  width: 100% !important;
  height: 100vh !important;
  background-color: #ffffff88;
  position: absolute;
  top: 0;
  left: 0;
  border: 0;
  -webkit-box-shadow: 0 0 30px black;
  box-shadow: 0 0 30px black;
  padding: 0;
  margin: 0;
}
.embedded a {
  display: none !important;
}
.dropdown-menu-large {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  font-family: "Lato", "Tahoma", sans-serif;
  font-size: 12px;
  -webkit-box-shadow: inset 0px 7px 5px -5px rgba(0, 0, 0, 0.5);
  box-shadow: inset 0px 7px 5px -5px rgba(0, 0, 0, 0.5);
  max-height: 400px;
  width: 240px;
  line-height: 1;
  padding: 0 4px;
}
.dropdown-menu-large > ul {
  height: 100%;
  overflow: auto;
  list-style: none;
  margin: 0;
  padding: 0;
}
.dropdown-menu-large > ul > li > ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.dropdown-menu-large > ul > li > ul > li {
  list-style: none;
}
.dropdown-menu-large > ul > li > ul > li > a {
  display: block;
  padding: 0;
  clear: both;
  font-weight: normal;
  line-height: 20px;
  color: #333333;
  white-space: normal;
  padding-left: 0.5rem;
}
.dropdown-menu-large > ul > li ul > li > a:hover,
.dropdown-menu-large > ul > li ul > li > a:focus {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5;
}
.dropdown-menu-large .disabled > a,
.dropdown-menu-large .disabled > a:hover,
.dropdown-menu-large .disabled > a:focus {
  color: #999999;
}
.dropdown-menu-large .disabled > a:hover,
.dropdown-menu-large .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  cursor: not-allowed;
}
.dropdown-menu-large .dropdown-header {
  color: #428bca;
  font-size: 14px;
  padding: 0;
  margin: 0;
  line-height: 24px;
}
.hx-court {
  width: 18px;
  height: 18px;
  fill: rgba(0, 0, 0, 0.6);
}
.x-accordion.accordion .bg.bg-img .group {
  padding: 0;
  margin: 0;
}
.x-accordion.accordion .bg.bg-img .group img {
  width: 100%;
  height: 100%;
  opacity: 1;
}
.x-accordion.accordion .bg.bg-img .group .hx-btn {
  position: absolute;
  top: 7px;
  right: 6px;
  padding: 6px 4px;
  border: 1px solid #ffffff33;
  background: #ffffff11;
  line-height: 1;
  border-radius: 3px;
  color: #a5cae5;
}
.x-accordion.accordion .bg.bg-img .group .hx-btn:hover {
  border: 1px solid #ffffff66;
  background: #ffffff22;
}
.x-accordion.accordion .bg.bg-img .group .hx-btn:active {
  border: 1px solid #ffffff11;
  background: #ffffff00;
}
.x-accordion.accordion .bg.bg-img .group .hx-btn-invisible {
  height: 40px;
  position: absolute;
  top: 1px;
  left: 1px;
  width: 180px;
  border-top-left-radius: 3px;
}
.x-accordion.accordion .bg.bg-img .group .hx-btn-invisible:hover {
  background: #ffffff22;
}
.x-accordion.accordion .bg.bg-img .group .hx-btn-invisible:active {
  background: #ffffff11;
  -webkit-box-shadow: inset 0 0 5px 0 #000000;
  box-shadow: inset 0 0 5px 0 #000000;
}
.x-accordion.accordion .bg.bg-img .group .hx-btn-invisible.selected {
  background: #ffffff11;
  -webkit-box-shadow: inset 0 0 5px 0 #000000;
  box-shadow: inset 0 0 5px 0 #000000;
}
.x-accordion.accordion .bg.bg-img .group .hx-btn-invisible.selected:hover {
  background: #ffffff22;
  -webkit-box-shadow: inset 0 0 5px 0 #000000;
  box-shadow: inset 0 0 5px 0 #000000;
}
#pills-waiting .grey,
#pills-waiting .ticket-info {
  background: #f9f0d8;
}
#pills-waiting .bet-type.blue {
  color: #805a4e !important;
}
.h-100v {
  height: 100vh !important;
}
.dc-video-player-wrapper {
  width: 100% !important;
  height: 100% !important;
}
.preloader-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 1500;
}
.preloader {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1500;
}
.loader-wrapper {
  position: absolute;
  height: calc(100vh - 156px);
}
.loader-wrapper .loader {
  margin: -75px 0 0 -75px !important;
}
.loader {
  display: block;
  position: relative;
  left: 50%;
  top: 50%;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border-radius: 50%;
  border: 5px solid transparent;
  border-top-color: #11749bcc;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}
.loader:before {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border-radius: 50%;
  border: 4px solid transparent;
  border-top-color: #0f4f8ccc;
  -webkit-animation: spin 3s linear infinite;
  animation: spin 3s linear infinite;
}
.loader:after {
  content: "";
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #0a234ccc;
  -webkit-animation: spin 1.5s linear infinite;
  animation: spin 1.5s linear infinite;
}
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.round-alert {
  background: #092a4bee;
  color: #a5cae5;
  text-align: center;
  font-size: 12px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  line-height: 35px;
  border-collapse: collapse;
  border-radius: 3px;
  border-top: 1px solid #4f7eabcc;
  border-left: 1px solid #4f7eabcc;
  border-right: 1px solid #2f669bcc;
  border-bottom: 1px solid #2f669bcc;
  -webkit-box-shadow: 0 0 3px #00000088;
  box-shadow: 0 0 3px #00000088;
}
@-webkit-keyframes heartbeat {
  0% {
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
    opacity: 0.5;
  }
  20% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
  40% {
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
    opacity: 0.5;
  }
  60% {
    -webkit-transform: scale(1.25);
    transform: scale(1.25);
    opacity: 1;
  }
  80% {
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
    opacity: 0.5;
  }
  100% {
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
    opacity: 0.5;
  }
}
@keyframes heartbeat {
  0% {
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
    opacity: 0.5;
  }
  20% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
  40% {
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
    opacity: 0.5;
  }
  60% {
    -webkit-transform: scale(1.25);
    transform: scale(1.25);
    opacity: 1;
  }
  80% {
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
    opacity: 0.5;
  }
  100% {
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
    opacity: 0.5;
  }
}
.content .logo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-animation: heartbeat 2s infinite;
  animation: heartbeat 2s infinite;
}
.content .logo img {
  height: 36px;
  opacity: 0.5;
  display: block;
}
.timer {
  color: #014273;
  font-size: 11px;
  margin-left: 4px;
  width: 13px;
}
.blink_me {
  -webkit-animation: blinker 1s linear infinite;
  animation: blinker 1s linear infinite;
}
@-webkit-keyframes blinker {
  50% {
    opacity: 1;
  }
  75% {
    opacity: 0;
  }
}
@keyframes blinker {
  50% {
    opacity: 1;
  }
  75% {
    opacity: 0;
  }
}
.invisible,
.invisible td {
  height: 0 !important;
  border: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
}
@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.main-item {
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-collapse: collapse;
}
.main-item .visible {
  overflow: visible;
}
.main-item .preloader {
  height: 100px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.main-item .list-item {
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.fade-me-in {
  -webkit-animation: fadein 500ms;
  animation: fadein 500ms;
}
.fade-enter-active,
.fade-leave-active {
  -webkit-transition: opacity 0.5s;
  -o-transition: opacity 0.5s;
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.modal-backdrop.show {
  -webkit-backdrop-filter: grayscale(100%);
  backdrop-filter: grayscale(100%);
}
#static-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -100;
  overflow: hidden;
  background: #0f4f8c;
  background: -o-radial-gradient(center, ellipse, #0f4f8c 0%, #052442 100%);
  background: radial-gradient(ellipse at center, #0f4f8c 0%, #052442 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0f4f8c', endColorstr='#052442', GradientType=1);
}
.minimal {
  min-width: 0;
  min-height: 0;
}
.lds-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 1.5rem;
  overflow: hidden !important;
}
.lds-roller {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}
.lds-roller div {
  -webkit-animation: lds-roller 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  animation: lds-roller 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  -webkit-transform-origin: 40px 40px;
  -ms-transform-origin: 40px 40px;
  transform-origin: 40px 40px;
}
.lds-roller div:after {
  content: " ";
  display: block;
  position: absolute;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #fff;
  margin: -4px 0 0 -4px;
}
.lds-roller div:nth-child(1) {
  -webkit-animation-delay: -0.036s;
  animation-delay: -0.036s;
}
.lds-roller div:nth-child(1):after {
  top: 63px;
  left: 63px;
}
.lds-roller div:nth-child(2) {
  -webkit-animation-delay: -0.072s;
  animation-delay: -0.072s;
}
.lds-roller div:nth-child(2):after {
  top: 68px;
  left: 56px;
}
.lds-roller div:nth-child(3) {
  -webkit-animation-delay: -0.108s;
  animation-delay: -0.108s;
}
.lds-roller div:nth-child(3):after {
  top: 71px;
  left: 48px;
}
.lds-roller div:nth-child(4) {
  -webkit-animation-delay: -0.144s;
  animation-delay: -0.144s;
}
.lds-roller div:nth-child(4):after {
  top: 72px;
  left: 40px;
}
.lds-roller div:nth-child(5) {
  -webkit-animation-delay: -0.18s;
  animation-delay: -0.18s;
}
.lds-roller div:nth-child(5):after {
  top: 71px;
  left: 32px;
}
.lds-roller div:nth-child(6) {
  -webkit-animation-delay: -0.216s;
  animation-delay: -0.216s;
}
.lds-roller div:nth-child(6):after {
  top: 68px;
  left: 24px;
}
.lds-roller div:nth-child(7) {
  -webkit-animation-delay: -0.252s;
  animation-delay: -0.252s;
}
.lds-roller div:nth-child(7):after {
  top: 63px;
  left: 17px;
}
.lds-roller div:nth-child(8) {
  -webkit-animation-delay: -0.288s;
  animation-delay: -0.288s;
}
.lds-roller div:nth-child(8):after {
  top: 56px;
  left: 12px;
}
@-webkit-keyframes lds-roller {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes lds-roller {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.w-140 {
  width: 140px;
  min-width: 140px;
  max-width: 140px;
}