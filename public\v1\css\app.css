:root {
  --market-tab-height: 45px;
}

.back-link {
  color: #ffd452cc;
  font-weight: 600;
  text-transform: uppercase;
  font-family: "Lato";
  padding-right: 16px;
  padding-left: 8px;
  margin-right: 16px;
  border-right: 1px solid #ffffff44;
}

.back-link:hover {
  color: #ffd452;
}

.back-link i {
  margin-right: 4px;
}

.back-link a {
  color: #ffd452cc;
  font-weight: 600;
  text-transform: uppercase;
}

.back-link a:hover {
  color: #ffd452;
}

.topbar.info-top .hl-container a {
  letter-spacing: 1px;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 16px;
  font-family: "Lato", sans-serif;
  text-decoration: none;
  line-height: 24px;
  color: #ffd452cc;
  margin-right: 16px;
  margin-left: 8px;
  padding-right: 16px;
  border-right: 1px solid #ffffff44;
}

.topbar.info-top .hl-container a:hover {
  color: #ffd452;
}

.topbar.info-top .hl-container i {
  margin-right: 4px;
}

.xx-1 {
  height: 18px !important;
  line-height: 18px !important;
  margin-top: 0 !important;
}

.noscrollbar {
  overflow: hidden !important;
}

.content .left ul.subgroup li a .flex-fill.subtitle .sub-name {
  font-size: 12px;
  font-weight: 500;
}

.content .left ul.subgroup li a .flex-fill.subtitle .sub-icon {
  font-size: 12px;
  height: 16px;
  padding-left: 8px;
  padding-right: 6px;
}

.content .left ul.subgroup li a .flex-fill.subtitle .sub-icon img {
  height: 16px;
}

.info-content.hl-mod1 {
  padding: 0 !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.info-content.hl-mod1 .logo img {
  height: 36px !important;
}

.info-content.hl-mod1 .logo {
  margin-right: 0 !important;
}

.noscrollbar {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

.video-refresh {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
  cursor: pointer;
  margin: 8px 12px;
  font-size: 16px;
  color: #ffffff;
  text-shadow: 0 0 16px #000;
  background: #00000088;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding-top: 1px;
}

.video-refresh:hover {
  color: #ff0;
}

.content .left.active .vgames-link .subtitle.flex-fill {
  text-align: center;
}

.content .left.active .vgames #heading-vgames .group {
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-full.carousel-item .banner-image {
  max-width: 600px;
  bottom: -30px;
}

@media(min-width: 576px) {
  .banner-full.carousel-item .banner-image {
    max-width: 600px;
    bottom: -30px;
  }
}

@media(min-width: 768px) {
  .banner-full.carousel-item .banner-image {
    width: 90%;
    max-width: 800px;
    bottom: -30px;
  }
}

@media(min-width: 960px) {
  .banner-full.carousel-item .banner-image {
    width: 100%;
    max-width: 800px;
    bottom: 0;
  }
}

@media(min-width: 1280px) {
  .banner-full.carousel-item .banner-image {
    width: 70%;
    max-width: 950px;
    bottom: -30px;
  }
}

.mini-game-bg,
.mini-game-container {
  position: relative;
}

.mini-game-bg {
  z-index: 0;
  width: 100%;
  border-radius: 0 0 5px 5px;
}

.mini-game-container .slide {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  top: 0;
  left: 0;
}

.mini-game {}

.mini-game-thumb {
  width: 33%;
  padding: 0;
  -webkit-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
}

.mini-game-thumb img {
  width: 100%;
  cursor: pointer;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.mini-game-thumb:hover {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.mini-game-container .carousel-inner {
  height: auto;
  width: 100%;
  padding: 0 20px
}

.mini-game-container .carousel-item {
  padding: 0
}

.mini-game-container .carousel-control-next,
.mini-game-container .carousel-control-prev {
  width: 28px;
  text-align: center
}

.mini-game-container .carousel-indicators {
  margin-bottom: 0
}

.mini-game-container .carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%232379C4' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
}

.mini-game-container .carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%232379C4' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
}

.mini-game-container .carousel-indicators .active {
  background-color: #2379C4;
  opacity: .5;
}

.luckybox {
  color: #0A4782;
  font-size: 10px;
  width: 100%;
  font-family: Roboto, Tahoma, -apple-system, system-ui, BlinkMacSystemFont, Helvetica Neue, sans-serif
}

.luckyitem {
  border-top: 1px solid rgba(0, 0, 0, .05);
  padding: 4px 8px
}

.luckyeven {
  background: rgba(0, 0, 0, .05);
}

.luckybettype {
  font-size: 10px;
  color: #0d3a64;
  line-height: 1
}

.luckyvs {
  color: #545454;
  line-height: 1;
  padding-right: 4px
}

.luckyteam {
  font-size: 11px;
  line-height: 1;
  padding: 2px 0;
  color: #01122b;
  font-weight: 600
}

.luckytarget {
  color: #912817
}

.luckyodds {
  /* color: #fff; */
  font-size: 12px;
  font-weight: 700;
  line-height: 1;
  padding: 2px 0
}

.luckymuted {
  color: #000;
  font-weight: 400
}

.luckytext {
  color: #212529;
  font-weight: 400
}

.luckybox .btn-2 {
  height: 25px;
  border: 1px solid #b28705;
  font-weight: 700;
  line-height: 1;
  font-size: 11px;
  margin: 2px;
  background: transparent linear-gradient(90deg, #145693 0%, #276FA8 100%) 0% 0% no-repeat padding-box;
  border: 0;
  color: #FFFFFF;
}

.luckybox .btn-2:hover {
  background: #ffd452;
  -webkit-box-shadow: 0 1px 0 hsla(0, 0%, 100%, .5333333333) inset, 1px 1px 0 hsla(0, 0%, 100%, .2666666667) inset, 0 0 3px rgba(0, 0, 0, .2666666667);
  box-shadow: inset 0 1px 0 hsla(0, 0%, 100%, .5333333333), inset 1px 1px 0 hsla(0, 0%, 100%, .2666666667), 0 0 3px rgba(0, 0, 0, .2666666667);
  color: #000;
}

.luckybox .btn-1 {
  height: 25px;
  font-weight: 700;
  line-height: 1;
  font-size: 11px;
  margin: 2px;
  color: hsla(0, 0%, 100%, .8);
  background-color: #7799B7;
  border: 0;
}

.luckybox .btn-1:hover {
  background: #345684;
}

.luckyslider {
  width: 100%;
  padding: 0 12px
}

.luckyslider-label {
  padding-left: 4px;
  padding-right: 4px;
  color: #0A4782;
}

.luckyslider .vue-slider-rail {
  background-color: #9CC2E3;
}

.vue-slider:hover .vue-slider-rail {
  background-color: #9CC2E3 !important;
}

.luckyslider .vue-slider-rail .vue-slider-process {
  background-color: #419120;
}

.vue-slider:hover .vue-slider-process {
  background-color: #419120 !important;
}

.luckyslider .vue-slider-rail .vue-slider-dot-handle {
  background-color: #419120;
  border: 2px solid #419120;
}

.vue-slider:hover .vue-slider-dot-handle {
  background-color: #419120 !important;
  border: 2px solid #419120 !important;
}

.luckybox .switch-wrap {
  margin-left: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding-right: 4px
}

.luckybox .switch-wrap .label {
  padding-right: 2px
}

.luckybox .switch-wrap .switch {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 16px;
  margin-bottom: 0;
  top: 0
}

.luckybox .switch-wrap .switch input {
  opacity: 0;
  width: 0;
  height: 0
}

.luckybox .switch-wrap .switch input:checked+.slider {
  background-color: #419120;
}

.luckybox .switch-wrap .switch input:checked+.slider:before {
  -webkit-transform: translateX(14px);
  transform: translateX(14px)
}

.luckybox .switch-wrap .switch input:focus+.slider {
  -webkit-box-shadow: 0 0 1px #4ed164;
  box-shadow: 0 0 1px #4ed164
}

.luckybox .switch-wrap .switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #5991C1;
  -webkit-transition: .4s;
  transition: .4s
}

.luckybox .switch-wrap .switch .slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 2px;
  bottom: 1px;
  background-color: #fff;
  -webkit-transition: .4s;
  transition: .4s
}

.luckybox .switch-wrap .switch .slider.round {
  border-radius: 10px 10px 10px 10px
}

.luckybox .switch-wrap .switch .slider.round:before {
  border-radius: 50%
}

.luckydelete {
  padding: 2px 4px;
  margin: 0 0 0 12px;
  border: 1px solid hsla(0, 0%, 100%, .2666666667);
  border-radius: 3px;
  cursor: pointer;
  background: #7799B7;
  color: #fff;
}

.luckydelete:hover {
  background: #345684;
  color: #fff
}

.luckydelete:active {
  background: hsla(0, 0%, 100%, .1333333333);
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #fff
}

.vue-slider-dot-tooltip-inner {
  font-family: Tahoma, -apple-system, system-ui, BlinkMacSystemFont, Helvetica Neue, sans-serif;
  font-size: 11px !important
}

.vue-slider-marks {
  display: none !important
}

@-webkit-keyframes blinker {
  0% {
    opacity: 1
  }

  50% {
    opacity: .5
  }

  to {
    opacity: 1
  }
}

@-moz-keyframes blinker {
  0% {
    opacity: 1
  }

  50% {
    opacity: .5
  }

  to {
    opacity: 1
  }
}

@-ms-keyframes blinker {
  0% {
    opacity: 1
  }

  50% {
    opacity: .5
  }

  to {
    opacity: 1
  }
}

@keyframes blinker {
  0% {
    opacity: 1
  }

  50% {
    opacity: .5
  }

  to {
    opacity: 1
  }
}

.blink3 {
  -webkit-animation: blinker 1s linear 3;
  -moz-animation: blinker 1s linear 3;
  -ms-animation: blinker 1s linear 3;
  animation: blinker 1s linear 3
}

.hot-blink {
  -webkit-animation: blink 2s step-start 0s infinite;
  -moz-animation: blink 2s step-start 0s infinite;
  -ms-animation: blink 2s step-start 0s infinite;
  animation: blink 2s step-start 0s infinite;
  text-shadow: 0 0 10px #ffd452;
}

.game-list {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: scroll
}

.game-list .row .col {
  color: #fff;
  background-color: transparent;
  background-clip: border-box;
  border: 0;
  opacity: .9;
  margin: 15px 0;
}

.game-list .row .col:hover {
  cursor: pointer;
  opacity: 1
}

.game-list .row .col img:hover {
  -webkit-filter: drop-shadow(0 0 15px #fff);
  filter: drop-shadow(0 0 15px #fff);
}

.game-list .card {
  color: #fff;
  background-color: transparent;
  background-clip: border-box;
  border: 0;
  border-radius: .25rem;
  overflow: hidden;
  opacity: .8;
  margin: 5px 0
}

.game-list .card:hover {
  cursor: pointer;
  opacity: 1;
  -webkit-box-shadow: hsla(0, 0%, 100%, .8) 0 0 3px 0, #000 0 0 1px 0;
  box-shadow: 0 0 3px 0 hsla(0, 0%, 100%, .8), 0 0 1px 0 #000
}

.game-list .card-body {
  -ms-flex: 1 1 auto;
  -webkit-box-flex: 1;
  flex: 1 1 auto;
  min-height: 0;
  padding: 0;
  text-align: center;
  overflow: hidden
}

.game-list .card-body img {
  width: 100%
}

.game-list .card-columns {
  -webkit-column-count: 3;
  -moz-column-count: 3;
  column-count: 3;
  -webkit-column-gap: 1.25rem;
  -moz-column-gap: 1.25rem;
  column-gap: 1.25rem
}

@media screen and (max-width: 640px) {
  .game-list .card-columns {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3
  }
}

@media screen and (min-width: 640px) {
  .game-list .card-columns {
    -webkit-column-count: 4;
    -moz-column-count: 4;
    column-count: 4
  }
}

@media screen and (min-width: 1024px) {
  .game-list .card-columns {
    -webkit-column-count: 5;
    -moz-column-count: 5;
    column-count: 5
  }
}

@media screen and (min-width: 1366px) {
  .game-list .card-columns {
    -webkit-column-count: 6;
    -moz-column-count: 6;
    column-count: 6
  }
}

@media screen and (min-width: 1600px) {
  .game-list .card-columns {
    -webkit-column-count: 7;
    -moz-column-count: 7;
    column-count: 7
  }
}

@media screen and (min-width: 1920px) {
  .game-list .card-columns {
    -webkit-column-count: 8;
    -moz-column-count: 8;
    column-count: 8
  }
}

.game-list .btn-outline-primary {
  color: #c146a1;
  border-width: 2px;
  border-style: solid;
  -o-border-image: linear-gradient(90deg, #08627e, #a80077) 1;
  border-image: -ms-gradient(linear, left top, right top, from(#08627e), to(#a80077)) 1;
  border-image: -webkit-gradient(linear, left top, right top, from(#08627e), to(#a80077)) 1;
  border-image: linear-gradient(90deg, #08627e, #a80077) 1;
  border-radius: 0;
  -webkit-box-shadow: 0 0 5px 1px rgba(0, 0, 0, .5333333333);
  box-shadow: 0 0 5px 1px rgba(0, 0, 0, .5333333333);
  text-shadow: 0 0 3px rgba(0, 0, 0, .5333333333);
  font-family: Oswald;
  min-width: 160px
}

.game-list .btn-outline-primary.active,
.game-list .btn-outline-primary:hover {
  color: #fcc8ee;
  background: #e06d7f;
  background: -webkit-gradient(linear, left top, left bottom, from(#e06d7f), to(#a849a3));
  background: linear-gradient(180deg, #e06d7f 0, #a849a3);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#e06d7f", endColorstr="#a849a3", GradientType=0)
}

.game-list .col {
  max-width: 200px;
  text-align: center;
  vertical-align: middle
}

.game-list .badge-left,
.game-list .badge-right {
  position: absolute;
  width: 64px;
  height: 64px;
  overflow: hidden;
  text-align: center;
  vertical-align: middle
}

.game-list .badge-left {
  top: -8px;
  left: 8px
}

.game-list .badge-right {
  top: -8px;
  left: 120px
}

.game-list .badge-left img,
.game-list .badge-right img {
  width: 64px;
  height: 64px
}

.jili-wrapper {
  height: calc(100% - 108px);
  background: #00000088;
  color: #c146a1;
  border-width: 2px;
  border-style: solid;
  border-image: linear-gradient(to right, #08627e, #a80077) 1;
  border-radius: 0;
  box-shadow: 0 0 5px 1px #00000088;
  text-shadow: 0 0 3px #00000088;
  font-family: "Oswald";
}

.jili-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.jili-bg {
  background: #12083D url('../../img/lobby/jili-bg.jpg')top center no-repeat;
  background-attachment: fixed;
  background-size: cover;
}

.jili-logo {
  background: #0C0529;
  padding: 0.75rem 4rem;
  border-bottom-left-radius: 3rem;
  border-bottom-right-radius: 3rem;
}

.live22-bg {
  background: #12083D url('../../img/lobby/live22-bg.jpg')top center no-repeat;
  background-attachment: fixed;
  background-size: cover;
}


.slider {
  padding: 0;
}

.slider .slick-track {
  height: auto;
  padding: 0;
}

.slick-slide .esports-game-thumb {
  margin: 0;
  border-radius: 5px
}

.slick-slide.slick-current .esports-game-thumb {}

.slick-slide.slick-current .esports-game-thumb img {
  -webkit-box-shadow: inset 0 0 4px #fff, 0 0 4px #00000088;
  box-shadow: inset 0 0 4px #fff, 0 0 4px #00000088;
  height: 95px;
  width: 270px;
}

.slick-slide .esports-game-thumb img {
  padding: 0;
  border-radius: 5px
}

.esports-main .slick-slide .esports-game-thumb img {
  opacity: .5;
}

.esports-main .slick-slide.slick-current.slick-active.slick-center .esports-game-thumb img {
  opacity: 1;
}

.esports {
  position: relative;
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  padding: 1px 1px 4px 1px
}

.esports-heroes.sports-52 {
  background: url(/images/esports/video-bg/video-bg-dragonball.png) no-repeat;
  background-size: cover;
}

.esports-heroes.sports-51 {
  background: url(/images/esports/video-bg/video-bg-marble-survival.png) no-repeat;
  background-size: cover;
}

.esports-heroes.sports-50 {
  background: url(/images/esports/video-bg/video-bg-marble-clash.png) no-repeat;
  background-size: cover;
}

.esports-heroes.sports-49 {
  background: url(/images/esports/video-bg/video-bg-marble.png) no-repeat;
  background-size: cover;
}

.esports-heroes.sports-47 {
  background: url(/images/esports/video-bg/video-bg-tk8.png) no-repeat;
  background-size: cover;
}

.esports-heroes.sports-46 {
  background: url(/images/esports/video-bg/video-bg-speedway.png) no-repeat;
  background-size: cover;
}

.esports-heroes.sports-45 {
  background: url(/images/esports/video-bg/video-bg-greyhound.png) no-repeat;
  background-size: cover;
}

.esports-heroes.sports-44 {
  background: url(/images/esports/video-bg/video-bg-mk.png) no-repeat;
  background-size: cover;
}

.esports-heroes.sports-43 {
  background: url(/images/esports/video-bg/video-bg-kof.png) no-repeat;
  background-size: cover;
}

.esports-heroes.sports-42 {
  background: url(/images/esports/video-bg/video-bg-ufc.png) no-repeat;
  background-size: cover;
}

.esports-heroes.sports-41 {
  background: url(/images/esports/video-bg/video-bg-sf6.png) no-repeat;
  background-size: cover;
}

.esports-heroes.sports-53 {
  background: url(/images/esports/video-bg/video-bg-naruto.png) no-repeat;
  background-size: cover;
}

.landing .logo img {
  width: 100%;
  max-width: 320px
}

.mini-bg1 {
  background: url(/images/bg2.png);
  background-size: auto 100%;
  border-radius: 0 0 5px 5px;
}

.mini-bg1,
.mini-bg1 .productx {
  overflow: hidden
}

.w-270 {
  width: 270px;
  min-width: 270px;
  max-width: 270px
}

.esports-heroes iframe {
  background: rgba(0, 0, 0, .5333333333)
}

.e-event {
  text-align: center
}

.e-away-team,
.e-home-team {
  padding-top: 0;
  padding-left: 8px;
  padding-bottom: 0;
  padding-right: 4px;
  text-align: left;
  -webkit-box-flex: 1 !important;
  -ms-flex: 1 1 auto !important;
  flex: 1 1 auto !important;
  overflow: hidden;
  overflow-wrap: break-word;
  line-height: .8rem;
  color: #fff;
}

.e-home-team {
  padding-left: 4px;
  padding-right: 8px;
  text-align: right;
}

.esports-cell {
  min-height: 50px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin: 1px 0;
  overflow: hidden
}

.esports-body .esports-cell {
  height: 50px
}

.ef-bet {
  position: relative;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  overflow: hidden;
  max-width: 120px;
}

.ef-bet .bet-value {
  cursor: pointer
}

.ef-bet.ef-gh .bet-value,
.ef-bet.ef-mc .bet-value {
  cursor: pointer;
  width: 100%;
  height: 100%;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-line-pack: center;
  align-content: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center
}

.ef-bet.ef-mc {
  max-width: 100%;
}

.esports-cell .ball-value {
  color: hsla(0, 0%, 100%, .5333333333);
  line-height: 1.1rem;
  font-weight: 400;
  font-size: .75rem;
  min-height: 1rem;
  width: 100%;
  background: rgba(0, 0, 0, .4)
}

.esports-cell .bet-value {
  font-weight: 700;
  line-height: 1.3rem;
  min-height: 1.3rem;
  font-size: .85rem;
  -webkit-box-flex: 1 !important;
  -ms-flex: 1 1 auto !important;
  flex: 1 1 auto !important;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center
}

.esports-head .esports-row.bottom-line {
  background-color: rgba(5, 40, 61, .8);
  color: #fff;
}

@keyframes blinker {
  50% {
    opacity: 0
  }
}

.live-now {
  -webkit-animation: blinker 2s cubic-bezier(.5, .2, 1, 1) infinite alternate;
  animation: blinker 2s cubic-bezier(.5, .2, 1, 1) infinite alternate
}

.game-top-line {}

.e-event.horiz {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}

.e-event.horiz .e-date {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 16px;
  padding-right: 4px
}

.e-event.horiz .e-time {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 16px;
}

.e-event.horiz .e-no {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 16px;
}

.esports-body .esports-cell.esports-cell-sm {
  height: 32px;
  min-height: 32px;
  width: 100%;
}

.esports-body .esports-cell.esports-cell-sm .live-now {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 4px 8px;
  background-color: #e61839;
  border-radius: 3px 3px 3px 3px;
  font-weight: 700;
}

.esports-body .esports-cell.esports-cell-sm .live-now img {
  width: 15px;
}

.esports-body .esports-cell.esports-cell-sm .live-now span {
  font-size: 11px;
  margin: 0 6px;
}

.esports-body .esports-cell.esports-cell-sm .live-now .fa-circle {
  font-size: 6px;
}

.game-cell.game-cell-sm {
  min-height: 30px;
}

.bg-win1 {
  background-color: #e61839;
}

.bg-win2 {
  background-color: #08627e;
}

.bg-win3 {
  background-color: #4ed164;
}

.esports-main .slick-prev {
  left: 0;
  z-index: 1;
}

.esports-main .slick-next {
  right: 0;
  z-index: 1;
}

.esports-heroes.esports-play {
  height: 494px;
}

.esports-heroes.esports-play iframe {
  width: 100%;
}

.content .left .vgames {
  padding: 0;
  border: 0;
  margin: 0;
  border-radius: 5px;
}

.color1 .content .left .vgames .group,
.color2 .content .left .vgames .group,
.color3 .content .left .vgames .group,
.color4 .content .left .vgames .group,
.content .left .vgames .group.selected {
  background: none;
}

#rain-effect,
#rain-effect2,
#rain-effect3 {
  width: 100%;
  height: 100%;
  position: absolute;
}

.rain-effect {
  width: 40px;
  height: 40px;
  position: absolute;
  background: url(https://r.myw0011001.com/images/landing2/cny_angbow.png);
  background-size: 100% 100%;
}

.rain-effect2 {
  width: 20px;
  height: 20px;
}

.rain-effect2,
.rain-effect3 {
  position: absolute;
  background: url(https://r.myw0011001.com/images/landing2/cny_coin.png);
  background-size: 100% 100%;
}

.rain-effect3 {
  width: 15px;
  height: 15px;
}

.worldcup h1 {
  text-align: center;
}

.worldcup h1 img {
  width: 70%;
  max-width: 250px;
  margin: -20px auto 0;
}

.worldcup-timer {
  width: 100%;
  min-width: 300px;
  margin: 0 auto 20px;
  position: relative;
  display: block;
}

.worldcup-timer .timer-logo {
  background-color: #970037;
  padding: 20px 80px 0 80px;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
}

.worldcup-timer .timer-logo:after {
  background-image: url(https://r.myw0011001.com/images/landing2/wordcup_deco_mobile.svg);
  content: "";
  background-size: 100%;
  background-position: 100%;
  right: 0;
  bottom: -30px;
  width: 100%;
  height: 30px;
  position: absolute;
}

.worldcup-timer .timer-logo img {
  max-width: 200px;
  margin: 0 auto;
}

.worldcup-timer-wrapper {
  background-image: url(https://r.myw0011001.com/images/landing2/worldcup_bg.jpg);
  background-size: cover;
  padding: 25px 0;
  font-family: Oswald, sans-serif;
}

.worldcup-timer-wrapper h4 {
  margin-top: 15px;
}

.worldcup-timer #timer {
  font-size: 2.2em;
  font-weight: 100;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  max-width: 400px;
}

.worldcup-timer #timer div {
  width: 25%;
  font-weight: 700;
  text-align: center;
}

.worldcup-timer #timer div span {
  color: #fff;
  font-size: .85rem;
  font-weight: 400;
  text-transform: uppercase;
  display: block;
}

@media(min-width: 960px) {
  .worldcup-timer {
    width: 100%;
    min-width: auto;
    max-width: 1000px;
    margin: 30px auto 80px auto;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .worldcup-timer-wrapper h4 {
    margin-top: 0;
    font-size: 1.8rem;
  }

  .worldcup-timer .timer-logo {
    width: 30%;
    padding: 30px;
  }

  .worldcup-timer .timer-logo img {
    max-width: 250px;
    margin: 0 auto;
  }

  .worldcup-timer .timer-logo:after {
    background-image: url(https://r.myw0011001.com/images/landing2/wordcup_deco.svg);
    content: "";
    background-size: 100%;
    background-position: 100%;
    right: -50px;
    top: 0;
    width: 50px;
    height: 100%;
    position: absolute;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  .worldcup-timer #timer {
    width: 100%;
    max-width: 400px;
    font-size: 2em;
  }

  .worldcup-timer #timer div span {
    font-size: .35em;
  }
}

@media(min-width: 1280px) {
  .worldcup-timer {
    width: 120%;
    min-width: auto;
    max-width: 1200px;
    margin: 30px -10% 80px -10%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .worldcup-timer #timer {
    font-size: 3em;
    max-width: 600px;
  }

  .worldcup h1 {
    text-align: left;
  }

  .worldcup h1 img {
    width: 70%;
    max-width: 450px;
    margin: 0 auto;
  }
}

.content .left .worldcup2022 {
  background-image: url('../../img/worldcup2022.jpg');
  width: 100%;
  padding: 0;
  border: 0;
  margin: 0;
}

.color1 .content .left .worldcup2022 .group,
.color2 .content .left .worldcup2022 .group,
.color3 .content .left .worldcup2022 .group,
.color4 .content .left .worldcup2022 .group,
.content .left .worldcup2022 .group.selected {
  background: none;
}

header .toolbar .menu ul.nav li.nav-item a.nav-link.feedback-button {
  color: #212529;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
  -webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.5333333333) inset, 1px 1px 0 rgba(255, 255, 255, 0.2666666667) inset;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.5333333333) inset, 1px 1px 0 rgba(255, 255, 255, 0.2666666667) inset;
  background: #ffc107;
  border: 1px solid #b28705;
}

header .toolbar .menu ul.nav li.nav-item a.nav-link.feedback-button:hover {
  background: #ffd452;
  -webkit-box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset, 0 0 3px #00000044;
  box-shadow: 0 1px 0 #ffffff88 inset, 1px 1px 0 #ffffff44 inset, 0 0 3px #00000044;
}

.mr-16px {
  margin-right: 16px;
}

.special-event.carousel-item .banner-text,
.special-event.carousel-item .banner-text p {
  color: #000;
  text-shadow: 1px 1px 5px rgba(255, 255, 255, 0.9);
}

.special-event.carousel-item .banner-text .mobile .special-event-title {
  margin: -20px auto 0 auto;
  max-width: 400px;
}

@media (min-width: 768px) {
  .special-event.carousel-item .banner-text .mobile .special-event-title {
    max-width: 550px;
  }

  .special-event.carousel-item .banner-text .desktop {
    display: none;
  }

  .special-event.carousel-item .banner-text .mobile {
    display: block;
  }

  .special-event.carousel-item .banner-text .mobile .special-event-title {
    margin: -10px auto 0 auto;
  }

  .carousel-inner {
    height: 800px;
  }
}

@media (min-width:1280px) {
  .special-event.carousel-item .banner-text .desktop {
    display: block;
  }

  .special-event.carousel-item .banner-text .mobile {
    display: none;
  }
}

/* added */

.ef-time-content {
  position: relative;
}

.ef-time {
  position: absolute;
  bottom: 8px;
  right: 16px;
  z-index: 1;
  color: #daf6ff;
  font-weight: 600;
  font-family: "Roboto", monospace;
}

.live-label {
  padding: 1px 8px;
  background: #e61839cc;
  box-shadow: 0 0 3px #ffffff;
  border-radius: 3px;
  border: 1px solid #ffffff66;
  text-align: center;
}

.time-label {
  padding: 1px 8px;
  background: #000000cc;
  box-shadow: 0 0 3px #ffffff;
  border-radius: 3px;
  border: 1px solid #ffffff66;
  text-align: center;
}

.ef-new {
  position: absolute;
  top: 8px;
  right: 16px;
  z-index: 1;
  color: #FFFFFF;
  font-weight: 600;
  font-family: "Roboto", monospace;
}

.new-label {
  text-transform: uppercase;
  padding: 1px 8px;
  background: #e61839cc;
  box-shadow: 0 0 3px #ffffff;
  border-radius: 3px;
  border: 1px solid #ffffff66;
  text-align: center;
}

header .topbar .logo {
  max-width: 220px;
}

.top-main .slick-next:before,
.top-main .slick-prev:before {
  opacity: .75;
  color: #bababa;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.top-main .slick-next:focus:before,
.top-main .slick-next:hover:before,
.top-main .slick-prev:focus:before,
.top-main .slick-prev:hover:before {
  opacity: 1
}

.top-main .slick-next.slick-disabled:before,
.top-main .slick-prev.slick-disabled:before {
  opacity: .25
}

.top-main {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  border: 0;
  padding: 0;
  margin: 0;
}

.top-main .slick-list {
  margin: 0 24px;
}

.top-main .slick-slider .slick-next {
  right: 0;
  z-index: 1;
  height: 100%;
}

.top-main .slick-slider .slick-prev {
  left: 0;
  z-index: 1;
  height: 100%;
}

.top-main .slick-slide {
  opacity: 1;
}

.top-main .slick-slide.slick-active {
  opacity: 1;
}

.top-main .slick-slide.slick-current.slick-active.slick-center {
  opacity: 1;
}

.top-main .slick-slide {
  margin: 0 2px;
  height: 67px;
}

.top-main .slick-slide .top-game-thumb {
  margin: 0;
  border-radius: 5px;
  overflow: hidden;
}

.top-main .slick-slide.slick-current .top-game-thumb {
  margin: 0;
}

.top-main .slick-slide .top-game-thumb img {
  padding: 0;
  border-radius: 5px;
  overflow: hidden;
}

.top-main .slick-slide.slick-current .top-game-thumb img {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.content .left .group .number.vgames {
  background: #00000088;
  box-shadow: 0 0 3px #ffffff88;
}

.content .left .group .number.vgames.danger {
  background: #C11A00;
}

.ball-yellow {
  background: #fbee1c;
  box-shadow: inset 0 0 6px #00000088, 0 0 1px #ffffff88;
  font-weight: 600;
  text-shadow: 0 0 3px #ffffff;
}

.ball-red {
  background: #dc3545;
  box-shadow: inset 0 0 6px #00000088, 0 0 1px #ffffff88;
}

.ball-blue {
  background: #007bff;
  box-shadow: inset 0 0 6px #00000088, 0 0 1px #ffffff88;
}

.ball-over {
  background: #FF8B60;
  box-shadow: inset 0 0 6px #00000088, 0 0 1px #ffffff88;
}

.ball-under {
  background: #009E9E;
  box-shadow: inset 0 0 6px #00000088, 0 0 1px #ffffff88;
}

.ball-odd {
  background: #FBB871;
  box-shadow: inset 0 0 6px #00000088, 0 0 1px #ffffff88;
}

.ball-even {
  background: #6667AB;
  box-shadow: inset 0 0 6px #00000088, 0 0 1px #ffffff88;
}

.ball-a {
  background: #c4a701;
  box-shadow: inset 0 0 6px #00000088, 0 0 1px #ffffff88;
}

.ball-b {
  background: #aaaaaa;
  box-shadow: inset 0 0 6px #00000088, 0 0 1px #ffffff88;
}

.ball-c {
  background: #cd7f32;
  box-shadow: inset 0 0 6px #00000088, 0 0 1px #ffffff88;
}

/*
.product-wrap {
  width: 1280px;
  margin: 0 auto !important;
  overflow-x: scroll;
  overflow-y: hidden;
  scroll-snap-type: x mandatory;
  display: flex;
}

.product {
  scroll-snap-align: center;
  box-sizing: border-box;
  flex-shrink: 0;
} */

.hx-table-xhtft {
  border-left: 1px solid #c0c0c0;
  border-right: 1px solid #c0c0c0;
  border-bottom: 1px solid #c0c0c0;
  background: #ffffff;
  color: #212529;
  display: flex;
  width: 100%;
  box-sizing: border-box;
}

.hx-wrap-xhtft {
  display: flex;
  flex-wrap: wrap;
  padding: 4px;
  width: 100%;
  background: #d8d8d8;
  align-items: start;
  justify-content: start;
  box-sizing: border-box;
}

.hx-item-xhtft {
  display: flex;
  flex: 0 0 20%;
  padding: 2px;
  margin: 0;
  align-items: start;
  justify-content: start;
  box-sizing: border-box;
}

.hx-field-xhtft {
  display: flex;
  background: #ffffff;
  padding: 3px 8px;
  width: 100%;
  border-radius: 3px;
}

.hx-field-xhtft.hx-alter-xhtft {
  background: #ecf0f3;
}

.hx-label-xhtft {
  display: flex;
  align-items: center;
  justify-content: start;
  flex: 1 1 auto;
  font-weight: 400;
}

.hx-value-xhtft {
  text-align: center;
  padding-left: 5px;
  box-sizing: border-box;
  margin: 0;
  width: 50%;
}

.hx-table-xhtft-head {
  border-left: 1px solid #c0c0c0;
  border-right: 1px solid #c0c0c0;
  background: #d8d8d8;
  color: #212529;
  display: flex;
  width: 100%;
  box-sizing: border-box;
  padding: 3px 5px 0 5px;
}


.hx-table-xhtft-head.live {
  border-left: 1px solid #c0c0c0;
  border-right: 1px solid #c0c0c0;
  background: #F6E3DC;
  color: #212529;
  display: flex;
  width: 100%;
  box-sizing: border-box;
  padding: 3px 5px 0 5px;
}

.hx-xhtft-caption {
  text-align: center;
  font-weight: 400;
  font-size: 10px;
  margin-bottom: 1px;
}

.btn-toggle, .btn-toggle-field {
  height: 25px;
  font-size: 11px;
  cursor: pointer;
  padding: 0;
  left: 0;
  line-height: 23px;
  width: calc(100% - 2px);
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  color: #fff;
  font-weight: 600;
  line-height: 1;
  font-size: 10px;
  margin: 0;
  background: transparent linear-gradient(90deg, #145693 0%, #276FA8 100%) 0% 0% no-repeat padding-box;
  border: 1px solid #d0d0d0;
  -ms-flex-align: center !important;
  align-items: center !important;
  -ms-flex-pack: justify !important;
  justify-content: center !important;
  display: -ms-flexbox !important;
  display: flex !important;
  padding-left: .5rem !important;
  padding-right: .5rem !important;
}

.btn-toggle:hover {
  background: #ffd452;
  -webkit-box-shadow: 0 1px 0 hsla(0, 0%, 100%, .5333333333) inset, 1px 1px 0 hsla(0, 0%, 100%, .2666666667) inset, 0 0 3px rgba(0, 0, 0, .2666666667);
  box-shadow: inset 0 1px 0 hsla(0, 0%, 100%, .5333333333), inset 1px 1px 0 hsla(0, 0%, 100%, .2666666667), 0 0 3px rgba(0, 0, 0, .2666666667);
  color: #000;
}

.btn-toggle:active {
  background: #b28705;
}

.btn-toggle-field {
  font-size: 11px;
  background: #ffffff;
  color: #000;
  justify-content: space-between !important;
}

.btn-toggle-field:hover {
  background: #ffc107;
}

.btn-toggle-field:active {
  background: #b28705;
}

.hx-dropdown-menu, .hx-dropdown-menu-2 {
  transform: none !important;
  top: 67px !important;
  left: 7px !important;
  height: 120px;
  overflow: auto;
  width: 232px;
  border-radius: .25rem;
  padding: 0;
  margin: 0;
}

.hx-dropdown-menu-2 {
  left: 241px !important;
}

.hx-dropdown-item {
  padding: 6px 8px;
  font-size: 12px;
}

.hx-xhtft-value {
  height: 25px;
  font-size: 11px;
  cursor: pointer;
  font-weight: bold;
  margin: 1px 1px 1px 1px;
  padding: 0;
  left: 0;
  line-height: 23px;
  width: calc(100% - 2px);
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  background: #ffffff;
  border: 1px solid #d0d0d0;
  -ms-flex-align: center !important;
  align-items: center !important;
  -ms-flex-pack: justify !important;
  justify-content: center !important;
  display: -ms-flexbox !important;
  display: flex !important;
  padding-left: .5rem !important;
  padding-right: .5rem !important;
  border-radius: .25rem;
}

.hx-xhtft-value .bet-value {
  padding-left: 8px !important;
  text-align: center !important;
  margin: 0 auto !important;
  float: none !important;
  width: 100px;
}

.hx-card {
  overflow: visible !important;
}

.hx-main .card {
  z-index: auto !important;
}

.hx-table.hx-ot-more {
  background: #FFFFFF;
  color: #212529;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  flex-direction: row;
  flex-wrap: nowrap;
  overflow: hidden;
  align-items: end;
  justify-content: end;
  box-sizing: border-box;
  padding: 2px 40px 2px 2px;
  font-size: 11px;
}

.hx-table.hx-ot-more.alternate {
  background: #F4F7FC;
}

.hx-table.hx-ot-more.live {
  background: #FBE9E1;
}

.hx-table.hx-ot-more.live.alternate {
  background: #FFF0EA;
}

.hx-table.hx-ot-more .btn {
  line-height: 1;
  padding: 1px 8px;
  font-size: 10px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  background: rgba(0, 0, 0, .05);

  width: 66px;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.hx-table.hx-ot-more .btn:hover {
  background: rgba(0, 0, 0, .075);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.075) 100%);
}

.hx-table.hx-ot-more .btn:focus {
  background: rgba(0, 0, 0, .075);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.075) 0%, rgba(0, 0, 0, 0) 100%);
}

.hx-table.hx-ot-more .btn i {
  margin-right: 4px;
  line-height: 1;
  font-size: 10px;
}

.hx-table.hx-ot-more .btn span {
  line-height: 1;
  font-size: 11px;
  font-weight: 600;
}


.left #market-tab .tab-pane {
  height: calc(100vh - 310px - var(--market-tab-height));
}

.left #market-tab .tab-pane.mmo {
  height: calc(100vh - 350px - var(--market-tab-height));
}

.left #market-tab.higher .tab-pane {
  height: calc(100vh - 244px - var(--market-tab-height));
}

.left #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 284px - var(--market-tab-height));
}

.left.active #market-tab .tab-pane {
  height: calc(100vh - 353px - var(--market-tab-height));
}

.left.active #market-tab .tab-pane.mmo {
  height: calc(100vh - 393px - var(--market-tab-height));
}

.left.active #market-tab.higher .tab-pane {
  height: calc(100vh - 287px - var(--market-tab-height));
}

.left.active #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 327px - var(--market-tab-height));
}

.left.extend #market-tab .tab-pane {
  height: calc(100vh - 390px - var(--market-tab-height));
}

.left.extend #market-tab .tab-pane.mmo {
  height: calc(100vh - 430px - var(--market-tab-height));
}

.left.extend #market-tab.higher .tab-pane {
  height: calc(100vh - 324px - var(--market-tab-height));
}

.left.extend #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 364px - var(--market-tab-height));
}

.left.extend.active #market-tab .tab-pane {
  height: calc(100vh - 433px - var(--market-tab-height));
}

.left.extend.active #market-tab .tab-pane.mmo {
  height: calc(100vh - 473px - var(--market-tab-height));
}

.left.extend.active #market-tab.higher .tab-pane {
  height: calc(100vh - 367px - var(--market-tab-height));
}

.left.extend.active #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 407px - var(--market-tab-height));
}

.left.extend1 #market-tab .tab-pane {
  height: calc(100vh - 430px - var(--market-tab-height));
}

.left.extend1 #market-tab .tab-pane.mmo {
  height: calc(100vh - 470px - var(--market-tab-height));
}

.left.extend1 #market-tab.higher .tab-pane {
  height: calc(100vh - 364px - var(--market-tab-height));
}

.left.extend1 #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 404px - var(--market-tab-height));
}

.left.extend1.active #market-tab .tab-pane {
  height: calc(100vh - 353px - var(--market-tab-height));
}

.left.extend1.active #market-tab .tab-pane.mmo {
  height: calc(100vh - 393px - var(--market-tab-height));
}

.left.extend1.active #market-tab.higher .tab-pane {
  height: calc(100vh - 287px - var(--market-tab-height));
}

.left.extend1.active #market-tab.higher .tab-pane.mmo {
  height: calc(100vh - 327px - var(--market-tab-height));
}


/* Optimized collapse transitions */
.collapse {
  will-change: height, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.collapse.show {
  transform: translateZ(0);
  will-change: height;
}
.collapse:not(.show) {
  height: 0;
  opacity: 0;
}

/* Result AIO Gaming */
.result-container { 
  width: 100%;
}
.result-container table {
  background: #F4F7FC;
  font-family: "Roboto", sans-serif !important;
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}
.result-container table tr:hover {
  background: #f5eeb8;
}
.result-container table tr:nth-child(even) {
  background: #FFFFFF;
}
.result-container table tr:nth-child(odd) {
  background: #F4F7FC;
}
.result-container table th {
  color: #fff;
  padding: 6px 6px 12px 6px;
  line-height: 1;
  font-weight: normal;
  border-right: 1px solid #5991C1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.result-container table th:last-child {
  border-right: 0;
}
.result-container table td {
  color: #01122b;
  padding: 3px 6px;
  line-height: 20px;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}
.result-container table td:last-child {
  border-right: 0;
}
.result-container table td .red {
  color: #b53f39;
}
.result-container table td .blue {
  color: #2556b3;
}