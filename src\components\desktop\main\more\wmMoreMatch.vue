<template lang="pug">
.col-12.hx-more-row
  .card
    .card-header(
      :id="'heading-wm-' + uid"
      data-toggle="collapse"
      :data-target="'#collapse-wm-' + uid"
      :aria-controls="'collapse-wm-' + uid"
      aria-expanded=true
      :class="layoutIndex == 3 ? 'live': 'non-live'"
    )
      i.fad.fa-chevron-circle-down
      span.header-bettype {{ $t("m.BT_WM") }}
    .collapse.show(
      :aria-labelledby="'heading-wm-' + uid"
      :id="'collapse-wm-' + uid"
    )
      .card-body.p-0(:id="'accordian-wm-' + uid")
        .hx-table.hx-match.hx-more-bet(:class="{ 'live': marketType == 3 }")
          .d-flex.flex-column.w-100
            .hx-cell.w-100.h-18
              .hx-row.hx-more-col.hx-more-col5.header.bl-1.br-1.bb-1
                .hx-col(v-for="item in col1")
                  .hx {{ $t("m.BK_" + item) }}
            .d-flex.flex-row
              .hx-cell.w-100
                .hx-row.hx-more-col.hx-more-col5.body.bl-1.br-1.bb-1
                  .hx-col(v-for="item in col1")
                    .hx
                      .hxs(v-if="details != null && details[item] != null && details[item][5] != null && details[item][5] != ''").hx-flex-c
                        oddsItem(:odds="details[item]" idx=5 :typ="oddsType" dataType="3" cls="more-value hx-w80")
        .hx-table.hx-match.hx-more-bet(:class="{ 'live': marketType == 3 }")
          .d-flex.flex-column.w-100
            .hx-cell.w-100.h-18
              .hx-row.hx-more-col.hx-more-col5.header.bl-1.br-1.bb-1
                .hx-col(v-for="item in col2")
                  .hx {{ $t("m.BK_" + item) }}
            .d-flex.flex-row
              .hx-cell.w-100
                .hx-row.hx-more-col.hx-more-col5.body.bl-1.br-1.bb-1
                  .hx-col(v-for="item in col2")
                    .hx
                      .hxs(v-if="details != null && details[item] != null && details[item][5] != null && details[item][5] != ''").hx-flex-c
                        oddsItem(:odds="details[item]" idx=5 :typ="oddsType" dataType="3" cls="more-value hx-w80")
</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";
import config from "@/config";

export default {
  components: {
    oddsItem,
  },
  props: {
    uid: {
      type: String,
    },
    details: {
      type: Object,
    },
    matchId: {
      type: Number,
    },
    leagueId: {
      type: Number,
    },
    marketType: {
      type: Number,
    },
    sportsType: {
      type: Number,
    },
    betType: {
      type: String,
    },
    layoutIndex: {
      type: Number,
    },
  },
  data() {
    return {
      col1: ["H1", "H2", "H3Up", "D"],
      col2: ["A1", "A2", "A3Up", "NG"],
    };
  },
  computed: {
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
  },
  methods: {},
};
</script>
