<template lang="pug">
eventengine
  //- #static-background(v-if="!whiteLabel")
  topBar
  router-view
  leagueFilter
  //- ads(v-if="!whiteLabel && adsPopup && popup && branded")
</template>

<script>
import config from "@/config";
import eventengine from "@/components/eventEngine";

export default {
  components: {
    eventengine,
    topBar: () => import("@/components/desktop/topBar"),
    leagueFilter: () => import("@/components/desktop/main/leagueFilter"),
    leftBar: () => import("@/components/desktop/leftBar"),
    rightBar: () => import("@/components/desktop/rightBar"),
    ads: () => import("@/components/desktop/ads"),
  },
  computed: {
    branded() {
      return config.brand == "WBET";
    },
    whiteLabel() {
      return config.whiteLabel;
    },
    adsPopup() {
      return this.$store.getters.adsPopup;
    },
    adsKey() {
      return this.$store.getters.adsKey;
    },
    popup() {
      return config.popup;
    },
  },
  mounted() {
    $("html").removeClass("minimal");
    $("body").removeClass("minimal");
    $("html").removeClass("hl");
    $("body").removeClass("hl");

    const key = 1;
    if (this.adsKey != key) {
      this.$store.dispatch("layout/setAdsKey", key);
    }
  },
  methods: {
    closeMe() {
      $("#overlay-widget").html("");
      $("#overlay").hide();
    },
  },
};
</script>
