body.color5 {
	background: #1F2128 !important;
}
.color5 header,.color5 header .topbar {
	background: #282A31;
}
.color5 header .toolbar {
	background-color: #282A31;
	border-top: 0;
	border-bottom: 8px solid #1F2128;
	top: 0;
}
.color5 header .toolbar.active.white-label .new-timezone {
	min-width: 256px;
}
.color5 header .toolbar.active.white-label .menu {
	min-width: 256px;
}
.color5 header .toolbar.active .container .logo {
	padding-top: 1px;
	display: none !important;
}
.color5 .content .right .z-side .card .card-header {
	background: #1D1D25;
	color: #fff;
	border: 1px solid rgba(255, 255, 255, 0.1);
}
.color5 .content .right .text-icon.selected {
}
.color5 .frame-header {
	background: #282A31;
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
	border-left: 1px solid rgba(255, 255, 255, 0.2);
	border-right: 1px solid rgba(255, 255, 255, 0.2);
}
.color5 .frame-wrapper {
}
.color5 header .toolbar .show-user {
	display: none !important;
}
.color5 header .toolbar .menu ul.nav li.nav-item a.nav-link {
	height: 32px;
    border-radius: 3px;
    margin: 1px;
    background-color: #1F2026;
	border: 1px solid rgba(255, 255, 255, 0.1);
}
.color5 .dropdown-panel {
	background: #1F2128;
    box-shadow: none;
    border-radius: 0 0 3px 3px !important;
}
.color5 .dropdown-li {
	border-left: 0;
	border-right: 0;
}
.color5 .dropdown-li:last-child {
	border-bottom: 0;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0;
}
.color5 .dropdown-li .caption {
	font-size: 10px;
	color: #fff;
}
.color5 .dropdown-li .value .unit {
	font-size: 10px;
	color: #ffffff82;
}
.color5 .dropdown-li .value {
	font-size: 12px;
	font-weight: 700;
	color: #fff;
}
.color5 .user-info-wrapper {
	border-radius: 3px;
	margin-bottom: 4px;
	font-size: 11px;
	font-family: "Lato", sans-serif;
}
.color5 .profile .dropdown-li {
	padding: 2px 2px;
	border-top: 1px solid rgba(255, 255, 255, .1);
	border-bottom: 0;
}
.color5 .user-info-wrapper {
	background-color: #282A31;
	border: 1px solid rgba(255, 255, 255, 0.2);
	color: #fff;
}
.color5 .user-info-wrapper .user {
	color: #fff;
}
.color5 .user-info-wrapper .balance-drop {
	border-top: 1px solid rgba(255, 255, 255, 0.2);
	background-color: #eeeff4;
	color: #353f5e;
}
.color5 .user-info-wrapper .details {
	background-color: #363944;
	color: #fff;
	border-top: 1px solid rgba(255, 255, 255, .1);
}
.color5 .user-info-wrapper .details .balance-text {
	cursor: pointer;
	padding: 0.5rem;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	    -ms-flex-pack: justify;
	        justify-content: space-between;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	text-align: left;
	position: relative;
}
.color5 .user-info-wrapper .details .balance-text::after {
    content: "";
    height: 19px;
    border-right: 1px solid rgba(255, 255, 255, .1);
    padding-right: 16px;
}
.color5 .user-info-wrapper .details .balance .caption {
	font-size: 9px;
	color: #fff;
}
.color5 .user-info-wrapper .details .balance {
	padding: 0.5rem;
	-webkit-box-flex: 1;
	    -ms-flex-positive: 1;
	        flex-grow: 1;
	text-align: right;
	background-color: #363944;
	color: #F6C344;
	font-weight: bold;
}
.color5 .content .main .header-wrap {
}
.color5 .content .main .filter-area {
	background: #1F2128;
}
.color5 .content .main .filter-area .filter-item .filter-icon {
	color: #e7fffa;
}
.color5 .content .main .filter-area .filter-item .filter-icon.page-button:hover {
	background-color: #22222A;
}
.color5 .page-button {
	border: 1px solid rgba(255, 255, 255, 0.2);
}
.color5 .x-accordion.accordion .bg {
	background: #363944;
}
.color5 .language-selector .dropdown-menu {
	background: #353f5e !important;
	border: 1px solid rgba(0, 0, 0, 0.3) !important;
	-webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset !important;
	        box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset !important;
}
.color5 .x-btn-default,.color5 .x-btn {
	border: 1px solid rgba(0, 0, 0, 0.5);
	-webkit-box-shadow: 0 1px 0 #ffffff3d inset, 1px 1px 0 #ffffff2e inset;
	        box-shadow: 0 1px 0 #ffffff3d inset, 1px 1px 0 #ffffff2e inset;
}
.color5 .x-btn-default:hover {
	-webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 1px 1px 0 rgba(255, 255, 255, 0.2) inset, -1px -1px 1px rgba(0, 0, 0, 0.2) inset, 0 0 3px rgba(0, 0, 0, 0.2);
	        box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 1px 1px 0 rgba(255, 255, 255, 0.2) inset, -1px -1px 1px rgba(0, 0, 0, 0.2) inset, 0 0 3px rgba(0, 0, 0, 0.2);
}
.color5 .content .left .nav-header {
	color: #353f5e;
}
.color5 .content .left.active .nav-header.nav-right {
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.color5 .content .left .nav-header .collapsed {
	background: #353f5e;
}
.color5 .content .left .group.selected {
	color: #fff;
}
.color5 .content .left.active .collapse.show .group {
	background: #363944;
}
.color5 .content .left.active .group.changed.selected {
	background: #282A31 !important;
}
.color5 .content .left.active .heading-collapse[aria-expanded="false"] .group.changed:hover {
	background: #282A31 !important;
}
.color5 .content .left .group {
	color: #fff;
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.color5 .content .left #market-group {
	margin-top: -1px;
}
.color5 .content .left.active #collapse-allsports.collapse.show #market-group {
	border-left: 1px solid rgba(255, 255, 255, .1);
    border-right: 1px solid rgba(255, 255, 255, .1);
}
.color5 .content .left .side-row .group { 
	border-top: 0;
}
.color5 .content .left .tb {
	border-top: 1px solid rgba(255, 255, 255, .1);
	border-radius: 5px;
}
.color5 .content .left .xb {
	border-left: 1px solid rgba(255, 255, 255, .1);
	border-right: 1px solid rgba(255, 255, 255, .1);
}
.color5 .content .left ul.subgroup li.small {
	border-bottom: 1px solid rgba(255, 255, 255, .1);
}
.color5 .content .left ul.subgroup li.small:last-child {
	border-bottom: 0;
}
.color5 .content .left .group .sport-type {
	background: #363944;
}
.color5 .content .left .group .sport-type a {
	color: #e8f9ff;
}
.color5 .content .left .group .sport-type.active a {
	color: #F6C344;
}
.color5 .content .left .group .sport-type.active a, .content .left .group .sport-type.active a i {
	color: #F6C344;
}
.color5 .content .left .group.selected,.color5 .content .left .group .sport-type.active {
	background: #1D1D25;
}
.color5 .content .left .side-row {
	background: #1D1D25;
	border: 1px solid rgba(255, 255, 255, .1);
}
.color5 .content .left .xb a:hover {
	text-decoration: none;
}
.color5 .content .left .group.changed.selected, .color5 .content .left .group .sport-type.active {
    background: #282A31;
}
.color5 .content .main .filter-block {
}
.color5 .round-alert {
	background: #00000044;
	border: 1px solid #2e3752;
}
.color5 .text-info {
	color: #2e3752 !important;
}
.color5 .content .main .filter-area .filter-item .dropdown .dropdown-menu a.dropdown-item,.color5 .content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item .filter-icon {
	color: #2e3752;
}
.color5 .content .main .filter-area .filter-item .dropdown .dropdown-menu a:hover.dropdown-item {
	background-color: #9f9f9f;
	color: #fff;
}
.color5 .x-accordion.accordion .bg:last-child {
	border-bottom: 1px solid rgba(255, 255, 255, .1);
}
.color5 .modal .modal-dialog .modal-content .modal-header {
	background: #22222A !important;
}
.color5 .modal .modal-dialog .modal-content .modal-body #select-league .card .card-header {
	background: #9f9f9f;
	color: #3d496d;
}
.color5 .modal .modal-dialog .modal-content .modal-footer .btn-primary {
	background: #22222A;
}
.color5 .table-info tr th,.color5 .table-betresult tr th {
	background: #2e3752;
}
.color5 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link.active {
	background: #282A31;
}
.color5 header .topbar .info-content .nav-info a:hover,.color5 header .topbar .nav-info a.active {
	background-color: #2e3752;
	border-left: 1px #353f5e solid;
	border-right: 1px #353f5e solid;
	text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
}
.color5 .info-wrapper .info-tablewrap .nav-tabs {
	border-bottom: 1px solid rgba(255, 255, 255, .1);
}
.color5 .info-wrapper .info-tablewrap .nav-tabs li.nav-item {
	background: #282A31;
	border: 1px solid rgba(255, 255, 255, .1);
	border-bottom: 0;
}
.color5 .result-selection .form-control.datepicker {
	background: #1F2128;
}
.color5 .accordion-rules .card .card-header {
	padding: 8px;
	background: #353f5e;
	color: #efdd00;
	border-radius: 0 0 0 0;
}
.color5 .info-tablewrap .setting-right .btn-result.active {
	color: #fff;
	border: 1px solid #ffffff;
	border-radius: 0 0 0 0;
}
.color5 .modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap:hover input ~ .checkmark {
	background-color: #2e3752;
	border: 1px solid #353f5e;
}
.color5 .hx-league {
	background: #575757;
	border-bottom: 0;
}
.color5 .hx-league.live {
	background: #4E3A37;
	border-bottom: 0;
}
.color5 .hx-table {
	background: url(../../images/color5/market-head-bg.png);
    background-size: auto 100%;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.color5 .hx-cell {
	border-left: 1px solid rgba(255,255,255,.1);
}
.color5 .hx-row {
	border-top: 1px solid rgba(255,255,255,.1);
}
.color5 .hx-col {
	border-left: 1px solid rgba(255,255,255,.1);
}
.color5 .hx-more {
	background: #22222A;
}
.color5 .morebet-wrapper .body-bet .nav-tabs li.nav-item a.nav-link.active {
	background: #22222A;
	border-bottom: 2px solid #22222A;
}
.color5 .morebet-wrapper .body-bet .tab-content .tab-pane .card .card-header {
	background: #8d8d8d;
}
.color5 .morebet-wrapper .body-bet .nav-tabs.live-tab li.nav-item a.nav-link.active {
	background: #85360b;
	border-bottom: 2px solid #85360b;
}
.color5 .morebet-wrapper .body-bet .tab-content .tab-pane .card .card-header.live {
	background: #85360b;
}
.color5 .profile .dropdown-li a {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	padding: 6px;
	margin: 2px;
	color: #a5cae5;
	border: 1px solid #1a2030;
	-webkit-box-shadow: 0 1px 0 #435077cc inset, 1px 1px 0 #43507744 inset;
	        box-shadow: 0 1px 0 #435077cc inset, 1px 1px 0 #43507744 inset;
	border-radius: 3px;
	text-align: center;
	text-decoration: none;
	line-height: 1;
}
.color5 .profile .dropdown-li a i {
	margin-right: 4px;
	font-size: 12px;
}
.color5 .profile .dropdown-li a:hover {
	color: #ffc107;
}
.color5 .content .left ul.subgroup input[type="checkbox"] {
	position: relative;
	width: 15px !important;
	height: 15px !important;
	color: #1a2030;
	border: 1px solid #fff;
	border-radius: 3px;
	-webkit-appearance: none;
	   -moz-appearance: none;
	        appearance: none;
	outline: 0;
	cursor: pointer;
	-webkit-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	-o-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
}
.color5 .content .left ul.subgroup input[type="checkbox"]::before {
	position: absolute;
	content: "";
	display: block;
	top: 0;
	left: 4px;
	width: 5px !important;
	height: 10px !important;
	border-style: solid;
	border-color: #fff;
	border-width: 0 2px 2px 0;
	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	        transform: rotate(45deg);
	opacity: 0;
}
.color5 .content .left ul.subgroup input[type="checkbox"]:checked {
	color: #fff;
	border-color: #1a2030;
	background: #1a2030;
}
.color5 .content .left ul.subgroup input[type="checkbox"]:checked::before {
	opacity: 1;
}
.color5 .content .left ul.subgroup input[type="checkbox"]:checked ~ label::before {
	-webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
	        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}
.color5 .hx-table.hx-match.live .morebet-wrapper {
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	border-bottom: 1px solid #abb2c9;
	border-top: 1px solid #abb2c9;
	border-collapse: collapse;
}
.color5 .content .main .single-match {
	background: #353f5e;
	color: #ffffffcc;
	border: 1px solid rgba(255, 255, 255, 0.2);
}
.color5 .content .main .single-match .action-block {
	border-right: 1px solid rgba(255, 255, 255, 0.2);
}
.color5 .content .main .single-match .content img {
	-webkit-filter: hue-rotate(40deg);
	        filter: hue-rotate(40deg);
}
.color5 .content .left .nav-header [aria-expanded="true"] {
	color: #F6C344;
	background: #282A31;
}
.color5 .content .left .nav-header [aria-expanded="false"] {
	color: #FFFFFF;
	background: #363944;
}
.color5 .luckybox .btn-2 {
	background: #FDB338;
	color: #000;
}
.color5 .mini-game-container .carousel-control-prev-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
}
.color5 .mini-game-container .carousel-control-next-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
}
.color5 .mini-game-container .carousel-indicators .active {
	background-color: #fff;
}
.color5 .luckyslider-label {
    color: #fff;
}
.color5 .luckybox .btn-1 {
	background-color: rgba(255, 255, 255, 0.1);
	color: #fff;
}
.color5 header .toolbar .menu .nav {
    justify-content: flex-end;
}
.color5 .right .text-icon.selected i, .color5 .right .text-icon.selected svg {
    color: #fff;
}
.color5 .right .text-icon.selected {
    color: #fff;
}
.color5 .luckybox {
    color: #fff;
}
.color5 .content .main .filter-area .filter-item {
    color: #fff;
}
.color5 .content .main .filter-area .filter-item .filter-icon {
    color: #fff;
}
.color5 #new-searchbar.searchbar .input-group .input-group-prepend .input-group-text {
	color: #fff;
}
.color5 #new-searchbar.searchbar .input-group .form-control {
	color: #fff;
}
.color5 #new-searchbar.searchbar .input-group .form-control {
	background-color: #282A31;
}
.color5 #new-searchbar.filter-item.searchbar .input-group .form-control::-webkit-input-placeholder {
	color: #fff !important;
}
.color5 #new-searchbar.filter-item.searchbar .input-group .form-control:-ms-input-placeholder {
	color: #fff !important;
}
.color5 #new-searchbar.filter-item.searchbar .input-group .form-control:-moz-placeholder {
	color: #fff !important;
}
.color5 .content .left .content-bet .nav li.nav-item a.nav-link.active {
    background: #f9d040;
}
.color5 .content .left .content-bet .nav li.nav-item a.nav-link {
    background: #eeeff4;
    color: #333;
}
.color5 .frame-wrapper {
    background: #1F2128;
	border: 1px solid rgba(255, 255, 255, .1);
    border-top: 0;
}
.color5 .luckybox .switch-wrap .switch .slider {
    background-color: #AEAEAE;
}
.color5 .mini-bg1 {
    background: #1F2128;
}
.color5 .morebet-wrapper .body-bet .tab-content .tab-pane .card .card-header.live {
    background: #F4D1C5;
}
.color5 .morebet-wrapper .body-bet .nav-tabs.live-tab li.nav-item a.nav-link.active {
    background: #C85F3F;
    border-bottom: 2px solid #C85F3F;
}
.color5 .hx-table.alternate.hx-match {
    background: #363944;
}
.color5 .content .main .filter-block .filter-single .filter-date {
    border: 1px solid rgba(0, 0, 0, 0.1);
}
.color5 .content .main .filter-block .filter-single .filter-date .filter-date-title {
    background-color: #363944;
}
.color5 .content .main .filter-block .filter-single .filter-date.active {
	color: #000;
}
.color5 .content .main .filter-block .filter-single .filter-date.active .filter-date-title {
    background-color: #F6C344;
}
.color5 .content .main .filter-block .filter-single .filter-date .filter-date-body {
	background: #282A31;
	color: #fff;
}
.color5 .content .main .filter-block .filter-single .filter-date.active .filter-date-body {
	background: #FFF0C9;
	color: #000;
}
.color5 .content .left.active .nav-header .collapsed, .content .left.active .nav-header [aria-expanded="true"], 
.color5 .content .left.active .nav-header [aria-expanded="false"] {
    background: transparent;
}
.color5 .content .left.active .nav-header.nav-left, .color5 .content .left.active .nav-header.nav-right {
	background: #363944;
}
.color5 .hx-title {
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* whole black */
.color5 .hx-table.hx-match {
	background: #404351;
}
.color5 .hx-table .bet-value {
	color: #fff;
}
.color5 .hx.team-red {
	color: #FF806D;
}
.color5 .hx.team-black {
	color: #fff;
}
.color5 .hx.draw {
	color: #A0A0A0;
}
.color5 .hx-table .ball-value {
	color: #A0A0A0;
}
.color5 .hx-icon i {
	color: rgba(255, 255, 255, 0.5);
}
.color5 .hx-table.hx-match .hx-rows {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}
.color5 .hx-table.hx-match .hx-cell {
    border-left: 1px solid rgba(255, 255, 255, 0.05);
}
.color5 .hx-event .duration {
	color: #fff;
}
.color5 .hx-event .danger {
	color: #FF2200;
}
.color5 .hx-table.hx-match:hover, 
.color5 .hx-table.alternate.hx-match:hover, 
.color5 .hx-table.live.hx-match:hover, 
.color5 .hx-table.live.alternate.hx-match:hover, 
.color5 .hx-table.hx-match.hx-morebet-body:hover, 
.color5 .hx-table.hx-match.hx-morebet-body.live:hover {
	background: rgba(255, 255, 255, .2);
}
.color5 .hx-table.live.alternate.hx-match {
	background: #342B2A;
}
.color5 .hx-table.live.hx-match {
	background: #2C2220;
}
.color5 .pointable.dark {
	color: #fff;
}
.color5 .pointable.dark i {
	color: rgba(255, 255, 255, 0.5);
}
.color5 .hx-league .hx-corner-btn {
	color: rgba(255, 255, 255, 0.5);
}
.color5 .hx-event .label {
	color: #fff;
}
.color5 .hx-table .text-red {
	color: #FF6148 !important;
}
.color5 .hx-table .bet-value.highlighted.up {
	background: #555B4C;
	border: 1px solid #58775F;
	color: #fff;
}
.color5 .hx-table .bet-value.highlighted.down {
	background: #4D2B26;
	border: 1px solid #755954;
	color: #FF6148;
}
.color5 .hx-event .score {
	color: #FF2200;
}
.color5 .hx-table.hx-match.live .hx-rows {
	border-bottom: 1px solid rgba(255, 255, 255, .05);
}
.color5 .hx-table.hx-match.live .hx-cell {
	border-left: 1px solid rgba(255, 255, 255, .05);
}
.color5 .hx-table.hx-match.live .hx-cell:last-child {
	border-right: 1px solid rgba(255, 255, 255, .05);
}
.color5 .hx-table.hx-match .hx-cell:last-child {
	border-right: 1px solid rgba(255, 255, 255, .05);
}
.color5 .hx-table.hx-top-rounded .hx-cell:last-child {
	border-right: 1px solid rgba(255, 255, 255, .05);
}
.color5 .hx-table.hx-more-bet .bet-value {
	color: #000;
}
.color5 .hx-table.hx-match.hx-morebet.live.alternate.collapse.show .hx-table.live.hx-match {
	background: #FBE9E1;
}
.color5 .hx-table.hx-match.hx-morebet.collapse.show .hx-table.hx-match {
	background: #fff;
}
.color5 .content .left ul.subgroup li.hot { 
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.color5 .content .main .filter-area .filter-item {
	border: 1px solid rgba(255, 255, 255, .1);
	background: #282A31;
	color: #fff;
}
.color5 .content .main .filter-area .filter-item.refresh.live, 
.color5 .content .main .filter-area .filter-item.refresh.live i, 
.color5 .content .main .filter-area .filter-item.refresh.live .timer {
	color: #FDB338;
}
.color5 .content .main .filter-area .filter-item.refresh i,  .color5 .timer {
	color: #fff;
}
.color5 .content .left .group .sport-type i {
	color: #fff;
}
.color5.europeview .hx-table.hx-match.live .bet-value {
	background-color: #4E3A37;
}
.color5.europeview .hx-table.hx-match.hx-more-bet.live .bet-value {
	background-color: #F6DFD8;
}
.color5.europeview .hx-table.hx-match.hx-more-bet .bet-value {
	background-color: #ECECEC;
}
.color5.europeview .hx-table.hx-match.live .bet-value.highlighted.up {
	background-color: #555B4C;
}
.color5.europeview .content .main .single-match {
	background: #363944;
    border: 1px solid #363944;
}
.color5.europeview .content .main .single-match .action-block {
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}
.color5.europeview .hx-table.hx-match .bet-value {
	background-color: #515665;
}
.color5 .morebet-wrapper .body-bet .nav-tabs li.nav-item {
	background: #575757;
}
.color5 .x-side {
	background: #1F2128;
}
.color5 .x-side::before {
	border-left: 12px solid #1F2128;
}
.color5 .x-side::after {
	border-right: 8px solid #1F2128;
}
.color5 .luckybox .btn-1:hover {
	background: rgba(255, 255, 255, .5);
}
.color5 .luckydelete {
	background: rgba(0, 0, 0, 0.3);
}
.color5 .luckydelete:hover {
	background: rgba(255, 255, 255, .05);
}
.color5 .luckyodds:last-child {
	color: #F6C344;
}
.color5 .luckyteam {
	color: #fff;
}
.color5 .luckytarget {
	color: #FF6148;
}
.color5 .luckyvs {
	color: #A0A0A0;
}
.color5 .luckybettype {
	color: #fff;
}
.color5 .luckytext {
	color: #fff;
}
.color5 .luckymuted {
	color: #fff;
}
.color5 .luckyitem {
	border-top: 1px solid rgba(255, 255, 255, .05);
}
.color5 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link {
	color: #fff;
}
.color5 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link.active {
    color: #F6C344;
}
.color5 .info-tablewrap .setting-right .btn-result.active {
	background: #282A31 !important;
}
.color5 .result-selection .btn-result {
	border: 1px solid rgba(255, 255, 255, .1);
}
.color5 .result-selection .btn-result:hover, .result-selection .btn-result.active {
	background: #282A31 !important;
	border: 1px solid rgba(255, 255, 255, .1);
}
.color5 .info-tablewrap .pagination .page-item .page-link {
	border: 1px solid rgba(255, 255, 255, .1);
	color: #fff;
}
.color5 .info-tablewrap .pagination .page-item .page-link.active {
	background: #282A31;
	color: #F6C344;
}
.color5 .info-tablewrap .setting-right .custom-control-input:checked ~ .custom-control-label::before {
    background: #282A31;
    border: 1px solid rgba(255, 255, 255, .1);
}
.color5 .modal .modal-dialog .modal-content .modal-footer .btn-primary {
    background: #282A31;
}
.color5 .info-wrapper .info-title .page-title {
	color: #fff;
}
.color5 .info-wrapper .info-title .info-icon {
	color: #fff;
}
.color5 .info-wrapper .info-title .page-title .breadcrumb .breadcrumb-item.active {
	color: #fff;
}
.color5 .breadcrumb-item+.breadcrumb-item::before {
	color: #fff;
}
.color5 .info-wrapper .info-title .page-title .breadcrumb .clickable {
	color: #fff;
}
.color5 .info-wrapper .info-title .page-title .breadcrumb .breadcrumb-item a {
	color: #fff;
}
.color5 .result-selection .form-control.datepicker {
	border: 1px solid rgba(255, 255, 255, .1);
}
.color5 .result-selection .input-group .input-group-prepend .input-group-text, 
.color5 .result-selection .input-group .input-group-append .input-group-text {
	border: 1px solid rgba(255, 255, 255, .1);
}
.color5 .result-selection .input-group .input-group-prepend .input-group-text, 
.color5 .result-selection .input-group .input-group-append .input-group-text {
	color: #fff;
}
.color5 .result-selection {
	border-bottom: 1px solid rgba(255, 255, 255, .1);
}
.color5 .result-selection .btn-result {
	color: #fff;
}
.color5 .result-selection .dropdown-menu .dropdown-item:hover, 
.color5 .result-selection .dropdown-menu .dropdown-item:focus, 
.color5 .result-selection .dropdown-menu .dropdown-item.active {
	background: #9f9f9f;
}
.color5 .tournament-main {
	background: #1F2128;
}
.color5 .tournament-top, 
.color5 .tournament-top-plus {
	background: #282A31;
}
.color5 .tournament-btn-search {
	background-color: rgba(255, 255, 255, .5);
}
.color5 .tournament-btn-search:hover, 
.color5 .tournament-btn-search:focus {
	background-color: rgba(255, 255, 255, .5);
}
.color5 .tournament-user {
	background: #FDB338;
	color: #000;
}
.color5 .tournament-pagination ul li .page-link.disable,
.color5 .tournament-pagination ul li .page-link,
.color5 .tournament-pagination ul li .page-link:hover {
	border: 1px solid rgba(255, 255, 255, .1);
}
.color5 .tournament-pagination ul li .page-link i,
.color5 .tournament-pagination ul li .page-link {
	color: #fff;
}
.color5 .modal-dialog .tournament-pagination ul li .page-link i,
.color5 .modal-dialog .tournament-pagination ul li .page-link {
	color: #000;
}
.color5 .tournament-pagination ul li .page-link.active {
	background-color: #282A31;
	border: 1px solid rgba(255, 255, 255, .1);
	color: #F6C344;
}
.color5 .tournament-pagination ul li .page-link:hover {
	color: #000;
}
.color5 .tournament-btn {
	background: url('../../img/tn/bg-match-color5.png') top center no-repeat;
}
.color5 .tournament-btn .tournament-text,
.color5 .tournament-arrow {
	color: #000;
}
.color5 .tournament-pool-fee {
	background: rgba(0,0,0,0.5);
	border: 1px rgba(255,255,255,0.2) solid;
}
.color5 .tournament-pool-result {
    background-color: #363944;	
}
.color5 .tournament-page-wrapper ul li.nav-item .nav-link {
	color: #000;
}
.color5 .select-day-top {
	color: #000;
}
.color5 .select-day-bottom {
	color: #A0A0A0;
}
.color5 .select-day.today .select-day-bottom {
	color: #F6C344;
}
.color5 .select-league-title {
	color: #000;
}
.color5 .select-league-teams {
	color: #fff;
}
.color5 .modal-dialog .select-league-teams {
	color: #000;
}
.color5 .select-league-time {
	color: #000;
}
.color5 .select-league .date-check input:checked ~ .checkmark-date {
	background-color: #363944;
	border: 1px solid #363944;
}
.color5 .room-bottom {
	color: #000;
}
.color5 .room-rate-select select option, 
.color5 .room-limit-select select option,
.color5 .tournament-search select option {
	background-color: #363944;
}
.color5 .room-rate-select select,
.color5 .room-limit-select select {
	color: #000;
}
.color5 .tournament-point {
	background-color: #282A31;
}
.color5 .tournament-menu ul li.active {
	background-color: #F6C344;
}
.color5 .tournament-menu ul li.active a {
	color: #000;
}
.color5 .bet-info .bet-type.blue {
	color: #F6C344;
}
.color5 .tournament-mybet-inner {
	background: #D1D9E1;
}
.color5 .tournament-details-icon {
	filter: brightness(0%);
    opacity: 0.5;
}
.color5 .player-prize-icon {
	filter: brightness(0%);
    opacity: 0.5;
}
.color5 .tournament-details-top {
	color: #000;
}
.color5 .tournament-details-bottom {
	color: #F6C344;
}
.color5 .tournament-betslip-room {
	background: #363944;
}
.color5 .tournament-betslip {
    background: #D1D9E1;
}
.color5 .alert-tournament {
	background-color: #fff;
	color: #000;
}
.color5 .tournament-odds {
	background-color: #fff;
}
.color5 .tournament-odds::before {
	filter: brightness(0) invert(1);
}
.color5 .tournament-odds::after {
	filter: brightness(0) invert(1);
}
.color5 .tournament-betslip-matches {
	color: #000;
}
.color5 .tournament-odds-text {
	color: #000;
}
.color5 .tournament-betslip-matches .tn-team {
	color: #000;
}
.color5 .tournament-betslip-matches .tn-vs {
	color: #333;
}
.color5 .tournament-betslip-header-title {
	background: rgba(0,0,0,.5);
}
.color5 .player-betslip-top {
	background-color: #282A31;
}
.color5 .player-prize-top {
	color: #000;
}
.color5 .player-prize-bottom {
	color: #F6C344;
}
.color5 .player-betslip-room {
    color: #000;
}
.color5 .player-betslip-content .tournament-mybet-single .tournament-mybet-date {
    color: rgba(0, 0, 0, .7);
}
.color5 .player-betslip-content .tournament-mybet-single .tournament-mybet-small span {
	color: #F6C344;
}
.color5 .tournament-mybet-status {
	color: #50AE1B;
}
.color5 .tournament-betslip-wrapper ul li.nav-item .nav-link {
	background-color: #363944;
}
.color5 .hl-select-date {
	background-color: rgba(255, 255, 255, .5);
}
.color5 .hl-filter-bar .dropdown-menu {
	background: #9f9f9f;
}
.color5 .hl-filter-bar .dropdown-menu .dropdown-item:hover {
	background: #363944;	
}
.color5 .hl-filter-bar .dropdown-menu .dropdown-item.active,
.color5 .hl-filter-bar .dropdown-menu .dropdown-item:active,
.color5 .hl-filter-bar .dropdown-menu .dropdown-item.active:hover {
	background-color: #282A31;
}
.color5 .hl-league-titlebar {
	background: #2e3752;
}
.color5 .hl-content .slick-next:before,
.color5 .hl-content .slick-prev:before {
	filter: brightness(0) invert(1);
}
.color5 .hl-title-matches {
	color: #fff;
}
.color5 .tournament-betslip-matches .text-center {
	color: #000 !important;
}
.color5 .tournament-table-entry .table-tournament {
	color: #000;
}
.color5 .tournament-stake-field .input-group-text {
	background: rgba(0, 0, 0, .5);
}
.color5 .swal2-confirm {
	background: #282A31 !important;
    border: 1px solid rgba(255, 255, 255, .1) !important;
}
.color5 .hx-table.hx-match.hx-morebet.collapse.show .hx-table.hx-match .bet-value {
	color: #000;
}
.color5 .hx-table.hx-match.hx-morebet.collapse.show .hx-table.hx-match .hx.team-black {
	color: #000;
}
.color5 .ng-result .logo span {
	color: #fff;
}

.color5 .tournament-betslip-league span {
	color: #000;
}
.color5 .tournament-odds2 .tournament-odds-text {
	color: #fff;
}