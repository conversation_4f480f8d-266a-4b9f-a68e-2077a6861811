<template lang="pug">
#slick-vgames.slider.slider-nav.fadeIn(style="display: none;")
  .esports-game-thumb.ef-time-content(v-for="item in gameList" :class="'sports-' + item")
    template(v-if="params.hasOwnProperty(item)")
      img.img-fluid(:src="params[item].thumbnail")
      .ef-time
</template>

<script>
/*
  41: "vDYS8-n3Zn1", SF4
  42: "vDYS8-t1rfZ", UFC3
  43: "vDYS8-N7FsO", KOF
  44: "vDYS8-vbyfY", MK
  45: "vDYS8-UgMqO", GREYHOUND
  46: "vDYS8-AXaJR", MOTOR
  47: "vDYS8-wZpoI", TEKKEN 8
  49: "vDYS8-lTgN2", MARBLE RUN
  50: "vDYS8-lTgN2", MARBLE CLASH
  51: "vDYS8-eemJ4", MARBLE SURVIVAL
  */

import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {},
  data() {
    return {
      options: {
        slidesToShow: 3,
        slidesToScroll: 1,
        dots: false,
        arrows: true,
        variableWidth: true,
        infinite: true,
        focusOnSelect: true,
        centerMode: true,
        centerPadding: "24px",
      },
    };
  },
  computed: {
    params() {
      return config.params;
    },
    newFeatures() {
      return config.newFeatures;
    },
    menu2() {
      return this.$store.getters.menu2;
    },
    gameList() {
      return config.gameList;
    },
  },
  watch: {
    menu2(newValue, oldValue) {
      var menu2 = 0;
      var val = null;
      if (newValue) {
        val = newValue;
      } else {
        val = oldValue;
      }
      for (var m = 0; m < this.gameList.length; m++) {
        if (this.gameList[m] == val) {
          menu2 = m;
          break;
        }
      }
      this.setSlide(menu2);
    },
  },
  destroyed() {
    clearTimeout(this.countDown);
    this.slickDestroy();
  },
  mounted() {
    this.slickInit();

    setTimeout(() => {
      var menu2 = 0;
      for (var m = 0; m < this.gameList.length; m++) {
        if (this.gameList[m] == this.menu2) {
          menu2 = m;
          break;
        }
      }
      this.setSlide(menu2);
    }, 1000);

    const tz = Intl.DateTimeFormat().resolvedOptions().timeZone;

    try {
      fetch("https://mywbet88.com/timezone?tz=" + tz)
        .then((response) => response.json())
        .then((data) => {
          this.diff = new Date(data.datetime).getTime() - new Date().getTime();
          this.countDown();
        });
    } catch (err) {
      console.log(err);
    }
  },
  methods: {
    getTimer(e) {
      if (this.params.hasOwnProperty(e) == false) {
        return "<div class='time-label'>ERROR</div>";
      }
      const dt = new Date(new Date().getTime());
      let n = dt.getMinutes();
      let f1 = Math.floor(n / this.params[e].duration);
      let f2 = Math.floor(n % this.params[e].duration);
      let f3 = f1 * this.params[e].duration + this.params[e].delay;
      let f4 = f3;
      if (f3 <= n) {
        f4 = f3 + this.params[e].duration;
      }
      let s = dt.getSeconds();
      let m = s == 0 ? f4 - n : f4 - n - 1;
      if (s != 0) {
        s = 60 - s;
      }

      var mmm = this.params[e].duration - this.params[e].live;
      if (m >= mmm) {
        return '<div class="live-label blink-now">' + this.$t("ui.live") + "</div>";
      } else {
        if (m == 0 && s <= 30) {
          return '<div class="live-label blink-now">' + this.$t("ui.live") + "</div>";
        } else {
          return '<div class="time-label">' + `${m}:${s < 10 ? "0" + s : s}` + "</div>";
        }
      }
    },
    countDown() {
      for (var m = 0; m < this.gameList.length; m++) {
        $(".sports-" + this.gameList[m] + " .ef-time").html(this.getTimer(this.gameList[m]));
      }
      setTimeout(this.countDown, 1000);
    },
    setMenu2(e) {
      var mi5 = {};
      mi5["menuX"] = false;
      mi5["menuY"] = "0";
      mi5["menu0"] = "all";
      mi5["menu1"] = "today";
      mi5["menu2"] = e;
      mi5["menu3"] = "hdpou";
      var changed = false;
      if (mi5["menuX"] != this.menuX) {
        changed = true;
      }
      if (mi5["menuY"] != this.menuY) {
        changed = true;
      }
      if (mi5["menu0"] != this.menu0) {
        changed = true;
      }
      if (mi5["menu1"] != this.menu1) {
        changed = true;
      }
      if (mi5["menu2"] != this.menu2) {
        changed = true;
      }
      if (mi5["menu3"] != this.menu3) {
        changed = true;
      }

      if (changed) {
        this.resetSelectLeague();
        this.clearSearch();
        mi5["src"] = "efSlider.setMenu2";
        this.$store.dispatch("layout/setMenuItems", mi5);
        EventBus.$emit("INVALIDATE");
        $(".market-menu").removeClass("active");
      }
    },
    clearSearch() {
      this.$store.dispatch("layout/setSearch", null);
    },
    resetSelectLeague() {
      this.$store.dispatch("layout/resetSelectLeague");
    },
    onInit() {},
    onDestroy() {},
    setSlide(e) {
      const $slick = $(this.$el);
      if (e != null) {
        $slick.slick("slickGoTo", e, true);
      }
    },
    onAfterChange(e, s, c) {
      this.setMenu2(parseInt(this.gameList[c]));
    },
    slickInit() {
      const $slick = $(this.$el);
      $slick.on("afterChange", this.onAfterChange);
      $slick.on("destroy", this.onDestroy);
      $slick.on("init", this.onInit);
      $slick.slick(this.options);
      $slick.slick("slickGoTo", 0, true);
      $("#slick-vgames").show();
    },
    slickDestroy() {
      const $slick = $(this.$el);
      $slick.off("afterChange", this.onAfterChange);
      $slick.off("destroy", this.onDestroy);
      $slick.off("init", this.onInit);
      $(this.$el).slick("unslick");
    },
  },
};
</script>

<style></style>
