<template lang="pug">
  .hx-main.orzOdds
    //- small.text-white {{ source.orz }}
    .hx-table.hx-match.hx-compact.hx-orz(:class="{ 'live': source.marketId == 3, 'alternate' : index % 2 == 0 }")
      .hx-cell.w-128
        .hx-row
          .hx-outright-date {{ this.$dayjs(source.matchTime).format("MM/DD/YYYY") }}
      .hx-cell.flex-fill
        .hx-row
          .hx-col.d-block.w-40r
            .hx.team-black {{ source.homeTeam }}
          //- xFavorite(:source="source")
      .hx-cell.w-128
        .hx-row
          orzItem(:details="details" :oddsType="oddsType")
      //- .hx-cell.w-40
      //-   .hx-row
      //-     .hx-col.hx-cols.w-100.d-flex.align-items-center.justify-content-center
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";
// import xFavorite from "@/components/desktop/main/xtable/xitem/xFavorite";
// import orzItem from "@/components/desktop/main/xtable/xitem/orzItem";

export default {
  components: {
    xFavorite: () => import("@/components/desktop/main/xtable/xitem/xFavorite"),
    orzItem: () => import("@/components/desktop/main/xtable/xitem/orzItem")
  },
  mixins: [mixinHDPOUOdds],
};
</script>