# Pinia Migration Example

## Overview
This document provides concrete examples of how to migrate from Vuex to Pinia, including state management patterns and best practices.

## Current Vuex Store Structure

```javascript
// src/store/_user.js (Current)
import errors from "@/errors";
import service from "@/library/_xhr-user";

export default {
  namespaced: true,
  state: {
    balance: 0,
    account: {},
    rememberMe: null,
    isPublic: false
  },
  mutations: {
    updateAccount(state, payload) {
      state.account = payload;
      if (state.account.player_wallet) {
        state.balance = state.account.player_wallet.available_balance;
      }
    },
    deleteAccount(state) {
      state.account = {};
      state.balance = 0;
      state.mmoMode = false;
    },
    updateBalance(state, payload) {
      state.balance = payload.balance;
      if (state.account != null && state.account.player_wallet != null) {
        state.account.player_wallet.available_balance = payload.balance;
        state.account.player_wallet.cash_balance = payload.account_balance;
        state.account.player_wallet.frozen_balance = payload.outstanding_balance;
      }
    },
    updateRememberMe(state, payload) {
      state.rememberMe = payload;
    }
  },
  actions: {
    doLogin(context, user) {
      return service.doLogin(context, user);
    },
    doLogout(context) {
      const feedback = {
        success: true,
        status: errors.logout.succeed
      };
      return new Promise(resolve => {
        context.commit("deleteAccount");
        resolve(feedback);
      });
    },
    getBalance(context) {
      return service.getBalance(context);
    }
  }
};
```

## Migrated Pinia Store

```javascript
// src/stores/user.js (Pinia)
import { defineStore } from 'pinia'
import errors from "@/errors"
import service from "@/library/_xhr-user"

export const useUserStore = defineStore('user', {
  state: () => ({
    balance: 0,
    account: {},
    rememberMe: null,
    isPublic: false
  }),

  getters: {
    isLoggedIn: (state) => {
      return state.account?.player_info?.session_token ? true : false
    },
    
    playerInfo: (state) => {
      return state.account?.player_info || {}
    },
    
    playerWallet: (state) => {
      return state.account?.player_wallet || {}
    },
    
    currencyCode: (state) => {
      return state.account?.player_wallet?.currency_code || null
    },
    
    sessionToken: (state) => {
      return state.account?.player_info?.session_token || null
    }
  },

  actions: {
    updateAccount(payload) {
      this.account = payload
      if (this.account.player_wallet) {
        this.balance = this.account.player_wallet.available_balance
      }
    },

    deleteAccount() {
      this.account = {}
      this.balance = 0
      this.mmoMode = false
    },

    updateBalance(payload) {
      this.balance = payload.balance
      if (this.account?.player_wallet) {
        this.account.player_wallet.available_balance = payload.balance
        this.account.player_wallet.cash_balance = payload.account_balance
        this.account.player_wallet.frozen_balance = payload.outstanding_balance
      }
    },

    updateRememberMe(payload) {
      this.rememberMe = payload
    },

    async doLogin(user) {
      return await service.doLogin(this, user)
    },

    async doLogout() {
      const feedback = {
        success: true,
        status: errors.logout.succeed
      }
      this.deleteAccount()
      return feedback
    },

    async getBalance() {
      return await service.getBalance(this)
    },

    async doLaunch(user) {
      return await service.doLaunch(this, user)
    },

    async doSwitch(user) {
      return await service.doSwitch(this, user)
    },

    async reLogin() {
      return await service.reLogin(this)
    },

    async setSettings(payload) {
      return await service.setSettings(this, payload)
    },

    setNickname(payload) {
      if (this.account.player_info) {
        this.account.player_info.nickname = payload
        this.account.player_info.has_secondary_account = true
      }
    }
  }
})
```

## Component Usage Migration

### Before (Vuex)
```vue
<template>
  <div>
    <p>Balance: {{ balance }}</p>
    <p>User: {{ playerInfo.nickname }}</p>
    <button @click="logout">Logout</button>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  computed: {
    ...mapGetters(['balance', 'playerInfo', 'isLoggedIn'])
  },
  methods: {
    ...mapActions('user', ['doLogout']),
    async logout() {
      await this.doLogout()
      this.$router.push('/')
    }
  }
}
</script>
```

### After (Pinia)
```vue
<template>
  <div>
    <p>Balance: {{ userStore.balance }}</p>
    <p>User: {{ userStore.playerInfo.nickname }}</p>
    <button @click="logout">Logout</button>
  </div>
</template>

<script>
import { useUserStore } from '@/stores/user'

export default {
  setup() {
    const userStore = useUserStore()
    
    const logout = async () => {
      await userStore.doLogout()
      // Navigation would be handled by router
    }
    
    return {
      userStore,
      logout
    }
  }
}
</script>
```

### Using Composition API (Recommended)
```vue
<template>
  <div>
    <p>Balance: {{ balance }}</p>
    <p>User: {{ playerInfo.nickname }}</p>
    <button @click="logout">Logout</button>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'

const userStore = useUserStore()
const router = useRouter()

// Make state reactive
const { balance, playerInfo, isLoggedIn } = storeToRefs(userStore)

const logout = async () => {
  await userStore.doLogout()
  router.push('/')
}
</script>
```

## Store Persistence with Pinia

### Install Pinia Plugin Persist
```bash
yarn add pinia-plugin-persistedstate
```

### Configure Persistence
```javascript
// src/stores/user.js
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    balance: 0,
    account: {},
    rememberMe: null,
    isPublic: false
  }),

  // ... actions and getters

  persist: {
    key: 'user-storage',
    storage: localStorage,
    paths: ['account', 'rememberMe'], // Only persist specific state
  }
})
```

## Main Application Setup

### Current (Vue 2 + Vuex)
```javascript
// src/main.js
import Vue from 'vue'
import store from '@/store'
import App from '@/app.vue'

new Vue({
  store,
  render: h => h(App)
}).$mount('#app')
```

### Migrated (Vue 3 + Pinia)
```javascript
// src/main.js
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import App from '@/app.vue'

const app = createApp(App)
const pinia = createPinia()

pinia.use(piniaPluginPersistedstate)
app.use(pinia)

app.mount('#app')
```

## Migration Benefits

### 1. Simplified Syntax
- No more mutations, actions can directly modify state
- Better TypeScript support
- Cleaner component integration

### 2. Better Developer Experience
- Hot module replacement out of the box
- Devtools support
- Better tree-shaking

### 3. Modular Design
- Each store is independent
- Easy to test individual stores
- Better code organization

## Migration Checklist

### Phase 1: Setup
- [ ] Install Pinia and related plugins
- [ ] Create stores directory structure
- [ ] Set up main application with Pinia

### Phase 2: Store Migration
- [ ] Convert each Vuex module to Pinia store
- [ ] Update service calls to work with Pinia
- [ ] Configure persistence for each store

### Phase 3: Component Updates
- [ ] Update all component store usage
- [ ] Replace mapGetters/mapActions with Pinia equivalents
- [ ] Test all store interactions

### Phase 4: Testing
- [ ] Update unit tests for stores
- [ ] Test component integration
- [ ] Verify persistence functionality

## Store Structure Mapping

```
Current Vuex Structure → Pinia Structure
├── src/store/
│   ├── index.js              → src/main.js (pinia setup)
│   ├── _user.js              → src/stores/user.js
│   ├── _layout.js            → src/stores/layout.js
│   ├── _cache.js             → src/stores/cache.js
│   ├── _betsingle.js         → src/stores/betting/single.js
│   ├── _betparlay.js         → src/stores/betting/parlay.js
│   ├── _betsinglemmo.js      → src/stores/betting/singleMmo.js
│   └── _betparlaymmo.js      → src/stores/betting/parlayMmo.js
```

## Common Pitfalls to Avoid

1. **Direct State Mutation in Components**
   - Don't modify store state directly in components
   - Always use actions for state modifications

2. **Forgetting storeToRefs**
   - Use `storeToRefs()` to maintain reactivity when destructuring

3. **Not Using Composition API**
   - Pinia works best with Composition API
   - Consider migrating components to `<script setup>`

4. **Overusing Global State**
   - Not all state needs to be in stores
   - Use local component state when appropriate

---

*This example shows the pattern for migrating all Vuex stores to Pinia. Follow this structure for each store module in your application.* 