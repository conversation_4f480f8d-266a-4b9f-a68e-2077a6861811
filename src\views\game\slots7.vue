<template lang="pug">
//- custom lobby
.wrapper.h-100v
  .preloader.d-flex.align-items-center.justify-content-center(v-if="error")
    .game-warning
      i.fad.fa-exclamation-triangle.mr-1
      span {{ error }}
  template(v-else)
    .lds-overlay(v-if="loading || (gameList == null && !url)")
      .lds-roller
        div
        div
        div
        div
        div
        div
        div
        div
    .game-list(v-else)
      template(v-if="url")
        .row.m-0
          .d-flex.w-100.align-items-center.justify-content-between.mt-3.ml-3.mr-3
            img(src="img/lobby/uusl-logo.png" height="54px")
            .btn.btn-outline-primary.btn-lg.ml-2.mb-1.text-uppercase(@click="url = ''") {{ $t("ui.back") }}
        .row.m-3.jili-wrapper
          iframe.jili-iframe(:src="url")
      template(v-else)
        .row.m-0
          .d-flex.w-100.align-items-center.justify-content-start.mt-3.ml-3
            img(src="img/lobby/uusl-logo.png" height="54px")
        .row.m-3
          .col(v-for='item in gameList' @click="launchGame(item.GameId)")
            img(:src="item.ImageUrl" style="width: 160px;")
        //- .row.m-3.mb-4
        //-   .btn.btn-outline-primary.btn-lg.ml-2.mb-1(@click="listMode = 0" :class="listMode == 0 ? 'active' : ''") ALL GAMES
        //-   .btn.btn-outline-primary.btn-lg.ml-2.mb-1(@click="listMode = 1" :class="listMode == 1 ? 'active' : ''") SLOTS
        //-   .btn.btn-outline-primary.btn-lg.ml-2.mb-1(@click="listMode = 2" :class="listMode == 2 ? 'active' : ''") FISHING
        //- .row.m-3(v-if="listMode == 0")
        //- .row.m-3
        //-   .col(v-for='item in fishGames' @click="launchGame(item.GameId)")
        //-     img(:src="getThumbnail(item.GameId)" style="width: 160px;")
        //-   .col(v-for='item in slotGames' @click="launchGame(item.GameId)")
        //-     img(:src="getThumbnail(item.GameId)" style="width: 160px;")
        //- .row.m-3(v-if="slotGames && listMode == 1")
        //-   .col(v-for='item in slotGames' @click="launchGame(item.GameId)")
        //-     img(:src="getThumbnail(item.GameId)" style="width: 160px;")
        //- .row.m-3(v-if="fishGames && listMode == 2")
        //-   .col(v-for='item in fishGames' @click="launchGame(item.GameId)")
        //-     img(:src="getThumbnail(item.GameId)" style="width: 160px;")
</template>

<script>
import config from "@/config";
import xhr from "@/library/_xhr-game.js";
import mixinTypes from "@/library/mixinTypes";

export default {
  data() {
    return {
      url: "",
      error: "",
      loading: false,
      gameList: null,
      listMode: 0,
      slotGames: [],
      fishGames: [],
      hotGames: [],
      hotGamesList: [],
    };
  },
  computed: {
    whiteLabel() {
      return this.$store.getters.whiteLabel.mode;
    },
  },
  beforeCreate() {
    $("html").addClass("minimal noscrollbar");
    $("body").addClass("minimal noscrollbar jili-bg");
  },
  created() {
    $("html").addClass("minimal noscrollbar");
    $("body").addClass("minimal noscrollbar jili-bg");

    if (this.whiteLabel) return;

    this.getGameList();
  },
  methods: {
    isMobileTablet() {
      var check = false;
      (function (a) {
        if (
          /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(
            a
          ) ||
          /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(
            a.substr(0, 4)
          )
        )
          check = true;
      })(navigator.userAgent || navigator.vendor || window.opera);

      if (!check) {
        if (
          navigator.userAgent.match(/Mac/) &&
          navigator.maxTouchPoints &&
          navigator.maxTouchPoints > 2
        ) {
          check = true;
        }
      }
      return check;
    },
    // getThumbnail(e) {
    //   return config.resourceUrl + "img/slots/uusl/ID_" + e.toString() + ".jpg";
    // },
    getGameList() {
      var args = {
        username: this.$store.getters.accountId,
        sessionid: this.$store.getters.sessionToken,
      };

      this.loading = true;
      xhr.getUuslGameList(args).then(
        (res) => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.gameList = res.data;
              // this.slotGames = [];
              // this.fishGames = [];
              // this.hotGames = [];

              // if (this.gameList.length > 0) {
              //   for (var i = 0; i < this.gameList.length; i++) {
              //     if (this.gameList[i].GameCategoryId == 5) {
              //       this.fishGames.push(this.gameList[i]);
              //     } else {
              //       this.slotGames.push(this.gameList[i]);
              //     }
              //   }
              // } else {
              //   this.slotGames = [];
              //   this.fishGames = [];
              //   this.hotGames = [];
              // }
            } else {
              this.error = this.$t("error.game_maintenance");
            }
          }
        },
        (err) => {
          this.loading = false;
          this.error = this.$t("error.game_maintenance");
        }
      );
    },
    launchGame(e) {
      //- this.gameList = null;
      var mobile = false;
      if (this.isMobileTablet()) {
        mobile = true;
      }
      var args = {
        username: this.$store.getters.accountId,
        sessionid: this.$store.getters.sessionToken,
        lang: this.$store.getters.language,
        mobile: mobile.toString(),
        game_code: e,
      };

      this.loading = true;
      xhr.launchUusl(args).then(
        (res) => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  //- window.location.replace(this.url);
                }, 200);
              } else {
                switch (res.status) {
                case "lotteryDisabled":
                case "currencyNotSupported":
                case "liveCasinoDisabled":
                case "EsportsDisabled":
                case "MiniGameDisabled":
                  this.error = this.$t("error." + res.status);
                  break;
                case "account_status_suspend":
                  this.error = this.$t("error.SUSPEND");
                  break;
                default:
                  this.error = this.$t("error.game_maintenance");
                  break;
                }
              }
            } else {
              switch (res.status) {
              case "lotteryDisabled":
              case "currencyNotSupported":
              case "liveCasinoDisabled":
              case "EsportsDisabled":
              case "MiniGameDisabled":
                this.error = this.$t("error." + res.status);
                break;
              case "account_status_suspend":
                this.error = this.$t("error.SUSPEND");
                break;
              default:
                this.error = this.$t("error.game_maintenance");
                break;
              }
            }
          }
        },
        (err) => {
          this.loading = false;
          switch (err.status) {
          case "lotteryDisabled":
          case "currencyNotSupported":
          case "liveCasinoDisabled":
          case "EsportsDisabled":
          case "MiniGameDisabled":
            this.error = this.$t("error." + err.status);
            break;
          case "account_status_suspend":
            this.error = this.$t("error.SUSPEND");
            break;
          default:
            this.error = this.$t("error.game_maintenance");
            break;
          }
        }
      );
    },
  },
};
</script>
