<template lang="pug">
  .hx-main.fglgOdds
    //- small.text-white {{ details }}
    .hx-table.hx-match.hx-compact(:class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }")
      .hx-cell.w-62
        .hx-row.h-100.hx-rows
          timePanel(:source="source")
      .hx-cell.flex-fill
        .hx-row.h-100.hx-rows
          xTeam(:source="source" isDraw=false)
          xFavorite(:source="source")
      template(v-for="item in cols")
        .hx-cell.w-138
          .hx-row
            .hx-col.hx-cols.w-69(v-for="i in fglg1")
              fglgItem(:details="details" :oddsType="oddsType" :i="i" :item="item")
          .hx-row
            .hx-col.hx-cols.w-69(v-for="i in fglg2")
              fglgItem(:details="details" :oddsType="oddsType" :i="i" :item="item")
        .w-69
          .hx-row.h-100
            .hx-col.hx-cols.w-69
              fglgItem(:details="details" :oddsType="oddsType" i="NG" :item="item")
      .hx-cell.w-40
        .hx-row.h-100.hx-rows
          .hx-col.hx-cols.w-100.d-flex.align-items-center.justify-content-center
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

// import timePanel from "@/components/desktop/main/xtable/timePanel";
// import xTeam from "@/components/desktop/main/xtable/xitem/xTeam";
// import xFavorite from "@/components/desktop/main/xtable/xitem/xFavorite";
// import fglgItem from "@/components/desktop/main/xtable/xitem/fglgItem";

export default {
  components: {
    // timePanel,
    // xTeam,
    // xFavorite,
    // fglgItem

    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    xTeam: () => import("@/components/desktop/main/xtable/xitem/xTeam"),
    xFavorite: () => import("@/components/desktop/main/xtable/xitem/xFavorite"),
    fglgItem: () => import("@/components/desktop/main/xtable/xitem/fglgItem")
  },
  mixins: [mixinHDPOUOdds],
  data() {
    return {
      cols: ['fglgo', 'fglgho'],
      fglg1: ['HFG', 'HLG'],
      fglg2: ['AFG', 'ALG']
    };
  }
};
</script>