<template lang="pug">
  .hx-main.hdpouoe1x2Odds
    //- small.text-white {{ source }}
    .hx-table.hx-match(:class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0 }")
      .hx-cell.w-62
        .hx-row.h-100.hx-rows
          timePanel(:source="source")
      .hx-cell.flex-fill
        .hx-row.h-100.hx-rows
          xTeam(:source="source" isDraw=true)
          xFavorite(:source="source")
      .hx-cell.w-328
        .hx-row.hx-rows(v-for="(dn, i) in details['kns']" :class="{ 'h-66' : (details['oxt'] != null && details['oxt'][i] != null) || (details['oxth'] != null && details['oxth'][i] != null) }")
          hdpItem(:details="details" :oddsType="oddsType" :i="i" betType="hdp")
          ouItem(:details="details" :oddsType="oddsType" :i="i" betType="ou")
          oxtItem(:details="details" :oddsType="oddsType" :i="i" betType="oxt")
          oeItem1(:details="details" :oddsType="oddsType" :i="i" betType="oe")
      .hx-cell.w-262
        .hx-row.hx-rows(v-for="(dn, i) in details['kns']" :class="{ 'h-66' : (details['oxt'] != null && details['oxt'][i] != null) || (details['oxth'] != null && details['oxth'][i] != null) }")
          hdpItem(:details="details" :oddsType="oddsType" :i="i" betType="hdph")
          ouItem(:details="details" :oddsType="oddsType" :i="i" betType="ouh")
          oxtItem(:details="details" :oddsType="oddsType" :i="i" betType="oxth")
      .hx-cell.w-40
        .hx-row.h-100.hx-rows
          .hx-col.hx-cols.w-100.d-flex.align-items-center.justify-content-center
            template(v-if="details['more'] > 0")
              .hx-more(
                :class="single ? '' : 'collapsed'"
                :id="'morehead_' + id"
                data-toggle='collapse'
                :aria-expanded="false"
                :data-target="'#morebet_' + id"
                :aria-controls="'morebet_' + id"
                @click="handleMore(matchId, $event.target)"
                )
                i.far.fa-chevron-up
                span &nbsp;{{ details['more'] }}

    template(v-if="details['more'] > 0")
      .hx-table.hx-match.hx-morebet.collapse(
        :id="'morebet_' + id"
        :aria-labelledby="'morehead_' + id"
        data-parent="#hdpou"
        :class="{ 'live': source.marketId == 3, 'alternate' : source.matchIndex % 2 == 0, 'show' : single }"
        )
        morePanel(
          v-if="source.matchId == selectedMatch"
          ref="morePanel"
          :uid="id"
          :details="moreItems"
          :child1Ids="details['child1']"
          :child2Ids="details['child2']"
          :matchId="matchId"
          :leagueId="leagueId"
          :marketType="marketType"
          :sportsType="sportsType"
          :betType="betType"
          :layoutIndex="layoutIndex"
        )
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

// import timePanel from "@/components/desktop/main/xtable/timePanel";
// import morePanel from "@/components/desktop/main/xheader/morePanel";
// import xTeam from "@/components/desktop/main/xtable/xitem/xTeam";
// import xFavorite from "@/components/desktop/main/xtable/xitem/xFavorite";
// import hdpItem from "@/components/desktop/main/xtable/xitem/hdpItem";
// import ouItem from "@/components/desktop/main/xtable/xitem/ouItem";
// import oxtItem from "@/components/desktop/main/xtable/xitem/oxtItem";
// import oeItem1 from "@/components/desktop/main/xtable/xitem/oeItem1";

export default {
  components: {
    // timePanel,
    // morePanel,
    // xTeam,
    // xFavorite,
    // hdpItem,
    // ouItem,
    // oxtItem,
    // oeItem1,

    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    morePanel: () => import("@/components/desktop/main/xheader/morePanel"),
    xTeam: () => import("@/components/desktop/main/xtable/xitem/xTeam"),
    xFavorite: () => import("@/components/desktop/main/xtable/xitem/xFavorite"),
    hdpItem: () => import("@/components/desktop/main/xtable/xitem/hdpItem"),
    ouItem: () => import("@/components/desktop/main/xtable/xitem/ouItem"),
    oxtItem: () => import("@/components/desktop/main/xtable/xitem/oxtItem"),
    oeItem1: () => import("@/components/desktop/main/xtable/xitem/oeItem1")
  },
  mixins: [mixinHDPOUOdds]
  // updated: function() {
  //   // this.$nextTick(function() {
  //   //   console.log("odds", new Date(), this.source.homeTeam);
  //   // });
  // },
};
</script>
