header.short {
	min-width: inherit !important;
}
header.short .topbar {
	height: 45px;
}
header .topbar .info-content {
	padding: 0 8px;
	height: 45px;
	/* -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
	        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5); */
}
header .topbar .info-content .logo {
	margin-right: 10px;
	min-width: 120px;
}
header .topbar .info-content .logo img {
	height: 40px;
	width: inherit;
}
header .topbar .info-content .nav-info {
	margin: 0 1px;
	margin-top: 0;
}
header .topbar .info-content .nav-info a {
	text-transform: capitalize;
	color: #fff;
	padding: 0 .8rem;
	letter-spacing: 1px;
	white-space: nowrap;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	   text-overflow: ellipsis;
	font-weight: 600;
	text-transform: uppercase;
	font-size: 14px;
	font-family: "Lato", sans-serif;
	text-decoration: none;
	line-height: 24px;
	border: 1px solid rgba(0, 0, 0, 0);
	position: relative;
	margin-right: 4px;
}
header .topbar .info-content .nav-info a:hover,header .topbar .info-content .nav-info a:focus,header .topbar .nav-info a.active {
}
header .topbar .nav-info a.active, header .topbar .nav-info a:hover, header .topbar .nav-info a:focus {
	transform: skewX(-15deg);
	color: #0A4782;
}
header .topbar .nav-info a.active::before, header .topbar .nav-info a:hover::before, header .topbar .nav-info a:focus::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background-color: #F6C344;
    height: 24px;
    z-index: -1;
	box-shadow: 0px 0px 6px #0000005C;
}
.info-wrapper {
	margin: 0 auto;
	padding: 1rem 1rem;
	font-size: 13px;
	position: relative;
	font-family: "Roboto", "Tahoma", sans-serif;
	min-width: 900px;
	width: 100%;
	max-width: 1340px;
	display: block;
	height: auto;
	min-height: 100%;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	will-change: transform;
	margin-top: 45px;
}
.info-wrapper.flexible {
	min-width: 0;
	max-width: auto;
	overflow: auto;
}
.info-wrapper .info-tablewrap.info-settings {
}
.info-wrapper .info-title {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: start;
	    -ms-flex-pack: start;
	        justify-content: start;
	font-family: "Lato", sans-serif;
	font-weight: 700;
	height: 31px;
}
.info-wrapper .info-title .info-icon {
	color: #014273;
	line-height: 1;
	font-size: 24px;
	width: 40px;
}
.info-wrapper .info-title .page-title {
	color: #014273;
	line-height: 1;
	text-transform: capitalize;
	font-size: 16px;
	-webkit-box-flex: 1;
	    -ms-flex-positive: 1;
	        flex-grow: 1;
	text-transform: uppercase;
}
.info-wrapper .info-title button {
	border: 1px solid #88ABC8;
	border-radius: 3px;
	background: #FFFFFF;
	color: #0C4B86;
	font-weight: 700;
}
.info-wrapper .info-title button.active {
	background-color: #276FA8 !important;
	color: #F6C344 !important;
	border: 1px solid #276FA8 !important;
}
.info-wrapper .info-title .page-title .breadcrumb {
	background: transparent;
}
.info-wrapper .info-title .page-title .breadcrumb .breadcrumb-item a {
	color: #014273;
}
.info-wrapper .info-title .page-title .breadcrumb .breadcrumb-item a:hover {
	text-decoration: none;
}
.info-wrapper .info-title .page-title .breadcrumb .breadcrumb-item.active {
	color: #014273;
}
.info-wrapper .info-title .page-title .breadcrumb .breadcrumb-item.active a {
	color: inherit;
}
.info-wrapper .info-title .page-title .breadcrumb .breadcrumb-item + .breadcrumb-item::before {
	content: ">";
}
.breadcrumb-item+.breadcrumb-item::before {
	color: #014273;
}
.info-wrapper .info-title .sorting .dropdown .dropdown-toggle {
	background: #0d3a64;
	border: none;
	font-size: 14px;
}
.info-wrapper .info-title .sorting .dropdown .dropdown-menu {
	font-size: 13px;
	padding: 0;
}
.info-wrapper .info-title .sorting .dropdown .dropdown-menu .dropdown-item {
	text-transform: capitalize;
	line-height: 33px;
	padding: 0 10px;
}
.info-wrapper .info-title .sorting .dropdown .dropdown-menu .dropdown-item:hover,.info-wrapper .info-title .sorting .dropdown .dropdown-menu .dropdown-item:focus,.info-wrapper .info-title .sorting .dropdown .dropdown-menu .dropdown-item.active {
	color: #fff;
	background: #0f4f8c;
}
.info-wrapper .info-title .info-btn {
	background: #0d3a64;
	color: #fff;
	line-height: 33px;
	border-radius: 5px 5px 5px 5px;
	padding: 0 16px;
	margin: 0 8px;
	cursor: pointer;
}
.info-wrapper .info-tablewrap {
	margin: 10px auto;
}
.info-wrapper .info-tablewrap .tab-content {
	padding: 0;
	margin: 0;
}
.table-info {
	background: #F4F7FC;
	font-family: "Roboto", sans-serif !important;
}
.table-info tr:hover,.table-info tr.grey:hover {
	background: #f5eeb8;
}
.table-info tr.grey {
	background: #FFFFFF;
}
.table-info > tbody > tr:first-child {
	background: url(/images/market-head-bg.png) no-repeat;
	background-size: cover;
}
.table-info tr th {
	color: #fff;
	padding: 6px 6px 12px 6px;
	line-height: 1;
	font-weight: normal;
	border-right: 1px solid #5991C1;
	white-space: nowrap;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	   text-overflow: ellipsis;
}
.table-info tr th:last-child {
	border-right: 0;
}
.table-info tr td {
	color: #01122b;
	padding: 3px 6px;
	line-height: 20px;
	border-right: 1px solid rgba(0, 0, 0, 0.1);
}
.table-info tr td:last-child {
	border-right: 0;
}
.table-info tr td .red {
	color: #b53f39;
}
.table-info tr td .blue {
	color: #2556b3;
}
.table-info tr td .number-circle {
	width: 24px;
	height: 24px;
	line-height: 24px;
	font-size: 12px;
	border-radius: 50%;
	color: #ffffff;
	background-color: #5574a7;
	text-align: center;
	margin: 0 2px;
}
.table-info tr td .color-vs {
	color: #7c7c7c;
}
.icon-info {
	color: #345684;
	font-size: 16px;
	cursor: pointer;
}
.icon-info:hover {
	color: #7a99c8;
}
.table-total {
	background: #DCE6EF;
	border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.table-total tr td {
	color: #01122b;
	padding: 0 6px;
	line-height: 20px;
}
.table-total tr td .red {
	color: #b53f39;
}
.table-result {
}
.table-result tr td {
	padding: 0 6px;
	line-height: 20px;
	border: 1px solid #bbbbbb;
	text-align: center;
}
.table-result tr.odd {
	background: #a4a4a4;
}
.table-result tr.odd td {
	border: 1px solid #a4a4a4;
	color: #fff;
}
.table-result tr.even {
	background: #fff;
	color: #01122b;
}
.table-result tr.even:hover {
	background: #f5eeb8;
}
.table-betresult {
	font-family: "Roboto", sans-serif !important;
}
.table-betresult tr.odd {
	background: #d0d0d0;
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
	padding: 0 6px;
	color: #000;
}
.table-betresult tr.even {
	background: #F4F7FC;
}
.table-betresult tr.even:hover {
	background: #f5eeb8;
}
.table-betresult tr.other {
	background: #FFFFFF;
}
.table-betresult tr.other:hover {
	background: #f5eeb8;
}
.table-betresult tr th {
	color: #fff;
	padding: 6px 6px 12px 6px;
	line-height: 1;
	font-weight: normal;
	border-right: 1px solid #215380;
    background: url(/images/market-head-bg.png) no-repeat;
    background-size: cover;
}
.table-betresult tr td {
	line-height: 28px;
	padding: 0 6px;
	border-right: 1px solid #bbbbbb;
}
.table-betresult tr th:last-child {
	border-right: 0;
}
.table-betresult tr td .red {
	color: #b53f39;
}
.table-betresult tr td:last-child {
	border-right: 0;
}
.table-betresult tr td .table-inside {
}
.table-betresult tr td .table-inside tr td {
	padding: 0 6px;
	line-height: 20px;
	border-right: 1px solid #bbbbbb;
	text-align: center;
}
.table-betresult tr td .table-inside tr.special {
	background: #b1b1b1;
}
.table-betresult tr td .table-inside tr.odd {
	background: #a4a4a4;
}
.table-betresult tr td .table-inside tr.odd td {
	border: 1px solid #a4a4a4;
	color: #fff;
}
.table-betresult tr td .table-inside tr.even {
	background: #fff;
	color: #01122b;
}
.table-betresult tr td .table-inside tr.even:hover {
	background: #f5eeb8;
}
.table-betresult tr td .table-inside tr td .red {
	color: #b53f39;
}
.table-message tr td {
	line-height: 20px;
	padding: 3px 6px 12px 6px;
}
.table-message tr td.special {
	color: #b53f39;
}
.no-message {
	background: #dfdfdf;
	color: #333;
	padding: 0.833em;
	text-align: center;
	font-weight: 700;
}
.info-wrapper .info-tablewrap .nav-tabs {
	border-bottom: 0;
	font-family: "Oswald", sans-serif;
	font-size: 14px;
	font-weight: 600;
	border-bottom: 1px solid #276FA8;
}
.info-wrapper .info-tablewrap .nav-tabs li.nav-item {
	min-width: 100px;
	background: #DCE6EF;
	margin-right: 4px;
	margin-bottom: 0;
	border-radius: 3px 3px 0px 0px;
}
.info-wrapper .info-tablewrap .nav-tabs li.nav-item:last-child {
	margin-right: 0;
}
.info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link {
	line-height: 1;
	padding: 0 1rem;
	text-align: center;
	border: 0;
	color: #568AB6;
	font-weight: 400;
	text-transform: uppercase;
	padding: 0.5rem 1rem;
	border-radius: 3px 3px 0px 0px;
}
.info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link:hover {
	border: none;
	border: 0;
}
.info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link.active {
	background: #276FA8;
	color: #F6C344;
	border: 0;
}
.bet-info {
	position: relative;
	font-size: 11px;
}
.bet-info .close-bet {
	position: absolute;
	top: 7px;
	right: 10px;
	cursor: pointer;
	color: #7c7c7c;
}
.bet-info.grey {
	background: #ececec;
}
.bet-info.grey .bet-type {
	color: #5574a7 !important;
}
.bet-info .bet-type {
	color: #b53f39;
	text-transform: capitalize;
	font-size: 11px;
	font-weight: 700;
}
.bet-info .bet-type.blue {
	color: #5574a7;
}
.bet-info .bet-detail {
	padding: 0 9px 0 7px;
}
.bet-info .bet-detail .name {
	font-size: 13px;
	font-weight: 400;
	text-transform: capitalize;
	color: #000;
	position: relative;
}
.bet-info .bet-detail .name .dollar {
	background: #0d3a64;
	color: #fff;
	text-align: center;
	border-radius: 3px 3px 3px 3px;
	cursor: pointer;
	position: absolute;
	right: 0;
}
.bet-info .bet-detail .name.red {
	color: #b53f39;
}
.bet-info .bet-detail .oddsdetail {
	font-size: 12px;
	font-weight: 300;
}
.bet-info .bet-detail .oddsdetail .selector-name {
	margin-right: 0.25em;
	font-size: 13px;
	display: flex;
	align-items: center;
}
.bet-info .bet-detail .oddsdetail .selector-score {
	color: #7c7c7c;
	margin-right: 0.25em;
	display: flex;
	align-items: center;
}
.bet-info .bet-detail .oddsdetail .selector-other {
	margin-right: 0.25em;
	display: flex;
	align-items: center;
	font-size: 12px;
}
.bet-info .bet-detail .oddsdetail .selector-odds {
	color: #01122b;
	font-weight: 700;
	margin-right: 0.25em;
	font-size: 16px;
	display: flex;
	align-items: center;
}
.bet-info .bet-detail .oddsdetail .selector-odds.accent {
	color: #b53f39;
}
.bet-info .bet-detail .oddsdetail .selector-odds.small {
	font-size: 13px;
	color: inherit;
}
.bet-info .bet-detail .oddsdetail .selector-odds.red {
	color: #b53f39 !important;
}
.bet-info .bet-detail .oddsdetail .stacks {
	font-weight: 700;
}
.bet-info.parlay-info {
	padding: 0px !important;
}
.bet-info.parlay-info .head {
	height: 19px;
}
.bet-info.parlay-info.live .head {
	background: #F4D1C5;
	color: #5e4037;
}
.bet-info.parlay-info.live .bet-parlay {
	background: #FFF0EA;
}
.bet-info .bet-detail.bet-parlay {
	border-left: 0px !important;
	padding: 0 0 0px 5px !important;
	margin: 0px !important;
}
.bet-info .bet-type {
	padding-left: 3px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
}
.bet-info .bet-detail .team {
	font-size: 10px;
	color: grey;
}
#modal-result {
	font-size: 13px;
	z-index: 2000;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100vh;
}
#modal-result .namelist-sport {
	font-size: 13px;
	color: #5574a7;
	font-weight: 700;
}
#modal-result .team-sport {
	line-height: 20px;
}
.result-selection {
	color: #014273;
	border-bottom: 1px solid #215380;
}
.result-selection .text-common {
	line-height: 28px;
}
.result-selection .form-control.datepicker {
	background: transparent;
	border: 1px solid #276FA8;
	background: #276FA8;
}
.result-selection .input-group .input-group-prepend .input-group-text,.result-selection .input-group .input-group-append .input-group-text {
	background: transparent;
	border: 1px solid #88ABC8;
	color: #0C4B86;
}
.result-selection .form-control.datepicker {
	padding: 0 10px;
	line-height: 28px;
	height: 30px;
	font-size: 13px;
	color: #fff;
}
.result-selection .btn-result {
	padding: 0 10px;
	line-height: 28px;
	height: 30px;
	font-size: 13px;
	border: 1px solid #88ABC8;
	background: transparent;
	color: #0C4B86;
	font-weight: 500;
}
.result-selection .btn-result:hover,.result-selection .btn-result.active {
	color: #fff;
	background: #276FA8 !important;
	border: 1px solid #276FA8;
}
.result-selection .form-control.datepicker::-webkit-input-placeholder {
	color: #333 !important;
}
.result-selection .form-control.datepicker::-moz-placeholder {
	color: #333 !important;
}
.result-selection .form-control.datepicker:-ms-input-placeholder {
	color: #333 !important;
}
.result-selection .form-control.datepicker:-moz-placeholder {
	color: #333 !important;
}
.result-selection .dropdown-menu {
	padding: 0;
	font-size: 13px;
}
.result-selection .dropdown-menu .dropdown-item {
	text-transform: capitalize;
	line-height: 33px;
	padding: 0 10px;
}
.result-selection .dropdown-menu .dropdown-item:hover,.result-selection .dropdown-menu .dropdown-item:focus,.result-selection .dropdown-menu .dropdown-item.active {
	color: #fff;
	background: #276FA8;
}
.info-tablewrap .pagination .page-item {
	margin: 0 2px;
}
.info-tablewrap .pagination .page-item .page-link {
	padding: 0 6px;
	line-height: 20px;
	height: 20px;
	font-size: 13px;
	background: #00000000;

	border: 1px solid #0F4F8C;
	border-radius: 3px;
	color: #0F4F8C;
}
.info-tablewrap .pagination .page-item .page-link.active {
	background: #0d3a64;
	color: #fff;
}
.info-tablewrap .setting-left {
}
.info-tablewrap .setting-left ul {
	margin: 0;
	padding: 0;
}
.info-tablewrap .setting-left ul li {
	background: #e4e4e4;
	list-style: none;
}
.info-tablewrap .setting-left ul li a {
	text-transform: capitalize;
	color: #545454;
	line-height: 30px;
	display: block;
	padding: 0 10px;
}
.info-tablewrap .setting-left ul li a.active,.info-tablewrap .setting-left ul li a.active:hover {
	background: #0f4f8c;
	color: #fff;
	text-decoration: none;
}
.info-tablewrap .setting-left ul li a:hover {
	text-decoration: none;
	color: #0f4f8c;
}
.info-tablewrap .setting-right {
	background: #e4e4e4;
}
.info-tablewrap .setting-right .text-account {
	line-height: 28px;
	text-transform: capitalize;
}
.info-tablewrap .setting-right .text-account.normal {
	text-transform: inherit;
}
.info-tablewrap .setting-right .form-control {
	padding: 0 10px;
	line-height: 28px;
	height: 30px;
	font-size: 13px;
	border-radius: 0 0 0 0;
}
.info-tablewrap .setting-right .btn-result {
	padding: 0 16px;
	line-height: 28px;
	height: 30px;
	font-size: 13px;
	border: 1px solid #ffffff;
	background: #cdcdcd;
	color: #333;
	border-radius: 0 0 0 0;
}
.info-tablewrap .setting-right .btn-result.active {
	color: #fff;
	background: #0f4f8c !important;
	border: 1px solid #ffffff;
	border-radius: 0 0 0 0;
}
.info-tablewrap .setting-right .text-muted {
	font-size: 12px;
}
.tooltip-inner {
	text-align: left;
	font-size: 12px;
}
.info-tablewrap .setting-right .custom-control-label {
	line-height: 28px;
	cursor: pointer;
}
.info-tablewrap .setting-right .custom-control-label::before {
	top: 6px;
}
.info-tablewrap .setting-right .custom-control-label::after {
	top: 6px;
}
.info-tablewrap .setting-right .custom-control-input:checked ~ .custom-control-label::before {
	background: #276FA8;
	border: 1px solid #276FA8;
}
.info-tablewrap .setting-right .number-list ul {
	padding: 0 0.5em;
	counter-reset: section;
	-webkit-column-count: 4;
	   -moz-column-count: 4;
	        column-count: 4;
	-webkit-column-gap: 1em;
	   -moz-column-gap: 1em;
	        column-gap: 1em;
}
.info-tablewrap .setting-right .number-list li {
	padding: 0;
	page-break-inside: avoid-column;
	-webkit-column-break-inside: avoid;
	   -moz-column-break-inside: avoid;
	        break-inside: avoid-column;
	overflow: hidden;
	min-height: 2.5em;
}
.info-tablewrap .setting-right .number-list li:not(.active)::before {
	counter-increment: section;
	content: counter(section) ".";
	width: 1.5em;
	display: inline-block;
	position: relative;
	top: 0.2em;
	text-align: right;
	margin-right: 0.3em;
	font-size: 13px;
}
.info-tablewrap .setting-right fieldset li {
	display: -ms-flexbox;
	display: -webkit-box;
	display: flex;
	-ms-flex-flow: row wrap;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	        flex-flow: row wrap;
	padding: 1em;
	position: relative;
	padding-right: 25%;
}
.info-tablewrap .setting-right .number-list li > div {
	float: none;
	-ms-flex: 1;
	-webkit-box-flex: 1;
	        flex: 1;
	overflow: hidden;
	margin-bottom: 0.5em;
}
.info-tablewrap .setting-right .number-list li .form {
	background: #fff;
	color: #545454;
	border: 1px solid #cdcdcd;
	min-height: 1.5em;
	border-radius: 3px;
	padding: 0 0.5em;
	font-size: 12px;
	cursor: move;
	line-height: 24px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
}
.info-tablewrap .setting-right .number-list li .form img {
	margin-right: 2px;
}
.info-tablewrap .setting-right .number-list li .form.drag > div {
	padding-right: 1.2em;
	white-space: nowrap;
	-o-text-overflow: ellipsis;
	   text-overflow: ellipsis;
	overflow: hidden;
}
.info-tablewrap .setting-right .number-list li .form.drag::after {
	font-family: "Font Awesome 5 Duotone";
	content: "\f39c";
	position: absolute;
	top: 3px;
	right: 10px;
	font-weight: 700;
	font-size: 15px;
	color: #545454;
}
.info-tablewrap .setting-right .number-list li .form.drag:hover {
	background-color: #f5eeb8;
	cursor: move;
	border-color: #a5a5a5;
	color: #01122b;
}
.dark {
	color: #000;
}
.bg-blue {
	background: #345684;
	color: #fff;
	height: 19px;
	width: 22px;
	position: relative;
	border-radius: 3px 3px 3px 3px;
	border: 1px solid hsla(0, 0%, 100%, 0.4);
	display: inline-block;
	text-align: center;
	line-height: 18px;
	cursor: pointer;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
}
.bg-blue.x-blue {
	border: 0;
	border-radius: 0;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	margin: -1px 0 0 0;
	height: 19px;
	width: 20px;
	font-size: 10px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
}
.bg-blue.x-blue.btn-inner {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	width: 21px;
	border-radius: 3px 3px 3px 3px;
	border: 1px solid #ffffff88;
	margin: 0;
	padding: 0;
	line-height: 1;
	background: #345684cc;
}
.bg-blue.x-blue.btn-inner:hover {
	background: #345684;
}
.fa-chevron-down,.fa-chevron-up {
	-webkit-transition: 0.3s -webkit-transform ease-in-out;
	transition: 0.3s -webkit-transform ease-in-out;
	-o-transition: 0.3s transform ease-in-out;
	transition: 0.3s transform ease-in-out;
	transition: 0.3s transform ease-in-out, 0.3s -webkit-transform ease-in-out;
	font-size: 12px;
}
.collapsed .fa-chevron-down,.collapsed .fa-chevron-up {
	-webkit-transform: rotate(180deg);
	    -ms-transform: rotate(180deg);
	        transform: rotate(180deg);
}
.ticket-status {
	text-transform: capitalize;
	border-radius: 10px 10px 10px 10px;
	background: #0d3a64;
	padding: 0 10px;
	color: #fff;
	line-height: 22px;
	display: inline-block;
}
.notes {
}
@media (max-width: 1140px) {
	header .topbar,header .toolbar {
		display: inline-block;
	}
}
@media (min-width: 838px) and (max-width: 1271px) {
	body {
		width: 100%;
	}
}
.bg-tnc {
	background: #e4e4e4;
}
.rules-wrap {
}
.rules-wrap h6 {
	font-family: "Oswald";
	font-weight: 600;
	font-size: 20px;
}
#navbar-rules {
	height: calc(100vh - 120px);
	overflow-x: hidden;
	overflow-y: auto;
}
.scrollspyied {
	position: relative;
	overflow-x: hidden;
	overflow-y: auto;
	padding: 12px 12px 0 0;
}
.scrollspyied #item-1 {
}
.scrollspyied #item-2 {
}
.accordion-rules .left-content {
	max-height: calc(100vh - 275px);
	position: relative;
	overflow-x: hidden;
	overflow-y: overlay;
}
.accordion-rules ul > li {
	list-style: none;
}
.accordion-rules ul > li > a.nav-link {
	margin: 2px 0;
	padding: 4px 8px;
	color: #01122b;
}
.rules-content {
}
.rules-content:last-child {
	padding: 0;
}
.rules-content ol {
	counter-reset: section;
	list-style-type: none;
}
.rules-content ol > li {
	padding-top: 8px;
}
.rules-content ol > li::before {
	counter-increment: section;
	content: counters(section, ".") ".";
	padding-right: 20px;
	font-weight: normal;
}
.rules-content ol.layer01 li::before {
	font-weight: 700;
	position: absolute;
	left: 0;
}
#item-4.rules-content ol.layer01 > li::before {
	visibility: hidden;
}
.rules-content ol.layer02 {
	list-style-position: outside;
	margin-left: 20px;
}
.rules-content ol.layer02 li::before {
	font-weight: normal;
	left: 40px;
}
.rules-content ol.layer03 li::before {
	left: 100px;
}
.rules-content ol.layer03 li {
	margin-left: 20px;
}
.accordion-rules .card {
	background: #e4e4e4;
	border-radius: 0 0 0 0;
}
.accordion-rules .card-header:after {
	font-family: "Font Awesome 5 Duotone";
	content: "\f068";
	float: right;
	font-weight: 700;
}
.accordion-rules .card-header.collapsed:after {
	content: "\f067";
}
.accordion-rules .card .card-header {
	padding: 8px;
	background: #0f4f8c;
	color: #efdd00;
	border-radius: 0 0 0 0;
}
.accordion-rules .card .card-header.collapsed {
	padding: 8px;
	background: #a4a4a4;
	color: #fff;
	cursor: pointer;
}
ul.ul-01 > li {
	list-style-type: none;
}
ul.ul-02 > li {
	list-style-type: disc;
}
ul.ul-02 > li > a.nav-link {
	padding: 2px 4px 2px 0;
	color: #01122b;
}
ul.ul-02 > li > a.nav-link:hover {
	color: #0361ee;
}
.rules-content ol.alpha {
	padding: 0 0 0 16px;
	counter-reset: list;
}
.rules-content ol.alpha > li {
	padding-left: 20px;
	list-style: none;
}
.rules-content ol.layer03 > li > ol.alpha > li {
	margin-left: 10px;
}
.rules-content ol.layer02 ol.alpha li::before {
	counter-increment: list;
	content: counter(list, lower-alpha) ") ";
	left: 100px;
}
.rules-content ol.layer03 ol.alpha li::before {
	left: 160px;
}
.rules-content ol.roman {
	padding: 0 0 0 16px;
	counter-reset: roman;
}
.rules-content ol.roman > li {
	padding-left: 20px;
	list-style: none;
	position: relative;
}
.rules-content ol.layer03 > li > ol.roman > li {
	margin-left: 10px;
}
.rules-content ol.layer02 ol.roman li::before {
	counter-increment: roman;
	content: "(" counter(roman, lower-roman) ") ";
}
.rules-content ol.layer03 ol.roman li::before {
	left: -25px;
}
.underline {
	text-decoration: underline;
}
.info-wrapper .match-info {
	font-size: 10px;
}
.accordion-rules a {
	color: #fff;
}
.btn-seperator {
	height: 100%;
	border-left: 1px solid #00000066;
	border-right: 1px solid #ffffff22;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	margin: 0 7px;
}
.bet-detail .small {
	font-size: 11px;
	line-height: 1.2;
}
.bet-detail .small span:first-child {
	width: 50px;
	display: inline-block;
	text-align: right;
}