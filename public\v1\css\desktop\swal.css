.swal2-shown {
  border-right: 0 !important;
  margin-right: 0 !important;
  padding-right: 0 !important;
}
.swal2-container.swal2-shown {
  background-color: rgba(0, 0, 0, 0) !important;
}
.swal2-container {
  padding: 1px !important;
  font-family: "<PERSON><PERSON>", "<PERSON>o", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  border-radius: 0.5rem !important;
  z-index: 20000 !important;
}
.swal2-modal {
  border: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.33) !important;
  box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.33) !important;
  background-color: #fff !important;
  color: #a3bedbdd !important;
  z-index: 20000 !important;
}
.swal2-actions {
  -webkit-box-pack: end !important;
  -ms-flex-pack: end !important;
  justify-content: flex-end !important;
  margin: 0.5rem 0 !important;
  padding: 0 0.5rem !important;
}
.swal2-styled {
  margin: 0.25rem !important;
  padding: 0.3725rem 2rem !important;
  font-weight: 500 !important;
  font-size: 0.8725em !important;
  outline: none !important;
  -webkit-animation-name: dx-zoom-in;
  animation-name: dx-zoom-in;
  -webkit-animation-duration: 250ms;
  animation-duration: 250ms;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  border-radius: 0.2rem !important;
}
.swal2-styled:focus {
  outline: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}
.swal2-confirm {
  color: #fff !important;
  background: #1C62A0 !important;
  border: 1px solid #0062cc !important;
}
.swal2-confirm:hover {
  color: #fff !important;
  background: #0f5291 !important;
}
.swal2-cancel {
  padding: 0.3725rem 1rem !important;
  color: #6c757d !important;
  background: transparent !important;
  border: 1px solid #6c757d !important;
}
.swal2-cancel:hover {
  color: #fff !important;
  background: #6e7880 !important;
}
.swal2-content {
  font-size: 1em !important;
  padding: 1.5em 0 !important;
  -webkit-animation-name: dx-zoom-in;
  animation-name: dx-zoom-in;
  -webkit-animation-duration: 250ms;
  animation-duration: 250ms;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  color: #014273 !important;
}
.swal2-popup {
  padding: 0 !important;
}
.swal2-header {
  display: -ms-flexbox !important;
  display: -webkit-box !important;
  display: flex !important;
  -ms-flex-direction: row !important;
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: normal !important;
  flex-direction: row !important;
  -ms-flex-align: center !important;
  -webkit-box-align: center !important;
  align-items: center !important;
  border-top-left-radius: 0.5rem !important;
  border-top-right-radius: 0.5rem !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background-color: #DCE6EF;
  padding: 8px 8px !important;
}
.swal2-title {
  margin: 0 !important;
  font-size: 1rem !important;
  -webkit-animation-name: dx-zoom-in;
  animation-name: dx-zoom-in;
  -webkit-animation-duration: 250ms;
  animation-duration: 250ms;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  color: #000 !important;
  font-size: 14px !important;
}
.swal2-success {
  border-color: rgba(40, 167, 69, 1) !important;
}
.swal2-success .swal2-success-line-tip {
  background-color: rgba(40, 167, 69, 1) !important;
}
.swal2-success .swal2-success-line-long {
  background-color: rgba(40, 167, 69, 1) !important;
}
.swal2-success .swal2-success-ring {
  border-color: rgba(40, 167, 69, 0.3) !important;
}
.swal2-error {
  border-color: rgba(220, 53, 69, 0.3) !important;
}
.swal2-error .swal2-x-mark-line-left {
  background-color: rgba(220, 53, 69, 1) !important;
}
.swal2-error .swal2-x-mark-line-right {
  background-color: rgba(220, 53, 69, 1) !important;
}
.swal2-info {
  border-color: rgba(23, 162, 184, 0.3) !important;
}
.swal2-info .swal2-icon-text {
  color: rgba(23, 162, 184, 1) !important;
}
.swal2-warning {
  border-color: rgba(255, 193, 7, 0.3) !important;
}
.swal2-warning .swal2-icon-text {
  color: rgba(255, 193, 7, 1) !important;
}
.swal2-question {
  border-color: rgba(108, 117, 125, 0.3) !important;
}
.swal2-question .swal2-icon-text {
  color: rgba(108, 117, 125, 1) !important;
}
.swal2-icon {
  margin: 0.5em !important;
  display: none !important;
}
.swal2-icon.swal2-error [class^="swal2-x-mark-line"][class$="left"] {
  left: 16px !important;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.swal2-icon.swal2-error [class^="swal2-x-mark-line"][class$="right"] {
  display: none !important;
}
.swal2-icon.swal2-error [class^="swal2-x-mark-line"] {
  display: none !important;
}
.swal2-icon.swal2-error .swal2-x-mark {
  display: none !important;
}
.swal2-icon.swal2-success [class^="swal2-success-circular-line"][class$="left"] {
  display: none !important;
}
.swal2-icon.swal2-success [class^="swal2-success-circular-line"] {
  display: none !important;
}
.swal2-icon.swal2-success [class^="swal2-success-line"][class$="tip"] {
  display: none !important;
}
.swal2-icon.swal2-success [class^="swal2-success-line"] {
  display: none !important;
}
.swal2-icon.swal2-success [class^="swal2-success-line"][class$="long"] {
  display: none !important;
}
.swal2-icon.swal2-success [class^="swal2-success-line"] {
  display: none !important;
}
.swal2-icon.swal2-success .swal2-success-ring {
  display: none !important;
}
.swal2-icon.swal2-success .swal2-success-fix {
  display: none !important;
}
.swal2-icon.swal2-success [class^="swal2-success-circular-line"][class$="right"] {
  display: none !important;
}
.swal2-icon.swal2-success [class^="swal2-success-circular-line"] {
  display: none !important;
}
.swal2-icon.swal2-error::before {
  content: "\f00d";
  color: #dc3545;
  font-family: "Font Awesome 5 Duotone";
  font-weight: 900;
  height: auto !important;
}
.swal2-icon.swal2-success {
  border-color: #a5dc86 !important;
}
.swal2-icon.swal2-success::before {
  content: "\f00c";
  color: #28a745;
  font-family: "Font Awesome 5 Duotone";
  font-weight: 900;
  height: auto !important;
}

.swal2-icon.swal2-warning .swal2-icon-content {
  font-size: 1.75rem !important;
  font-weight: 900 !important;
  margin-top: -3px;
}
.swal2-icon.swal2-info .swal2-icon-content {
  font-size: 1.75rem !important;
  font-weight: 900 !important;
  margin-top: -3px;
}
.swal2-icon {
  width: 32px !important;
  height: 32px !important;
  margin: 8px !important;
  line-height: 32px !important;
}
.swal2-icon::before {
  font-size: 24px !important;
  height: 92% !important;
}