<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 19.2.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 50 50" style="enable-background:new 0 0 50 50;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#009AAF;}
	.st1{fill:#004D82;}
	.st2{fill:#07BED6;}
	.st3{fill:#D6D5CC;}
	.st4{fill:#F4F2E4;}
	.st5{fill:#ED6E0F;}
	.st6{fill:#4C5254;}
	.st7{fill:#202D2A;}
	.st8{fill:#EA1F0A;}
	.st9{fill:#EF683F;}
	.st10{fill:#AA2011;}
	.st11{fill:#F9B843;}
	.st12{fill:#E88813;}
	.st13{fill:#C9C9C9;}
</style>
<g>
	<g>
		<rect x="2.2" y="28.7" class="st0" width="45.6" height="18.4"/>
		<g>
			<polyline class="st1" points="2.2,28.7 47.8,28.7 47.8,47.1 			"/>
			<path class="st1" d="M40.4,13.5c0,4.2-5.4,7.8-9.5,7.8H19c-4.1,0-9.5-3.6-9.5-7.8v-3.3c0-4.2,5.4-7.3,9.5-7.3h11.8
				c4.2,0,9.6,3.1,9.6,7.3V13.5z"/>
		</g>
		<path class="st2" d="M47.8,29.5c0,1.2-1,2.1-2.2,2.1H4.4c-1.2,0-2.2-1-2.2-2.1V10.1c0-1.2,1-2.1,2.2-2.1h41.2
			c1.2,0,2.2,0.9,2.2,2.1V29.5z"/>
		<path class="st0" d="M2.2,10.1c0-1.2,1-2.1,2.2-2.1h41.2c1.2,0,2.2,0.9,2.2,2.1v19.3c0,1.2-1,2.1-2.2,2.1"/>
		<path class="st3" d="M44.1,26.2c0,0.8-0.4,1-1.1,1H7c-0.7,0-1.1-0.3-1.1-1V13.5c0-0.7,0.4-1.8,1.1-1.8h36c0.8,0,1.1,1,1.1,1.8
			V26.2z"/>
		<path class="st4" d="M44.1,26c0,0.8-0.4,1.2-1.1,1.2H7c-0.7,0-1.1-0.4-1.1-1.2V15.7C5.9,14.8,6.3,14,7,14h36
			c0.8,0,1.1,0.8,1.1,1.7V26z"/>
		<g>
			<path class="st3" d="M11,26.2c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2-0.1-0.4-0.1c-0.1-0.1-0.2-0.1-0.3-0.2c-0.1-0.1-0.1-0.2-0.1-0.4
				v-0.1v-0.1c0.1-0.5,0.1-1,0.4-1.5c0.1-0.5,0.4-1,0.7-1.6c0.3-0.6,0.5-1.1,0.9-1.6c0.3-0.6,0.7-1.1,1-1.7
				c-0.3-0.1-0.5-0.1-0.7-0.1c-0.1-0.1-0.3-0.1-0.4-0.1c-0.2-0.1-0.3-0.1-0.4-0.1c-0.1,0-0.2,0-0.4,0s-0.3,0-0.4,0
				c-0.1,0-0.2,0-0.3,0.1c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0.1-0.1,0.1-0.2,0.3c-0.1,0.3-0.3,0.4-0.4,0.6c-0.1,0.1-0.2,0.2-0.3,0.2
				s-0.1,0-0.2-0.1s-0.1-0.1-0.1-0.3c0-0.1,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2c0.1-0.5,0.1-0.9,0.3-1.2c0.1-0.3,0.3-0.5,0.4-0.7
				c0.1-0.1,0.4-0.3,0.6-0.4c0.2-0.1,0.4-0.1,0.6-0.1c0.2,0,0.4,0,0.6,0.1c0.2,0.1,0.4,0.1,0.6,0.1c0.2,0.1,0.4,0.1,0.7,0.1
				C12.8,17,13,17,13.2,17c0.1,0,0.2,0,0.3,0s0.1,0,0.2,0c0.2,0,0.4,0,0.5-0.1c0.1,0,0.2,0,0.3-0.1c0.1,0,0.1,0,0.1-0.1h0.1
				c0.2,0,0.4,0.1,0.4,0.1c0.1,0.1,0.1,0.3,0.1,0.5c0,0.1,0,0.3,0,0.5c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.1,0.1-0.3,0.2
				c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.2,0.1-0.2,0.1c-0.3,0.5-0.6,1-0.8,1.5c-0.2,0.5-0.4,1-0.6,1.5c-0.1,0.5-0.3,1.1-0.4,1.6
				c-0.1,0.6-0.3,1.2-0.4,1.8c0,0.1-0.1,0.3-0.2,0.4c-0.1,0.1-0.2,0.1-0.4,0.2c-0.1,0.1-0.3,0.1-0.4,0.1
				C11.2,26.2,11.1,26.2,11,26.2z"/>
			<path class="st3" d="M12.2,19.6c-0.1,0.3-0.4,0.5-0.5,0.8c-0.3,0.6-0.6,1.1-0.9,1.6c-0.3,0.5-0.4,1.1-0.7,1.6
				c-0.1,0.5-0.3,1-0.4,1.5v0.1v0.1c0,0.2,0.1,0.4,0.1,0.4c0.1,0.1,0.2,0.2,0.3,0.2c0.1,0.1,0.2,0.1,0.4,0.1c0.1,0,0.2,0,0.2,0
				c0.1,0,0.2,0,0.4-0.1c0.1,0,0.3-0.1,0.4-0.1c0.1-0.1,0.2-0.1,0.4-0.2c0.1-0.1,0.1-0.2,0.2-0.4c0.1-0.7,0.3-1.2,0.4-1.8
				c0.1-0.6,0.3-1.1,0.4-1.6c0.1-0.5,0.4-1,0.6-1.5c0.2-0.5,0.5-1,0.8-1.5l0.1-0.1L12.2,19.6z"/>
			<path class="st3" d="M23.8,26.2c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2-0.1-0.4-0.1C23,26,22.9,26,22.9,25.9s-0.1-0.2-0.1-0.4v-0.1v-0.1
				c0.1-0.5,0.1-1,0.4-1.5c0.1-0.5,0.4-1,0.7-1.6c0.3-0.6,0.5-1.1,0.9-1.6c0.3-0.6,0.7-1.1,1-1.7c-0.3-0.1-0.5-0.1-0.7-0.1
				c-0.1-0.1-0.3-0.1-0.4-0.1c-0.2-0.1-0.4-0.1-0.4-0.1s-0.2,0-0.4,0c-0.1,0-0.3,0-0.4,0s-0.2,0-0.3,0.1s-0.1,0.1-0.2,0.1
				c-0.1,0.1-0.1,0.1-0.2,0.3c-0.1,0.3-0.3,0.4-0.4,0.6c-0.1,0.1-0.2,0.2-0.3,0.2s-0.1,0-0.2-0.1c-0.1-0.1-0.1-0.1-0.1-0.3
				c0-0.1,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2c0.1-0.5,0.1-0.9,0.3-1.2c0.1-0.3,0.3-0.5,0.4-0.7c0.1-0.1,0.4-0.3,0.6-0.4s0.4-0.1,0.6-0.1
				s0.4,0,0.6,0.1c0.2,0.1,0.4,0.1,0.6,0.1c0.2,0.1,0.4,0.1,0.7,0.1C25.6,17,25.8,17,26,17c0.1,0,0.2,0,0.3,0c0.1,0,0.1,0,0.2,0
				c0.2,0,0.4,0,0.5-0.1c0.1,0,0.2,0,0.3-0.1c0.1,0,0.1,0,0.1-0.1h0.1c0.2,0,0.4,0.1,0.4,0.1c0.1,0.1,0.1,0.3,0.1,0.5
				c0,0.1,0,0.3,0,0.5c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.1,0.1-0.3,0.2c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.2,0.1-0.2,0.1
				c-0.3,0.5-0.6,1-0.8,1.5c-0.2,0.5-0.4,1-0.6,1.5c-0.1,0.5-0.3,1.1-0.4,1.6c-0.1,0.6-0.3,1.2-0.4,1.8c0,0.1-0.1,0.3-0.2,0.4
				c-0.1,0.1-0.2,0.1-0.4,0.2S24,25.9,23.9,26C24,26.2,23.8,26.2,23.8,26.2z"/>
			<path class="st3" d="M25,19.6c-0.1,0.3-0.4,0.5-0.5,0.8c-0.3,0.6-0.6,1.1-0.9,1.6c-0.3,0.5-0.4,1.1-0.7,1.6
				c-0.1,0.5-0.3,1-0.4,1.5v0.1v0.1c0,0.2,0.1,0.4,0.1,0.4c0.1,0.1,0.2,0.2,0.3,0.2c0.1,0.1,0.2,0.1,0.4,0.1s0.2,0,0.2,0
				c0.1,0,0.2,0,0.4-0.1c0.1,0,0.3-0.1,0.4-0.1c0.1-0.1,0.2-0.1,0.4-0.2c0.1-0.1,0.1-0.2,0.2-0.4c0.1-0.7,0.3-1.2,0.4-1.8
				c0.1-0.6,0.3-1.1,0.4-1.6c0.1-0.5,0.4-1,0.6-1.5c0.2-0.5,0.5-1,0.8-1.5l0.1-0.1L25,19.6z"/>
			<path class="st3" d="M36.5,26.2c-0.1,0-0.1,0-0.2,0s-0.2-0.1-0.4-0.1c-0.1-0.1-0.2-0.1-0.3-0.2c-0.1-0.1-0.1-0.2-0.1-0.4v-0.1
				v-0.1c0.1-0.5,0.1-1,0.4-1.5c0.1-0.5,0.4-1,0.7-1.6c0.3-0.6,0.5-1.1,0.9-1.6c0.3-0.6,0.7-1.1,1-1.7c-0.3-0.1-0.5-0.1-0.7-0.1
				c-0.1-0.1-0.3-0.1-0.4-0.1c-0.1-0.1-0.3-0.1-0.4-0.1s-0.2,0-0.4,0c-0.1,0-0.3,0-0.4,0c-0.1,0-0.2,0-0.3,0.1
				c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0.1-0.1,0.1-0.2,0.3c-0.1,0.3-0.3,0.4-0.4,0.6c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0-0.1,0-0.2-0.1
				c-0.1-0.1-0.1-0.1-0.1-0.3c0-0.1,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2c0.1-0.5,0.1-0.9,0.3-1.2s0.3-0.5,0.4-0.7
				c0.1-0.1,0.4-0.3,0.6-0.4s0.4-0.1,0.6-0.1c0.2,0,0.4,0,0.6,0.1c0.2,0.1,0.4,0.1,0.6,0.1c0.2,0.1,0.4,0.1,0.7,0.1
				c0.2,0.1,0.4,0.1,0.7,0.1c0.1,0,0.2,0,0.3,0c0.1,0,0.1,0,0.2,0c0.2,0,0.4,0,0.5-0.1c0.1,0,0.2,0,0.3-0.1c0.1,0,0.1,0,0.1-0.1
				s0.1,0,0.1,0c0.2,0,0.4,0.1,0.4,0.1c0.1,0.1,0.1,0.3,0.1,0.5c0,0.1,0,0.3,0,0.5c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.1,0.1-0.3,0.2
				c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.2,0.1-0.2,0.1c-0.3,0.5-0.6,1-0.8,1.5c-0.2,0.5-0.4,1-0.6,1.5s-0.3,1.1-0.4,1.6
				c-0.1,0.6-0.3,1.2-0.4,1.8c0,0.1-0.1,0.3-0.2,0.4c-0.1,0.1-0.2,0.1-0.4,0.2c-0.1,0.1-0.3,0.1-0.4,0.1
				C36.8,26.2,36.6,26.2,36.5,26.2z"/>
			<path class="st3" d="M37.7,19.6c-0.1,0.3-0.4,0.5-0.5,0.8c-0.3,0.6-0.6,1.1-0.9,1.6c-0.3,0.5-0.4,1.1-0.7,1.6
				c-0.1,0.5-0.3,1-0.4,1.5v0.1v0.1c0,0.2,0.1,0.4,0.1,0.4c0.1,0.1,0.2,0.2,0.3,0.2c0.1,0.1,0.2,0.1,0.4,0.1s0.2,0,0.2,0
				c0.1,0,0.2,0,0.4-0.1c0.1,0,0.3-0.1,0.4-0.1c0.1-0.1,0.2-0.1,0.4-0.2c0.1-0.1,0.1-0.2,0.2-0.4c0.1-0.7,0.3-1.2,0.4-1.8
				c0.1-0.6,0.3-1.1,0.4-1.6c0.1-0.5,0.4-1,0.6-1.5c0.2-0.5,0.5-1,0.8-1.5l0.1-0.1L37.7,19.6z"/>
		</g>
		<path class="st5" d="M11,24.9c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2-0.1-0.4-0.1c-0.1-0.1-0.2-0.1-0.3-0.2c-0.1-0.1-0.1-0.2-0.1-0.4V24
			V24c0.1-0.5,0.1-1,0.4-1.5c0.1-0.5,0.4-1,0.7-1.6c0.3-0.6,0.5-1.1,0.9-1.6c0.3-0.6,0.7-1.1,1-1.7c-0.3-0.1-0.5-0.1-0.7-0.1
			s-0.3-0.1-0.4-0.1c-0.1-0.1-0.2-0.1-0.4-0.1c-0.1,0-0.2,0-0.4,0s-0.3,0-0.4,0s-0.2,0-0.3,0.1c-0.1,0.1-0.1,0.1-0.2,0.1
			s-0.1,0.1-0.2,0.3c-0.1,0.3-0.3,0.4-0.4,0.6c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0-0.1,0-0.2-0.1c-0.1-0.1-0.1-0.1-0.1-0.3
			c0-0.1,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2C9,17.2,9,16.8,9.2,16.5c0.1-0.3,0.3-0.5,0.4-0.7c0.1-0.1,0.4-0.3,0.6-0.4
			c0.2-0.1,0.4-0.1,0.6-0.1c0.2,0,0.4,0,0.6,0.1c0.2,0.1,0.4,0.1,0.6,0.1c0.2,0.1,0.4,0.1,0.7,0.1c0.2,0.1,0.4,0.1,0.7,0.1
			c0.1,0,0.2,0,0.3,0s0.1,0,0.2,0c0.2,0,0.4,0,0.5-0.1c0.1,0,0.2,0,0.3-0.1c0.1,0,0.1,0,0.1-0.1h0.1c0.2,0,0.4,0.1,0.4,0.1
			c0.1,0.1,0.1,0.3,0.1,0.5c0,0.1,0,0.3,0,0.5c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.1,0.1-0.3,0.2c-0.1,0.1-0.2,0.1-0.3,0.2
			c-0.1,0.1-0.2,0.1-0.2,0.1c-0.3,0.5-0.6,1-0.8,1.5c-0.2,0.5-0.4,1-0.6,1.5c-0.1,0.5-0.3,1.1-0.4,1.6c-0.1,0.6-0.3,1.2-0.4,1.8
			c0,0.1-0.1,0.3-0.2,0.4c-0.1,0.1-0.2,0.1-0.4,0.2c-0.1,0.1-0.3,0.1-0.4,0.1C11.2,24.8,11.1,24.9,11,24.9z"/>
		<path class="st5" d="M23.8,24.9c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2-0.1-0.4-0.1c-0.1-0.1-0.2-0.1-0.3-0.2c-0.1-0.1-0.1-0.2-0.1-0.4V24
			V24c0.1-0.5,0.1-1,0.4-1.5c0.1-0.5,0.4-1,0.7-1.6c0.3-0.6,0.5-1.1,0.9-1.6c0.3-0.6,0.7-1.1,1-1.7c-0.3-0.1-0.5-0.1-0.7-0.1
			s-0.3-0.1-0.4-0.1c-0.1-0.1-0.2-0.1-0.4-0.1c-0.1,0-0.2,0-0.4,0c-0.1,0-0.3,0-0.4,0s-0.2,0-0.3,0.1c-0.1,0.1-0.1,0.1-0.2,0.1
			c-0.1,0.1-0.1,0.1-0.2,0.3c-0.1,0.3-0.3,0.4-0.4,0.6c-0.1,0.1-0.2,0.1-0.3,0.1s-0.1,0-0.2-0.1s-0.1-0.1-0.1-0.3c0-0.1,0-0.1,0-0.1
			c0-0.1,0-0.1,0-0.2c0.1-0.5,0.1-0.9,0.3-1.2c0.1-0.3,0.3-0.5,0.4-0.7c0.1-0.1,0.4-0.3,0.6-0.4s0.4-0.1,0.6-0.1
			c0.2,0,0.4,0,0.6,0.1c0.2,0.1,0.4,0.1,0.6,0.1c0.2,0.1,0.4,0.1,0.7,0.1c0.2,0.1,0.4,0.1,0.7,0.1c0.1,0,0.2,0,0.3,0
			c0.1,0,0.1,0,0.2,0c0.2,0,0.4,0,0.5-0.1c0.1,0,0.2,0,0.3-0.1c0.1,0,0.1,0,0.1-0.1h0.1c0.2,0,0.4,0.1,0.4,0.1
			c0.1,0.1,0.1,0.3,0.1,0.5c0,0.1,0,0.3,0,0.5c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.1,0.1-0.3,0.2c-0.1,0.1-0.2,0.1-0.3,0.2
			c-0.1,0.1-0.2,0.1-0.2,0.1c-0.3,0.5-0.6,1-0.8,1.5s-0.4,1-0.6,1.5c-0.1,0.5-0.3,1.1-0.4,1.6c-0.1,0.6-0.3,1.2-0.4,1.8
			c0,0.1-0.1,0.3-0.2,0.4c-0.1,0.1-0.2,0.1-0.4,0.2c-0.1,0.1-0.3,0.1-0.4,0.1C24,24.8,23.8,24.9,23.8,24.9z"/>
		<path class="st5" d="M36.5,24.9c-0.1,0-0.1,0-0.2,0s-0.2-0.1-0.4-0.1c-0.1-0.1-0.2-0.1-0.3-0.2c-0.1-0.1-0.1-0.2-0.1-0.4V24V24
			c0.1-0.5,0.1-1,0.4-1.5c0.1-0.5,0.4-1,0.7-1.6c0.3-0.6,0.5-1.1,0.9-1.6c0.3-0.6,0.7-1.1,1-1.7c-0.3-0.1-0.5-0.1-0.7-0.1
			s-0.3-0.1-0.4-0.1c-0.1-0.1-0.2-0.1-0.4-0.1s-0.2,0-0.4,0c-0.1,0-0.3,0-0.4,0c-0.1,0-0.2,0-0.3,0.1c-0.1,0.1-0.1,0.1-0.2,0.1
			s-0.1,0.1-0.2,0.3c-0.1,0.3-0.3,0.4-0.4,0.6c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0-0.1,0-0.2-0.1c-0.1-0.1-0.1-0.1-0.1-0.3
			c0-0.1,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2c0.1-0.5,0.1-0.9,0.3-1.2c0.1-0.3,0.3-0.5,0.4-0.7c0.1-0.1,0.4-0.3,0.6-0.4
			c0.2-0.1,0.4-0.1,0.6-0.1c0.2,0,0.4,0,0.6,0.1c0.2,0.1,0.4,0.1,0.6,0.1c0.2,0.1,0.4,0.1,0.7,0.1c0.2,0.1,0.4,0.1,0.7,0.1
			c0.1,0,0.2,0,0.3,0c0.1,0,0.1,0,0.2,0c0.2,0,0.4,0,0.5-0.1c0.1,0,0.2,0,0.3-0.1c0.1,0,0.1,0,0.1-0.1s0.1,0,0.1,0
			c0.2,0,0.4,0.1,0.4,0.1c0.1,0.1,0.1,0.3,0.1,0.5c0,0.1,0,0.3,0,0.5c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.1,0.1-0.3,0.2
			c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.2,0.1-0.2,0.1c-0.3,0.5-0.6,1-0.8,1.5c-0.2,0.5-0.4,1-0.6,1.5c-0.1,0.5-0.3,1.1-0.4,1.6
			c-0.1,0.6-0.3,1.2-0.4,1.8c0,0.1-0.1,0.3-0.2,0.4c-0.1,0.1-0.2,0.1-0.4,0.2c-0.1,0.1-0.3,0.1-0.4,0.1
			C36.8,24.8,36.6,24.9,36.5,24.9z"/>
		<g>
			<path class="st0" d="M19.1,28.5c0,0.4-0.4,0.7-0.7,0.7l0,0c-0.4,0-0.7-0.3-0.7-0.7V11.8c0-0.4,0.4-0.7,0.7-0.7l0,0
				c0.4,0,0.7,0.3,0.7,0.7V28.5z"/>
			<path class="st0" d="M32.4,28.5c0,0.4-0.4,0.7-0.7,0.7l0,0c-0.4,0-0.7-0.3-0.7-0.7V11.8c0-0.4,0.4-0.7,0.7-0.7l0,0
				c0.4,0,0.7,0.3,0.7,0.7V28.5z"/>
			<ellipse transform="matrix(0.9015 -0.4328 0.4328 0.9015 -0.7284 7.2587)" class="st0" cx="15.6" cy="5.2" rx="3.5" ry="0.8"/>
		</g>
		<rect x="23.5" y="41.2" class="st6" width="2.9" height="5.9"/>
		<polyline class="st7" points="23.5,41.2 26.5,41.2 26.5,46.3 		"/>
		<circle class="st8" cx="25" cy="34" r="6"/>
		<ellipse transform="matrix(0.7069 -0.7073 0.7073 0.7069 -15.1725 24.7326)" class="st9" cx="22.3" cy="30.7" rx="2.1" ry="1"/>
		<path class="st10" d="M29.2,29.9c2.4,2.4,2.4,6.1,0,8.4c-2.4,2.4-6.1,2.4-8.4,0"/>
		<path class="st11" d="M28.9,40.4c0,0.7-0.6,1.5-1.2,1.5h-5.2c-0.7,0-1.2-0.7-1.2-1.5l0,0c0-0.7,0.6-1.5,1.2-1.5h5.2
			C28.3,39,28.9,39.8,28.9,40.4L28.9,40.4z"/>
		<path class="st12" d="M28.9,40.6L28.9,40.6c0,0.7-0.6,1.3-1.2,1.3h-5.2c-0.7,0-1.2-0.6-1.2-1.3l0,0"/>
		<g>
			<circle class="st1" cx="6.8" cy="35.5" r="1.2"/>
			<circle class="st1" cx="6.8" cy="39.6" r="1.2"/>
			<circle class="st1" cx="6.8" cy="43.7" r="1.2"/>
		</g>
		<g>
			<circle class="st0" cx="43.2" cy="35.5" r="1.2"/>
			<circle class="st0" cx="43.2" cy="39.6" r="1.2"/>
			<circle class="st0" cx="43.2" cy="43.7" r="1.2"/>
		</g>
	</g>
	<path class="st13" d="M30.9,2.9c3.4,0,7.7,2.1,9.1,5.1h5.6c1.2,0,2.2,0.9,2.2,2.1v18.5v0.8v17.6H26.5h-2.9H2.2V29.5v-0.8V10.1
		c0-1.2,1-2.1,2.2-2.1H10c1.4-3.1,5.6-5.1,9-5.1H30.9 M30.9,1.9H19c-3.4,0-7.7,1.9-9.6,5.1h-5c-1.8,0-3.2,1.4-3.2,3.1v18.5v0.8v17.6
		c0,0.6,0.4,1,1,1h21.3h2.9h21.3c0.6,0,1-0.4,1-1V29.5v-0.8V10.1c0-1.7-1.4-3.1-3.2-3.1h-5C38.7,3.9,34.3,1.9,30.9,1.9L30.9,1.9z"/>
</g>
</svg>
