<template lang="pug">
  .new-betdetail(:class="{ changed: betslip.oddsChanged, 'grey': betslip.marketType != 3 }")
    .team(
      :class="isBallDisplay == false ? '' : ((betslip.giving == 0 && betslip.homeAway == 2) ? 'red' : (betslip.giving == 1 && betslip.homeAway == 1) ? 'red' : '')")
      | {{ betslip.betDisplay }}
    .score.text-center.font-weight-bold(v-if="betslip.marketType == 3 && betslip.score != ''") [{{ betslip.score }}]
    .new-oddsdetail
      span.font-weight-bold &nbsp;{{ betslip.betType }}
      span.font-weight-bold &nbsp;
        span(:class="{ red : ballDisplay.ball_display < 0 }") {{ ballDisplay.ball_display }}
        span(:class="{ red : ballDisplay.criteria2 < 0 }") ({{ ballDisplay.criteria2 }})
      span &nbsp;@
      span.font-weight-bold.odds(:class="{ red: betslip.val < 0 }") &nbsp;{{ betslip.val }}
</template>

<script>
import config from "@/config";
import naming from "@/library/_name";

export default {
  props: {
    betslip: {
      type: Object
    }
  },
  computed: {
    debug() {
      return config.debugMode;
    },
    betType() {
      return this.$t("m.BT_" + this.betslip.betType);
    },
    ballDisplay() {
      return this.betslip.fact;
    },
    isBallDisplay() {
      var result = this.ballDisplay;
      if (["HDP", "HDPH"].includes(this.betslip.betType)) {
        return result != null && result != "0";
      } else {
        return false;
      }
    }
  }
};
</script>