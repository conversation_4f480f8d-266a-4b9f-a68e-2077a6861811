import naming from "@/library/_name";
import config from "@/config";

export default {
  methods: {
    setPick(sd, odds) {
      switch (sd.betType) {
        case "HDP":
        case "HDPH":
          sd.giving = odds.home_giving;
          sd.parlay = 1;
          sd.ballDisplay = odds.ball_display;
          switch (sd.idx) {
            case "10":
              sd.betTeamId = sd.giving == 1 ? sd.homeId : sd.awayId;
              sd.betTeamName = sd.giving == 1 ? sd.homeName : sd.awayName;
              sd.origin = odds.odds_g2;
              sd.homeAway = sd.giving == 1 ? 1 : 2;
              break;
            case "9":
              sd.betTeamId = sd.giving == 1 ? sd.awayId : sd.homeId;
              sd.betTeamName = sd.giving == 1 ? sd.awayName : sd.homeName;
              sd.origin = odds.odds_e2;
              sd.homeAway = sd.giving == 1 ? 2 : 1;
              break;
          }
          sd.betDisplay = sd.betTeamName;
          break;
        case "OU":
        case "OUH":
          sd.giving = odds.home_giving;
          sd.parlay = 1;
          sd.ballDisplay = odds.ball_display;
          switch (sd.idx) {
            case "12":
              sd.betTeamId = sd.homeId;
              sd.betTeamName = sd.homeName;
              sd.betDisplay = this.$t("ui.over");
              sd.origin = odds.odds_o2;
              sd.homeAway = 1;
              break;
            case "11":
              sd.betTeamId = sd.awayId;
              sd.betTeamName = sd.awayName;
              sd.betDisplay = this.$t("ui.under");
              sd.origin = odds.odds_u2;
              sd.homeAway = 2;
              break;
          }
          break;
        case "OE":
        case "OEH":
        case "OEHM":
        case "OEHMH":
        case "OEAW":
        case "OEAWH":
          sd.giving = 0;
          sd.parlay = 1;
          sd.ballDisplay = "0";
          switch (sd.idx) {
            case "5":
              sd.betTeamId = sd.homeId;
              sd.betTeamName = sd.homeName;
              sd.betDisplay = this.$t("m.BT_ODD");
              sd.origin = odds.odds_o2;
              sd.homeAway = 1;
              break;
            case "7":
              sd.betTeamId = sd.awayId;
              sd.betTeamName = sd.awayName;
              sd.betDisplay = this.$t("m.BT_EVEN");
              sd.origin = odds.odds_e2;
              sd.homeAway = 2;
              break;
          }
          break;
        case "1X2":
        case "1X2H":
          sd.giving = 0;
          sd.parlay = 1;
          sd.ballDisplay = "0";
          switch (sd.idx) {
            case "5":
              sd.betTeamId = sd.homeId;
              sd.betTeamName = sd.homeName;
              sd.betDisplay = sd.betType == "1X2" ? this.$t("m.BT_FT1") : this.$t("m.BT_HT1");
              sd.origin = odds.odds_1_ori;
              sd.homeAway = 1;
              break;
            case "7":
              sd.betTeamId = sd.awayId;
              sd.betDisplay = sd.betType == "1X2" ? this.$t("m.BT_FT2") : this.$t("m.BT_HT2");
              sd.origin = odds.odds_2_ori;
              sd.homeAway = 2;
              break;
            case "6":
              sd.betTeamId = null;
              sd.betTeamName = null;
              sd.betDisplay = sd.betType == "1X2" ? this.$t("m.BT_FTX") : this.$t("m.BT_HTX");
              sd.origin = odds.odds_X_ori;
              sd.homeAway = 3;
              break;
          }
          break;
      }
    },
    setBetType(sd, odds) {
      switch (sd.betType) {
        case "HDP":
        case "HDPH":
          sd.giving = odds[7];
          sd.parlay = odds[13];
          sd.ballDisplay = odds[8];
          switch (sd.idx) {
            case "10":
              sd.betTeamId = sd.giving == 1 ? sd.homeId : sd.awayId;
              sd.betTeamName = sd.giving == 1 ? sd.homeName : sd.awayName;
              sd.origin = odds[24];
              sd.homeAway = sd.giving == 1 ? 1 : 2;
              break;
            case "9":
              sd.betTeamId = sd.giving == 1 ? sd.awayId : sd.homeId;
              sd.betTeamName = sd.giving == 1 ? sd.awayName : sd.homeName;
              sd.origin = odds[23];
              sd.homeAway = sd.giving == 1 ? 2 : 1;
              break;
          }
          sd.betDisplay = sd.betTeamName;
          break;
        case "OU":
        case "OUH":
          sd.giving = odds[7];
          sd.parlay = odds[13];
          sd.ballDisplay = odds[8];
          switch (sd.idx) {
            case "12":
              sd.betTeamId = sd.homeId;
              sd.betTeamName = sd.homeName;
              sd.betDisplay = this.$t("ui.over");
              sd.origin = odds[24];
              sd.homeAway = 1;
              break;
            case "11":
              sd.betTeamId = sd.awayId;
              sd.betTeamName = sd.awayName;
              sd.betDisplay = this.$t("ui.under");
              sd.origin = odds[23];
              sd.homeAway = 2;
              break;
          }
          break;
        case "OE":
        case "OEH":
        case "OEHM":
        case "OEHMH":
        case "OEAW":
        case "OEAWH":
          sd.giving = 0;
          sd.parlay = odds[8];
          sd.ballDisplay = odds[17];
          switch (sd.idx) {
            case "5":
              sd.betTeamId = sd.homeId;
              sd.betTeamName = sd.homeName;
              sd.betDisplay = this.$t("m.BT_ODD");
              sd.origin = odds[18];
              sd.homeAway = 1;
              break;
            case "7":
              sd.betTeamId = sd.awayId;
              sd.betTeamName = sd.awayName;
              sd.betDisplay = this.$t("m.BT_EVEN");
              sd.origin = odds[19];
              sd.homeAway = 2;
              break;
          }
          break;
        case "ML":
        case "MLH":
          sd.giving = 0;
          sd.parlay = odds[8];
          sd.ballDisplay = odds[17];
          switch (sd.idx) {
            case "5":
              sd.betTeamId = sd.homeId;
              sd.betTeamName = sd.homeName;
              sd.betDisplay = this.$t("ui.home");
              sd.origin = odds[18];
              sd.homeAway = 1;
              break;
            case "7":
              sd.betTeamId = sd.awayId;
              sd.betTeamName = sd.awayName;
              sd.betDisplay = this.$t("ui.away");
              sd.origin = odds[19];
              sd.homeAway = 2;
              break;
          }
          break;
        case "1X2":
        case "1X2H":
          sd.giving = 0;
          sd.parlay = odds[8];
          sd.ballDisplay = odds[17];
          switch (sd.idx) {
            case "5":
              sd.betTeamId = sd.homeId;
              sd.betTeamName = sd.homeName;
              sd.betDisplay = sd.betType == "1X2" ? this.$t("m.BT_FT1") : this.$t("m.BT_HT1");
              sd.origin = odds[18];
              sd.homeAway = 1;
              break;
            case "7":
              sd.betTeamId = sd.awayId;
              sd.betDisplay = sd.betType == "1X2" ? this.$t("m.BT_FT2") : this.$t("m.BT_HT2");
              sd.origin = odds[20];
              sd.homeAway = 2;
              break;
            case "6":
              sd.betTeamId = null;
              sd.betTeamName = null;
              sd.betDisplay = sd.betType == "1X2" ? this.$t("m.BT_FTX") : this.$t("m.BT_HTX");
              sd.origin = odds[19];
              sd.homeAway = 3;
              break;
          }
          break;
        case "1X2HDP":
        case "1X2HDPH":
          sd.giving = 0;
          sd.parlay = odds[8];
          sd.ballDisplay = odds[17].toString();
          switch (sd.idx) {
            case "5":
              sd.betTeamId = sd.homeId;
              sd.betTeamName = sd.homeName;
              sd.betDisplay = this.$t("m.BT_H");
              sd.origin = odds[18];
              sd.homeAway = 1;
              break;
            case "7":
              sd.betTeamId = sd.awayId;
              sd.betTeamName = sd.awayName;
              sd.betDisplay = this.$t("m.BT_A");
              sd.origin = odds[20];
              sd.homeAway = 2;
              break;
            case "6":
              sd.betTeamId = null;
              sd.betTeamName = null;
              sd.betDisplay = this.$t("ui.draw");
              sd.origin = odds[19];
              sd.homeAway = 3;
              break;
          }
          break;
        case "CS":
        case "CSH":
        case "TG":
        case "TGH":
        case "BS":
          sd.giving = 0;
          sd.parlay = odds[8];
          sd.betDisplay = odds[6];
          sd.origin = odds[17];
          sd.homeAway = 0;
          break;

        case "CSHTFT":
          sd.giving = 0;
          sd.parlay = odds[38];
          sd.betDisplay = sd.criteria1 + ' / ' + config.CSHTFTX[sd.idx - 5];
          sd.origin = odds[sd.idx + 42];
          sd.homeAway = 0;
          // console.log("setBetType", sd.origin, sd.idx, odds[sd.idx + 42], odds);
          break;
        case "ETGHTFT":
          sd.giving = 0;
          sd.parlay = odds[38];
          sd.betDisplay = sd.criteria1 + ' / ' + config.ETGHTFTX[sd.idx - 5];
          sd.origin = odds[sd.idx + 42];
          sd.homeAway = 0;
          break;
        case "HTFT":
          sd.giving = 0;
          sd.parlay = odds[8];
          sd.betDisplay = this.$t("m.BT_" + odds[6]);
          sd.origin = odds[17];
          sd.homeAway = 0;
          break;
        case "DC":
        case "DCH":
          sd.giving = 0;
          sd.parlay = odds[8];
          sd.betDisplay = this.$t("m.BT_" + odds[6] + "_DC");
          sd.origin = odds[17];
          sd.homeAway = 0;
          break;
        case "FGLG":
        case "FGLGH":
        case "CL":
        case "HNB":
        case "ANB":
        case "DNB":
        case "DNBH":
        case "TWTN":
        case "1X2OU":
        case "DCOU":
        case "ETG":
        case "EHTG":
        case "EATG":
        case "ETGH":
        case "EHTGH":
        case "EATGH":
          sd.giving = 0;
          sd.parlay = odds[8];
          sd.betDisplay = this.$t("m.BT_" + odds[6]);
          sd.origin = odds[17];
          sd.homeAway = 0;
          break;
        case "WM":
        case "OUOE":
        case "HTFTOE":
          sd.giving = 0;
          sd.parlay = odds[8];
          sd.betDisplay = this.$t("m.BK_" + odds[6]);
          sd.origin = odds[17];
          sd.homeAway = 0;
          break;
        case "OR":
          sd.giving = 0;
          sd.parlay = odds[8];
          sd.betDisplay = odds[6];
          sd.origin = odds[15];
          sd.homeAway = 0;
          sd.betTeamId = sd.homeId;
          break;
      }
    },
    setBetTypeMMO(sd, odds) {
      switch (sd.betType) {
        case "HDP":
        case "HDPH":
          sd.giving = odds[7];
          sd.parlay = odds[13];
          sd.ballDisplay = odds[25];
          sd.criteria2 = odds[24];
          switch (sd.idx) {
            case "10":
              sd.betTeamId = sd.giving == 1 ? sd.homeId : sd.awayId;
              sd.betTeamName = sd.giving == 1 ? sd.homeName : sd.awayName;
              sd.origin = odds[22];
              sd.homeAway = sd.giving == 1 ? 1 : 2;
              break;
            case "9":
              sd.betTeamId = sd.giving == 1 ? sd.awayId : sd.homeId;
              sd.betTeamName = sd.giving == 1 ? sd.awayName : sd.homeName;
              sd.origin = odds[22];
              sd.homeAway = sd.giving == 1 ? 2 : 1;
              break;
          }
          sd.betDisplay = sd.betTeamName;
          sd.fact = naming.ballDisplayMMO(sd.ballDisplay, sd.giving, sd.homeAway, sd.betType, sd.criteria2);
          break;
        case "OU":
        case "OUH":
          sd.giving = odds[7];
          sd.parlay = odds[13];
          sd.ballDisplay = odds[25];
          sd.criteria2 = odds[24];
          switch (sd.idx) {
            case "12":
              sd.betTeamId = sd.homeId;
              sd.betTeamName = sd.homeName;
              sd.betDisplay = this.$t("ui.over");
              sd.origin = odds[23];
              sd.homeAway = 1;
              break;
            case "11":
              sd.betTeamId = sd.awayId;
              sd.betTeamName = sd.awayName;
              sd.betDisplay = this.$t("ui.under");
              sd.origin = odds[23];
              sd.homeAway = 2;
              break;
          }
          sd.fact = naming.ballDisplayMMO(sd.ballDisplay, sd.giving, sd.homeAway, sd.betType, sd.criteria2);
          break;
      }
    },
  },
};
