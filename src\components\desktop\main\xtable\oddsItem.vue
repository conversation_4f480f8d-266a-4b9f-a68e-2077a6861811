<template lang="pug">
.bet-value(:class="[valueClass]" @click="placeBet($event.target)")
  | {{ value }}
</template>

<script>
import cal from "@/library/_calculation";
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";

export default {
  props: {
    odds: {
      type: Array
    },
    idx: {
      type: String
    },
    typ: {
      type: String
    },
    cls: {
      type: String
    },
    dataType: {
      type: String
    }
  },
  data() {
    return {
      trigger: false,
      timer: null,
      old: null,
      dir: null,
      defStake: 0,
      newPage: true
    };
  },
  computed: {
    betType() {
      return this.$store.state.layout.menu3;
    },
    id() {
      return this.odds[3];
    },
    value() {
      return this.odds[this.idx];
    },
    dv() {
      var idx = parseInt(this.idx);
      if (this.odds[idx]) {
        switch (this.dataType) {
        case "1": // HDP/OU
          idx = parseInt(this.idx) + 16;
          return parseFloat(this.odds[idx]);
          break;
        case "2": // OE/ML/1X2
          idx = parseInt(this.idx) + 16;
          return parseFloat(this.odds[idx]);
          break;
        default:
          return parseFloat(this.odds[idx]);
          break;
        }
      } else {
        return 0;
      }
    },
    valueClass() {
      var result = [];
      if (this.cls) {
        result.push(this.cls);
      }
      if (this.trigger == true) {
        result.push("highlighted");
      }
      if (this.dir != null) {
        result.push(this.dir);
      }
      if (!this.allowBet) {
        result.push("nb");
      }
      if (this.value < 0) {
        result.push("text-red");
      }
      return result;
    },
    allowBet() {
      switch (this.dataType) {
      case "1":
        return this.odds[15] == 1;
      case "2":
        return this.odds[10] == 1;
      case "3":
        return this.odds[9] == 1;
      case "4":
        return this.odds[9] == 1;
      }
      return false;
    },
    pause() {
      switch (this.dataType) {
      case "1":
        return this.odds[14] == 1;
      case "2":
        return this.odds[9] == 1;
      case "3":
        return false;
      case "4":
        return false;
      }
      return true;
    },
    mixParlay() {
      switch (this.dataType) {
      case "1": // HDP/OU
        return this.odds[13] == 1;
      case "2": // OE/ML/1X2
        return this.odds[8] == 1;
      case "3": // CS/DC...
        return this.odds[8] == 1;
      case "4": // OUTRIGHT
        return this.odds[8] == 1;
      }
      return false;
    }
  },
  watch: {
    dv(newVal, oldVal) {
      var nv = newVal;
      var ov = oldVal;
      if (nv == "" || nv == 0) {
        this.trigger = false;
        this.dir = null;
        return;
      } else {
        if (ov != nv) {
          this.trigger = true;
          if (nv < 0) {
            if (ov < nv) {
              this.dir = "down";
            } else {
              this.dir = "up";
            }
          } else {
            if (ov > nv) {
              this.dir = "down";
            } else {
              this.dir = "up";
            }
          }
          setTimeout(() => {
            this.trigger = false;
            this.dir = null;
          }, 10000);
        }
      }
      this.old = oldVal;
    },
    id(newVal, oldVal) {
      this.reset();
    },
    typ(newVal, oldVal) {
      this.reset();
    },
    betType(newVal, oldVal) {
      this.reset();
    }
  },
  mounted() {},
  destroyed() {},
  methods: {
    reset() {
      this.trigger = false;
      this.old = null;
      this.dir = null;
    },
    placeBet(e) {
      if (this.value) {
        $(".bet-value").removeClass("selected-odds");
        if (this.allowBet) {
          if (this.$store.state.layout.betting.defaultStake != "3") {
            this.defStake = this.$store.state.layout.betting.defaultStakeAmount;
          }
          if (this.betType != "parlay") {
            this.$nextTick(() => {
              setTimeout(() => {
                $(e).addClass("selected-odds");
              }, 500);
            });
            if (EventBus.betSingle) {
              EventBus.betSingle(this.odds, this.typ, this.idx, this.value, this.betType, this.defStake, this.mixParlay, false, e);
            }
          } else {
            if (this.mixParlay) {
              if (EventBus.betParlay) {
                EventBus.betParlay(this.odds, this.typ, this.idx, this.value, this.betType, this.defStake, false, e);
              }
            }
          }
        }
      }
    }
  }
};
</script>
