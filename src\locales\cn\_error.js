export default {
  OK: "OK",
  BLOCK: "Account has been blocked!",
  CLOSE: "账号已被关闭!",
  passwordRequired: "密码不能为空!",
  usernameRequired: "用户名不能为空!",

  session_not_exists: "时域已过期！请重新登录...",
  session_expired: "时域已过期！请重新登录...",
  sessionExpired: "时域已过期！请重新登录...",
  invalidSession: "时域已过期！请重新登录...",
  accountIdRequired: "时域已过期！请重新登录...",
  NoUserAccessRight: "没有权限！ 请与您的上线联系...",
  account_not_exists: "賬號無效.",
  invalid_password: "* 密码不正确",
  secondary_account_exists: "您的昵称已创建.",
  systemError: "内部系统错误.",
  new_login_id_exists: "无效昵称，请尝试设置其他昵称.",
  loginLimit: "您登录的过于频密，请在1分钟后重试...",
  requestLimit: "您的请求次数过于频密.",
  unableToGetBalanceAtTheMoment: "内置服务器错误.",
  oddsDisplayInvalid: "赔率显示无效.",
  oddsIsUpdating: "赔率正在更新...",
  atLeastTwoMatchToBetParlay: "最少混合2场赛事方能下注.",
  atLeastThreeMatchToBetParlay: "最少混合3场赛事方能下注.",

  duplicate_debit_record: "赔率正在更新...",

  insufficient_balance: "余额不足",
  betOverMaxPerMatch: "您的投注金额超过最高投注额。",
  betOverMax: "您的投注金额超过最高投注额。",
  betLowerMin: "您的投注金额少于最低投注额。",

  loginFailed: "登录失败，请稍后再试...",
  requestFailed: "你的连接不稳定。请检查您的连接并重试。",

  close: "账号已被关闭！ 请与您的上线联系...",
  suspend: "账号已被暂停！ 请与您的上线联系...",
  freeze: "账号已被冻结！ 请与您的上线联系...",

  alphaNumOnly: "* 只允许使用英数字",

  // vuelidate
  isRequired: "* 请输入{fname}.",
  isMinValue: "* {fname}最低需为{fvalue}",
  isMaxValue: "* {fname}最高需为{fvalue}",
  isMinLength: "* {fname}长度至少需为{fvalue}个字元",
  isMaxLength: "* {fname}长度需为不超過{fvalue}个字元",
  isAlpha: "* {fname}只允许使用英文字母",
  isAlphaNum: "* {fname}只允许使用英数字",
  isNumeric: "* {fname}只允许使用数字",
  isEmail: "* {fname}格式错误，请确认",
  isIpAddress: "{fname}仅接受有效的IPv4地址.",
  isSameAs: "* {fname}必须与{fname2}相同",
  isUrl: "{fname}仅接受URL.",
  containAlphaNum: "* {fname}必须包含英数字",
  selectOption: "* 请选择{fname}",
  notSameAs: "* {fname}与{fname2}相同",

  currPasswordRequired: "* 请输入当前密码",
  newPasswordRequired: "* 请输入新密码",
  confirmNewPasswordRequired: "* 请再次输入您的新密码",
  passwordsNotMatch: "* 密码不匹配",
  nickNameRequired: "* 请输入昵称",

  startDateRequired: "开始日期不能为空!",
  endDateRequired: "结束日期不能为空!",
  pageSizeRequired: "页面大小不能为空!",
  pageNumberRequired: "页码不能为空!",
  sportsTypeRequired: "运动类型不能为空!",

  workingDateRequired: "工作日不能为空!",
  leagueIdRequired: "联赛ID不能为空!",
  isOutrightRequired: "总冠军不能为空!",

  maxParlayTicket: "达到混合过关单注票的最高上限!",
  selectLeague: "请选择至少一个联赛",

  liveCasinoDisabled: "真人娱乐场已禁用",
  lotteryDisabled: "彩票禁用",
  MiniGameDisabled: "迷你游戏已禁用",
  EsportsDisabled: "电子竞技已禁用",

  login_id_not_exists: "登录无效，请重新登录。",
  noMatchSelected: "请选择至少1场比赛。",
  invalidRoomRate: "无效的房间费率。",
  invalidRoomLimit: "无效的房间限制。",
  invalidRoomPassword: "无效的PIN码。PIN码必须为6位数字。",
  InvalidRoomPassword: "无效的PIN码。PIN码必须为6位数字。",
  invalidPassword: "无效的密码",
  InvalidPassword: "无效的密码。",
  roomPasswordRequired: "PIN码是必需的。",
  minMatch: "至少选择3场比赛！",
  maxMatch: "最多选择{max}场比赛！",
  roomIdRequired: "房间ID是必需的。",
  roomTypeRequired: "房间类型是必需的。",
  sessionTokenRequired: "会话令牌是必需的。",
  matchDateRequired: "比赛日期是必需的。",
  matchRequired: "比赛是必需的。",
  roomLimitRequired: "房间限制是必需的。",
  roomRateRequired: "房间费率是必需的。",
  minRoomRate: "房间费率必须大于或等于10。",
  maxRoomRate: "房间费率必须小于或等于1000。",
  minRoomLimit: "房间限制必须大于或等于3。",
  maxRoomLimit: "房间限制必须小于或等于30。",
  invalidRoomType: "无效的房间类型。",
  invalidMatch: "无效的比赛。",
  dataError: "数据错误",
  MemberExists: "会员已存在。",
  invalidMember: "无效的会员。",
  invalidleague: "无效的联赛。",
  "createRoomFailed-1": "创建房间失败 1。",
  "createRoomFailed-2": "创建房间失败 2。",
  "createRoomFailed-3": "创建房间失败 3。",
  invalidOperator: "无效的运营商。",
  roomFull: "房间已满。",
  memberExists: "会员已存在。",
  "joinFailed-1": "加入房间失败 1。",
  "joinFailed-2": "加入房间失败 2。",
  IPJoinedBefore: "IP已加入。",
  // memberJoinedBefore: "You have already joined before, please take note that public free room only can join once.",
  memberJoinedBefore: "您已经加入过了，请注意，公共免费房间只能加入一次。",
  roomNotExists: "房间不存在。",
  roomIsPublicFree: "房间是公共免费的。",
  roomEndedOrCancelled: "房间已结束或已取消。",
  roomEnded: "房间已结束。",
  matchLowerMin: "比赛低于最小值错误。",
  matchOverMax: "比赛超过最大值错误。",
  pointsOverLimit: "点数超过限制错误。",
  roomOverMax: "房间超过最大值错误。",
  invalidRoom: "无效的房间。",
  roomLimitOverMax: "房间限制超过最大值错误。",
  rateLowerMin: "费率低于最小值错误。",
  rateOverMax: "费率超过最大值错误。",
  liveMatchNotAllowed: "不允许直播比赛。",
  roomLimitLowerMin: "房间限制低于最小值错误。",
  roomClosed: "房间已关闭。",
  tournamentLeagueExists: "锦标赛联赛已存在。",
  roomExists: "不允许创建多于1个私人房间。",
  RoomExists: "不允许创建多于1个私人房间。",
  betExists: "投注已存在。",
  roomClosedNotAllowed: "不允许关闭房间。",
  tournamentMatchExists: "锦标赛比赛已存在。",
  account_id_required: "帐户ID是必需的。",
  agent_comm_rate_required: "代理佣金率是必需的。",
  agent_id_required: "代理ID是必需的。",
  bet_amount_required: "投注金额是必需的。",
  bet_id_required: "投注ID是必需的。",
  credit_amount_required: "信用额是必需的。",
  currency_required: "货币是必需的。",
  debit_amount_required: "借方金额是必需的。",
  duplicate_credit_record: "重复的信用记录。",
  master_comm_rate_required: "总代理佣金率是必需的。",
  master_id_required: "总代理ID是必需的。",
  member_id_required: "会员ID是必需的。",
  member_wallet_not_exists: "会员钱包不存在。",
  permission_denied: "权限被拒绝。",
  rate_required: "费率是必需的。",
  result_exists: "结果已存在。",
  room_id_required: "房间ID是必需的。",
  s_senior_comm_rate_required: "超级资深代理佣金率是必需的。",
  s_senior_id_required: "超级资深代理ID是必需的。",
  senior_comm_rate_required: "资深代理佣金率是必需的。",
  senior_id_required: "资深代理ID是必需的。",
  shareholder_comm_rate_required: "股东佣金率是必需的。",
  shareholder_id_required: "股东ID是必需的。",
  transfer_id_required: "转账ID是必需的。",
  minBetAmount: "最小投注金额错误。",
  matchChangeTime: "房间已经关门了。",
  changeStakeMin: "由于最低限制，输入赌注金额已更改。",
  changeStakeMax: "由于最大限制，输入本金金额已更改。",
  betNotExists: "请下注有效的投注以参加比赛。",
};
