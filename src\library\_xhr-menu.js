import config from "@/config";
import errors from "@/errors";
import Vue from "vue";
import pako from "pako";

export default {
  loading: false,
  getMenu(args) {
    const url = config.getMenuUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "MENU"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("arguments" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading = true;
        fetch(url, {
          method: "POST",
          body: JSON.stringify(args),
          headers: { "Content-Type": "application/json", "Raw-Data": "on" }
        })
          .then(res => res.text())
          .then(text => {
            var data = JSON.parse(text);

            this.loading = false;
            if (data) {
              // check status code
              if (typeof data.status == "string") {
                feedback.success = data.status == "1";
              } else {
                feedback.success = data.status == 1;
              }

              feedback.status = data.statusdesc;
              feedback.debug = data.debug;

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = data.data;
                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.login.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          })
          .catch(err => {
            this.loading = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          });
      } else {
        reject(feedback);
      }
    });
  }
};
