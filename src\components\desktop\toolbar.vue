<template lang="pug">
.toolbar(:class="{ active: !header, 'white-label': whiteLabel.mode }")
  .container.d-flex.flex-row
    .new-timezone
      .logo.d-flex(@click="hardRefresh()")
        logo
      #user-qprofile.show-user(data-toggle="dropdown" aria-haspopup="true" aria-expanded="false")
        span {{ displayName }}
      .dropdown-menu.dropdown-menu-left.profile.small(aria-labelledby="user-qprofile")
        .dropdown-bar.d-flex.m-1.mb-2.align-items-center.justify-content-between
          .dropdown-title {{ $t('ui.account') }}
        .dropdown-panel
          .dropdown-li
            .caption {{ $t('ui.cash_balance') }}
            .value.d-flex.flex-row.align-items-start.justify-content-end
              .unit {{ currency_code }}
              .amount {{ cash_balance }}
          .dropdown-li
            .caption {{ $t('ui.outstanding') }}
            .value.d-flex.flex-row.align-items-start.justify-content-end
              .unit {{ currency_code }}
              .amount {{ frozen_balance }}
          .dropdown-li
            .caption {{ $t('ui.bet_credit') }}
            .value.d-flex.flex-row.align-items-start.justify-content-end
              .unit {{ currency_code }}
              .amount {{ available_balance }}
          .dropdown-li
            .caption {{ $t('ui.given_credit') }}
            .value.d-flex.flex-row.align-items-start.justify-content-end
              .unit {{ currency_code }}
              .amount {{ given_credit }}
        .action-panel.text-right
          router-link.x-btn(role="button" to="info/settings"  target="_blank" onclick="window.open(this.href,'info','top=10,height=600,width=1280,status=no,toolbar=no,menubar=no,location=no');return false;")
            i.fad.fa-key
            span {{ $t("ui.password") }}
          button.x-btn.x-btn-default(href="javascript:void(0);" role="button" @click="logout")
            i.fad.fa-sign-out-alt
            span {{ $t("ui.logout") }}
      .show-time
        span.px-1.text-left.font-weight-normal {{ calendar }}
        span.pr-1.text-center.text-info.font-weight-bold.w-72 {{ clock }}
        span.pr-1.text-left.font-weight-light(style="color: #ffffffaa;") GMT+8
    .news.d-flex.flex-column
      .show-time
        span.pr-1.text-left.font-weight-normal {{ calendar }}
        span.pr-1.text-center.text-info.font-weight-bold.w-62 {{ clock }}
        span.pr-1.text-left.font-weight-light(style="color: #ffffffaa;") GMT+8
      .show-news
        marquee(v-if="marqueeList.length >= 1" behavior="scroll" direction="left" scrollamount="5") {{ marqueeList[0]["msg_" + language] }}
    .menu
      ul.nav.d-flex.flex-row
        li.flex-fill(v-if="whiteLabel.mode")

        template(v-if="whiteLabel.mode")
          li.nav-item
            router-link.nav-link(to="info/betlist")
              i.fad.fa-receipt
              .flash {{ $t('ui.bet_list') }}
          li.nav-item
            router-link.nav-link(to="info/statement")
              i.fad.fa-file-alt
              .flash.d-block {{ $t('ui.statement') }}
          li.nav-item
            router-link.nav-link(to="info/result")
              i.fad.fa-poll-people
              .flash {{ $t('ui.result') }}
          li.nav-item
            router-link.nav-link(to="info/settings")
              i.fad.fa-cog
              .flash {{ $t('ui.settings') }}
          li.nav-item(v-if="whiteLabel.mode")
            router-link.nav-link(to="info/message")
              i.fad.fa-envelope
              .flash {{ $t('ui.message') }}
        template(v-else)
          li.nav-item
            router-link.nav-link(to="info/betlist" target="_blank" onclick="window.open(this.href,'info','top=10,height=600,width=1280,status=no,toolbar=no,menubar=no,location=no');return false;")
              i.fad.fa-receipt
              .flash {{ $t('ui.bet_list') }}
          li.nav-item
            router-link.nav-link(to="info/statement" target="_blank" onclick="window.open(this.href,'info','top=10,height=600,width=1280,status=no,toolbar=no,menubar=no,location=no');return false;")
              i.fad.fa-file-alt
              .flash.d-block {{ $t('ui.statement') }}
          li.nav-item
            router-link.nav-link(to="info/result" target="_blank" onclick="window.open(this.href,'info','top=10,height=600,width=1280,status=no,toolbar=no,menubar=no,location=no');return false;")
              i.fad.fa-poll-people
              .flash {{ $t('ui.result') }}
          li.nav-item
            router-link.nav-link(to="info/settings" target="_blank" onclick="window.open(this.href,'info','top=10,height=600,width=1280,status=no,toolbar=no,menubar=no,location=no');return false;")
              i.fad.fa-cog
              .flash {{ $t('ui.settings') }}
          li.nav-item(v-if="whiteLabel.mode")
            router-link.nav-link(to="info/message" target="_blank" onclick="window.open(this.href,'info','top=10,height=600,width=1280,status=no,toolbar=no,menubar=no,location=no');return false;")
              i.fad.fa-envelope
              .flash {{ $t('ui.message') }}
        li.nav-item(v-if="!whiteLabel.mode && !mmoMode && contact")
          a#customer-support.nav-link(href="javascript:void(0);" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false")
            i.fad.fa-phone-alt
            .flash {{ $t('ui.support') }}
          .dropdown-menu.contact-list(aria-labelledby="customer-support")
            a.dropdown-item(href="mailto:<EMAIL>")
                .contact-1
                  i.fab.fa-google.mr-1
                  span Gmail
                .contact-2 <EMAIL>
            .dropdown-divider
            a.dropdown-item(href="https://t.me/+639560532283" target="_blank")
                .contact-1
                  i.fab.fa-telegram.mr-1
                  span Telegram
                .contact-2 +63 ************
            .dropdown-divider
        li.nav-item(v-if="!whiteLabel.mode")
          a.nav-link(href="javascript:void(0);" @click="goMobile()")
            i.fad.fa-mobile-alt
            .flash {{ $t('ui.mobile') }}
        li.nav-item(v-if="!whiteLabel.mode && !mmoMode")
          a.nav-link(href="https://docs.google.com/forms/d/16N4aJg8nc2c3yqiAMG1byLhbmUnyfaG74_BABeo-2aY/viewform?edit_requested=true" target="_blank")
            i.fad.fa-thumbs-up
            .flash {{ $t('ui.feedback') }}

        li.flex-fill(v-if="!whiteLabel.mode")

        li.nav-item(v-if="!whiteLabel.mode")
          a.nav-link(style="min-width: 26px !important;" href="javascript:void(0);" v-if="header == false" @click="toggleHeader(true)" :title="$t('ui.hide_header')")
            i.fad.fa-angle-double-down
          a.nav-link(style="min-width: 26px !important;" href="javascript:void(0);" v-else @click="toggleHeader(false)" :title="$t('ui.show_header')")
            i.fad.fa-angle-double-up
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import service from "@/library/_xhr-message";
import { EventBus } from "@/library/_event-bus.js";
import court from "@/components/desktop/court";

export default {
  components: { court, logo: () => import("@/components/desktop/logo") },
  data() {
    return {
      loading: false,
      livetv: false,
      calendar: "",
      clock: "",
      timezone: "",
      hide: false,
      idx: 0,
      unreadCount: 0,
      marqueeList: [],
      feedback: {
        success: false,
        status: errors.session.invalidSession,
      },
      intervals: [],
    };
  },
  computed: {
    rememberMe() {
      return this.$store.getters.rememberMe;
    },
    contact() {
      return config.contact;
    },
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
    whiteLabel() {
      return this.$store.getters.whiteLabel;
    },
    header() {
      return this.$store.getters.header;
    },
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
    displayName() {
      if (this.nickname) return this.nickname;
      else return this.account_id;
    },
    account_id() {
      return this.$store.getters.accountId;
    },
    nickname() {
      return this.$store.getters.nickName;
    },
    last_login_date() {
      if (
        this.$store.getters.playerInfo &&
        this.$store.getters.playerInfo.last_login_time
      )
        return this.$dayjs(this.$store.getters.playerInfo.last_login_time).format(
          "MM/DD/YYYY"
        );
      else return "";
    },
    last_login_time() {
      if (
        this.$store.getters.playerInfo &&
        this.$store.getters.playerInfo.last_login_time
      )
        return this.$dayjs(this.$store.getters.playerInfo.last_login_time).format(
          "h:mm:ss A"
        );
      else return "-";
    },
    wallet() {
      if (this.$store.getters.playerWallet) return this.$store.getters.playerWallet;
      return null;
    },
    bet_limit() {
      if (this.$store.getters.playerBetLimit) return this.$store.getters.playerBetLimit;
      return null;
    },
    balance() {
      return this.$store.getters.balance;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
    available_balance() {
      if (this.wallet) {
        return this.$numeral(this.balance).format("0,0.00");
      }
      return "-";
    },
    cash_balance() {
      if (this.wallet) {
        return this.$numeral(this.wallet.cash_balance).format("0,0.00");
      }
      return "-";
    },
    frozen_balance() {
      if (this.wallet) {
        return this.$numeral(this.wallet.frozen_balance).format("0,0.00");
      }
      return "-";
    },
    given_credit() {
      if (this.wallet) {
        return this.$numeral(this.wallet.credit_limit).format("0,0.00");
      }
      return "-";
    },
    language() {
      return this.$store.getters.language;
    },
  },
  beforeDestroy() {
    this.intervals.forEach(clearInterval);
    this.intervals = [];
  },
  mounted() {
    this.getLocalTime();
    this.getMarqueeList();
    this.checkHeader();

    const marqueeInterval = setInterval(this.getMarqueeList, 900000);
    const timeInterval = setInterval(this.getLocalTime, 1000);
    
    this.intervals.push(marqueeInterval, timeInterval);
  },
  methods: {
    hardRefresh() {
      window.location.reload(true);
    },
    goMobile() {
      if (this.$store.getters.isLoggedIn) {
        let domain = window.location.hostname;
        let url =
          "https://m." +
          domain.replace("www.", "") +
          "?u=" +
          this.$store.getters.accountId +
          "&st=" +
          this.$store.getters.sessionToken;
        window.location.replace(url);
      }
    },
    getMarqueeList() {
      if (this.$store.getters.isLoggedIn) {
        var json = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
        };
        this.loading = true;
        service.getMarqueeList(json).then(
          (result) => {
            this.loading = false;
            if (result) {
              this.feedback.status = result.status;
              if (result.success == true) {
                this.marqueeList = result.data;
              } else {
                this.$helpers.handleFeedback(this.feedback.status);
              }
            }
          },
          (err) => {
            this.loading = false;
            this.feedback.success = false;
            this.feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);
          }
        );
      }
    },
    getPersonalUnread() {
      if (this.$store.getters.isLoggedIn) {
        var json = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
        };
        this.loading = true;
        service.getPersonalUnread(json).then(
          (result) => {
            this.loading = false;
            if (result) {
              this.feedback.status = result.status;
              if (result.success == true) {
                this.unreadCount = result.data[0].count_unread;
              } else {
                this.$helpers.handleFeedback(this.feedback.status);
              }
            }
          },
          (err) => {
            this.loading = false;
            this.feedback.success = false;
            this.feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);
          }
        );
      }
    },
    getLocalTime() {
      var m = this.$dayjs.utc().tz("Asia/Kuala_Lumpur");
      this.calendar = m.format("ddd, D MMM YYYY");
      this.clock = m.format("h:mm:ss A");
    },
    logout() {
      this.$helpers.logout();
    },
    toggleHeader(e) {
      this.$store.dispatch("layout/setMinimizer", { property: "header", value: e });
      this.checkHeader();
    },
    checkHeader() {
      if (this.header) {
        $(".scroll-sports").removeClass("higher");
        $(".content .right .accordion").removeClass("higher");
      } else {
        $(".scroll-sports").addClass("higher");
        $(".content .right .accordion").addClass("");
      }
    },
    settings() {
      $("#modal-settings").modal("show");
    },
    go(e) {
      var routeData = this.$router.resolve(e);
      this.$helpers.info(routeData.href);
    },
    handleSearch(e) {
      this.$store.dispatch("layout/setSearch", e.target.value);
      if (e.target.value == "" || e.target.value == null) {
        this.$store.dispatch("layout/setMenu", {
          property: "menu0",
          value: "all",
          src: "handleSearch",
        });
      } else {
        this.$store.dispatch("layout/setMenu", {
          property: "menu0",
          value: "search",
          src: "handleSearch",
        });
      }
      EventBus.$emit("INVALIDATE");
    },
    clear() {
      this.$store.dispatch("layout/setSearch", null);
      this.$store.dispatch("layout/setMenu", {
        property: "menu0",
        value: "all",
        src: "clear",
      });
      EventBus.$emit("INVALIDATE");
    },
  },
};
</script>
