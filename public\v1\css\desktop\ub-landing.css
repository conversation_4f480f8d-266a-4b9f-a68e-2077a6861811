.landing.ubett .top .login-wrapper {
  background-color: #40486c;
}

.ubett .btn-select {
  border: 1px solid #575d78;
  background-color: #343a56;
}

.ubett .lobby-wrap:before {
  background-image: url(https://r.myw0011001.com/images/ubett/ub/line1.svg);
}

.ubett .inner-wrap {
  background-color: #40486c;
}

.ubett .lobbyinner .lobby-item .front .info {
  background-color: #819aea;
}

.ubett .lobbyinner .lobby-item .back h3 {
  color: #40486c;
}

.ubett .lobbyinner .lobby-item .back .btn {
  background-color: #819aea;
}

.landing.ubett .login-btn,
.landing.ubett .join-btn {
  color: #fff;
  text-shadow: none;
}

.ubett .feature-wrap:before,
.ubett .feature-wrap:after {
  background-image: url(https://r.myw0011001.com/images/ubett/ub/line2.svg);
}

.ubett .feature-wrap {
  background-color: #313750;
}

.ubett .deco1 {
  right: 0px;
}

.ubett .deco2 {
  left: 0px;
}

.ubett .inner-wrap {
  background-image: url(https://r.myw0011001.com/images/ubett/ub/lobby_bg.jpg);
}

.ubett .feature-item .feature-content h1,
.ubett .feature-item .feature-content p {
  color: #fff;
}

.landing.ubett .license .image-wrap {
  border: 1px rgba(0, 0, 0, 0.2) solid;
}

.landing.ubett .footer .tnc {
  border-top: 1px rgba(0, 0, 0, 0.2) solid;
}

.landing.ubett .footer {
  color: #fff;
  background-color: #3f476c;
}

.ubett .lang-dropdown-wrap,
.ubett #lang-dropdown-list {
  background-color: #3f476c;
}

@media (min-width: 768px) {
  .landing.ubett .top-wrapper.small-top {
    background-color: #343a56;
  }
}

@media (min-width: 960px) {
  .landing.ubett .footer {
    color: #000;
    background-color: #fff;
  }
}
