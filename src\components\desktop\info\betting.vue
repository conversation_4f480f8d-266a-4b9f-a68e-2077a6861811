<template lang="pug">
.col-12.setting-right.py-3
  .w-100.mx-auto
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.default_stake") }}
      .col-8
        .d-flex.flex-row
          .form-group.mb-0.w-75
            .input-group
              .input-group-prepend(v-model="betting.defaultStake")
                button.btn.btn-result.btn-dropdown.btn-outline-secondary.dropdown-toggle(
                  type='button'
                  data-toggle='dropdown'
                  aria-haspopup='true'
                  aria-expanded='false'
                  style="min-width: 100px;"
                  ) {{ $t(stakeOptions[betting.defaultStake-1]) }}
                  .dropdown-menu.custom-dropdown-menu
                    a.dropdown-item.custom-dropdown-item(
                      v-for="(item, index) in stakeOptions"
                      @click="onChange(index)"
                      ) {{ $t(item) }}
              input.form-control(
                type='text'
                aria-label='Default Stake Amount'
                :disabled="betting.defaultStake != 2"
                v-model="betting.defaultStakeAmount"
                )
          .text-account.icon-info.ml-2(:title='$t("message.select_default_stake")')
            i.fad.fa-question-circle
        small.form-text.text-danger.w-75(v-if="feedback.defaultStakeAmount") {{ feedback.defaultStakeAmount }}
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.accept_better_odds") }}
      .col-8
        .custom-control.custom-radio.custom-control-inline(@click="betting.acceptBetterOdds = true")
          input#betterOddsOn.custom-control-input(type='radio' v-model="betting.acceptBetterOdds" value="true")
          label.custom-control-label(for='betterOddsOn') {{ $t("ui.on") }}
        .custom-control.custom-radio.custom-control-inline(@click="betting.acceptBetterOdds = false")
          input#betterOddsOff.custom-control-input(type='radio' v-model="betting.acceptBetterOdds" value="false")
          label.custom-control-label(for='betterOddsOff') {{ $t("ui.off") }}
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.accept_any_odds") }}
      .col-8
        .custom-control.custom-radio.custom-control-inline(@click="betting.acceptAnyOdds = true")
          input#anyOddsOn.custom-control-input(type='radio' v-model="betting.acceptAnyOdds" value="true")
          label.custom-control-label(for='anyOddsOn') {{ $t("ui.on") }}
        .custom-control.custom-radio.custom-control-inline(@click="betting.acceptAnyOdds = false")
          input#anyOddsOff.custom-control-input(type='radio' v-model="betting.acceptAnyOdds" value="false")
          label.custom-control-label(for='anyOddsOff') {{ $t("ui.off") }}
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.auto_refresh_odds") }}
      .col-8
        .custom-control.custom-radio.custom-control-inline(@click="betting.autoRefreshOdds = true")
          input#refreshOn.custom-control-input(type='radio' v-model="betting.autoRefreshOdds" value="true")
          label.custom-control-label(for='refreshOn') {{ $t("ui.on") }}
        .custom-control.custom-radio.custom-control-inline(@click="betting.autoRefreshOdds = false")
          input#refreshOff.custom-control-input(type='radio' v-model="betting.autoRefreshOdds" value="false")
          label.custom-control-label(for='refreshOff') {{ $t("ui.off") }}
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.quick_bet") }}
      .col-8
        .custom-control.custom-radio.custom-control-inline(@click="betting.quickBet = true")
          input#quickBetOn.custom-control-input(type='radio' v-model="betting.quickBet" value="true")
          label.custom-control-label(for='quickBetOn') {{ $t("ui.on") }}
        .custom-control.custom-radio.custom-control-inline(@click="betting.quickBet = false")
          input#quickBetOff.custom-control-input(type='radio' v-model="betting.quickBet" value="false")
          label.custom-control-label(for='quickBetOff') {{ $t("ui.off") }}
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.quick_bet_stake") }}
      .col-6
        .input-group
          input.form-control(
            type='text'
            v-model="betting.quickBetAmount"
            )
    .text-center.mb-2.mt-4
      SpinButton(type="button" @click="save" :loading="loading" css="btn-primary btn-result active w-25" :text="$t('ui.save')").mr-1
      SpinButton(type="button" @click="restore" :loading="loading" css="btn-primary btn-result w-25" :text="$t('ui.restore')")
</template>

<script>
import SpinButton from "@/components/ui/SpinButton";
import config from "@/config";
import errors from "@/errors";
import { required, requiredIf, numeric } from "vuelidate/lib/validators";

export default {
  components: {
    SpinButton
  },
  data: () => ({
    loading: false,
    feedback: {
      defaultStakeAmount: "",
      timeout: null
    },
    defaultBetting: {
      defaultStake: "3",
      acceptBetterOdds: "false",
      acceptAnyOdds: "false",
      autoRefreshOdds: "true",
      quickBet: "false",
      defaultStakeAmount: "1"
    },
    betting: {
      defaultStake: "3",
      acceptBetterOdds: "false",
      acceptAnyOdds: "false",
      autoRefreshOdds: "true",
      quickBet: "false",
      defaultStakeAmount: "1",
      quickBetAmount: "1"
    },
    stakeOptions: ["ui.last_bet", "ui.customize", "ui.disable"]
  }),
  validations: {
    betting: {
      defaultStakeAmount: {
        required: requiredIf(function() {
          return this.betting.defaultStake == 2;
        }),
        numeric
      }
    }
  },
  mounted() {
    this.betting.defaultStakeAmount = this.$store.getters.playerBetLimit["SOCCER"]["min_bet"];
    this.betting.quickBetAmount = this.$store.getters.playerBetLimit["SOCCER"]["min_bet"];
    this.betting.defaultBetting = this.$store.getters.playerBetLimit["SOCCER"]["min_bet"];

    this.initial();
  },
  methods: {
    onChange(selectedIndex) {
      this.betting.defaultStake = (selectedIndex + 1).toString();

      if (this.betting.defaultStake != 2) this.betting.defaultStakeAmount = "";
      else {
        if (this.betting.defaultStakeAmount == "") this.betting.defaultStakeAmount = this.$store.getters.playerBetLimit["SOCCER"]["min_bet"];
      }
    },
    validate() {
      this.$v.$touch();
      if (!this.$v.$invalid) {
        this.feedback.defaultStakeAmount = "";
        return true;
      } else {
        this.loading = false;
        this.feedback.defaultStakeAmount = "";
        clearTimeout(this.feedback.timeout);

        if (!this.$v.betting.defaultStakeAmount.required) {
          this.feedback.defaultStakeAmount = this.$t("error.isRequired", { fname: this.$t("ui.default_stake") });
        }

        if (!this.$v.betting.defaultStakeAmount.numeric) {
          this.feedback.defaultStakeAmount = this.$t("error.isNumeric", { fname: this.$t("ui.default_stake") });
        }

        this.feedback.timeout = setTimeout(() => {
          this.$v.$reset();
          this.feedback.defaultStakeAmount = "";
        }, 10000);
      }

      return false;
    },
    save() {
      if (this.validate()) {
        this.$store.dispatch("layout/setBetting", this.betting).then(res => {
          this.$store
            .dispatch("user/setSettings", {
              page_display: JSON.stringify(this.$store.state.layout.pageDisplay),
              sports_order: JSON.stringify(this.$store.state.layout.order.sports),
              betting: JSON.stringify(this.$store.state.layout.betting)
            })
            .then(
              res => {
                this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.succeed"), "success");
              },
              err => {
                this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.failed"), "error");
              }
            );
        });
      }
    },
    restore() {
      this.betting.defaultStake = this.defaultBetting.defaultStake;
      this.betting.acceptBetterOdds = this.defaultBetting.acceptBetterOdds;
      this.betting.acceptAnyOdds = this.defaultBetting.acceptAnyOdds;
      this.betting.autoRefreshOdds = this.defaultBetting.autoRefreshOdds;
      this.betting.quickBet = this.defaultBetting.quickBet;
      this.betting.defaultStakeAmount = this.defaultBetting.defaultStakeAmount;
      this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.succeed"), "success");
    },
    initial() {
      this.betting.defaultStake = this.$store.state.layout.betting.defaultStake;
      this.betting.acceptBetterOdds = this.$store.state.layout.betting.acceptBetterOdds;
      this.betting.acceptAnyOdds = this.$store.state.layout.betting.acceptAnyOdds;
      this.betting.autoRefreshOdds = this.$store.state.layout.betting.autoRefreshOdds;
      this.betting.quickBet = this.$store.state.layout.betting.quickBet;
      this.betting.defaultStakeAmount = this.$store.state.layout.betting.defaultStakeAmount;
      this.betting.quickBetAmount = this.$store.state.layout.betting.quickBetAmount;
    }
  }
};
</script>
