<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 50 50">
  <defs>
    <style>
      .cls-1 {
        fill: #e5e5e5;
      }

      .cls-1, .cls-2, .cls-3 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: purple;
      }

      .cls-3 {
        fill: #fff;
      }
    </style>
  </defs>
  <circle class="cls-1" cx="25" cy="25" r="25"/>
  <g>
    <circle class="cls-2" cx="25" cy="25" r="19.5"/>
    <path class="cls-3" d="M25.3,34.6c-.2-.2-.3-.5-.3-1.1v-2.6h-7.8c-.6,0-.9-.3-.9-.9v-2.2c0-.4,0-.7.1-.9,0-.2.2-.4.5-.7l8.5-10.4c.3-.3.5-.5.7-.7.2-.1.5-.2,1-.2h2.3c.5,0,.9.1,1.2.4.2.2.4.6.4,1.1v10.2h1.4c.5,0,.9.1,1.1.3.2.2.3.6.3,1.1v1.3c0,.5,0,.9-.3,1.1s-.6.3-1.1.3h-1.4v2.6c0,.5-.1.9-.3,1.1-.2.2-.6.3-1.2.3h-2.8c-.6,0-1,0-1.2-.3,0,0-.2.2-.2.2ZM26.2,26.7v-5.3l-4.1,5.3h4.1Z"/>
  </g>
</svg>