<template lang="pug">
.bet-value(v-if="odds" :class="[valueClass]" @click="placeBet($event.target)") {{ value }}
</template>

<script>
import cal from "@/library/_calculation";
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";

export default {
  props: {
    odds: {
      type: Array,
    },
    typ: {
      type: String,
    },
    cls: {
      type: String,
    },
    idx: {
      type: Number,
    },
  },
  data() {
    return {
      trigger: false,
      timer: null,
      old: null,
      dir: null,
      defStake: 0,
      newPage: true,
    };
  },
  computed: {
    betType() {
      return this.$store.state.layout.menu3;
    },
    id() {
      if (this.odds) {
        return this.odds[3];
      } else {
        return null;
      }
    },
    value() {
      if (this.odds) {
        return this.odds[this.idx + 5];
      } else {
        return null;
      }
    },
    dv() {
      if (this.odds) {
        return parseFloat(this.odds[this.idx + 5]);
      } else {
        return 0;
      }
    },
    valueClass() {
      var result = [];
      if (this.cls) {
        result.push(this.cls);
      }
      if (this.trigger == true) {
        result.push("highlighted");
      }
      if (this.dir != null) {
        result.push(this.dir);
      }
      if (!this.allowBet) {
        result.push("nb");
      }
      if (this.value < 0) {
        result.push("text-red");
      }
      return result;
    },
    allowBet() {
      return this.odds[39] == 1;
    },
    pause() {
      return false;
    },
    mixParlay() {
      return this.odds[38] == 1;
    },
  },
  watch: {
    dv(newVal, oldVal) {
      var nv = newVal;
      var ov = oldVal;
      if (nv == "" || nv == 0) {
        this.trigger = false;
        this.dir = null;
        return;
      } else {
        if (ov != nv) {
          this.trigger = true;
          if (nv < 0) {
            if (ov < nv) {
              this.dir = "down";
            } else {
              this.dir = "up";
            }
          } else {
            if (ov > nv) {
              this.dir = "down";
            } else {
              this.dir = "up";
            }
          }
          setTimeout(() => {
            this.trigger = false;
            this.dir = null;
          }, 10000);
        }
      }
      this.old = oldVal;
    },
    id(newVal, oldVal) {
      this.reset();
    },
    typ(newVal, oldVal) {
      this.reset();
    },
    betType(newVal, oldVal) {
      this.reset();
    },
  },
  mounted() {},
  destroyed() {},
  methods: {
    reset() {
      this.trigger = false;
      this.old = null;
      this.dir = null;
    },
    placeBet(e) {
      if (this.value) {
        $(".bet-value").removeClass("selected-odds");
        if (this.allowBet) {
          if (this.$store.state.layout.betting.defaultStake != "3") {
            this.defStake = this.$store.state.layout.betting.defaultStakeAmount;
          }
          if (this.betType != "parlay") {
            this.$nextTick(() => {
              setTimeout(() => {
                $(e).addClass("selected-odds");
              }, 500);
            });
            if (EventBus.betSingle) {
              EventBus.betSingle(this.odds, this.typ, this.idx + 5, this.value, this.betType, this.defStake, this.mixParlay, false, e);
            }
          } else {
            if (this.mixParlay) {
              if (EventBus.betParlay) {
                // console.log(this.odds, this.typ, this.idx, this.value, this.betType, this.defStake, false, e);
                // console.log(this.idx, this.idx + 5, this.odds[this.idx + 5]);
                EventBus.betParlay(this.odds, this.typ, this.idx + 5, this.value, this.betType, this.defStake, false, e);
              }
            }
          }
        }
      }
    },
  },
};
</script>
