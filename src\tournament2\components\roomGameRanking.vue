<template lang="pug">
.tournament-pool-body
  template(v-if="roomRank && roomRank.length > 0")
    .tournament-details-row(v-for="(item, index) in roomRank" :class="{ isme : item.isme == '1'}")
      .tournament-details-content.pointer(@click="showRecords(item)")
        a
          .tournament-details-left
            .icon-player
              i.fas.fa-user-circle
              //- span {{ index+1 }}.
            .tournament-position(style="color: #ffffffcc;") {{ item.member_id }}
          .tournament-details-right.flex-fill.d-flex.justify-content-end.align-items-center
            div(v-if="roomFormula == 1")
              img(v-if="roomStatus == 3 && [1,2,3].includes(item.top_position)" :src="'img/tn/trophy' + item.top_position + '.svg'")
              b {{ $numeral(item.points).format("0,0.00") }}
            div(v-else)
              img(v-if="roomStatus == 3 && [1].includes(item.top_position)" :src="'img/tn/trophy' + item.top_position + '.svg'")
              b {{ $numeral(item.points).format("0,0.00") }}
            //- small(style="color: #ffffff88;") &nbsp;({{ getRecordLength(participants[item.ref]) }})&nbsp;
          .tournament-details-right(style="width: 32px;")
            i.fas.fa-angle-right.pl-1(v-if="roomStatus == 3 || getRecordLength(participants[item.ref]) > 0")
  template(v-else)
    .tournament-pool-body-wrapper
      .text-center
        span {{ $t('message.no_information_available') }}
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";

export default {
  props: {
    roomRank: {
      type: Array,
    },
    participants: {
      type: Object,
    },
    roomId: {
      type: Number,
    },
    roomStatus: {
      type: Number,
    },
    roomFormula: {
      type: Number,
    },
  },
  computed: {},
  methods: {
    getRecordLength(item) {
      if (item && item.hasOwnProperty("bet_list")) {
        return item.bet_list.length;
      } else {
        return 0;
      }
    },
    showRecords(item) {
      // console.log(item);
      var r = {
        id: item.ref,
        record: this.participants[item.ref],
        member_id: item.member_id,
        ranking: item.top_position,
        prize: item.points,
        room_id: this.roomId,
        room_status: this.roomStatus,
      };
      // if (this.roomStatus == 3 || this.getRecordLength(this.participants[item.ref]) > 0) {
      EventBus.$emit("TN_PLAYER_BETSLIP2", r);
      // }
    },
  },
};
</script>

<style>
.tournament-details-row.isme {
  background: #ffdd0088;
}
</style>