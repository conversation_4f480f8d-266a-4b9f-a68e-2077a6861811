<template lang="pug">
.col-6.hx-more-row
  .card
    .card-header(
      :id="'heading-oe-' + uid"
      data-toggle="collapse"
      :data-target="'#collapse-oe-' + uid"
      :aria-controls="'collapse-oe-' + uid"
      aria-expanded=true
      :class="layoutIndex == 3 ? 'live': 'non-live'"
    )
      i.fad.fa-chevron-circle-down
      span.header-bettype {{ $t("m.BT_OE") }}
    .collapse.show(
      :aria-labelledby="'heading-oe-' + uid"
      :id="'collapse-oe-' + uid"
    )
      .card-body.p-0(:id="'accordian-oe-' + uid")
        .hx-table.hx-match.hx-more-bet(:class="{ 'live': marketType == 3 }")
          .d-flex.flex-column.w-100
            .hx-cell.w-100.h-18
              .hx-row.hx-more-col.hx-more-col3.header.bl-1.br-1.bb-1
                .hx-col
                  .hx {{ $t("m.BT_ODD") }}
                .hx-col
                  .hx {{ $t("m.BT_EVEN") }}
            .d-flex.flex-row
              .hx-cell.w-100
                .hx-row.hx-more-col.hx-more-col3.body.bl-1.br-1.bb-1
                  .hx-col
                    .hx
                      .hxs(v-if="details != null && details[0] != null && details[0][6] != null").hx-flex-c
                        oddsItem(:odds="details[0]" idx=5 :typ="oddsType" dataType="2" cls="more-value hx-w80")
                  .hx-col
                    .hx
                      .hxs(v-if="details != null && details[0] != null && details[0][6] != null").hx-flex-c
                        oddsItem(:odds="details[0]" idx=7 :typ="oddsType" dataType="2" cls="more-value hx-w80")

</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";
import config from "@/config";

export default {
  components: {
    oddsItem
  },
  props: {
    uid: {
      type: String
    },
    details: {
      type: Array
    },
    matchId: {
      type: Number
    },
    leagueId: {
      type: Number
    },
    marketType: {
      type: Number
    },
    sportsType: {
      type: Number
    },
    betType: {
      type: String
    },
    layoutIndex: {
      type: Number
    }
  },
  data() {
    return {
    }
  },
  computed: {
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    }
  },
  methods: {}
};
</script>