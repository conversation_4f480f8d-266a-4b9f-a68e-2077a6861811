export const GAME_MENU_CONFIG = {
  casino: [
    {
      name: "Sexy Bacarrat",
      urlName: "ext2Url", // Maps to GamingSoft (Sexy Bacarrat)
      isHidden: false,
      isNewFeatures: false,
      toPath: "/casino?suite=11",
      popup: "window.open(this.href,'casino11','top=10,height=780,width=1246,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/casino-sexy.png?v=5",
      title: "ui.sexy_bacarrat",
      exclude: [],
      currency: ["CNY", "IDR", "THB", "MYR", "USD", "SGD", "MMK"],
    },
    {
      name: "Pragmatic Casino",
      urlName: "pragmaticCasinoUrl", // Maps to Pragmatic Play
      isHidden: true,
      isNewFeatures: false,
      toPath: "/casino?suite=9",
      popup: "window.open(this.href,'casino9','top=10,height=500,width=900,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/casino-pragmatic.png?v=5",
      title: "ui.london_suite",
      exclude: [],
      currency: ["SGD", "CNY", "IDR", "THB", "VND", "MYR", "USD"],
    },
    {
      name: "Yee Bet",
      urlName: "ybUrl", // Maps to Yee Bet
      isHidden: false,
      isNewFeatures: false,
      toPath: "/casino?suite=8",
      popup: "window.open(this.href,'casino8','top=10,height=540,width=900,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/casino-yeebet.png?v=5",
      title: "ui.yeebet_suite",
      exclude: [],
      currency: ["SGD", "CNY", "MMK", "HKD", "IDR", "THB", "VND", "KRW", "MYR", "USD"],
    },
    {
      name: "CT855",
      urlName: "ctUrl", // Maps to CT Gaming
      isHidden: false,
      isNewFeatures: false,
      toPath: "/casino?suite=4",
      popup: "window.open(this.href,'casino4','top=10,height=540,width=900,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/casino-ct855.png?v=5",
      title: "ui.vietnam_suite",
      exclude: ["IBCBET"],
      currency: ["SGD", "CNY", "MMK", "IDR", "THB", "VND", "KRW", "MYR", "USD"],
    },
    {
      name: "Gameplay",
      urlName: "gpUrl", // Maps to Gameplay
      isHidden: false,
      isNewFeatures: false,
      toPath: "/casino?suite=1",
      popup: "window.open(this.href,'casino1','top=10,height=540,width=900,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/casino-gameplay.png?v=5",
      title: "ui.manila_suite",
      exclude: [],
      currency: ["MYR", "VND", "IDR", "THB", "USD", "CNY", "HKD", "MMK", "KRW"],
    },
    {
      name: "SA Gaming",
      urlName: "saUrl", // Maps to SA Gaming
      isHidden: false,
      isNewFeatures: false,
      toPath: "/casino?suite=3",
      popup: "window.open(this.href,'casino3','top=10,height=540,width=900,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/casino-sagaming.png?v=5",
      title: "ui.bangkok_suite",
      exclude: ["IBCBET"],
      currency: ["SGD", "MMK", "IDR", "THB", "VND", "MYR", "USD"],
    },
    {
      name: "Evolution",
      urlName: "evoUrl", // Maps to Evolution
      isHidden: false,
      isNewFeatures: false,
      toPath: "/casino?suite=5",
      popup: "window.open(this.href,'casino5','top=10,height=540,width=900,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/casino-evo.png?v=5",
      title: "ui.vegas_suite",
      exclude: ["IBCBET"],
      currency: ["SGD", "CNY", "IDR", "THB", "VND", "KRW", "MYR", "USD"],
    },
    {
      name: "Pretty Gaming",
      urlName: "pgUrl", // Maps to Pretty Gaming
      isHidden: false,
      isNewFeatures: false,
      toPath: "/casino?suite=7",
      popup: "window.open(this.href,'casino7','top=10,height=900,width=528,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/casino-pretty-gaming.png?v=5",
      title: "ui.phnom_penh_suite",
      exclude: [],
      currency: ["SGD", "CNY", "IDR", "THB", "VND", "MYR", "USD"],
    },
    {
      name: "AI Gaming",
      urlName: "aiUrl", // Maps to AI Gaming
      isHidden: false,
      isNewFeatures: false,
      toPath: "/casino?suite=10",
      popup: "window.open(this.href,'casino10','top=10,height=800,width=450,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/casino-ai.png?v=5",
      title: "ui.ai_casino",
      exclude: [],
      currency: ["SGD", "CNY", "MMK", "IDR", "THB", "MYR", "USD"],
    },
  ],
  slots: [
    {
      name: "EpicWin",
      urlName: "epwUrl", // Maps to EpicWin
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots14",
      popup: "window.open(this.href,'slots14','top=10,height=594,width=1093,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-epicwin.png?v=5",
      title: "ui.epicwin",
      exclude: [],
      currency: ["CNY", "IDR", "THB", "MYR", "USD", "SGD", "MMK"],
    },
    {
      name: "Live22",
      urlName: "live22Url", // Maps to Live22
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots12",
      popup: "window.open(this.href,'slots12','top=10,height=594,width=1093,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-live22.png?v=5",
      title: "ui.live22",
      exclude: [],
      currency: ["CNY", "IDR", "THB", "MYR", "USD", "SGD", "MMK"],
    },
    {
      name: "WF Gaming",
      urlName: "wfUrl", // Maps to WF Gaming
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots13",
      popup: "window.open(this.href,'slots13','top=10,height=594,width=1093,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-wf.png?v=5",
      title: "ui.wf_gaming",
      exclude: [],
      currency: ["CNY", "IDR", "THB", "MYR", "USD", "SGD", "MMK"],
    },
    {
      name: "DBHASH",
      urlName: "dbhashUrl", // Maps to DBHASH
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots9",
      popup: "window.open(this.href,'slots9','top=10,height=800,width=480,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-dbhash.png?v=5",
      title: "ui.dbhash",
      exclude: [],
      currency: ["CNY", "THB", "MYR", "USD", "SGD"],
    },
    {
      name: "UUSL",
      urlName: "uuslUrl", // Maps to UUSL
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots7",
      popup: "window.open(this.href,'slots7','top=10,height=658,width=1200,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-uuslots.png?v=5",
      title: "ui.uusl",
      exclude: [],
      currency: ["CNY", "IDR", "THB", "MYR", "USD", "SGD", "MMK", "VND"],
    },
    {
      name: "NEXTSPIN",
      urlName: "nextUrl", // Maps to NEXTSPIN
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots8",
      popup: "window.open(this.href,'slots8','top=10,height=658,width=1200,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-nextspin.png?v=5",
      title: "ui.nextspin",
      exclude: [],
      currency: ["CNY", "IDR", "THB", "MYR", "USD", "SGD", "MMK"],
    },
    {
      name: "Pragmatic",
      urlName: "pragmaticUrl", // Maps to Pragmatic Play
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots3",
      popup: "window.open(this.href,'slots3','top=10,height=658,width=1024,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-pragmatic.png?v=5",
      title: "ui.pragmatic",
      exclude: [],
      currency: ["CNY", "IDR", "THB", "MYR", "USD", "SGD", "VND"],
    },
    {
      name: "Joker",
      urlName: "jokerUrl", // Maps to Joker Gaming
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots4",
      popup: "window.open(this.href,'slots4','top=10,height=658,width=1024,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-joker.png?v=5",
      title: "ui.joker",
      exclude: [],
      currency: ["CNY", "IDR", "THB", "MYR", "USD"],
    },
    {
      name: "PG Soft",
      urlName: "pgsUrl", // Maps to PG Soft
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots8",
      popup: "window.open(this.href,'slots2','top=10,height=640,width=360,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-pgsoft.png?v=5",
      title: "ui.pgs",
      exclude: [],
      currency: ["CNY", "MMK", "IDR", "THB", "VND", "KRW", "USD"],
    },
    {
      name: "Simple Play",
      urlName: "sppUrl", // Maps to Simple Play
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots5",
      popup: "window.open(this.href,'slots5','top=10,height=528,width=1024,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-simpleplay.png?v=5",
      title: "ui.simpleplay",
      exclude: [],
      currency: ["CNY", "IDR", "THB", "MYR", "USD", "SGD", "VND"],
    },
    {
      name: "JILI",
      urlName: "jiliUrl", // Maps to JILI
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots6",
      popup: "window.open(this.href,'slots6','top=10,height=562,width=1093,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-jili.png?v=5",
      title: "ui.jili",
      exclude: [],
      currency: ["THB", "MYR", "USD"],
    },
    {
      name: "WOW Gaming",
      urlName: "wowUrl", // Maps to WOW Gaming
      isHidden: false,
      isNewFeatures: false,
      toPath: "/slots11",
      popup: "window.open(this.href,'slots11','top=10,height=800,width=480,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/slot-wow.png?v=5",
      title: "ui.wow_gaming",
      exclude: [],
      currency: ["CNY", "IDR", "THB", "MYR", "USD", "SGD", "MMK"],
    },
  ],
  others: [
    {
      name: "DBPOKER",
      urlName: "dbpokerUrl", // Maps to DBPOKER
      isHidden: false,
      isNewFeatures: false,
      toPath: "/others",
      popup: "window.open(this.href,'others','top=10,height=572,width=1024,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/dbpoker.png?v=5",
      title: "ui.dbpoker",
      exclude: [],
      currency: ["MYR"],
    },
    {
      name: "BINARY",
      urlName: "aioUrl", // Maps to AIO (Binary)
      isHidden: false,
      isNewFeatures: false,
      toPath: "/others1",
      popup: "window.open(this.href,'others1','top=10,height=950,width=500,status=no,toolbar=no,menubar=no,location=no');return false;",
      thumbnail: "img/lobby/binary.png?v=5",
      title: "ui.binary",
      exclude: [],
      currency: ["CNY", "THB", "MYR", "USD", "SGD"],
    },
  ],
};
