export default {
  fglgGenerate(flgh, flg) {
    return new Object({
      FG: flg !== "" && flg !== "NG" ? (flg.split("-")[0] === "H" ? 1 : 0) : -1,
      LG: flg !== "" && flg !== "NG" ? (flg.split("-")[1] === "H" ? 1 : 0) : -1,
      FGH: flgh !== "" && flgh !== "NG" ? (flgh.split("-")[0] === "H" ? 1 : 0) : -1,
      LGH: flgh !== "" && flgh !== "NG" ? (flgh.split("-")[1] === "H" ? 1 : 0) : -1
    });
  }
}