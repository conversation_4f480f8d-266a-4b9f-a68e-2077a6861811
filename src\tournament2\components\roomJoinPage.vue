<template lang="pug">
.tournament-room-join
  template(v-if="roomData.room_join === 0")
    .tournament-pool-single
      .tournament-pool
        //- small {{ roomData.room_type }}
        .tournament-pool-top
          .tournament-background(v-if="roomData.league_logo && roomData.room_type != 2")
            img(:src="getBackground(roomData.league_logo)")
          .tournament-pool-amount
            .tournament-text.d-flex.align-items-center.justify-content-start
              span {{ $t('ui.pool') }}:&nbsp;
              .tournament-color-green {{ roomData.points }}
              .tournament-jackpot.ml-2(v-if="roomData.jackpot > 0")
                i.fad.fa-chevron-double-up
                span {{ roomData.jackpot * 100 }}%
            .tournament-pool-number {{ $t('ui.room') }}:&nbsp;
              | {{ roomData.room_id }}
          a.tournament-pool-fee(v-if="roomData.room_status === 0 && roomData.room_join === 0 && roomData.room_count < roomData.room_limit")
            .tournament-pool-people
              i.fad.fa-users
              span &nbsp;{{ $t('ui.players') }}:&nbsp;
              span &nbsp;{{ roomData.room_count }} / {{ roomData.room_limit }}&nbsp;
            .tournament-pool-entry(v-if="roomData.room_rate > 0")
              span {{ $t('ui.entry_fee') }}:&nbsp;
              span.tournament-color-green {{ roomData.room_rate }}&nbsp;&nbsp;
              small(style="color: #08ff00cc;") (&nbsp;{{ currency_code }}&nbsp;{{ $numeral(roomData.room_rate / roomData.rate).format("0,0.00")  }}&nbsp;)
            .tournament-pool-entry(v-else)
              span.tournament-color-green {{ $t('ui.free_room') }}
          a.tournament-pool-fee(v-else)
            .tournament-pool-people(v-if="roomData.room_count < roomData.room_limit")
              i.fad.fa-users
              span &nbsp;{{ $t('ui.players') }}:&nbsp;
              span &nbsp;{{ roomData.room_count }} / {{ roomData.room_limit }}&nbsp;
            .tournament-pool-people(v-else) {{ $t('message.room_full') }}
            .tournament-pool-entry(v-if="(roomData.room_join > 0)")
              span.tournament-fc-light {{ $t('message.room_joined') }}
          .tournament-pool-details
            .tournament-pool-type.private(v-if="roomData.room_type == 2")
              i.fas.fa-lock.mr-2
              | {{ $t('ui.private') }}
            .tournament-pool-status.mr-3(:class="'room_' + roomData.room_status") {{ $t('m.ROOM_' + roomData.room_status) }}
            //- .tournament-pool-result
            //-   i.fas.fa-list

    iframe(:src="rulesUrl" loading="lazy")
    .tournament-pool-single
      .select-league-row
        .select-league-checkbox
          label.date-check
            input(type="checkbox" v-model="isAgree")
            span.checkmark-date
        .select-league-teams {{ $t("message.accept_terms") }}
    .tournament-room.mt-4
      .tournament-space.flex-fill(v-if="isAgree && roomData.room_type == 2")
        .room-limit
          .room-limit-text {{ $t('ui.room_password') }}:
          .room-rate-input
            input.form-control(type="text" v-model="roomPassword" :placeholder="$t('message.pin_code')" style="width: 150px;" minlength="6" maxlength="6" pattern="[0-9]*" inputmode="numeric" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false")
      .tournament-button.tournament-button-primary.tournament-button-hover.mr-3(v-if="isAgree" @click="joinNow(roomData)" :class="{ 'disabled': loading.roomJoin }")
        .tournament-text {{ $t('ui.join_now') }}
        .tournament-icon
          i.fad.fa-user-plus
      .tournament-button.tournament-button-secondary.tournament-button-hover(@click="cancel" :class="{ 'disabled': loading.roomJoin }")
        .tournament-text {{ $t('ui.cancel') }}
        .tournament-icon
          i.fas.fa-times
  template(v-else)
    .tournament-room.mt-4
      .loader-wrapper(style="height: 400px;")
        .loader

</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import service from "@/tournament2/library/_xhr.js";
import vPagination from "vue-plain-pagination";

export default {
  props: {
    roomData: {
      type: Object,
    },
  },
  data() {
    return {
      isAgree: false,
      roomPassword: "",
      loading: {
        roomJoin: false,
        validateTournament: false,
      },
    };
  },
  computed: {
    language() {
      return this.$store.getters.language;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
    rulesUrl() {
      var lang = "en";
      switch (this.$store.getters.language) {
      case "cn":
        // case "en":
        // case "id":
        // case "kr":
        // case "th":
        // case "tw":
        // case "vi":
        lang = this.$store.getters.language;
        break;
      default:
        lang = "en";
      }
      return config.rulesUrl + "/tournament/rules_" + lang + ".html";
    },
  },
  mounted() {
    this.isAgree = false;
  },
  methods: {
    enterRoom(room) {
      this.$emit("room-enter2", room);
    },
    cancel() {
      this.$emit("room-leave2");
      // this.roomData.room_join == 0 ? this.roomData.room_join = 1 : this.roomData.room_join = 0;
    },
    joinNow(room) {
      if (!this.isAgree) {
        this.$swal("Warning", this.$t("message.accept_required"), "warning");
        return;
      }

      if (room.room_type === 2) {
        // check room password must be string and 6 digit number else show error
        if (isNaN(this.roomPassword) || this.roomPassword.length != 6) {
          // this.$swal("Warning", this.$t("error.roomPasswordRequired"), "warning");
          this.$swal({
            title: "Warning",
            text: this.$t("error.roomPasswordRequired"),
            type: "warning",
            showCancelButton: false,
          });
          return;
        }
      }

      if (room.room_rate > 0) {
        this.$swal({
          title: this.$t("ui.confirmation"),
          html: this.$t("message.deduct_confirm"),
          type: "info",
          showCancelButton: true,
        }).then((result) => {
          if (result.value) {
            this.validateTournament(this.roomJoin);
          }
        });
      } else {
        this.validateTournament(this.roomJoin);
      }
    },
    validateTournament(callback) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      this.loading.validateTournament = true;
      service.validateTournament(json).then(
        (result) => {
          this.loading.validateTournament = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              if (result.data.result === "true") {
                if (callback) callback();
              } else {
                this.$helpers.handleFeedback("tournamentDisabled");
              }
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          this.loading.validateTournament = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    roomJoin() {
      if (this.loading.roomJoin) {
        return;
      }

      var roomPassword = this.roomPassword;

      if (this.roomData.room_type === 2) {
        // check room password must be string and 6 digit number else show error
        if (isNaN(roomPassword) || roomPassword.length != 6) {
          this.$helpers.handleFeedback("invalidRoomPassword");
          return;
        }
      } else {
        roomPassword = "123456";
      }

      var operator_type = this.$store.getters.operatorType;
      var parent_id = this.$store.getters.parentId;

      // make json
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        room_id: this.roomData.room_id,
        room_type: this.roomData.room_type,
        room_pwd: roomPassword,
        operator_type: operator_type,
        parent_id: parent_id,
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      var url = config.tournamentUrl().roomjoin;
      if (operator_type && operator_type == 2) {
        url = config.tournamentUrl().vroomjoin;
      }

      this.loading.roomJoin = true;
      service.roomJoin(url, json).then(
        (result) => {
          this.loading.roomJoin = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              this.$emit("room-data-updated", { ...this.roomData, room_join: 1 });
              EventBus.$emit("roomEnterById2", this.roomData.room_id);
            } else {
              this.$helpers.handleFeedback(feedback.status, true);
            }
          }
        },
        (err) => {
          this.loading.roomJoin = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status, true);
        }
      );
    },
    getBackground(e) {
      return config.tournamentBackgroundPath + e;
    },
  },
};
</script>
