<template lang="pug">
iframe.rules-frame.magicY(:src="rulesUrl")
</template>

<script>
import config from "@/config";

export default {
  data() {
    return {
      anchor: "",
    };
  },
  computed: {
    rulesUrl() {
      var lang = "en";
      switch (this.$store.getters.language) {
      case "en":
      case "cn":
      case "id":
      case "kr":
      case "th":
      case "tw":
      case "vi":
        lang = this.$store.getters.language;
        break;
      default:
        lang = "en";
      }
      return config.rulesUrl + "/general/rules_" + lang + ".html" + this.anchor;
    },
  },
  mounted() {
    var q = this.$route.query;

    if (q.i) {
      this.anchor = "#" + q.i;
    } else {
      this.anchor = "";
    }
  },
};
</script>

<style scoped>
.rules-frame {
  width: 100%;
  height: 100%;
  border: 0;
  padding: 0;
  margin: 0;
  position: fixed;
  top: 46px;
  left: 0;
  bottom: 0;
  right: 0;
  display: block;
}
</style>