<template lang="pug">
.col-12.hx-more-row
  .card
    .card-header(
      :id="'heading-dcou-' + uid"
      data-toggle="collapse"
      :data-target="'#collapse-dcou-' + uid"
      :aria-controls="'collapse-dcou-' + uid"
      aria-expanded=true
      :class="layoutIndex == 3 ? 'live': 'non-live'"
    )
      i.fad.fa-chevron-circle-down
      span.header-bettype {{ $t("m.BT_DCOU") }}
    .collapse.show(
      :aria-labelledby="'heading-dcou-' + uid"
      :id="'collapse-dcou-' + uid"
    )
      .card-body.p-0(:id="'accordian-dcou-' + uid")
        .hx-table.hx-match.hx-more-bet(:class="{ 'live': marketType == 3 }")
          .d-flex.flex-column.w-100
            .hx-cell.w-100.h-18
              .hx-row.hx-more-col.hx-more-col7.header.bl-1.br-1.bb-1
                .hx-col(v-for="item in col")
                  .hx(v-if="details[item] && details[item][7]") {{ $t("m.BT_" + item) }} ({{ details[item][7] }})
                  .hx(v-else) {{ $t("m.BT_" + item) }}
            .d-flex.flex-row
              .hx-cell.w-100
                .hx-row.hx-more-col.hx-more-col7.body.bl-1.br-1.bb-1
                  .hx-col(v-for="item in col")
                    .hx
                      .hxs(v-if="details != null && details[item] != null && details[item][5] != null && details[item][5] != ''").hx-flex-c
                        oddsItem(:odds="details[item]" idx=5 :typ="oddsType" dataType="3" cls="more-value hx-w80")
                      // .hxs(v-else)
                      //   i.fad.fa-lock.lock-color
</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";
import config from "@/config";

export default {
  components: {
    oddsItem,
  },
  props: {
    uid: {
      type: String,
    },
    details: {
      type: Object,
    },
    matchId: {
      type: Number,
    },
    leagueId: {
      type: Number,
    },
    marketType: {
      type: Number,
    },
    sportsType: {
      type: Number,
    },
    betType: {
      type: String,
    },
    layoutIndex: {
      type: Number,
    },
  },
  data() {
    return {
      col: ["HDO", "HDU", "HAO", "HAU", "DAO", "DAU"],
    };
  },
  computed: {
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
  },
  methods: {},
};
</script>
