import config from "@/config";
import errors from "@/errors";
import Vue from "vue";
import store from "@/store";

import { EventBus } from "@/library/_event-bus.js";

export default {
  loading: {
    check: false,
    multicheck: false,
    betsingle: false,
    betparlay: false
  },
  abort() {},

  betMultiOddsCheck(args) {
    const url = config.betMultiOddCheckUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "betMultiOddsCheck"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (this.loading.multicheck == true) {
        // feedback.status = errors.request.processing;
        // canRequest = false;
        this.abort();
      }

      if (canRequest == true) {
        this.loading.multicheck = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.multicheck = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;


              if (feedback.success == true) {
                // Successfully response
                try {
                  // console.log(feedback, res);
                  feedback.data = res.data.parlay;
                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = error; // errors.login.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.multicheck = false;
            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },

  betSingleOddsCheck(args, e) {
    var url = config.betSingleOddCheckUrl();
    if (e == true) {
      url = config.betSingleOddCheckSpecialUrl();
    }
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "betSingleOddsCheck"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (this.loading.check == true) {
        // feedback.status = errors.request.processing;
        // canRequest = false;
        this.abort();
      }

      if (canRequest == true) {
        this.loading.check = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.check = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  // console.log(res);
                  feedback.data = res.data.odds_check_details;
                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.login.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.check = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },

  betSingle(args, e) {
    const operator_type = args.operator_type;
    const parent_id = args.parent_id;

    var url = config.betSingleUrl();
    var src = "betSingle";
    if (operator_type && operator_type == 2) {
      // Seamless Wallet
      if (e == true) {
        // For CSHTFT/ETGHTFT
        url = config.vBetSingleSpecialUrl();
        src = "vBetSingleSpecial";
      } else {
        url = config.vBetSingleUrl();
        src = "vBetSingle";
      }
    } else {
      // Credit or Fund Transfer Wallet
      if (e == true) {
        // For CSHTFT/ETGHTFT
        url = config.betSingleSpecialUrl();
        src = "betSingleSpecial";
      } else {
        url = config.betSingleUrl();
        src = "betSingle";
      }
    }
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: src
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!("parent_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.parent_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (this.loading.betsingle == true) {
        feedback.status = errors.request.processing;
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.betsingle = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.betsingle = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }
              feedback.status = res.data.statusdesc;
              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = {
                    betId: res.data.bet_id,
                    pending: res.data.pending_bet
                  };
                  if (res.data.retrieve_profile) {
                    store.dispatch("user/reLogin").then(
                      res => {
                        if (!res.success) {
                          if (res.status != "no_changes") {
                            Vue.prototype.$helpers.handleFeedback(err.status);
                          }
                        }
                        EventBus.$emit("INVALIDATE");
                      },
                      err => {
                        if (!err.success) {
                          if (err.status != "no_changes") {
                            Vue.prototype.$helpers.handleFeedback(err.status);
                          }
                        }
                      }
                    );
                  }
                  resolve(feedback);
                } catch (err) {
                  console.warn(err);
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.request.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.betsingle = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },

  betParlay(args) {
    const operator_type = args.operator_type;
    const parent_id = args.parent_id;

    var url = config.betParlayUrl();

    if (args.mode) {
      // normal parlay
      if (operator_type && operator_type == 2) {
        url = config.vBetParlayUrl();
      }
    } else {
      // system parlay
      if (operator_type && operator_type == 2) {
        url = config.vBetSystemParlayUrl();
      } else {
        url = config.betSystemParlayUrl();
      }
    }

    // console.log(args, url);
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "betParlay"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!("parent_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.parent_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (this.loading.betparlay == true) {
        feedback.status = errors.request.processing;
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.betparlay = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.betparlay = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              // console.log(res);
              feedback.status = res.data.statusdesc;
              // console.log(res);

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = {
                    betId: res.data.bet_id,
                    pending: res.data.pending_bet
                  };
                  if (res.data.retrieve_profile) {
                    store.dispatch("user/reLogin").then(
                      res => {
                        if (!res.success) {
                          if (res.status != "no_changes") {
                            Vue.prototype.$helpers.handleFeedback(err.status);
                          }
                        }
                        EventBus.$emit("INVALIDATE");
                      },
                      err => {
                        if (!err.success) {
                          if (err.status != "no_changes") {
                            Vue.prototype.$helpers.handleFeedback(err.status);
                          }
                        }
                      }
                    );
                  }
                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.login.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.betparlay = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  }
};
