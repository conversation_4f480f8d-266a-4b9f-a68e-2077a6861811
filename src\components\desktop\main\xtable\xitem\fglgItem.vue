<template lang="pug">
  .hx.h-100.hx-flex-c
    .hxs.w-100(v-if="details[item] != null && details[item][i] != null && details[item][i][6] != null && details[item][i][6] != ''&& details[item][i][5] != '' && details[item][i][5] != 0").px-2
      oddsItem(:odds="details[item][i]" idx=5 :typ="oddsType" dataType="3")
</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";

export default {
  components: {
    oddsItem
  },
  props: {
    details: {
      type: Object
    },
    oddsType: {
      type: String
    },
    item: {
      type: String
    },
    i: {
      type: String
    },
    betType: {
      type: String
    }
  },
  // updated: function() {
  //   this.$nextTick(function() {
  //     console.log("fglg", new Date(), this.oddsType, this.item);
  //   });
  // }
};
</script>
