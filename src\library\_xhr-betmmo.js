import config from "@/config";
import errors from "@/errors";
import Vue from "vue";
import store from "@/store";

import { EventBus } from "@/library/_event-bus.js";

export default {
  loading: {
    check: false,
    betsingle: false,
    betparlay: false
  },
  abort() {},
  betSingleOddsCheck(args) {
    const url = config.mmoSingleOddCheckUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "betSingleOddsCheck"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (this.loading.check == true) {
        // feedback.status = errors.request.processing;
        // canRequest = false;
        this.abort();
      }

      if (canRequest == true) {
        this.loading.check = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.check = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  // console.log(res);
                  feedback.data = res.data.value;
                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.login.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.check = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },

  betSingle(args) {
    const operator_type = args.operator_type;
    const parent_id = args.parent_id;
    // console.log("operator_type", operator_type);
    // console.log("parent_id", parent_id);

    var url = config.mmoSingleUrl();
    if (operator_type && operator_type == 2) {
      url = config.vMMOSingleUrl();
    }

    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "betSingle"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!("parent_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.parent_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (this.loading.betsingle == true) {
        feedback.status = errors.request.processing;
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.betsingle = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.betsingle = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              // console.log(res);
              feedback.status = res.data.statusdesc;

              // console.log(res);

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = {
                    betId: res.data.bet_id,
                    pending: res.data.pending_bet
                  };
                  if (res.data.retrieve_profile) {
                    store.dispatch("user/reLogin").then(
                      res => {
                        if (!res.success) {
                          if (res.status != "no_changes") {
                            Vue.prototype.$helpers.handleFeedback(err.status);
                          }
                        }
                        EventBus.$emit("INVALIDATE");
                      },
                      err => {
                        if (!err.success) {
                          if (err.status != "no_changes") {
                            Vue.prototype.$helpers.handleFeedback(err.status);
                          }
                        }
                      }
                    );
                  }
                  resolve(feedback);
                } catch (err) {
                  console.warn(err);
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.request.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.betsingle = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },

  betParlay(args) {
    const operator_type = args.operator_type;
    const parent_id = args.parent_id;
    // console.log("operator_type", operator_type);
    // console.log("parent_id", parent_id);

    var url = config.mmoParlayUrl();
    if (operator_type && operator_type == 2) {
      url = config.vMMOParlayUrl();
    }

    const feedback = {
      success: false,
      status: errors.request.failed,
      data: null,
      source: "betParlay"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!("parent_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!args.parent_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      if (this.loading.betparlay == true) {
        feedback.status = errors.request.processing;
        canRequest = false;
      }

      if (canRequest == true) {
        this.loading.betparlay = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.betparlay = false;
            if (res.data) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              // console.log(res);
              feedback.status = res.data.statusdesc;
              // console.log(res);

              if (feedback.success == true) {
                // Successfully response
                try {
                  feedback.data = {
                    betId: res.data.bet_id,
                    pending: res.data.pending_bet
                  };
                  if (res.data.retrieve_profile) {
                    store.dispatch("user/reLogin").then(
                      res => {
                        if (!res.success) {
                          if (res.status != "no_changes") {
                            Vue.prototype.$helpers.handleFeedback(err.status);
                          }
                        }
                        EventBus.$emit("INVALIDATE");
                      },
                      err => {
                        if (!err.success) {
                          if (err.status != "no_changes") {
                            Vue.prototype.$helpers.handleFeedback(err.status);
                          }
                        }
                      }
                    );
                  }
                  resolve(feedback);
                } catch (error) {
                  // Failed to login
                  feedback.success = false;
                  feedback.status = errors.login.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.betparlay = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  }
};
