<template lang="pug">
.bet-list-scroll.magicY(v-if="isDataExists", v-show="betShow")
  .new-betslip(v-if="loading.process", :class="{ live: betsingle.marketType == '3' }")
    .text-box.text-center {{ $t('ui.processing') }}
  .new-betslip(v-else, :class="{ live: betsingle.marketType == '3' }")
    .head.d-flex
      template(v-if="racingList.includes(betsingle.sportsType)")
        .head-text.flex-fill.text-ellipsis
          span.ml-1.text-ellipsis(:title="sports[betsingle.sportsType]")
            | {{ sports[betsingle.sportsType] }}
      template(v-else)
        .head-btn.head-btn-danger(v-if="isDataExists && setParlay", @click="addParlay()", :title="!checkAddParlay ? $t('ui.add_to_parlay') : $t('ui.remove_from_parlay')")
          i.fal(:class="[!checkAddParlay ? 'fa-plus' : 'fa-minus']")
        .head-text.flex-fill.text-ellipsis
          span.ml-1.text-ellipsis(:title="sports[betsingle.sportsType] + ' - ' + betTypeDisplay")
            | {{ sports[betsingle.sportsType] }} - {{ betTypeDisplay }}
    .px-2
      .betslip-content
        matchInfoSingle(:betslip="betsingle")
        betInfoSingle(:betslip="betsingle")
      .bet-infosub.p-1(v-if="isDataExists")
        .py-1
          label(@click="handleAcceptBetterOdds")
            input(name="odds", type="checkbox", :checked="betting.acceptBetterOdds == 'true'")
            span.text(@click="handleAcceptBetterOdds") {{ $t('ui.accept_better_odds') }}
      .warning(v-if="betsingle.oddsChanged")
        i.fal.fa-exclamation-circle.text-danger.mr-1
        span {{ $t('message.odds_changed') }} {{ betsingle.oddsChangedText }} {{ $t('message.to') }} {{ betsingle.val }}
        .clearfix
      .warning(v-if="oddsIsUpdating")
        i.fal.fa-exclamation-circle.text-danger.mr-1
        span {{ $t('error.oddsIsUpdating') }}
        .clearfix
      .warning(v-if="invalidOdds")
        i.fal.fa-exclamation-circle.text-danger.mr-1
        span {{ $t('error.oddsIsUpdating') }}
        .clearfix
      .warning(v-if="errorMessage")
        i.fal.fa-times-circle.text-danger.mr-1
        span {{ errorMessage }}
        .clearfix
      .pb-1
      template(v-if="betShow")
        template(v-if="!betConfirm")
          .stake-field.py-1
            .d-flex
              .flex-fill.w-50.pl-1(style="padding-top: 3px; font-weight: bold") {{ currency_code }}
              div
                StakeInput(v-model="stake", @handleStake="handleStake", ref="stake", :loadbet="loading.check")
          .p-1
            table.table-entry(width="100%")
              tbody
                tr
                  td(width="50%") {{ $t('ui.payout') }}
                  td.text-right(width="50%") {{ $numeral(payout).format('0,0.00[0]') }}
                tr
                  td {{ $t('ui.min') }}
                  td.text-right {{ $numeral(betsingle.minBet).format('0,0') }}
                tr
                  td {{ $t('ui.max') }}
                  td.text-right
                    span(v-if="!loading.check") {{ $numeral(betsingle.maxBet).format('0,0') }}
          .stake.pt-0.mt-0
            .d-flex.justify-content-around
              div
                SpinButton(css="btn btn-block btn-cancel btn-sm btn-secondary", @click="cancelBetClick", :text="$t('ui.cancel')", :loading="loading.cancel")
              .ml-1.flex-fill
                SpinButton(css="btn btn-sm btn-block btn-process btn-warning text-ellipsis", @click="processConfirmBet", :text="$t('ui.process_bet')", :loading="loading.process")
        template(v-else)
          .p-1
            table.table-entry(width="100%")
              tbody
                tr
                  td(width="50%") {{ $t('ui.payout') }}
                  td.text-right(width="50%") {{ $numeral(payout).format('0,0.00[0]') }}
                tr
                  td {{ $t('ui.stake') }}
                  td.text-right {{ $numeral(stake).format('0,0.00') }}
            .d-flex.justify-content-between.mt-3
              .title
                i.fad.fa-check-circle.mr-1.text-success
                span.text-dark {{ $t('message.confirm_bet') }}
          .stake.pt-0
            .d-flex.justify-content-around
              div
                SpinButton(css="btn btn-block btn-cancel btn-sm btn-secondary", @click="cancelProcess", :text="$t('ui.no')", :loading="loading.cancel")
              .ml-1.flex-fill
                SpinButton(css="btn btn-sm btn-block btn-process btn-warning text-ellipsis", @click="loadingBet", :text="$t('ui.yes')", :loading="loading.process")
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";
import xhrBet from "@/library/_xhr-bet.js";
import calc from "@/library/_calculation.js";
import config from "@/config";
import betInfoSingle from "@/components/desktop/left/betInfoSingle";
import matchInfoSingle from "@/components/desktop/left/matchInfoSingle";
import StakeInput from "@/components/desktop/left/stakeInput";
import SpinButton from "@/components/ui/SpinButton";
import mixinBetType from "@/library/mixinBetType";
import mixinDelay from "@/library/mixinDelay";
import naming from "@/library/_name";

export default {
  components: {
    betInfoSingle,
    matchInfoSingle,
    SpinButton,
    StakeInput,
  },
  mixins: [mixinBetType, mixinDelay],
  data() {
    return {
      errorMessage: "",
      debounceBet: null,
      debounceIn: null,
      loading: {
        check: false,
        cancel: false,
        process: false,
      },
      league: {},
      match: {},
      defaultCounter: 10,
      counter: 10,
      placeSlip: {},
      stake: null,
      customStake: null,
      payout: null,
      invalidOdds: false,
      oddsIsUpdating: false,
      autoCloseOddsIsUpdating: null,
      betConfirm: false,
      setParlay: false,
      isMMO: false,
      singleOdds: null,
      oddsType: null,
      oddsPosition: null,
      betShow: false,
    };
  },
  computed: {
    racingList() {
      return config.racingList;
    },
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
    betTypeDisplay() {
      return this.$t("m.BS_" + this.betsingle.betType);
    },
    commType() {
      return this.$store.getters.commType;
    },
    debug() {
      return config.debugMode;
    },
    betsingle() {
      return this.$store.state.betsingle;
    },
    pageDisplay() {
      return this.$store.getters.pageDisplay;
    },
    betting() {
      return this.$store.getters.betting;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
    isDataExists() {
      if (this.betsingle.hasOwnProperty("val")) {
        return true;
      } else {
        return false;
      }
    },
    betsingleValue() {
      return this.betsingle.val;
    },
    betsingleMulti() {
      return this.betsingle.multi;
    },
    betparlay() {
      return this.$store.state.betparlay.data;
    },
    checkAddParlay() {
      for (var n in this.betparlay) {
        if (this.betparlay[n].oddsId == this.betsingle.oddsId && this.betparlay[n].betTeamId == this.betsingle.betTeamId) {
          return true;
        }
      }
      return false;
    },
    sports() {
      return this.$store.state.layout.sports;
    },
    menu2() {
      return this.$store.getters.menu2;
    },
  },
  watch: {
    stake(newVal) {
      this.handlePayout();
    },
    betsingleValue(newVal) {
      this.handlePayout();
    },
    betsingleMulti(newVal) {
      this.handlePayout();
    },
  },
  destroyed() {
    clearInterval(this.runner);
    EventBus.$off("BETSINGLE", this.runbet);
  },
  mounted() {
    this.debounceBet = this.debounce(this.processBet, 200);
    setInterval(this.runner, this.defaultCounter * 1000);

    EventBus.$on("BETSINGLE", this.runbet);
    this.debounceCheck = this.debounce(this.triggerRunBet, 200);
    EventBus.betSingle = this.debounceCheck;
    EventBus.cancelSingle = this.cancelBet;
  },
  methods: {
    getBallDisplay(b, g, ha, bt) {
      return naming.ballDisplay2(b, g, ha, bt, this);
    },
    triggerRunBet(odds, typ, idx, val, bt, amt, allowParlay, isMMO, e) {
      if (EventBus.cancelSingleMMO) {
        EventBus.cancelSingleMMO();
      }
      EventBus.$emit("BETSINGLE", odds, typ, idx, val, bt, amt, allowParlay, isMMO, e);
    },
    runbet(odds, typ, idx, val, bt, amt, allowParlay, isMMO, e) {
      if (isMMO) {
        this.$helpers.showDialog(this.$t("ui.action"), this.$t("error.invalidCurrency"), "error");
        return;
      }

      if (EventBus.cancelSingleMMO) {
        EventBus.cancelSingleMMO();
      }

      this.betShow = false;
      this.showMe();
      this.loading.process = false;
      this.singleOdds = odds;
      this.oddsType = typ;
      this.setParlay = allowParlay;
      this.isMMO = isMMO;
      this.oddsPosition = idx;
      this.stake = amt;
      this.customStake = amt;
      this.betConfirm = false;
      this.handleStake();
      this.checkSingleBet(odds, typ, idx, val, bt, e);
      this.xFocus();
      this.bindKeyboard();
      clearTimeout(this.autoCloseOddsIsUpdating);
      this.invalidOdds = false;
      this.oddsIsUpdating = false;
    },
    xFocusTouchEnd() {},
    xFocus() {
      EventBus.$emit("STAKE_FOCUS");
    },
    addParlay() {
      if (this.checkAddParlay) {
        if (EventBus.closeParlay) {
          EventBus.closeParlay(this.betsingle.matchId);
        }
        return;
      }

      var parlayOdds = calc.convertSingleToParlay(this.commType, this.oddsType, this.singleOdds, true);
      if (EventBus.betParlay) {
        EventBus.betParlay(parlayOdds, this.oddsType, this.oddsPosition, parlayOdds[this.oddsPosition], "parlay", this.stake);
      }
    },
    runner() {
      this.autoRefresh();
    },
    cache() {
      return this.$store.getters.data;
    },
    bindKeyboard() {
      document.onkeyup = (event) => {
        if (event.which == 13 || event.keyCode == 13) {
          this.processConfirmBet();
        } else {
          if (event.which == 27 || event.keyCode == 27) {
            this.cancelBet("bindKeyboard");
          }
        }
      };
    },
    fastBet() {
      if (this.isDataExists) {
        this.handleStake();
        this.$swal({
          title: this.$t("ui.confirmation"),
          text: this.$t("message.confirmation"),
          type: "info",
          showCancelButton: true,
        }).then((result) => {
          if (result.value) {
            this.processConfirmBet();
          }
        });
      }
    },
    processConfirmBet() {
      this.loading.process = false;
      if (this.betConfirm) {
        this.loadingBet();
      } else {
        this.betConfirm = true;
      }
    },
    cancelProcess() {
      this.betConfirm = false;
    },
    getBetLimit(st) {
      var result = {
        min_bet: 0,
        max_bet: 0,
        max_payout: 0,
      };
      if (st) {
        switch (parseInt(st)) {
        case 1:
          result = this.$store.getters.playerBetLimit["SOCCER"];
          break;
        case 2:
          result = this.$store.getters.playerBetLimit["BASKETBALL"];
          break;
        case 7:
          result = this.$store.getters.playerBetLimit["TENNIS"];
          break;
        case 8:
          result = this.$store.getters.playerBetLimit["BASEBALL"];
          break;
        case 20:
          result = this.$store.getters.playerBetLimit["ESPORTS"];
          break;
        default:
          result = this.$store.getters.playerBetLimit["OTHERS"];
          break;
        }
      }

      return result;
    },

    showMe() {
      $("#collapse-betslip").collapse("show");
      $("#pills-single-tab").tab("show");
    },
    handlePayout() {
      var a = this.stake * this.betsingle.multi;
      var b = this.betsingle.maxBet * this.betsingle.multi;
      if (a > b) {
        a = b;
      }
      this.payout = a.toFixed(3);
      this.$store.dispatch("betsingle/setStake", this.stake);
    },
    handleStake() {
      if (this.stake % 1 != 0) {
        this.stake = Math.round(this.stake);
      }

      if (this.stake > this.betsingle.maxBet) {
        this.stake = this.betsingle.maxBet;
      } else {
        if (this.stake < this.betsingle.minBet) {
          this.stake = this.betsingle.minBet;
        }
      }
    },
    refreshOddsYes() {
      this.invalidOdds = false;
      this.counter = this.defaultCounter;
      this.requestSingleBet(this.betsingle, "refresh");
    },
    refreshOddsNo() {
      this.invalidOdds = false;
    },
    autoRefresh() {
      if (this.isDataExists) {
        if ((this.betting.autoRefreshOdds == true || this.betting.autoRefreshOdds == "true") && this.isDataExists) {
          this.requestSingleBet(this.betsingle, "refresh");
        }
      }
    },
    handleAutoRefresh() {
      this.$store.dispatch("layout/setSingleBetting", {
        property: "autoRefreshOdds",
        value: this.betting.autoRefreshOdds == "true" ? "false" : "true",
      });
      this.counter = this.defaultCounter;
    },
    handleAcceptBetterOdds() {
      this.$store.dispatch("layout/setSingleBetting", {
        property: "acceptBetterOdds",
        value: this.betting.acceptBetterOdds == "true" ? "false" : "true",
      });
    },
    loadingBet() {
      this.loading.process = true;
      if (this.debounceBet) {
        this.debounceBet();
      }
    },
    processBet() {
      if (!this.betConfirm || Object.keys(this.betsingle).length <= 3) return;
      this.betConfirm = false;
      this.loading.process = true;
      var old = this.placeSlip;
      this.placeSlip = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        odds_id: this.betsingle.oddsId,
        submatch_id: this.betsingle.subMatchId,
        match_id: this.betsingle.matchId,
        bet_type: this.betsingle.betType,
        bet_member: this.stake,
        bet_team_id: this.betsingle.betTeamId,
        home_away: this.betsingle.homeAway,
        odds_display: parseFloat(this.betsingle.val),
        odds_mo: parseFloat(this.betsingle.origin),
        odds_type: this.betsingle.typId,
        accept_better_odds: this.betting.acceptBetterOdds,
        ball_display: this.betsingle.ballDisplay,
        operator_type: this.$store.getters.operatorType,
        parent_id: this.$store.getters.parentId,
        market_type: this.betsingle.marketType,
        odds_col: this.betsingle.idx - 5
      };
      old = null;
      xhrBet.betSingle(this.placeSlip, this.betsingle.special).then(
        (res) => {
          if (res.success) {
            if (this.$store.state.layout.betting.defaultStake == "1") {
              this.$store.dispatch("layout/setSingleBetting", {
                property: "defaultStakeAmount",
                value: this.stake,
              });
            }
            if (res.data.pending == false || res.data.pending == undefined) {
              if (EventBus.betListAccept) {
                EventBus.betListAccept();
              }
            } else {
              if (EventBus.betListPending) {
                EventBus.betListPending();
              }
            }
            this.cancelBet("processBet");
            if (EventBus.getBalance) {
              EventBus.getBalance();
            }
          } else {
            if (this.$helpers.handleFeedback(res.status)) {
              this.errorMessage = res.status;
            } else {
              this.cancelBet("processBetElse");
            }
          }
        },
        (err) => {
          this.loading.process = false;
          this.handleChecking(err.status, "processBetError");
        }
      );
    },
    handleChecking(e, m) {
      this.loading.check = false;
      switch (e) {
      case "marketTypeRequired":
      case "invalidMarketType":
      case "insufficient_balance":
        this.errorMessage = this.$t("error." + e);
        break;
      case "invalidOdds":
        this.invalidOdds = true;
        this.refreshOddsYes();
        break;
      case "oddsIsUpdating":
      case "oddsDisplayInvalid":
      case "invalidOddsBall":
        this.oddsIsUpdating = true;
        clearTimeout(this.autoCloseOddsIsUpdating);
        this.autoCloseOddsIsUpdating = setTimeout(() => {
          this.oddsIsUpdating = false;
        }, this.defaultCounter * 1000);
        break;
      default:
        this.cancelBet(m);
        this.betConfirm = false;
        if (this.$helpers.handleFeedback(e)) {
        }
        break;
      }
    },
    cancelBetClick() {
      this.cancelBet("cancelBetClick");
      setTimeout(() => {
        if (config.vg1.includes(this.menu2)) {
          $("#collapse-vgames").collapse("show");
        } else {
          $("#collapse-allsports").collapse("show");
        }
      }, 100);
    },
    cancelBet(e) {
      clearTimeout(this.autoCloseOddsIsUpdating);
      this.invalidOdds = false;
      this.oddsIsUpdating = false;
      this.loading.cancel = true;
      this.league = {};
      this.match = {};
      this.counter = this.defaultCounter;
      this.$store.dispatch("betsingle/clearData").then(() => {
        this.loading.cancel = false;
        this.loading.process = false;
      });
      this.loading.check = false;
      this.loading.process = false;
      $(".bet-value").removeClass("selected-odds");
    },
    checkSingleBet(odds, typ, idx, val, bt, e) {
      this.counter = this.defaultCounter;
      this.loading.check = true;
      var cache = this.cache();
      if (cache.hasOwnProperty("league") && cache.hasOwnProperty("match")) {
        var sd = {};
        sd.betType = odds[4];

        // add support for special bet
        sd.special = false;
        switch (sd.betType.toUpperCase()) {
        case "CSHTFT":
        case "ETGHTFT":
          sd.special = true;
          break;
        }

        sd.typ = typ;
        sd.idx = idx;
        sd.val = val;
        // sd.origin = calc.fmt(val);

        sd.leagueId = odds[0];
        sd.matchId = odds[1];
        sd.subMatchId = odds[2];
        sd.oddsId = odds[3];

        sd.target = bt;
        if (odds.length == 18 || odds.length == 79) {
          sd.criteria1 = sd.special ? odds[36] : odds[6];
          sd.criteria2 = sd.special ? odds[37] : odds[7];
        }

        this.league = cache.league[sd.leagueId];
        this.match = cache.match[sd.matchId];

        sd.sportsType = this.league[1];
        sd.leagueId = this.league[0];
        sd.leagueName = this.league[4];

        sd.marketType = this.match[4];
        sd.homeName = this.match[5];
        sd.awayName = this.match[6];
        sd.workingDate = this.match[7];
        sd.matchTime = this.match[8];
        sd.homeId = this.match[22];
        sd.awayId = this.match[23];
        sd.score = this.match[11] ? this.match[11].toString().trim() : "";
        sd.typId = config.oddsTypeId[sd.typ];
        sd.ballDisplay = "";
        sd.betDisplay = "";
        sd.oddsChanged = false;
        sd.ballChanged = false;
        sd.oddsChangedText = "";
        sd.ballChangedText = "";
        var limit = this.getBetLimit(sd.sportsType);
        sd.minBet = limit.min_bet;
        sd.maxBet = limit.max_bet;
        sd.maxPayout = limit.max_payout;
        if (sd.typ != "DEC") {
          if (config.marking.includes(sd.betType)) {
            if (sd.val > 0) {
              sd.multi = (parseFloat(sd.val) + 1).toFixed(3);
            } else {
              sd.multi = (Math.abs(parseFloat(sd.val)) + 1).toFixed(3);
            }
          } else {
            sd.multi = sd.val;
          }
        } else {
          sd.multi = sd.val;
        }
        this.setBetType(sd, odds);
        this.$store.dispatch("betsingle/newData", sd);
        this.league = null;
        this.match = null;
        this.requestSingleBet(sd, e);
      }
    },
    requestSingleBet(data, e) {
      this.invalidOdds = false;
      this.errorMessage = "";

      if (data == null) {
        this.cancelBet("requestSingleBetNoData");
        return;
      }

      if (!data.betType || !data.oddsId || !data.sportsType || data.origin == null) {
        this.cancelBet("requestSingleBetInvalid");
        return;
      }

      var slip = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        sports_type: data.sportsType,
        parlay: data.target == "parlay",
        odds_id: data.oddsId,
        submatch_id: data.subMatchId,
        bet_type: data.betType,
        bet_team_id: data.betTeamId,
        home_away: data.homeAway,
        odds_display: parseFloat(data.val),
        odds_mo: parseFloat(data.origin),
        odds_type: data.typId,
        ball_display: data.ballDisplay,
        odds_col: data.idx - 5,
      };
      xhrBet.betSingleOddsCheck(slip, data.special).then(
        (res) => {
          if (res.success) {
            if (data != null) {
              let sd = JSON.parse(JSON.stringify(data));

              sd.oddsChanged = res.data[0].odds_change;
              sd.ballChanged = res.data[0].ball_change;
              if (res.data[0].home_giving == true) {
                sd.giving = 1;
              } else {
                sd.giving = 0;
              }
              if (res.data[0].ball_display_new != null) {
                sd.ballChangedText = this.getBallDisplay(sd.ballDisplay.toString(), sd.giving, sd.homeAway, sd.betType);
                sd.ballDisplay = res.data[0].ball_display_new;
              }
              if (res.data[0].odds_display_new != null) {
                sd.oddsChangedText = sd.val;
                sd.val = calc.fmtType(res.data[0].odds_display_new, this.commType, sd.betType);
              }
              if (res.data[0].odds_new != null) {
                sd.origin = calc.fmt(res.data[0].odds_new);
              }
              if (sd.minBet < res.data[0].min_bet) {
                sd.minBet = res.data[0].min_bet;
              }
              if (sd.maxBet > res.data[0].max_bet) {
                sd.maxBet = res.data[0].max_bet;
              }
              if (sd.maxPayout > res.data[0].max_parlay_payout) {
                sd.maxPayout = res.data[0].max_parlay_payout;
              }
              if (sd.typ != "DEC") {
                if (config.marking.includes(sd.betType)) {
                  if (sd.val > 0) {
                    sd.multi = (parseFloat(sd.val) + 1).toFixed(3);
                  } else {
                    sd.multi = (Math.abs(parseFloat(sd.val)) + 1).toFixed(3);
                  }
                } else {
                  sd.multi = sd.val;
                }
              } else {
                sd.multi = sd.val;
              }

              this.$store.dispatch("betsingle/setData", sd);
              this.handleStake();
              setTimeout(() => {
                if (this.stake == undefined) {
                  this.stake = sd.minBet;
                  if (this.customStake > sd.minBet) {
                    this.stake = this.customStake;
                  }
                  this.$store.dispatch("betsingle/setStake", this.stake);
                } else {
                  if (this.stake < sd.minBet) {
                    this.stake = sd.minBet;
                    this.$store.dispatch("betsingle/setStake", this.stake);
                  }
                }
              }, 10);
              this.loading.check = false;
              this.invalidOdds = false;
            }
          } else {
            this.handleChecking(err.status, "requestSingleBetElse");
          }
        },
        (err) => {
          this.handleChecking(err.status, "requestSingleBetError");
        }
      );
      this.$nextTick(() => {
        setTimeout(() => {
          this.betShow = true;
        }, 250);
      });
    },
  },
};
</script>
