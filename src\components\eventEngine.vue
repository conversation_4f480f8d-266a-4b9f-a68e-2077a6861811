<template lang="pug">
#event
  vue-snotify
  slot
</template>

<script>
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";
import mixinTypes from "@/library/mixinTypes";

export default {
  mixins: [mixinTypes],
  data() {
    return {
      engine: null,
      turbo: null,
      state: {
        GET_BALANCE: 0,
        GET_MENU: 0,
        GET_MARKET: 0,
        GET_LIVE: 0,
        GET_NONLIVE: 0,
      },
      limit: {
        GET_BALANCE: 30,
        GET_MENU: 30,
        GET_MARKET: 90,
        GET_LIVE: 20,
        GET_NONLIVE: 60,
      },
      cache: null,
      oldTimer: null,
      hide: true,
      loading: {
        market: false,
        live: false,
        today: false,
        early: false,
        match2: false,
      },
      marketTime: new Date(),
      liveTime: new Date(),
      todayTime: new Date(),
      earlyTime: new Date(),
      match2Time: new Date(),
    };
  },
  computed: {
    housePower() {
      return this.$store.state.interval;
    },
    isNonLive() {
      if (["all"].includes(this.menu0)) {
        if (["orz"].includes(this.menu3)) {
          return true;
        } else {
          if (["early"].includes(this.menu1)) {
            return true;
          }
        }
      } else {
        return false;
      }
      return false;
    },
    menu0() {
      return this.$store.getters.menu0;
    },
    menu1() {
      return this.$store.getters.menu1;
    },
    menu2() {
      return this.$store.getters.menu2;
    },
  },
  mounted() {
    // console.log("engine mounted");
    EventBus.$on("GET_LIVE", this.runLive);
    EventBus.$on("GET_NONLIVE", this.runNonLive);
    EventBus.$on("GET_MARKET", this.runMarket);
    EventBus.$on("INVALIDATE", this.invalidate);
    this.startEngine();
    this.$nextTick(() => {
      // setTimeout(() => {
      //   EventBus.$emit("INVALIDATE");
      // }, 1000);
      // EventBus.$emit("INVALIDATE");
    });
  },
  destroyed() {
    // console.warn("engine destroyed");
    EventBus.$off("GET_LIVE", this.runLive);
    EventBus.$off("GET_NONLIVE", this.runNonLive);
    EventBus.$off("GET_MARKET", this.runMarket);
    EventBus.$off("INVALIDATE", this.invalidate);
    this.stopEngine();
  },
  methods: {
    invalidate() {
      this.state.GET_LIVE = 0;
      this.state.GET_NONLIVE = 0;
      this.state.GET_MARKET = 0;
    },
    runLive() {
      this.state.GET_LIVE = 0;
    },
    runNonLive() {
      this.state.GET_NONLIVE = 0;
    },
    runMarket() {
      this.state.GET_NONLIVE = 0;
      this.state.GET_LIVE = 0;
      this.state.GET_MARKET = 0;
    },
    startEngine() {
      this.runEngine();
      this.oldTimer = new Date();
    },
    runEngine() {
      this.engine = setTimeout(() => {
        if (this.isLoggedIn) {
          // EventBus.$emit("TICKER");
          if (this.state.GET_MARKET > this.limit.GET_MARKET) {
            this.state.GET_MARKET = 0;
            this.state.GET_LIVE = 0;
            this.state.GET_NONLIVE = 0;
            EventBus.$emit("GET_MARKET");
          } else {
            this.state.GET_MARKET += 1;

            if (!this.isNonLive) {
              if (this.state.GET_LIVE > this.limit.GET_LIVE) {
                this.state.GET_LIVE = 0;
                EventBus.$emit("GET_LIVE");
              } else {
                this.state.GET_LIVE += 1;
              }
              EventBus.$emit("GET_LIVE_COUNT", this.limit.GET_LIVE + 1 - this.state.GET_LIVE);
            }

            if (this.state.GET_NONLIVE > this.limit.GET_NONLIVE) {
              this.state.GET_NONLIVE = 0;
              EventBus.$emit("GET_NONLIVE");
            } else {
              this.state.GET_NONLIVE += 1;
            }
            EventBus.$emit("GET_NONLIVE_COUNT", this.limit.GET_NONLIVE + 1 - this.state.GET_NONLIVE);
          }
          EventBus.$emit("GET_MARKET_COUNT", this.limit.GET_MARKET + 1 - this.state.GET_MARKET);
        } else {
          // this.$helpers.handleFeedback("invalidSession");
        }
        this.runEngine();
      }, this.housePower);
    },
    stopEngine() {
      clearTimeout(this.engine);
    },
    restartEngine() {
      this.stopEngine();
      this.startEngine();
    },
  },
};
</script>
