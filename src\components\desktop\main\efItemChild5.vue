<template lang="pug">
.game-row.game-row-fixed
  .game-cell.game-cell-sm.w-40.justify-content-start(v-if="!isTeam")
    img(:src="getImage()")
  .game-cell.game-cell-sm.flex-fill.justify-content-start
    .game-group
      .game-group-top(v-if="isTeam" style="font-weight: bold;") {{ team[0] }}
      .game-group-top(v-else v-html="team[1].replace(' ', '<br/>')" style="font-size: 12px;")
  .game-cell.game-cell-sm.w-50px.bg-01.ef-bet.ef-gh
    template(v-if="details['oxt'] != null && details['oxt'][0] != null")
      oddsItem(:odds="details['oxt'][0]" idx=5 :typ="oddsType" dataType="2")
    template(v-else)
      .fad.fa-lock-alt.text-muted
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

export default {
  components: {
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem"),
  },
  mixins: [mixinHDPOUOdds],
  props: {
    source: {
      type: Object,
    },
    player: {
      type: Number,
    },
    isTeam: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    team() {
      var n = this.source.homeTeam.split(" - ");
      return n;
    },
  },
  methods: {
    getImage() {
      // console.log(this.team[0]);
      return "images/esports/marble-survival/ball" + String(this.team[0]) + ".svg";
    },
  },
};
</script>
