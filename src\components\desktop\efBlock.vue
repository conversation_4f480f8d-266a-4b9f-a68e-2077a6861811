<template lang="pug">
.main-item
  .esports-main
    .esports-page-wrap
      ef-slider
      template(v-if="keys.length <= 0")
        .mt-1(:class="'sports-' + menu2")
          .round-alert {{ $t('message.no_event') }}
      template(v-else)
        .esports
          .video-refresh(@click="videoRefresh()")
            i.fad.fa-redo
          #videoLink.esports-heroes(:class="'sports-' + menu2")
            iframe#efight-player(:src="getPlayer()", frameborder="0", allowfullscreen="true", scrolling="no" loading="lazy")

          .esports-wrapper
            .esports-item
              .esports-table

                //- Live
                .esports-body
                  template(v-for="(ml, mk) in key3")
                    template(v-if="items[ml].sportsId == menu2")
                      template(v-for="(ll, lk) in items[ml].leagueList")
                        template(v-if="efightList.includes(parseInt(ll.sportsId))")
                          template(v-for="(mml, mmk) in ll.matchList")
                            efItem1(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id")
                        template(v-else)
                          efGroup(:source="ll.matchList" :sportsType="ll.sportsId")

                //- Pre-match
                .d-flex.align-items-center.justify-content-end
                  template(v-if="info.id != null")
                    a.icon-result(
                        v-if="whiteLabel"
                        :href="'/efresult?q=' + info.id + '&n=' + encodeURI(info.type.replace('+', '$')) "
                      )
                      img(src="/v1/images/esports/result-icon.svg")
                    a.icon-result(
                        v-else
                        :href="'/efresult?q=' + info.id + '&n=' + encodeURI(info.type.replace('+', '$')) "
                        target="_blank"
                        onclick="event.stopPropagation();window.open(this.href,'efresult','top=10,height=400,width=950,status=no,toolbar=no,menubar=no,location=no');return false;"
                      )
                      img(src="/v1/images/esports/result-icon.svg")

                .esports-head(v-if="(info.id != null) && (efightList.includes(parseInt(info.id)))")
                  .esports-row.single.bottom-line.text-uppercase
                    .esports-cell.w-130 {{ $t('ui.event') }}
                    .esports-cell.w-140.justify-content-start.pl-3  {{ $t('ui.bet_type') }}
                    .esports-cell.w-160 &nbsp;
                    .esports-cell.w-65 {{ $t('m.BT_H') }}
                    .esports-cell.w-70 &nbsp;
                    .esports-cell.w-65 {{ $t('m.BT_A') }}
                    .esports-cell.w-160 &nbsp;
                    .esports-cell.flex-fill &nbsp;

                .esports-body
                  template(v-for="(ml, mk) in key2")
                    template(v-if="items[ml].sportsId == menu2")
                      template(v-for="(ll, lk) in items[ml].leagueList")
                        template(v-if="efightList.includes(parseInt(ll.sportsId))")
                          //- Fighting Sports
                          template(v-for="(mml, mmk) in ll.matchList")
                            //- small {{ mmk }}
                            efItem1(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id")
                        template(v-else)
                          //- Racing Sports
                          efGroup(:source="ll.matchList" :sportsType="ll.sportsId")

</template>

<script>
import config from "@/config";

export default {
  components: {
    efSlider: () => import("@/components/desktop/efSlider.vue"),
    efItem1: () => import("@/components/desktop/main/efItem1.vue"),
    efGroup: () => import("@/components/desktop/main/efGroup.vue"),
  },
  props: {
    keys: {
      type: Array,
    },
    key1: {
      type: Array,
    },
    key2: {
      type: Array,
    },
    key3: {
      type: Array,
    },
    items: {
      type: Object,
    },
  },
  data() {
    return {
      videoPlay: false,
      counter: 0,
      ipAddress: null,
    };
  },
  computed: {
    whiteLabel() {
      return config.whiteLabel;
    },
    efightList() {
      return config.efightList;
    },
    newFeatures() {
      return config.newFeatures;
    },
    menu2() {
      return this.$store.getters.menu2;
    },
    info() {
      var result = {
        id: null,
        type: null,
      };
      if (this.keys.length > 0) {
        if (this.items != null && this.items.hasOwnProperty(this.keys[0])) {
          result.id = this.items[this.keys[0]].sportsId;
          result.type = this.items[this.keys[0]].sportsType;
        }
      }
      return result;
    },
  },
  destroyed() {
    window.removeEventListener("message", this.receiveMessage);
  },
  mounted() {
    window.addEventListener("message", this.receiveMessage, false);
  },
  methods: {
    getVersion() {
      const feedback = {
        success: false,
        status: errors.login.failed,
      };
      return new Promise((resolve, reject) => {
        this.$http.get(config.ipify1).then(
          (res) => {
            resolve(res.bodyText);
          },
          (err) => {
            reject(err);
          }
        );
      });
    },
    videoRefresh() {
      this.counter += 1;
    },
    receiveMessage(event) {
      const message = event.data.message;

      switch (message) {
      case "videoLink":
        if (event.data.value.name == "Play") {
          $("#videoLink").addClass("esports-play");
        } else {
          $("#videoLink").removeClass("esports-play");
        }
        break;
      }
    },
    getPlayer() {
      // https://wlive888.com/bintu?v=vDYS8-t1rfZ&u=mymymymy005&t=b0443b641e9ce9c3daf3fc40dba225cf&product=42
      // https://wlive888.com/bintu?v=vDYS8-N7FsO&u=mymymymy005&t=b0443b641e9ce9c3daf3fc40dba225cf&product=43
      // https://cache.uat002.com/player10?v=vDYS8-UgMqO&u=kdkdkdkd010&t=55e1141c712b258bbc7c75eaf192acd8&product=45
      // https://cache.uat002.com/player10?v=vDYS8-AXaJR&u=kdkdkdkd010&t=55e1141c712b258bbc7c75eaf192acd8&product=46

      return (
        config.player10Url() +
        config.gameStream[this.menu2 + (this.newFeatures ? 100 : (process.env.NODE_ENV != "production" ? 100 : 0))] +
        "&u=" +
        this.$store.getters.accountId +
        "&t=" +
        this.$store.getters.sessionToken +
        "&product=" +
        this.menu2 +
        "&counter=" +
        this.counter
      );
    },
  },
};
</script>

<style></style>
