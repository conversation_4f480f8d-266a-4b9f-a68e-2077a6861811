export default {
  getHomeTeam(e, lang) {
    var r = e["home_name_" + lang];
    if (r != null && r != "") {
      return r;
    }
    return e.home_team_name;
  },
  getAwayTeam(e, lang) {
    var r = e["away_name_" + lang];
    if (r != null && r != "") {
      return r;
    }
    return e.away_team_name;
  },

  betDisplay(item, t, lang) {
    var result = "";
    switch (item.bet_type) {
      case "PARLAY":
        result = "";
        break;
      case "HDP":
      case "HDPH":
        result = item.home_away == 1 ? this.getHomeTeam(item, lang) : this.getAwayTeam(item, lang);
        break;
      case "OU":
      case "OUH":
        result = item.home_away == 1 ? t.$t("ui.over") : t.$t("ui.under");
        break;
      case "OE":
      case "OEH":
      case "OEHM":
      case "OEHMH":
      case "OEAW":
      case "OEAWH":
        result = item.home_away == 1 ? t.$t("m.BT_ODD") : t.$t("m.BT_EVEN");
        break;
      case "ML":
      case "MLH":
        result = item.home_away == 1 ? t.$t("m.BT_H") : t.$t("m.BT_A");
        break;
      case "1X2":
        switch (item.home_away) {
          case 1:
            result = t.$t("m.BT_FT1");
            break;
          case 2:
            result = t.$t("m.BT_FT2");
            break;
          case 3:
            result = t.$t("m.BT_FTX");
            break;
        }
        break;
      case "1X2H":
        switch (item.home_away) {
          case 1:
            result = t.$t("m.BT_HT1");
            break;
          case 2:
            result = t.$t("m.BT_HT2");
            break;
          case 3:
            result = t.$t("m.BT_HTX");
            break;
        }
        break;
      case "1X2HDP":
      case "1X2HDPH":
        switch (item.home_away) {
          case 1:
            result = t.$t("m.BT_H");
            break;
          case 2:
            result = t.$t("m.BT_A");
            break;
          case 3:
            result = t.$t("m.BT_D");
            break;
        }
        break;
      case "CS":
      case "CSH":
      case "TG":
      case "TGH":
      case "OR":
      case "BS":
        result = item.criteria1;
        break;

      case "CSHTFT":
      case "ETGHTFT":
        if (item.odds_col_criteria) {
          result = item.criteria1 + " / " + item.odds_col_criteria;
        } else {
          if (item.criteria2) {
            result = item.criteria1 + " / <" + item.criteria2 + '>';
          } else {
            result = item.criteria1 + " / " + "-";
          }
        }
        break;

      case "HTFT":
      case "HTFTH":
      case "FGLG":
      case "FGLGH":
      case "CL":
      case "HNB":
      case "ANB":
      case "DNB":
      case "DNBH":
      case "TWTN":
      case "ETG":
      case "ETGH":
      case "EHTG":
      case "EHTGH":
      case "EATG":
      case "EATGH":
        result = t.$t("m.BT_" + item.criteria1);
        break;
      case "WM":
      case "HTFTOE":
        result = t.$t("m.BK_" + item.criteria1);
        break;
      case "1X2OU":
      case "DCOU":
        result = t.$t("m.BT_" + item.criteria1) + ` (${item.criteria2})`;
        break;
      case "OUOE":
        result = t.$t("m.BK_" + item.criteria1) + ` (${item.criteria2})`;
        break;
      case "DC":
      case "DCH":
        result = t.$t("m.BT_" + item.criteria1 + "_DC");
        break;
    }

    return result;
  },
  ballDisplay(item, t) {
    var result = null;
    switch (item.bet_type) {
      case "HDP":
      case "HDPH":
        result = item.ball.toString();
        break;
      case "OU":
      case "OUH":
        result = item.ball.toString();
        break;
      case "1X2HDP":
      case "1X2HDPH":
        result = this.ballDisplay3way(item.home_away, item.ball_home_3way, t);
        break;
    }

    return result;
  },
  ballDisplayMMO1(item, t) {
    var result = parseInt(item.ball_display);
    switch (item.bet_type) {
      case "HDP":
      case "HDPH":
        if (item.bet_team_id == item.team_g) {
          result = 0 - result;
        } else {
          result = result;
        }
        break;
      case "OU":
      case "OUH":
        if (item.bet_team_id == item.team_g) {
          result = result;
        } else {
          result = result;
        }
        break;
      case "1X2HDP":
      case "1X2HDPH":
        result = this.ballDisplay3way(item.home_away, item.ball_home_3way, t);
        break;
    }

    return result;
  },
  ballDisplayMMO2(item, t) {
    var result = parseInt(item.ball_display);
    var giving = item.home_giving ? 1 : 0;
    switch (item.bet_type) {
      case "HDP":
      case "HDPH":
        if (item.home_away != giving + 1) {
          result = 0 - result;
        }
        break;
      case "OU":
      case "OUH":
        break;
      default:
        result = null;
        break;
    }

    return result;
  },
  ballDisplay1(item, t) {
    var result = null;
    switch (item.bet_type) {
      case "HDP":
      case "HDPH":
        if (item.bet_team_id == item.team_g) {
          if (item.ball != 0) {
            result = "-" + item.ball.toString();
          } else {
            result = item.ball.toString();
          }
        } else {
          result = item.ball.toString();
        }
        break;
      case "OU":
      case "OUH":
        result = item.ball.toString();
        break;
      case "1X2HDP":
      case "1X2HDPH":
        result = this.ballDisplay3way(item.home_away, item.ball_home_3way, t);
        break;
    }

    return result;
  },
  ballDisplay2(v, g, b, bt, t) {
    if (v == null || v == "") {
      return null;
    }

    var result = v;
    switch (bt) {
      case "HDP":
      case "HDPH":
        if (v.includes("-")) {
          var balls = v.split("-");
          result = (parseFloat(balls[0]) + parseFloat(balls[1])) / 2;
        }
        if (b != g + 1) {
          if (result != 0) {
            result = "-" + result.toString();
          }
        }
        break;
      case "OU":
      case "OUH":
        if (v.includes("-")) {
          var balls = v.split("-");
          result = (parseFloat(balls[0]) + parseFloat(balls[1])) / 2;
        }
        break;
      case "1X2HDP":
      case "1X2HDPH":
        result = this.ballDisplay3way(b, result, t);
        break;
      default:
        result = null;
        break;
    }

    return result;
  },
  ballDisplayMMO(v, g, b, bt, c) {
    var result = {
      ball_display: parseInt(v),
      criteria2: parseFloat(c),
    };
    switch (bt) {
      case "HDP":
      case "HDPH":
        if (b != g + 1) {
          result.ball_display = 0 - result.ball_display;
        } else {
          result.criteria2 = 0 - result.criteria2;
        }
        break;
      case "OU":
      case "OUH":
        if (b == g + 1) {
          result.criteria2 = 0 - result.criteria2;
        }
        break;
      default:
        result = null;
        break;
    }

    return result;
  },
  ballDisplay3way(b, v, t) {
    var result = v;
    switch (b) {
      case 1:
        if (result > 0) {
          result = "+" + result;
        } else {
          if (result < 0) {
            result = result.toString();
          }
        }
        break;
      case 2:
        if (result > 0) {
          result = "-" + result;
        } else {
          if (result < 0) {
            result = "+" + (-result).toString();
          }
        }
        break;
      case 3:
        if (result > 0) {
          result = t.$t("m.BT_H") + "+" + result;
        } else {
          if (result < 0) {
            result = t.$t("m.BT_A") + "+" + (-result).toString();
          }
        }
        break;
    }
    return result;
  },
  parlayResult(e, t) {
    var payout = e.payout_odds;
    var display = e.odds_display;
    var status = e.status;
    var detail_status = e.detail_status;
    switch (true) {
      case payout === 0:
        return t.$t("ui.lose");
      case payout > 0 && payout < 1:
        return t.$t("ui.lose_half");
      case payout === 1:
        var result = "";
        if (detail_status == 2) {
          result = t.$t("ui.void");
        } else {
          switch (status) {
            case 5:
              result = t.$t("ui.abandoned");
              break;
            case 2:
              result = t.$t("ui.cancel");
              break;
            default:
              result = t.$t("ui.draw_cancel");
              break;
          }
        }
        // return t.$t("ui.draw");
        return result;
      case payout < display && payout > 1:
        return t.$t("ui.won_half");
      case payout > 1:
        return t.$t("ui.won");
      default:
        return "Running";
    }
  },
  mmoParlayResult(e, t) {
    var payout = e.payout_odds;
    var display = e.odds_display;
    var status = e.status;
    var detail_status = e.detail_status;
    switch (true) {
      case payout === 0:
        return t.$t("ui.lose");
      case payout > 0 && payout < 1:
        return t.$t("ui.lose_half");
      case payout === 1:
        var result = "";
        if (detail_status == 2) {
          result = t.$t("ui.void");
        } else {
          switch (status) {
            case 5:
              result = t.$t("ui.abandoned");
              break;
            case 2:
              result = t.$t("ui.cancel");
              break;
            default:
              result = t.$t("ui.draw_cancel");
              break;
          }
        }
        // return t.$t("ui.draw");
        return result;
      case payout > 1 && payout < 2:
        return t.$t("ui.won_half");
      case payout === 2:
        return t.$t("ui.won");
      default:
        return "Running";
    }
  },
  chooseBetType(choosenSport, menu) {
    var betTypes = [];
    for (var mm in menu[choosenSport]) {
      if (menu[choosenSport][mm] > 0) {
        switch (mm) {
          case "st":
          case "mt":
          case "lv":
          case "tn":
          case "hdp":
          case "ou":
          case "ml":
            break;
          case "pl":
            betTypes.push("parlay");
            break;
          default:
            betTypes.push(mm);
            break;
        }
      }
    }
    var sortedBetTypes = ["hdpou", "oxt", "cs", "oe", "dc", "tg", "htft", "fglg", "parlay", "orz"];
    var intersection = sortedBetTypes.filter((x) => betTypes.includes(x));
    var choosenBetType = "hdpou";
    if (!intersection.includes(choosenBetType)) {
      choosenBetType = intersection[0];
    }

    // if (choosenBetType == 'oxt') {
    //   choosenBetType = "hdpou";
    // }

    // console.log('bt', choosenBetType);

    return choosenBetType;
  },
  matchStatus(ht, ft) {
    var status = "";

    switch (ht) {
      case 0:
        switch (ft) {
          case 0:
            status = "ui.match_pending";
            break;
          case 1:
            status = "ui.match_completed";
            break;
          case 2:
            status = "ui.match_cancelled";
            break;
          case 5:
            status = "ui.match_suspended";
            break;
        }
        break;
      case 1:
        switch (ft) {
          case 0:
            status = "ui.running";
            break;
          case 1:
            status = "ui.match_completed";
            break;
          case 2:
            status = "ui.match_cancelled_2h";
            break;
          case 5:
            status = "ui.match_suspended_2h";
            break;
        }
        break;
      case 2:
        switch (ft) {
          case 0:
            status = "ui.running";
            break;
          case 1:
            status = "ui.match_cancelled_1h";
            break;
          case 2:
            status = "ui.match_cancelled";
            break;
          case 5:
            // status = "ui.match_suspended_2h";
            break;
        }
        break;
      case 5:
        switch (ft) {
          case 0:
            status = "ui.match_pending";
            break;
          case 1:
            status = "ui.match_suspended_1h";
            break;
          case 2:
            // status = "ui.match_cancelled";
            break;
          case 5:
            status = "ui.match_suspended";
            break;
        }
        break;
    }

    return status;
  },
};
