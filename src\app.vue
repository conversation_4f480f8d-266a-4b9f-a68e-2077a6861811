<template lang="pug">
router-view
</template>

<script>
import config from "@/config";

export default {
  name: "App",
  components: {},
  computed: {
    whiteLabel() {
      return this.$store.getters.whiteLabel;
    },
    theme() {
      return this.$store.getters.whiteLabel.theme;
    },
  },
  watch: {
    theme(newVal, oldVal) {
      if (this.whiteLabel.mode && this.whiteLabel.theme) {
        $("body").removeClass(oldVal);
        $("body").addClass(newVal);
      }
    },
  },
  mounted() {
    if (this.whiteLabel.mode && this.whiteLabel.theme) {
      $("body").addClass(this.whiteLabel.theme);
    }
  },
  metaInfo: {
    title: config.brand,
    titleTemplate: "%s | Sports Betting Platform",
    meta: [
      { charset: "utf-8" },
      {
        name: "description",
        content:
          "With a comprehensive selection of over 90 sports and thousands of live matches to bet on, the adrenalin rush never ends. Don’t miss out on your chance to win with our offer of only the best odds and top sports picks on the market.",
      },
      { name: "keywords", content: "Sportsbook, Sports, Betting, Gamble, Gambling, Soccer, Football, Basketball" },
      { name: "author", content: config.brand },
      { name: "publisher", content: config.brand },
    ]
  },
};
</script>

<style lang="scss">
@use "./assets/main.scss" as *;
</style>
