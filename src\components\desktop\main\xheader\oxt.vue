<template lang="pug">
  .hx-table.hx-top-rounded.hx-mt-2(:class="source.marketId == 3 ? 'live' : 'non-live'")
    .hx-cell.flex-fill
      .hx-row.h-100
        .d-flex.flex-row.align-items-center.h-100
          .pl-1
            img(:src="'/v1/images/icon-sport-svg/' + getImage(source.sportsId)" width="22")
          .d-flex.flex-row.align-items-baseline.align-item-margin
            .hx-header-title(:class="{ 'live' : source.marketId == 3 }") {{ source.marketType }}
            .hx-header-subtitle {{ source.sportsType }}
    .hx-cell.w-206
      .hx-row
        .hx-col.b-0.w-206
          .hx.text-center {{ $t("ui.full_time") }}
      .hx-row
        .hx-col.w-69
          .hx.text-center {{ $t("ui.home") }}
        .hx-col.w-69
          .hx.text-center {{ $t("ui.away") }}
        .hx-col.w-69
          .hx.text-center {{ $t("ui.draw") }}
    .hx-cell.w-206
      .hx-row
        .hx-col.w-206
          .hx.text-center {{ $t("ui.half_time") }}
      .hx-row
        .hx-col.b-0.w-69
          .hx.text-center {{ $t("ui.home") }}
        .hx-col.w-69
          .hx.text-center {{ $t("ui.away") }}
        .hx-col.w-69
          .hx.text-center {{ $t("ui.draw") }}
    .hx-cell.w-40
      .hx-row
</template>

<script>
import config from "@/config";
export default {
  props: {
    source: {
      type: Object
    },
  },
  methods: {
    getImage(e) {
      return config.getSportsImage(e);
    }
  }
}
</script>