import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";

export default {
  props: {
    // countKey: {
    //   type: Number
    // },
    length: {
      type: Number
    },
    source: {
      type: Object
    },
    index: {
      type: Number
    },
    single: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    id() {
      return this.source.id;
    },
    details() {
      return this.source.details;
    },
    mmoDetails() {
      return this.source.mmoDetails;
    },
    moreItems() {
      return this.source.moreItems;
    },
    mmoMoreItems() {
      return this.source.mmoMoreItems;
    },
    item() {
      return this.source.matchItem;
    },
    matchId() {
      return this.source.matchId;
    },
    matchData() {
      return this.source.matchBody;
    },
    leagueId() {
      return this.source.leagueId;
    },
    marketType() {
      return this.source.marketId;
    },
    sportsType() {
      return parseInt(this.source.sportsId);
    },
    betType() {
      return this.source.betTypeId;
    },
    layoutIndex() {
      return this.source.marketId;
    },
    selectedMatch() {
      return this.$store.getters.selectedMatch;
    },
    isSelected() {
      return this.selectedMatch == this.matchId;
    },
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
    debug() {
      return config.debugMode;
    },
    pageType() {
      return this.$store.getters.pageDisplay.pageType;
    }
  },
  watch: {},
  methods: {
    handleMore(matchId, e) {
      this.$store.dispatch("layout/setSelectedMatch", matchId);
      EventBus.$emit("GET_MATCH2");
    }
  }
};
