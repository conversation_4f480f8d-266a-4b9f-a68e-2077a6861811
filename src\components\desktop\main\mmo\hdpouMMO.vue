<template lang="pug">
.hx-main.hdpouMMO
  .hx-table.hx-match.hx-mmo(v-if="Object.keys(mmoDetails).length > 0 && mmoDetails['tn'] > 0" :class="{ live: source.marketId == 3, alternate: source.matchIndex % 2 == 0 }")
    .hx-cell.w-62
      .hx-row.h-100.hx-rows
        timePanel(:source="source")
    .hx-cell.flex-fill
      .hx-row.h-100.hx-rows
        xTeam(:source="source", isDraw=false, cls="w-193")
    .hx-cell.w-262
      .hx-row.hx-rows(v-for="(dn, i) in mmoDetails['kns']")
        hdpMMO(:details="mmoDetails", :oddsType="oddsType", :i="i", betType="hdp")
        ouMMO(:details="mmoDetails", :oddsType="oddsType", :i="i", betType="ou")
    .hx-cell.w-262
      .hx-row.hx-rows(v-for="(dn, i) in mmoDetails['kns']")
        hdpMMO(:details="mmoDetails", :oddsType="oddsType", :i="i", betType="hdph")
        ouMMO(:details="mmoDetails", :oddsType="oddsType", :i="i", betType="ouh")
    .hx-cell.w-40
      .hx-row.h-100.hx-rows(v-if="menuX")
        .hx-col.hx-cols.w-100.d-flex.align-items-center.justify-content-center
          template(v-if="more > 0")
            .hx-more(
              :class="single ? '' : 'collapsed'",
              :id="'morehead_' + id",
              data-toggle="collapse",
              :aria-expanded="false",
              :data-target="'#morebet_' + id",
              :aria-controls="'morebet_' + id",
              @click="handleMore(matchId, $event.target)"
            )
              i.far.fa-chevron-up
              span &nbsp;{{ more }}

  .hx-table.hx-match.hx-non-mmo(v-if="!menuX" :class="{ live: source.marketId == 3, alternate: source.matchIndex % 2 == 0 }")
    .hx-cell.w-62
      .hx-row.h-100.hx-rows
        timePanel(:source="source")
    .hx-cell.flex-fill
      .hx-row.h-100.hx-rows
        xTeam(:source="source", isDraw, cls="w-193")
        xFavorite(:source="source")
    .hx-cell.w-262
      .hx-row.hx-rows(v-for="(dn, i) in dlp", :class="{ 'h-66': (details['oxt'] != null && details['oxt'][i] != null) || (details['oxth'] != null && details['oxth'][i] != null) }")
        hdpItem(:details="details", :oddsType="oddsType", :i="i", betType="hdp")
        ouItem(:details="details", :oddsType="oddsType", :i="i", betType="ou")
        oxtItem(:details="details", :oddsType="oddsType", :i="i", betType="oxt")
    .hx-cell.w-262
      .hx-row.hx-rows(v-for="(dn, i) in dlp", :class="{ 'h-66': (details['oxt'] != null && details['oxt'][i] != null) || (details['oxth'] != null && details['oxth'][i] != null) }")
        hdpItem(:details="details", :oddsType="oddsType", :i="i", betType="hdph")
        ouItem(:details="details", :oddsType="oddsType", :i="i", betType="ouh")
        oxtItem(:details="details", :oddsType="oddsType", :i="i", betType="oxth")
    .hx-cell.w-40
      .hx-row.h-100.hx-rows
        .hx-col.hx-cols.w-100.d-flex.align-items-center.justify-content-center
          template(v-if="more > 0")
            .hx-more(
              :class="single ? '' : 'collapsed'",
              :id="'morehead_' + id",
              data-toggle="collapse",
              :aria-expanded="false",
              :data-target="'#morebet_' + id",
              :aria-controls="'morebet_' + id",
              @click="handleMore(matchId, $event.target)"
            )
              i.far.fa-chevron-up
              span &nbsp;{{ more }}
  template(v-if="kns > MAX_ITEMS") 
    .hx-table.hx-ot-more(
      :class="{ live: source.marketId == 3, alternate: source.matchIndex % 2 == 0 }"        
    )
      .btn(@click="expand($event.target)")
        span(v-if="!expanded") +
        span(v-else) -
        span {{ kns - MAX_ITEMS }}
  template(v-if="more > 0")
    .hx-table.hx-match.hx-morebet.collapse(
      :id="'morebet_' + id",
      :aria-labelledby="'morehead_' + id",
      data-parent="#hdpou",
      :class="{ live: source.marketId == 3, alternate: source.matchIndex % 2 == 0, show: single }"
    )
      morePanel(
        v-if="source.matchId == selectedMatch"
        ref="morePanel",
        :uid="id",
        :details="moreItems",
        :child1Ids="details['child1']",
        :child2Ids="details['child2']",
        :matchId="matchId",
        :leagueId="leagueId",
        :marketType="marketType",
        :sportsType="sportsType",
        :betType="betType",
        :layoutIndex="layoutIndex"
      )

</template>

<script>
import config from "@/config";
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

export default {
  components: {
    timePanel: () => import("@/components/desktop/main/xtable/timePanel"),
    morePanel: () => import("@/components/desktop/main/xheader/morePanel"),
    xTeam: () => import("@/components/desktop/main/xtable/xitem/xTeam"),
    xFavorite: () => import("@/components/desktop/main/xtable/xitem/xFavorite"),
    hdpItem: () => import("@/components/desktop/main/xtable/xitem/hdpItem"),
    ouItem: () => import("@/components/desktop/main/xtable/xitem/ouItem"),
    oxtItem: () => import("@/components/desktop/main/xtable/xitem/oxtItem"),
    hdpMMO: () => import("@/components/desktop/main/mmo/xitem/hdpMMO"),
    ouMMO: () => import("@/components/desktop/main/mmo/xitem/ouMMO")
  },
  mixins: [mixinHDPOUOdds],
  data() {
    return {
      expanded: false,
    };
  },
  computed: {
    MAX_ITEMS() {
      return config.OT_MAX_ITEMS;
    },
    pageType() {
      return this.$store.getters.pageDisplay.pageType
    },
    kns() {
      return this.details["kns"];
    },
    dlp() {
      if (this.expanded) {
        return this.kns;
      } else {
        if (this.kns > this.MAX_ITEMS) {
          return this.MAX_ITEMS;
        } else {
          return this.kns;
        }
      }
    },
    more() {
      return this.details["more"];
    },
    menuX() {
      return this.$store.getters.menuX;
    },
    menu0() {
      return this.$store.getters.menu0;
    }
  },
  mounted() {
    setTimeout(() => {
      if (this.source.matchId == this.selectedMatch) {
        this.$nextTick(() => {
          var test = $("#morehead_" + this.id);
          test.removeClass("collapsed");
          test.attr("aria-expanded", "true");
          $("#morebet_" + this.id).addClass("show");
        });
      }
    }, 500);
  },
  methods: {
    expand(e) {
      this.expanded = !this.expanded;
    },
  }
};
</script>
