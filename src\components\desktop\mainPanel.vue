<template lang="pug">
#main.main(:class="{ active: !sidebar, active2: !asidebar }")
  .main-top
    ul
      //- Tournament link (white label and non-white label)
      //- li
      //-   router-link(
      //-     v-if="whiteLabel"
      //-     to="/tournament2"
      //-     )
      //-     img.img-fluid(src="/images/main-top/tournament2.jpg")
      //-   router-link(
      //-     v-else
      //-     to="/tournament2"
      //-     target="_blank"
      //-     onclick="window.open(this.href,'tourplus','height=700,width=1440,status=no,toolbar=no,menubar=no,location=no');return false;"
      //-     )
      //-     img.img-fluid(src="/images/main-top/tournament2.jpg")
      //- li
      //-   a(@click="showComingSoon")
      //-     img.img-fluid(src="/images/main-top/tournament2.jpg")

      //- V-Games link
      li
        a(href="javascript:void(0);" @click="setGame")
          img.img-fluid(src="images/main-top/v-games1.jpg")

      //- Live22 link for specific currencies (non-white label)
      li
        router-link(
          v-if="!whiteLabel && ['MYR', 'USD', 'THB', 'MMK', 'CNY', 'IDR', 'SGD'].includes(currency_code)"
          to="/slots12",
          target="_blank",
          onclick="window.open(this.href,'slots','top=10,height=512,width=1093,status=no,toolbar=no,menubar=no,location=no');return false;"
          )
          img.img-fluid(src="images/main-top/live22.jpg")

      //- Highlights link (white label and non-white label)
      li
        router-link(
          v-if="whiteLabel"
          to="/highlight"
          )
          img.img-fluid(src="/v1/images/main-top/highlights.jpg")
        router-link(
          v-else
          to="/highlight"
          target="_blank"
          onclick="window.open(this.href,'highlight','top=10,height=700,width=1366,status=no,toolbar=no,menubar=no,location=no');return false;"
          )
          img.img-fluid(src="/v1/images/main-top/highlights.jpg")

  .header-wrap
    filterBar
  .page-wrap(:class="{ 'sport-early': this.imenu1 == 1 && menu3 != 'orz' }")
    #hdpou.sport.mt-1(:class="{ 'mini-bar': menu0 == 'all' && menu1 != 'today' && menu3 != 'parlay' && menu3 != 'orz', higher: !header, 'sport-early': this.imenu1 == 1 && menu3 != 'orz' }")
      #wrapper-table.wrapper(v-if="(marketKeys.length > 0) || gameList.includes(String(menu2))")
        template
          .main-item(v-if="menu0 == 'all' && menu1 == 'early' && menu3 != 'orz'")
            .hx-title
              dateFilter
          .main-item(v-if="!menuX && menu0 == 'parlay' && menu1 == 'parlay' && menu3 == 'parlay'")
            .hx-title
              parlayFilter

        template(v-if="!single.loading && show")
          template(v-if="single.mode")

            template(v-if="single.match_id && single.sports_type && sports[single.sports_type] && matchList[single.match_id]")
              singleBlock(:single="single" :matchList="matchList" :sports="sports[single.sports_type]")
              mainItem(:source="marketList[matchList[single.match_id].categoryId]", :index="1", :key="1")
              mainItem(:source="leagueList[matchList[single.match_id].groupId]", :index="1", :key="2")
              mainItem(:source="matchList[single.match_id]", :index="1", :key="3", :single="single.mode")
            template(v-else)
              .main-item
                .single-match
                  .header
                    .action-block(@click="resetSingle()")
                      i.far.fa-chevron-left
                    .name-block
                      span {{ $t('ui.back') }}
              .main-item.mt-1
                .round-alert {{ $t('message.no_event') }}

          template(v-else)

            template(v-if="gameList.includes(String(menu2)) && (String(menu0) == 'all') && (String(menu1) == 'today')")
              efBlock(:keys="marketKeys" :key1="marketKeys1" :key2="marketKeys2" :key3="marketKeys3" :items="marketList")
            template(v-else)
              template
                template(v-if="menuX == true")
                  //- MMO PARLAY
                  template(v-if="menu0 == 'parlay' && menu1 == 'parlay' && menu3 == 'parlay'")
                    template(v-for="(ml, mk) in paginatedMarketKeys3")
                      template(v-if="marketList[ml].xp")
                        mxItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                        template(v-for="(ll, lk) in marketList[ml].leagueList")
                          template(v-if="ll.xp")
                            mxItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                            template(v-for="(mml, mmk) in ll.matchList")
                              template(v-if="mml.xp")
                                mxItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                    template(v-for="(ml, mk) in paginatedMarketKeys2")
                      template(v-if="marketList[ml].xp")
                        mxItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                        template(v-for="(ll, lk) in marketList[ml].leagueList")
                          template(v-if="ll.xp")
                            mxItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                            template(v-for="(mml, mmk) in ll.matchList")
                              template(v-if="mml.xp")
                                mxItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                    template(v-for="(ml, mk) in paginatedMarketKeys1")
                      template(v-if="marketList[ml].xp")
                        mxItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                        template(v-for="(ll, lk) in marketList[ml].leagueList")
                          template(v-if="ll.xp")
                            mxItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                            template(v-for="(mml, mmk) in ll.matchList")
                              template(v-if="mml.xp")
                                mxItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                  template(v-else)
                    //- NORMAL
                    template(v-for="(ml, mk) in paginatedMarketKeys3")
                      template(v-if="marketList[ml].xn")
                        mainItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                        template(v-for="(ll, lk) in marketList[ml].leagueList")
                          template(v-if="ll.xn")
                            mainItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                            template(v-for="(mml, mmk) in ll.matchList")
                              template(v-if="mml.xn")
                                mainItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                    template(v-for="(ml, mk) in paginatedMarketKeys2")
                      template(v-if="marketList[ml].xn")
                        mainItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                        template(v-for="(ll, lk) in marketList[ml].leagueList")
                          template(v-if="ll.xn")
                            mainItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                            template(v-for="(mml, mmk) in ll.matchList")
                              template(v-if="mml.xn")
                                mainItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                    template(v-for="(ml, mk) in paginatedMarketKeys1")
                      template(v-if="marketList[ml].xn")
                        mainItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                        template(v-for="(ll, lk) in marketList[ml].leagueList")
                          template(v-if="ll.xn")
                            mainItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                            template(v-for="(mml, mmk) in ll.matchList")
                              template(v-if="mml.xn")
                                mainItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                  //- MMO PARLAY
                template(v-else)
                  //- NON-MMO
                  template(v-if="menu0 == 'parlay' && menu1 == 'parlay' && menu3 == 'parlay'")
                    //- PARLAY VIEW ONLY
                    template(v-for="(ml, mk) in paginatedMarketKeys3", v-if="[2, 6].includes(parlayMode)")
                      mainItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                      template(v-for="(ll, lk) in marketList[ml].leagueList")
                        mainItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                        template(v-for="(mml, mmk) in ll.matchList")
                          mainItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                    template(v-for="(ml, mk) in paginatedMarketKeys2", v-if="[2, 6].includes(parlayMode)")
                      mainItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                      template(v-for="(ll, lk) in marketList[ml].leagueList")
                        mainItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                        template(v-for="(mml, mmk) in ll.matchList")
                          mainItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                    template(v-for="(ml, mk) in paginatedMarketKeys1", v-if="[1, 6].includes(parlayMode)")
                      mainItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                      template(v-for="(ll, lk) in marketList[ml].leagueList")
                        mainItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                        template(v-for="(mml, mmk) in ll.matchList")
                          mainItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                  template(v-else)
                    //- NORMAL
                    template(v-for="(ml, mk) in paginatedMarketKeys3")
                      mainItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                      template(v-for="(ll, lk) in marketList[ml].leagueList")
                        mainItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                        template(v-for="(mml, mmk) in ll.matchList")
                          mainItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                    template(v-for="(ml, mk) in paginatedMarketKeys2")
                      mainItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                      template(v-for="(ll, lk) in marketList[ml].leagueList")
                        mainItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                        template(v-for="(mml, mmk) in ll.matchList")
                          mainItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                    template(v-for="(ml, mk) in paginatedMarketKeys1")
                      mainItem(:source="marketList[ml]", :index="parseInt(mk)", :key="mk + marketList[ml].id")
                      template(v-for="(ll, lk) in marketList[ml].leagueList")
                        mainItem(:source="ll", :index="parseInt(lk)", :key="lk + ll.id")
                        template(v-for="(mml, mmk) in ll.matchList")
                          mainItem(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id", :single="single.mode" :lmc="liveMatchCount")
                  //- NON-MMO

        template(v-else)
          .main-item.loader-wrapper
            .loader

        template
          .hx-footer
      #wrapper-table.wrapper(v-else)
        .main-item(v-if="menu0 == 'all' && menu1 == 'early' && menu3 != 'orz'")
          .hx-title
            dateFilter
        .main-item(v-if="!menuX && menu0 == 'parlay' && menu1 == 'parlay' && menu3 == 'parlay'")
          .hx-title
            parlayFilter
        .main-item.mt-1
          .round-alert {{ $t('message.no_event') }}
        .main-item.loader-wrapper(v-if="loading.logo")
          .loader

        .loading-indicator(v-if="pagination.loading")
          .spinner-border.text-primary(role="status")
            span.sr-only Loading...

</template>

<script>
import config from "@/config";
import xhrMarket from "@/library/_xhr-market";
import xhrMatch from "@/library/_xhr-match";
import sync1 from "@/library/_sync-market";
import sync2 from "@/library/_sync-match";
import { EventBus } from "@/library/_event-bus.js";
import mixinTypes from "@/library/mixinTypes";
import naming from "@/library/_name.js";
import { safePerformanceMonitorMixin } from '@/utils/safePerformanceMonitor';

// fast-deep-equal is a CommonJS module that doesn't export a default export
var equal = require("fast-deep-equal");

export default {
  name: "MainPanel", // Add component name for better monitoring
  components: {
    filterBar: () => import("@/components/desktop/main/filterBar"),
    dateFilter: () => import("@/components/desktop/main/dateFilter"),
    parlayFilter: () => import("@/components/desktop/main/parlayFilter"),
    mainItem: () => import("@/components/desktop/mainItem"),
    singleBlock: () => import("@/components/desktop/singleBlock"),
    mxItem: () => import("@/components/desktop/mxItem"),
    efBlock: () => import("@/components/desktop/efBlock"),
  },
  mixins: [mixinTypes, safePerformanceMonitorMixin],
  data() {
    return {
      gameList: config.gameList,
      liveMatchCount: 0,
      loading: {
        logo: false,
        market: false,
        live: false,
        today: false,
        early: false,
        match2: false,
      },
      marketTime: new Date(),
      liveTime: new Date(),
      todayTime: new Date(),
      earlyTime: new Date(),
      match2Time: new Date(),

      matchList: {},
      leagueList: {},
      marketList: {},
      oddsList: {},
      mmoList: {},

      refMMO: {},
      refOdds: {},

      single: {
        match_id: null,
        sports_type: null,
        market_type: null,
        mode: false,
        loading: false,
      },
      show: false,
      pagination: {
        pageSize: 20,
        currentPage: 1,
        totalItems: 0,
        loading: false,
      },
      virtualScroll: {
        itemHeight: 50, // Approximate height of each item
        visibleItems: 20,
        startIndex: 0,
        endIndex: 20,
      },
      timeouts: [],
    };
  },
  computed: {
    newFeatures() {
      return config.newFeatures;
    },
    currency_code() {
      return this.$store.getters.currencyCode;
    },
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
    parlayMode() {
      return this.$store.state.cache.parlayMode;
    },
    sportsradar() {
      return config.sportsradar;
    },
    marketKeys() {
      var r = Object.keys(this.marketList);
      return Object.freeze(r);
    },
    marketKeys3() {
      var m = [];
      for (var n in this.marketKeys) {
        var id = this.marketKeys[n];
        if (this.marketList[id].marketId == 3) {
          m.push(id);
        }
      }
      return m;
    },
    marketKeys2() {
      var m = [];
      for (var n in this.marketKeys) {
        var id = this.marketKeys[n];
        if (this.marketList[id].marketId == 2) {
          m.push(id);
        }
      }
      return m;
    },
    marketKeys1() {
      var m = [];
      for (var n in this.marketKeys) {
        var id = this.marketKeys[n];
        if (this.marketList[id].marketId == 1) {
          m.push(id);
        }
      }
      return m;
    },
    cacheData() {
      return this.$store.getters.data;
    },
    cacheMMO() {
      return this.$store.getters.data["mmo"];
    },
    paginatedMarketKeys3() {
      const start = (this.pagination.currentPage - 1) * this.pagination.pageSize;
      const end = start + this.pagination.pageSize;
      return this.marketKeys3.slice(start, end);
    },

    paginatedMarketKeys2() {
      const start = (this.pagination.currentPage - 1) * this.pagination.pageSize;
      const end = start + this.pagination.pageSize;
      return this.marketKeys2.slice(start, end);
    },

    paginatedMarketKeys1() {
      const start = (this.pagination.currentPage - 1) * this.pagination.pageSize;
      const end = start + this.pagination.pageSize;
      return this.marketKeys1.slice(start, end);
    },

    totalPages() {
      return Math.ceil(this.pagination.totalItems / this.pagination.pageSize);
    },
  },
  watch: {
    language() {
      EventBus.$emit("INVALIDATE");
    },
    commType() {
      EventBus.$emit("INVALIDATE");
    },
    oddsType() {
      EventBus.$emit("INVALIDATE");
    },
    eventSorting() {
      EventBus.$emit("INVALIDATE");
    },
    displayMarketType() {
      EventBus.$emit("INVALIDATE");
    },
  },
  mounted() {
    this.slickInit();

    EventBus.$on("INVALIDATE", this.run_invalidate);
    EventBus.$on("GET_MARKET", this.run_market);
    EventBus.$on("GET_LIVE", this.run_live);
    EventBus.$on("GET_NONLIVE", this.run_nonlive);
    EventBus.$on("GET_MATCH2", this.run_match2);
    EventBus.$on("FIND_EVENT", this.findEvent);
    EventBus.$on("RESET_SINGLE", this.resetSingle);
    EventBus.$on("PREPARE", this.prepare);
    EventBus.refresh = this.run_invalidate;
    EventBus.resetLayout = this.reset;

    this.show = true;
    this.resetPagination();
    window.addEventListener("scroll", this.handleScroll);
  },
  beforeDestroy() {
    this.slickDestroy();

    // Clear all timers
    this.timeouts.forEach(clearTimeout);
    this.timeouts = [];
    
    EventBus.$off("INVALIDATE", this.run_invalidate);
    EventBus.$off("GET_MARKET", this.run_market);
    EventBus.$off("GET_LIVE", this.run_live);
    EventBus.$off("GET_NONLIVE", this.run_nonlive);
    EventBus.$off("GET_MATCH2", this.run_match2);
    EventBus.$off("FIND_EVENT", this.findEvent);
    EventBus.$off("RESET_SINGLE", this.resetSingle);
    EventBus.$off("PREPARE", this.prepare);

    window.removeEventListener("scroll", this.handleScroll);
  },
  methods: {
    slickInit() {
      const $slick = $("#slick-top");
      $slick.on("afterChange", this.onAfterChange);
      $slick.on("destroy", this.onDestroy);
      $slick.on("init", this.onInit);
      $slick.slick(this.options);
      $slick.slick("slickGoTo", 0, true);
      $("#slick-top").show();
    },
    slickDestroy() {
      const $slick = $("#slick-top");
      $slick.off("afterChange", this.onAfterChange);
      $slick.off("destroy", this.onDestroy);
      $slick.off("init", this.onInit);
      $slick.slick("unslick");
    },
    setGame() {
      if (EventBus.setGame) {
        EventBus.setGame();
      }
    },
    prepare() {
      var q = this.$route.query;
      if (q.e && q.s && q.m) {
        this.$store.dispatch("cache/setFilterMode", 0);
        this.findEvent({
          match_id: q.e,
          sports_type: parseInt(q.s),
          market_type: parseInt(q.m),
        });
      } else if (q.vg != null) {
        this.setMarbleRunView(q.vg);
      } else {
        this.setDefaultView();
      }
    },
    findEvent(e) {
      let event = JSON.parse(JSON.stringify(e));
      if (event) {
        this.single.loading = true;
        this.setMenuX(event.market_type, event.sports_type, true);
        setTimeout(() => {
          this.single.mode = true;
          if (event) {
            this.single.match_id = event.match_id;
            this.single.sports_type = event.sports_type;
            this.single.market_type = event.market_type;
            this.$store.dispatch("layout/setSelectedMatch", event.match_id);
            EventBus.$emit("GET_MATCH2");
          }
          this.single.loading = false;
        }, 600);
      }
    },
    setDefaultView(e) {
      var mi5 = {};
      mi5["menuX"] = false;
      mi5["menuY"] = "0";
      mi5["menu0"] = "all";
      mi5["menu1"] = "today";
      mi5["menu3"] = "hdpou";
      this.$store.dispatch("layout/setSelectedDays", "1");
      this.$store.dispatch("layout/resetSelectLeague");
      mi5["src"] = "setDefaultView";
      this.$store.dispatch("layout/setMenuItems", mi5);
      EventBus.$emit("INVALIDATE");
    },
    setMarbleRunView(e) {
      var mi5 = {};
      mi5["menuX"] = false;
      mi5["menuY"] = "0";
      mi5["menu0"] = "all";
      mi5["menu1"] = "today";
      mi5["menu2"] = e ? (config.vg1.includes(parseInt(e)) ? parseInt(e) : 49) : 49;
      mi5["menu3"] = "hdpou";
      this.$store.dispatch("layout/setSelectedDays", "1");
      this.$store.dispatch("layout/resetSelectLeague");
      mi5["src"] = "setMarbleRunView";
      $("#heading-vgames").click();
      setTimeout(() => {
        this.$nextTick(() => {
          this.$store.dispatch("layout/setMenuItems", mi5);
          EventBus.$emit("INVALIDATE");
        });
      }, 1000);
    },
    invalidate() {
      EventBus.$emit("INVALIDATE");
    },
    setDays(e) {
      if (config.day1.includes(e)) {
        this.$store.dispatch("layout/setSelectedDays", "1");
      } else {
        this.$store.dispatch("layout/setSelectedDays", "0");
      }
    },
    clearSearch() {
      this.$store.dispatch("layout/setSearch", null);
    },
    resetSelectLeague() {
      this.$store.dispatch("layout/resetSelectLeague");
    },
    setMenuX(m, s, fc) {
      var mi5 = {};
      mi5["menuX"] = false;
      mi5["menuY"] = "0";
      mi5["menu0"] = "all";
      switch (m) {
      case 1:
        mi5["menu1"] = "early";
        break;
      case 2:
        mi5["menu1"] = "today";
        break;
      case 3:
        mi5["menu1"] = "today";
        break;
      }

      mi5["menu2"] = s;
      mi5["menu3"] = "hdpou";

      var changed = false;
      if (mi5["menuX"] != this.menuX) {
        changed = true;
      }
      if (mi5["menuY"] != this.menuY) {
        changed = true;
      }
      if (mi5["menu0"] != this.menu0) {
        changed = true;
      }
      if (mi5["menu1"] != this.menu1) {
        changed = true;
      }
      if (mi5["menu2"] != this.menu2) {
        changed = true;
      }
      if (mi5["menu3"] != this.menu3) {
        changed = true;
      }

      if (changed || fc) {
        this.setDays(0);
        this.resetSelectLeague();
        this.clearSearch();
        mi5["src"] = "mainPanel.setMenu0";
        this.$store.dispatch("layout/setMenuItems", mi5);
        EventBus.$emit("INVALIDATE");
      }
    },
    getImage(e) {
      return config.getSportsImage(e);
    },
    league() {
      return this.$store.getters.data.league;
    },
    leagueName(e) {
      var league = this.league();
      if (league.hasOwnProperty(e)) {
        return league[e][4];
      } else {
        return "--";
      }
    },
    match() {
      return this.$store.getters.data.match;
    },
    child() {
      return this.$store.getters.data.child;
    },
    group(e) {
      return this.$store.getters.data.group[e];
    },
    layout(e) {
      return this.$store.getters.data.layout[e];
    },
    reset() {
      this.$store.dispatch("cache/resetData");
      this.loading.logo = false;
      this.loading.market = false;
      this.loading.early = false;
      this.loading.today = false;
      this.loading.live = false;
      this.matchList = {};
      this.leagueList = {};
      this.marketList = {};
      this.oddsList = {};
      this.mmoList = {};
      this.refMMO = {};
      this.refOdds = {};

      this.resetSingle();
    },
    resetSingle() {
      this.single.mode = false;
      this.single.loading = false;
      this.single.match_id = null;
      this.single.sports_type = null;
      this.single.market_type = null;
    },
    run_invalidate() {
      if (!this.isLoggedIn) {
        return;
      }
      this.reset();
      this.$nextTick(() => {
        EventBus.$emit("GET_MARKET");
      });
    },
    run_market() {
      if (!this.isLoggedIn) {
        alert("Session expired!");
        return;
      }

      this.loading.logo = true;
      this.loading.market = false;
      this.loading.early = false;
      this.loading.today = false;
      this.loading.live = false;
      this.populateMarket(this.run_all);
    },
    run_live() {
      if (!this.isLoggedIn) {
        return;
      }
      this.loading.market = false;
      this.loading.live = false;
      this.populateMarketPartial(true, this.populateLive);
    },
    run_nonlive() {
      if (!this.isLoggedIn) {
        return;
      }
      this.loading.market = false;
      this.loading.early = false;
      this.loading.today = false;
      this.populateMarketPartial(false, this.populateNonLive);
    },
    populateNonLive() {
      this.populateToday(this.populateEarly);
    },
    run_all() {
      if (!this.isLoggedIn) {
        return;
      }

      this.populateLive();
      this.populateNonLive();
    },
    run_match2() {
      if (!this.isLoggedIn) {
        return;
      }

      this.populateMatch2();
    },
    getMatch(url, id, mid, mkt, bt, fn) {
      return new Promise((resolve, reject) => {
        if (mid != null && mid.length > 0) {
          var args = {
            account_id: this.$store.getters.accountId,
            session_token: this.$store.getters.sessionToken,
            arguments: [this.language, bt, mkt],
            ids: mid.join("|"),
            mmo: this.mmoMode ? "MMO" : "ODDS",
          };

          xhrMatch.get(url, args).then(
            (res) => {
              if (res.success) {
                if (res.data) {
                  var ot = this.oddsType;
                  var parlay = false;
                  if (this.betType == "parlay") {
                    ot = "DEC";
                    parlay = true;
                  }
                  var m = sync2.decode(res.data, this.menu3, this.commType, ot, parlay);
                  m.mkt = mkt;
                  m.id = id;
                  m.bt = bt;
                  this.$store.dispatch(fn, m).then(
                    (res) => {
                      if (fn == "cache/setOdds") {
                        this.processOdds("odds", m, id);
                        if (this.mmoMode) {
                          this.processMMO("mmo", m, id);
                        }
                      } else {
                        this.processOdds("more", m, id);
                        if (this.mmoMode) {
                          this.processMMO("moremmo", m, id);
                        }
                      }
                    },
                    (err) => {
                      // Error logging removed for production
                    }
                  );
                }
              } else {
                var test = this.$helpers.handleFeedback(res.status);
                if (test) {
                }
              }
              resolve();
            },
            (err) => {
              var test = this.$helpers.handleFeedback(err.status);
              if (test) {
              }
              resolve();
            }
          );
        } else {
          resolve();
        }
      });
    },
    populateLive(callback) {
      if (this.loading.live == true) {
        return;
      }

      if (this.isLoggedIn) {
        var marketIds = "3";
        switch (this.menu1) {
        case "early":
          if (this.menu0 == "all") {
            if (callback) {
              callback();
            }
            return;
          }
          break;
        case "today":
          if (this.menu0 == "all" && config.isTodayOnly == true) {
            if (callback) {
              callback();
            }
            return;
          }
          break;
        case "parlay":
          marketIds = "4";
          break;
        case "outright":
          marketIds = "5";
          break;
        }

        this.loading.live = true;
        var selectedMatch = this.$store.state.layout.selectedMatch;
        var live = Object.keys(this.$store.state.cache.live);
        var selectedDays = this.selectedDays;
        switch (this.menu3) {
        case "orz":
          selectedDays = "0";
          marketIds = "1|2|3";
          break;
        }
        var option3 = "";
        switch (this.menu3) {
        case "hdpou":
        case "live":
          option3 = "hdp|ou|oxt|oe|ml";
          if (config.racingList.includes(this.menu2)) {
            option3 = option3 + "|tw|cs";
          }

          break;
        case "parlay":
          if (this.menu0 == "event") {
            marketIds = "3";
          }
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        default:
          option3 = this.smenu3;
          break;
        }
        var option0 = "";
        switch (this.menu0) {
        case "all":
          option0 = marketIds;
          break;
        case "live":
          option0 = "3";
          break;
        case "upcoming":
          option0 = "2";
          break;
        case "favorite":
          option0 = "1|2|3|5";
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        case "parlay":
          option0 = "1|2|3";
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        case "outright":
          option0 = "5";
          option3 = "orz";
          break;
        case "search":
          option0 = "1|2|3|5";
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        case "event":
          option0 = marketIds;
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
          // case "category":
          //   option0 = marketIds;
          //   break;
        }

        var currTime = new Date();
        this.liveTime = new Date();

        if (live.length > 0) {
          new Promise((resolve, reject) => {
            this.getMatch(config.getMatchUrl() + "?match=live", "live", live, option0, option3, "cache/setOdds").then(() => {
              resolve();
            });
          }).then(() => {
            if (selectedMatch != null && live != null && live.includes(String(selectedMatch))) {
              this.getMatch(config.getMatch2Url() + "?match=live", "live2", [selectedMatch], option0, config.moreBetTypes, "cache/setMore").then(() => {
                this.loading.logo = false;
                this.loading.live = false;
                if (callback) {
                  callback();
                }
              });
            } else {
              this.loading.logo = false;
              this.loading.live = false;
              if (callback) {
                callback();
              }
            }
          });
        } else {
          this.loading.logo = false;
          this.loading.live = false;
          if (callback) {
            callback();
          }
        }
      }
    },
    populateToday(callback) {
      if (this.loading.today == true) {
        return;
      }
      if (this.isLoggedIn) {
        var marketIds = "2";
        switch (this.menu1) {
        case "today":
          if (this.menu0 == "all") {
            if (this.menu3 == "orz") {
              if (callback) {
                callback();
              }
              return;
            }
          }
          break;
        case "early":
          if (this.menu0 == "all") {
            if (callback) {
              callback();
            }
            return;
          }
          break;
        case "live":
          if (this.menu0 == "all") {
            if (callback) {
              callback();
            }
            return;
          }
          break;
        case "parlay":
          marketIds = "4";
          break;
        case "outright":
          marketIds = "5";
          break;
        }
        this.loading.today = true;
        var selectedMatch = this.sSelectedMatch;
        var today = Object.keys(this.$store.state.cache.today);
        var matches = today;
        var selectedDays = this.selectedDays;
        if (this.menu3 == "orz") {
          selectedDays = "0";
          marketIds = "1|2|3";
          var live = Object.keys(this.$store.state.cache.live);
          var early = Object.keys(this.$store.state.cache.early);
          matches = early.concat(today.concat(live));
        }
        var option3 = "";
        switch (this.menu3) {
        case "hdpou":
        case "live":
          option3 = "hdp|ou|oxt|oe|ml";
          if (config.racingList.includes(this.menu2)) {
            option3 = option3 + "|tw|cs";
          }
          break;
        case "parlay":
          if (this.menu0 == "event") {
            marketIds = "2";
          }
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        default:
          option3 = this.smenu3;
          break;
        }
        var option0 = "";
        switch (this.menu0) {
        case "all":
          option0 = marketIds;
          break;
        case "live":
          option0 = "3";
          break;
        case "upcoming":
          option0 = "2";
          break;
        case "favorite":
          option0 = "1|2|3|5";
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        case "parlay":
          option0 = "1|2|3";
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        case "outright":
          option0 = "5";
          option3 = "orz";
          break;
        case "search":
          option0 = "1|2|3|5";
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        case "event":
          option0 = marketIds;
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
          // case "category":
          //   option0 = marketIds;
          //   break;
        }

        var currTime = new Date();
        this.todayTime = new Date();

        if (matches.length > 0) {
          new Promise((resolve, reject) => {
            this.getMatch(config.getMatchUrl() + "?match=today", "today", matches, option0, option3, "cache/setOdds").then(() => {
              resolve();
            });
          }).then(() => {
            if (selectedMatch != null && today != null && today.includes(String(selectedMatch))) {
              this.getMatch(config.getMatch2Url() + "?match=today", "today2", [selectedMatch], option0, config.moreBetTypes, "cache/setMore").then(() => {
                this.loading.logo = false;
                this.loading.today = false;
                if (callback) {
                  callback();
                }
              });
            } else {
              this.loading.logo = false;
              this.loading.today = false;
              if (callback) {
                callback();
              }
            }
          });
        } else {
          this.loading.logo = false;
          this.loading.today = false;
          if (callback) {
            callback();
          }
        }
      }
    },
    populateEarly(callback) {
      if (this.loading.early == true) {
        return;
      }
      if (this.isLoggedIn) {
        var marketIds = "1";
        switch (this.menu1) {
        case "today":
          if (this.menu0 == "all") {
            if (this.menu3 != "orz") {
              if (callback) {
                callback();
              }
              return;
            }
          }
          break;
        case "live":
          if (this.menu0 == "all") {
            if (callback) {
              callback();
            }
            return;
          }
          break;
        case "parlay":
          marketIds = "4";
          break;
        case "outright":
          marketIds = "5";
          break;
        }

        this.loading.early = true;
        var selectedMatch = this.sSelectedMatch;
        var early = Object.keys(this.$store.state.cache.early);
        var matches = early;
        var selectedDays = this.selectedDays;
        if (this.menu3 == "orz") {
          selectedDays = "0";
          marketIds = "1|2|3";
          var live = Object.keys(this.$store.state.cache.live);
          var today = Object.keys(this.$store.state.cache.today);
          matches = early.concat(today.concat(live));
        }
        var option3 = "";
        switch (this.menu3) {
        case "hdpou":
        case "live":
          option3 = "hdp|ou|oxt|oe|ml";
          if (config.racingList.includes(this.menu2)) {
            option3 = option3 + "|tw|cs";
          }
          break;
        case "parlay":
          if (this.menu0 == "event") {
            marketIds = "1";
          }
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        default:
          option3 = this.smenu3;
          break;
        }
        var option0 = "";
        switch (this.menu0) {
        case "all":
          option0 = marketIds;
          break;
        case "live":
          option0 = "3";
          break;
        case "upcoming":
          option0 = "2";
          break;
        case "favorite":
          option0 = "1|2|3|5";
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        case "parlay":
          option0 = "1|2|3";
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        case "outright":
          option0 = "5";
          option3 = "orz";
          break;
        case "search":
          option0 = "1|2|3|5";
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        case "event":
          option0 = marketIds;
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
          // case "category":
          //   option0 = marketIds;
          //   break;
        }

        var currTime = new Date();
        this.earlyTime = new Date();
        if (matches.length > 0) {
          new Promise((resolve, reject) => {
            this.getMatch(config.getMatchUrl() + "?match=early", "early", matches, option0, option3, "cache/setOdds").then(() => {
              resolve();
            });
          }).then(() => {
            if (selectedMatch != null && early != null && early.includes(String(selectedMatch))) {
              this.getMatch(config.getMatch2Url() + "?match=early", "early2", [selectedMatch], option0, config.moreBetTypes, "cache/setMore").then(() => {
                this.loading.logo = false;
                this.loading.early = false;
                if (callback) {
                  callback();
                }
              });
            } else {
              this.loading.logo = false;
              this.loading.early = false;
              if (callback) {
                callback();
              }
            }
          });
        } else {
          this.loading.logo = false;
          this.loading.early = false;
          if (callback) {
            callback();
          }
        }
      }
    },
    populateMatch2(callback) {
      if (this.loading.match2 == true) {
        return;
      }
      this.loading.match2 = true;
      if (this.isLoggedIn) {
        var marketIds = "1|2|3";
        switch (this.menu1) {
        case "parlay":
          marketIds = "4";
          break;
        case "outright":
          marketIds = "5";
          break;
        }
        var selectedMatch = this.sSelectedMatch;
        var selectedDays = this.selectedDays;
        switch (this.menu3) {
        case "orz":
          selectedDays = "0";
          marketIds = "1|2|3";
          break;
        }
        var option3 = "";
        switch (this.menu3) {
        case "hdpou":
        case "live":
          option3 = "hdp|ou|oxt|oe|ml";
          if (config.racingList.includes(this.menu2)) {
            option3 = option3 + "|tw|cs";
          }
          break;
        case "parlay":
          option3 = "hdp|ou|oxt|oe|ml";
          break;
        default:
          option3 = this.smenu3;
          break;
        }
        var option0 = "";
        switch (this.menu0) {
        case "all":
          option0 = marketIds;
          break;
        case "live":
          option0 = "3";
          break;
        case "upcoming":
          option0 = "2";
          break;
        case "favorite":
          option0 = "1|2|3|5";
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        case "parlay":
          option0 = "1|2|3";
          option3 = "hdp|ou|oxt|oe|ml";
          break;
        case "outright":
          option0 = "5";
          option3 = "orz";
          break;
        case "search":
          option0 = "1|2|3|5";
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        case "event":
          option0 = marketIds;
          option3 = "hdp|ou|oxt|oe|ml|orz";
          break;
        }

        var currTime = new Date();
        this.match2Time = new Date();

        if (selectedMatch != null) {
          this.getMatch(config.getMatch2Url() + "?market=match2", "match2", [selectedMatch], option0, config.moreBetTypes, "cache/setMore").then(() => {
            this.loading.match2 = false;
            if (callback) {
              callback();
            }
          });
        } else {
          this.loading.match2 = false;
          if (callback) {
            callback();
          }
        }
      }
    },
    populateMarket(callback) {
      if (this.loading.market == true) {
        return;
      }

      if (this.isLoggedIn) {
        var menu = {
          menu0: this.menu0,
          menu1: this.menu1,
          menu2: this.menu2,
          menu3: this.menu3,
        };
        var option1 = [];
        var option2 = Object.keys(this.options.live)
          .map((key) => {
            return [key, this.options.live[key]];
          })
          .filter((item) => {
            return item[1] == true && item[0] != 0;
          })
          .map((item) => {
            return item[0];
          });
        var option3 = Object.keys(this.options.parlay)
          .map((key) => {
            return [key, this.options.parlay[key]];
          })
          .filter((item) => {
            return item[1] == true && item[0] != 0;
          })
          .map((item) => {
            return item[0];
          });

        //- if MMO PARLAY then force to sports = soccer
        if (this.menuX && this.menu0 == "parlay") {
          option3 = ["1"];
        }

        var marketIds = "2|3";
        if (config.isTodayOnly == true) {
          marketIds = "2";
        }
        switch (menu.menu1) {
        case "early":
          marketIds = "1";
          break;
        case "live":
          marketIds = "3";
          break;
        case "parlay":
          marketIds = "4";
          break;
        case "outright":
          marketIds = "5";
          break;
        }
        var selectedDays = this.selectedDays;
        switch (menu.menu3) {
        case "orz":
          selectedDays = "0";
          marketIds = "5";
          break;
        }

        option1.push(this.language);
        switch (menu.menu0) {
        case "all":
          option1.push("1");
          option1.push(marketIds);
          option1.push(this.smenu2);
          option1.push(this.smenu3);
          var days = selectedDays;
          switch (menu.menu1) {
          case "early":
            break;
          case "today":
            days = "0";
            this.$store.dispatch("layout/setSelectedDays", "0");
            break;
          case "live":
            days = "0";
            break;
          case "parlay":
            days = "0";
            break;
          case "outright":
            days = "0";
            break;
          }
          option1.push(days);
          option1.push("");
          break;
        case "live":
          option1.push("2");
          option1.push("3");
          option1.push(option2.join("|"));
          option1.push(menu.menu3);
          option1.push("0");
          option1.push("");
          break;
        case "upcoming":
          option1.push("2");
          option1.push("2");
          option1.push(option2.join("|"));
          option1.push(menu.menu3);
          option1.push("3");
          option1.push("");
          break;
        case "favorite":
          option1.push("3");
          option1.push("1|2|3|5");
          option1.push(Object.keys(this.sports).join("|"));
          option1.push(menu.menu3);
          option1.push("0");
          option1.push(this.favorite.join("|"));
          break;
        case "parlay":
          option1.push("4");
          option1.push("4");
          option1.push(option3.join("|"));
          option1.push(menu.menu3);
          option1.push("0");
          option1.push("");
          break;
        case "outright":
          option1.push("5");
          option1.push("1|2|3");
          option1.push(this.smenu2);
          option1.push(this.smenu3);
          option1.push("0");
          option1.push("");
          break;
        case "search":
          option1.push("6");
          option1.push("1|2|3");
          option1.push(Object.keys(this.sports).join("|"));
          option1.push(menu.menu3);
          option1.push("0");
          option1.push(this.search);
          break;
        case "event":
          option1.push("7");
          option1.push(marketIds);
          option1.push(Object.keys(this.sports).join("|"));
          option1.push(menu.menu3);
          option1.push("0");
          option1.push(this.menuY.toString());
          break;
        }

        var currTime = new Date();
        this.marketTime = new Date();

        var args = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          arguments: option1,
        };

        var url = config.getMarketUrl();

        this.loading.market = true;
        xhrMarket.get(url, args).then(
          (res) => {
            this.loading.market = false;
            if (res.success) {
              if (res.data && res.data.length > 0 && res.data[0].length > 0) {
                var ot = this.oddsType;
                var parlay = false;
                if (this.betType == "parlay") {
                  ot = "DEC";
                  parlay = true;
                }
                var bypass = menu.menu0 == "favorite" || menu.menu0 == "search" || menu.menu0 == "event";
                var bytime = this.eventSorting == "2";
                var bymarket = "2";
                if ([1].includes(menu.menu2)) {
                  // only soccer allow to change market type
                  //
                  bymarket = this.displayMarketType;
                }
                var m = sync1.decode(res.data, menu.menu3, this.commType, ot, parlay, bypass, bytime, bymarket);
                m.options.menu = menu;
                this.$store.dispatch("cache/setData", m).then(
                  (res) => {
                    if (menu.menu0 == "favorite") {
                      var mm = Object.keys(m["head"]).map((x) => {
                        return parseInt(x);
                      });
                      this.$store.dispatch("layout/copyFavorite", mm);
                      if (this.favorite.length <= 0) {
                        EventBus.$emit("INVALIDATE");
                      }
                    }
                    this.$store.dispatch("layout/setLeague", m["ln"]);

                    this.processList();
                  },
                  (err) => { }
                );

                if (callback) {
                  callback();
                }
              } else {
                this.loading.logo = false;
                if (menu.menu1 == "early") {
                  var mday = parseInt(this.selectedDays);
                  if (mday != 0) {
                    var n = mday + 1;
                    if (n <= 8) {
                      this.$store.dispatch("layout/setSelectedDays", n.toString());
                      EventBus.$emit("INVALIDATE");
                    } else {
                      // if (n > 8) {
                      //   this.$store.dispatch("layout/setSelectedDays", "0");
                      //   EventBus.$emit("INVALIDATE");
                      // }
                    }
                  }
                } else {
                  this.reset();
                }
              }
            } else {
              var test = this.$helpers.handleFeedback(res.status);
              if (test) {
                if (callback) {
                  callback();
                }
              }
            }
          },
          (err) => {
            this.loading.market = false;
            var test = this.$helpers.handleFeedback(err.status);
            if (test) {
              if (callback) {
                callback();
              }
            }
          }
        );
      }
      args = null;
    },
    populateMarketPartial(forceToLive, callback) {
      if (this.loading.market == true) {
        return;
      }
      var menu = {
        menu0: this.menu0,
        menu1: this.menu1,
        menu2: this.menu2,
        menu3: this.menu3,
      };
      if (this.isLoggedIn) {
        var option1 = [];
        var option2 = Object.keys(this.options.live)
          .map((key) => {
            return [key, this.options.live[key]];
          })
          .filter((item) => {
            return item[1] == true && item[0] != 0;
          })
          .map((item) => {
            return item[0];
          });
        var option3 = Object.keys(this.options.parlay)
          .map((key) => {
            return [key, this.options.parlay[key]];
          })
          .filter((item) => {
            return item[1] == true && item[0] != 0;
          })
          .map((item) => {
            return item[0];
          });

        var marketIds = "1|2|3";
        if (forceToLive == true) {
          // force to live
          marketIds = "3";
          switch (this.menu1) {
          case "early":
            return;
            break;
          case "today":
            if (config.isTodayOnly == true) {
              return;
            }
            break;
          case "live":
            if (this.menu3 == "orz") {
              if (callback) {
                callback();
              }
              return;
            }
            break;
          case "parlay":
            marketIds = "4";
            break;
          case "outright":
            marketIds = "5";
            break;
          }
        } else {
          // force to non-live
          switch (this.menu1) {
          case "early":
            marketIds = "1";
            break;
          case "today":
            marketIds = "2";
            break;
          case "live":
            return;
            break;
          case "parlay":
            marketIds = "4";
            break;
          case "outright":
            marketIds = "5";
            break;
          }
        }

        var selectedDays = this.selectedDays;
        switch (this.menu3) {
        case "orz":
          selectedDays = "0";
          marketIds = "5";
          break;
        }

        option1.push(this.language);
        switch (this.menu0) {
        case "all":
          option1.push("1");
          option1.push(marketIds);
          option1.push(this.smenu2);
          option1.push(this.smenu3);
          var days = selectedDays;
          switch (this.menu1) {
          case "early":
            break;
          case "today":
            days = "0";
            this.$store.dispatch("layout/setSelectedDays", "1");
            break;
          case "live":
            days = "0";
            break;
          case "parlay":
            days = "0";
            break;
          case "outright":
            days = "0";
            break;
          }
          option1.push(days);
          option1.push("");
          break;
        case "live":
          option1.push("2");
          option1.push("3");
          option1.push(option2.join("|"));
          option1.push(this.menu3);
          option1.push("0");
          option1.push("");
          break;
        case "upcoming":
          option1.push("2");
          option1.push("2");
          option1.push(option2.join("|"));
          option1.push(this.menu3);
          option1.push("3");
          option1.push("");
          break;
        case "favorite":
          option1.push("3");
          option1.push("1|2|3|5");
          option1.push(Object.keys(this.sports).join("|"));
          option1.push(this.menu3);
          option1.push("0");
          option1.push(this.favorite.join("|"));
          break;
        case "parlay":
          option1.push("4");
          option1.push("4");
          option1.push(option3.join("|"));
          option1.push(this.menu3);
          option1.push("0");
          option1.push("");
          break;
        case "outright":
          option1.push("5");
          option1.push("1|2|3");
          option1.push(this.smenu2);
          option1.push(this.smenu3);
          option1.push("0");
          option1.push("");
          break;
        case "search":
          option1.push("6");
          option1.push("1|2|3");
          option1.push(Object.keys(this.sports).join("|"));
          option1.push(this.menu3);
          option1.push("0");
          option1.push(this.search);
          break;
        case "event":
          option1.push("7");
          option1.push(marketIds);
          option1.push(Object.keys(this.sports).join("|"));
          option1.push(this.menu3);
          option1.push("0");
          option1.push(this.menuY.toString());
          break;
        }

        var currTime = new Date();
        this.marketTime = new Date();

        var args = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          arguments: option1,
        };

        var url = config.getMarketUrl();

        if (forceToLive == true) {
          url += "?market=inplay";
        } else {
          url += "?market=prematch";
        }

        this.loading.market = true;
        xhrMarket.get(url, args).then(
          (res) => {
            this.loading.market = false;
            if (res.success) {
              if (res.data && res.data.length > 0 && res.data[0].length > 0) {
                var ot = this.oddsType;
                var parlay = false;
                if (this.betType == "parlay") {
                  ot = "DEC";
                  parlay = true;
                }
                var bypass = this.menu0 == "favorite" || this.menu0 == "search" || menu.menu0 == "event";
                var bytime = this.eventSorting == "2";
                var bymarket = "2";
                if ([1].includes(this.menu2)) {
                  // only soccer allow to change market type
                  //
                  bymarket = this.displayMarketType;
                }
                var m = sync1.decode(res.data, this.menu3, this.commType, ot, parlay, bypass, bytime, bymarket);
                m.options.menu = menu;
                this.$store.dispatch("cache/setPartialData", m).then(
                  (res) => {
                    this.processList();
                  },
                  (err) => { }
                );

                if (callback) {
                  callback();
                }
              } else {
                this.loading.logo = false;
                this.$store.dispatch("cache/resetPartialData", option1).then(
                  (res) => {
                    this.processList();
                  },
                  (err) => { }
                );
              }
            } else {
              var test = this.$helpers.handleFeedback(res.status);
              if (test) {
                if (callback) {
                  callback();
                }
              }
            }
          },
          (err) => {
            this.loading.market = false;
            var test = this.$helpers.handleFeedback(err.status);
            if (test) {
              if (callback) {
                callback();
              }
            }
          }
        );
      }
      args = null;
    },
    details(sportsId, head, odds, cm, m) {
      // console.log(sportsId, head, odds, cm);
      var r = {};
      r["tn"] = 0;
      r["kns"] = 0;
      r["team"] = null;
      r["oxtn"] = 0;
      r["more"] = 0;
      r["child1"] = [];
      r["child2"] = [];
      r["team"] = 2;
      if (odds != null) {
        if (config.racingList.includes(sportsId)) {
          if (odds.hasOwnProperty("tw")) {
            if (odds["tw"]) {
              r["tw"] = odds["tw"].filter((v, i, r) => {
                return v[4] == "1X2HDP";
              });
              if (r["tw"] != null && r["tw"].length > 0) {
                if (r["tn"] < r["tw"].length) {
                  r["tn"] = r["tw"].length;
                }
              } else {
                r["tw"] = null;
              }
            }
          }
        }

        if (odds.hasOwnProperty("hdp")) {
          if (odds["hdp"] != null) {
            r["hdp"] = odds["hdp"].filter((v, i, r) => {
              return v[4] == "HDP";
            });
            r["hdph"] = odds["hdp"].filter((v, i, r) => {
              return v[4] == "HDPH";
            });
            if (r["hdp"] ? r["hdp"].length : 0 > r["tn"]) {
              r["tn"] = r["hdp"].length;
              if (r["hdp"][0][8] != null && r["hdp"][0][8] != "0") {
                r["team"] = r["hdp"][0][7];
              } else {
                r["team"] = 2;
              }
            } else {
              r["hdp"] = null;
            }
            if (r["hdph"] ? r["hdph"].length : 0 > r["tn"]) {
              if (r["tn"] < r["hdph"].length) {
                r["tn"] = r["hdph"].length;
              }
              if (r["team"] == null) {
                if (r["hdph"][0][8] != null && r["hdph"][0][8] != "0") {
                  r["team"] = r["hdph"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["hdph"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("ou")) {
          if (odds["ou"]) {
            r["ou"] = odds["ou"].filter((v, i, r) => {
              return v[4] == "OU";
            });
            r["ouh"] = odds["ou"].filter((v, i, r) => {
              return v[4] == "OUH";
            });
            if (r["ou"] ? r["ou"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ou"].length) {
                r["tn"] = r["ou"].length;
              }
            } else {
              r["ou"] = null;
            }
            if (r["ouh"] ? r["ouh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ouh"].length) {
                r["tn"] = r["ouh"].length;
              }
            } else {
              r["ouh"] = null;
            }
          }
        }

        r["oxtn"] = 0;
        if (odds.hasOwnProperty("oxt")) {
          if (odds["oxt"]) {
            r["oxt"] = odds["oxt"].filter((v, i, r) => {
              return v[4] == "1X2";
            });
            r["oxth"] = odds["oxt"].filter((v, i, r) => {
              return v[4] == "1X2H";
            });
            if (r["oxt"] ? r["oxt"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oxt"].length) {
                r["tn"] = r["oxt"].length;
              }
              r["oxtn"] = 1;
            } else {
              r["oxt"] = null;
            }
            if (r["oxth"] ? r["oxth"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oxth"].length) {
                r["tn"] = r["oxth"].length;
              }
              r["oxtn"] += 1;
            } else {
              r["oxth"] = null;
            }
          }
        }

        r["oetn"] = 0;
        r["oen"] = 0;
        r["oehn"] = 0;
        if (odds.hasOwnProperty("oe")) {
          if (odds["oe"]) {
            r["oe"] = odds["oe"].filter((v, i, r) => {
              return v[4] == "OE";
            });
            r["oeh"] = odds["oe"].filter((v, i, r) => {
              return v[4] == "OEH";
            });
            if (r["oe"] ? r["oe"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oe"].length) {
                r["tn"] = r["oe"].length;
              }
              r["oen"] = 1;
            } else {
              r["oe"] = null;
            }
            if (r["oeh"] ? r["oeh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oeh"].length) {
                r["tn"] = r["oeh"].length;
              }
              r["oehn"] = 1;
            } else {
              r["oeh"] = null;
            }
            if (r["oe"] ? r["oe"].length : 0 > 0) {
              r["oetn"] += 1;
            }
            if (r["oeh"] ? r["oeh"].length : 0 > 0) {
              r["oetn"] += 1;
            }
          }
        }

        r["mltn"] = 0;
        r["mln"] = 0;
        r["mlhn"] = 0;
        if (odds.hasOwnProperty("ml")) {
          if (odds["ml"]) {
            r["ml"] = odds["ml"].filter((v, i, r) => {
              return v[4] == "ML";
            });
            r["mlh"] = odds["ml"].filter((v, i, r) => {
              return v[4] == "MLH";
            });
            if (r["ml"] ? r["ml"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ml"].length) {
                r["tn"] = r["ml"].length;
              }
              r["mln"] = 1;
            } else {
              r["ml"] = null;
            }
            if (r["mlh"] ? r["mlh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["mlh"].length) {
                r["tn"] = r["mlh"].length;
              }
              r["mlhn"] = 1;
            } else {
              r["mln"] = null;
            }
            if (r["ml"] ? r["ml"].length : 0 > 0) {
              r["mltn"] += 1;
            }
            if (r["mlh"] ? r["mlh"].length : 0 > 0) {
              r["mltn"] += 1;
            }
          }
        }

        r["kns"] = r["tn"];

        r["csn"] = 0;
        if (odds.hasOwnProperty("cs")) {
          if (odds["cs"]) {
            r["cs"] = odds["cs"].filter((v, i, r) => {
              return v[4] == "CS";
            });
            r["csh"] = odds["cs"].filter((v, i, r) => {
              return v[4] == "CSH";
            });
            if (r["cs"] ? r["cs"].length : 0 > r["tn"]) {
              if (r["tn"] < r["cs"].length) {
                r["tn"] = r["cs"].length;
              }
              r["cso"] = {};
              for (var i in r["cs"]) {
                r["cso"][r["cs"][i][6]] = r["cs"][i];
              }
              r["csn"] = 1;
            } else {
              r["cs"] = null;
            }
            if (r["csh"] ? r["csh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["csh"].length) {
                r["tn"] = r["csh"].length;
              }
              r["csho"] = {};
              for (var i in r["csh"]) {
                r["csho"][r["csh"][i][6]] = r["csh"][i];
              }
              r["cshn"] = 1;
            } else {
              r["csh"] = null;
            }
            if (r["cs"] ? r["cs"].length : 0 > 0) {
              r["cstn"] += 1;
            }
            if (r["csh"] ? r["csh"].length : 0 > 0) {
              r["cstn"] += 1;
            }
          }
        }

        r["dcn"] = 0;
        if (odds.hasOwnProperty("dc")) {
          if (odds["dc"]) {
            r["dc"] = odds["dc"].filter((v, i, r) => {
              return v[4] == "DC";
            });
            r["dch"] = odds["dc"].filter((v, i, r) => {
              return v[4] == "DCH";
            });
            if (r["dc"] ? r["dc"].length : 0 > r["tn"]) {
              if (r["tn"] < r["dc"].length) {
                r["tn"] = r["dc"].length;
              }
              r["dco"] = {};
              for (var i in r["dc"]) {
                r["dco"][r["dc"][i][6]] = r["dc"][i];
              }
              r["dcn"] = 1;
            } else {
              r["dc"] = null;
            }
            if (r["dch"] ? r["dch"].length : 0 > r["tn"]) {
              if (r["tn"] < r["dch"].length) {
                r["tn"] = r["dch"].length;
              }
              r["dcho"] = {};
              for (var i in r["dch"]) {
                r["dcho"][r["dch"][i][6]] = r["dch"][i];
              }
              r["dchn"] = 1;
            } else {
              r["dch"] = null;
            }
            if (r["dc"] ? r["dc"].length : 0 > 0) {
              r["dctn"] += 1;
            }
            if (r["dch"] ? r["dch"].length : 0 > 0) {
              r["dctn"] += 1;
            }
          }
        }

        r["tgn"] = 0;
        if (odds.hasOwnProperty("tg")) {
          if (odds["tg"]) {
            r["tg"] = odds["tg"].filter((v, i, r) => {
              return v[4] == "TG";
            });
            r["tgh"] = odds["tg"].filter((v, i, r) => {
              return v[4] == "TGH";
            });
            if (r["tg"] ? r["tg"].length : 0 > r["tn"]) {
              if (r["tn"] < r["tg"].length) {
                r["tn"] = r["tg"].length;
              }
              r["tgo"] = {};
              for (var i in r["tg"]) {
                r["tgo"][r["tg"][i][6]] = r["tg"][i];
              }
              r["tgn"] = 1;
            } else {
              r["tg"] = null;
            }
            if (r["tgh"] ? r["tgh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["tgh"].length) {
                r["tn"] = r["tgh"].length;
              }
              r["tgho"] = {};
              for (var i in r["tgh"]) {
                r["tgho"][r["tgh"][i][6]] = r["tgh"][i];
              }
              r["tghn"] = 1;
            } else {
              r["tgh"] = null;
            }
            if (r["tg"] ? r["tg"].length : 0 > 0) {
              r["tgtn"] += 1;
            }
            if (r["tgh"] ? r["tgh"].length : 0 > 0) {
              r["tgtn"] += 1;
            }
          }
        }

        r["fglgn"] = 0;
        if (odds.hasOwnProperty("fglg")) {
          if (odds["fglg"]) {
            r["fglg"] = odds["fglg"].filter((v, i, r) => {
              return v[4] == "FGLG";
            });
            r["fglgh"] = odds["fglg"].filter((v, i, r) => {
              return v[4] == "FGLGH";
            });
            if (r["fglg"] ? r["fglg"].length : 0 > r["tn"]) {
              if (r["tn"] < r["fglg"].length) {
                r["tn"] = r["fglg"].length;
              }
              r["fglgo"] = {};
              for (var i in r["fglg"]) {
                r["fglgo"][r["fglg"][i][6]] = r["fglg"][i];
              }
              r["fglgn"] = 1;
            } else {
              r["fglg"] = null;
            }
            if (r["fglgh"] ? r["fglgh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["fglgh"].length) {
                r["tn"] = r["fglgh"].length;
              }
              r["fglgho"] = {};
              for (var i in r["fglgh"]) {
                r["fglgho"][r["fglgh"][i][6]] = r["fglgh"][i];
              }
              r["fglghn"] = 1;
            } else {
              r["fglgh"] = null;
            }
            if (r["fglg"] ? r["fglg"].length : 0 > 0) {
              r["fglgtn"] += 1;
            }
            if (r["fglgh"] ? r["fglgh"].length : 0 > 0) {
              r["fglgtn"] += 1;
            }
          }
        }

        r["htftn"] = 0;
        if (odds.hasOwnProperty("htft")) {
          if (odds["htft"]) {
            r["htft"] = odds["htft"].filter((v, i, r) => {
              return v[4] == "HTFT";
            });
            if (r["htft"] ? r["htft"].length : 0 > r["tn"]) {
              if (r["tn"] < r["htft"].length) {
                r["tn"] = r["htft"].length;
              }
              r["htfto"] = {};
              for (var i in r["htft"]) {
                r["htfto"][r["htft"][i][6]] = r["htft"][i];
              }
              r["htftn"] = 1;
            } else {
              r["htft"] = null;
            }
            if (r["htft"] ? r["htft"].length : 0 > 0) {
              r["htfttn"] += 1;
            }
          }
        }

        if (odds.hasOwnProperty("orz")) {
          if (odds["orz"]) {
            r["orz"] = odds["orz"].filter((v, i, r) => {
              return v[4] == "OR";
            });
          }
        }

        if (head == undefined) {
          r["more"] = 0;
        } else {
          r["more"] = head[0][7];
        }
        if ([2].includes(sportsId)) {
          r["more"] = r["mltn"];
        }

        if ([6, 8].includes(sportsId)) {
          r["more"] = 0;
        }

        if ([6, 8, 13].includes(sportsId)) {
          r["more"] = 0;
        }

        r["momo"] = 0;
        var match = this.cacheData.match;
        if (cm != null) {
          for (var n in cm) {
            var condition = true;
            var mh = this.cacheData.head;
            var cmid = cm[n][0];
            if (mh[cmid] != null && match[cmid] != null && match[cmid][26] != null) {
              if (cm[n][1] == 1 || cm[n][1] == 10) {
                var aa = [];
                aa.push(cmid);
                aa.push(match[cmid][26]);
                r["child1"].push(aa);
              } else {
                var aa = [];
                aa.push(cmid);
                if (match[cmid] != undefined) {
                  aa.push(match[cmid][26]);
                }
                aa.push(cm[n][1]);
                r["child2"].push(aa);
              }

              var mo = this.cacheData.odds[head[0][2]];
              if (mo) {
                if (mo.hasOwnProperty("hdp")) {
                  if (mo["hdp"][cmid] != null) {
                    r["child"] = mo["hdp"][cmid].filter((v, i, r) => {
                      return v[4] == "HDP";
                    });
                    if (r["child"] ? r["child"].length : 0 > r["more"]) {
                      if (r["child"][0][9] != "" && r["child"][0][10] != "" && condition) {
                        r["momo"] += r["child"].length;
                      }
                    } else {
                      r["child"] = null;
                    }
                  }
                }

                if (mo.hasOwnProperty("ou")) {
                  if (mo["ou"][cmid] != null) {
                    r["child"] = mo["ou"][cmid].filter((v, i, r) => {
                      return v[4] == "OU";
                    });
                    if (r["child"] ? r["child"].length : 0 > r["more"]) {
                      if (r["child"][0][11] != "" && r["child"][0][12] != "" && condition) {
                        r["momo"] += r["child"].length;
                      }
                    } else {
                      r["child"] = null;
                    }
                  }
                }

                if (mo.hasOwnProperty("oe")) {
                  if (mo["oe"][cmid] != null) {
                    r["child"] = mo["oe"][cmid].filter((v, i, r) => {
                      return v[4] == "OE";
                    });
                    if (r["child"] ? r["child"].length : 0 > r["more"]) {
                      if (r["child"][0][5] != "" && r["child"][0][7] != "" && condition) {
                        r["momo"] += r["child"].length;
                      }
                    } else {
                      r["child"] = null;
                    }
                  }
                }

                if (mo.hasOwnProperty("ml")) {
                  if (mo["ml"][cmid] != null) {
                    r["child"] = mo["ml"][cmid].filter((v, i, r) => {
                      return v[4] == "ML";
                    });
                    if (r["child"] ? r["child"].length : 0 > r["more"]) {
                      if (r["child"][0][5] != "" && r["child"][0][7] != "" && condition) {
                        r["momo"] += r["child"].length;
                      }
                    } else {
                      r["child"] = null;
                    }
                  }
                }

                if (mo.hasOwnProperty("oxt")) {
                  if (mo["oxt"][cmid] != null) {
                    r["child"] = mo["oxt"][cmid].filter((v, i, r) => {
                      return v[4] == "1X2";
                    });
                    if (r["child"] ? r["child"].length : 0 > r["more"]) {
                      if (r["child"][0][5] != "" && r["child"][0][6] != "" && r["child"][0][7] != "" && condition) {
                        r["momo"] += r["child"].length;
                      }
                    } else {
                      r["child"] = null;
                    }
                  }
                }
              }
            }
          }
        }
      }

      r["child2"].sort((a, b) => {
        if (a[1] < b[1]) return -1;
        if (a[1] > b[1]) return 1;
        return 0;
      });

      //- based on sports id will include which type of odds to be displayed in more panel
      //

      if ([1].includes(sportsId)) {
        if (this.pageType != 2) {
          r["more"] = r["more"] + r["oxtn"];
        }
      }

      if (![1, 2, 6, 8, 13].includes(sportsId)) {
        r["more"] = r["more"] - r["oetn"];
      }

      if ([6, 8, 13, 16].includes(sportsId)) {
        r["more"] = r["more"] - r["oen"];
      }

      if (![1, 2, 6, 8, 13].includes(sportsId)) {
        r["more"] = r["more"] - r["mltn"];
      }

      if ([6, 8, 13].includes(sportsId)) {
        if (r["more"] < 0) r["more"] = 0;
        if (r["mlhn"]) {
          r["more"] = r["more"] + r["mlhn"];
        }
      }

      if ([12].includes(sportsId)) {
        r["more"] = r["more"] - r["oxtn"];
      }

      if ([13].includes(sportsId)) {
        r["more"] = r["more"] + r["oehn"];
      }

      if (r["more"] < 0) r["more"] = 0;

      r["more"] += r["momo"];

      return r;
    },
    mmoDetails(sportsId, head, mmo, cm) {
      var r = {};
      r["tn"] = 0;
      r["team"] = null;
      r["oxtn"] = 0;
      r["more"] = 0;
      r["child1"] = [];
      r["child2"] = [];
      r["team"] = 2;
      if (mmo != null) {
        if (mmo.hasOwnProperty("hdp")) {
          if (mmo["hdp"] != null) {
            r["hdp"] = mmo["hdp"].filter((v, i, r) => {
              return v[4] == "HDP";
            });
            r["hdph"] = mmo["hdp"].filter((v, i, r) => {
              return v[4] == "HDPH";
            });
            if (r["hdp"] ? r["hdp"].length : 0 > r["tn"]) {
              r["tn"] = r["hdp"].length;
              if (r["hdp"][0][8] != null && r["hdp"][0][8] != "0") {
                r["team"] = r["hdp"][0][7];
              } else {
                r["team"] = 2;
              }
            } else {
              r["hdp"] = null;
            }
            if (r["hdph"] ? r["hdph"].length : 0 > r["tn"]) {
              if (r["tn"] < r["hdph"].length) {
                r["tn"] = r["hdph"].length;
              }
              if (r["team"] == null) {
                if (r["hdph"][0][8] != null && r["hdph"][0][8] != "0") {
                  r["team"] = r["hdph"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["hdph"] = null;
            }
          }
        }
        if (mmo.hasOwnProperty("ou")) {
          if (mmo["ou"]) {
            r["ou"] = mmo["ou"].filter((v, i, r) => {
              return v[4] == "OU";
            });
            r["ouh"] = mmo["ou"].filter((v, i, r) => {
              return v[4] == "OUH";
            });
            if (r["ou"] ? r["ou"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ou"].length) {
                r["tn"] = r["ou"].length;
              }
            } else {
              r["ou"] = null;
            }
            if (r["ouh"] ? r["ouh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ouh"].length) {
                r["tn"] = r["ouh"].length;
              }
            } else {
              r["ouh"] = null;
            }
          }
        }
      }

      r["kns"] = r["tn"];
      return r;
    },
    moreItems(sportsId, main, odds, match) {
      var r = {};
      r["total"] = 0;
      r["full"] = 0;
      r["half"] = 0;
      if (main != null) {
        if ([1].includes(sportsId)) {
          if (main.hasOwnProperty("oe")) {
            if (main["oe"]) {
              r["oe"] = main["oe"].filter((v, i, r) => {
                return v[4] == "OE";
              });
              r["oeh"] = main["oe"].filter((v, i, r) => {
                return v[4] == "OEH";
              });
              if (r["oe"] != null && r["oe"].length > 0) {
                r["full"] += 1;
              } else {
                r["oe"] = null;
              }
              if (r["oeh"] != null && r["oeh"].length > 0) {
                r["half"] += 1;
              } else {
                r["oeh"] = null;
              }
            }
          }

          if (main.hasOwnProperty("oxt")) {
            if (this.pageType != 2) {
              if (main["oxt"]) {
                r["oxt"] = main["oxt"].filter((v, i, r) => {
                  return v[4] == "1X2";
                });
                r["oxth"] = main["oxt"].filter((v, i, r) => {
                  return v[4] == "1X2H";
                });
                if (r["oxt"] != null && r["oxt"].length > 0) {
                  r["full"] += 1;
                } else {
                  r["oxt"] = null;
                }
                if (r["oxth"] != null && r["oxth"].length > 0) {
                  r["half"] += 1;
                } else {
                  r["oxth"] = null;
                }
              }
            }
          }
        }

        if ([6, 8, 13].includes(sportsId)) {
          if (main.hasOwnProperty("oe")) {
            if (main["oe"]) {
              r["oeh"] = main["oe"].filter((v, i, r) => {
                return v[4] == "OEH";
              });
              if (r["oeh"] != null && r["oeh"].length > 0) {
                r["half"] += 1;
              } else {
                r["oeh"] = null;
              }
            }
          }
          if (main.hasOwnProperty("ml")) {
            if (main["ml"]) {
              r["mlh"] = main["ml"].filter((v, i, r) => {
                return v[4] == "MLH";
              });
              if (r["mlh"] != null && r["mlh"].length > 0) {
                r["half"] += 1;
              } else {
                r["mlh"] = null;
              }
            }
          }
        }

        if ([1, 2].includes(sportsId)) {
          if (main.hasOwnProperty("ml")) {
            if (main["ml"]) {
              r["ml"] = main["ml"].filter((v, i, r) => {
                return v[4] == "ML";
              });
              r["mlh"] = main["ml"].filter((v, i, r) => {
                return v[4] == "MLH";
              });
              if (r["ml"] != null && r["ml"].length > 0) {
                r["full"] += 1;
              } else {
                r["ml"] = null;
              }
              if (r["mlh"] != null && r["mlh"].length > 0) {
                r["half"] += 1;
              } else {
                r["mlh"] = null;
              }
            }
          }
        }
      }

      if (odds != null) {
        //- Second Table Format
        //-
        if (odds.hasOwnProperty("oehm")) {
          if (odds["oehm"]) {
            r["oehm"] = odds["oehm"].filter((v, i, r) => {
              return v[4] == "OEHM";
            });
            r["oehmh"] = odds["oehm"].filter((v, i, r) => {
              return v[4] == "OEHMH";
            });
            if (r["oehm"] != null && r["oehm"].length > 0) {
              r["full"] += 1;
            } else {
              r["oehm"] = null;
            }
            if (r["oehmh"] != null && r["oehmh"].length > 0) {
              r["half"] += 1;
            } else {
              r["oehmh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("oeaw")) {
          if (odds["oeaw"]) {
            r["oeaw"] = odds["oeaw"].filter((v, i, r) => {
              return v[4] == "OEAW";
            });
            r["oeawh"] = odds["oeaw"].filter((v, i, r) => {
              return v[4] == "OEAWH";
            });
            if (r["oeaw"] != null && r["oeaw"].length > 0) {
              r["full"] += 1;
            } else {
              r["oeaw"] = null;
            }
            if (r["oeawh"] != null && r["oeawh"].length > 0) {
              r["half"] += 1;
            } else {
              r["oeawh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("tw")) {
          if (odds["tw"]) {
            r["tw"] = odds["tw"].filter((v, i, r) => {
              return v[4] == "1X2HDP";
            });
            r["twh"] = odds["tw"].filter((v, i, r) => {
              return v[4] == "1X2HDPH";
            });
            if (r["tw"] != null && r["tw"].length > 0) {
              r["full"] += 1;
            } else {
              r["tw"] = null;
            }
            if (r["twh"] != null && r["twh"].length > 0) {
              r["half"] += 1;
            } else {
              r["twh"] = null;
            }
          }
        }

        //- Outright Table
        //-
        if (odds.hasOwnProperty("orz")) {
          if (odds["orz"]) {
            r["orz"] = odds["orz"].filter((v, i, r) => {
              return v[4] == "OR";
            });
            if (r["orz"] != null && r["orz"].length > 0) {
              r["full"] += 1;
            } else {
              r["orz"] = null;
            }
          }
        }

        //- Third Table Format
        //-
        if (odds.hasOwnProperty("cs")) {
          if (odds["cs"]) {
            r["cs"] = odds["cs"].filter((v, i, r) => {
              return v[4] == "CS";
            });
            r["cso"] = {};
            for (var i in r["cs"]) {
              r["cso"][r["cs"][i][6]] = r["cs"][i];
            }
            r["csh"] = odds["cs"].filter((v, i, r) => {
              return v[4] == "CSH";
            });
            r["csho"] = {};
            for (var i in r["csh"]) {
              r["csho"][r["csh"][i][6]] = r["csh"][i];
            }
            if (r["cs"] != null && r["cs"].length > 0) {
              r["full"] += 1;
            } else {
              r["cs"] = null;
            }
            if (r["csh"] != null && r["csh"].length > 0) {
              r["half"] += 1;
            } else {
              r["csh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("htft")) {
          if (odds["htft"]) {
            r["htft"] = odds["htft"].filter((v, i, r) => {
              return v[4] == "HTFT";
            });
            r["htfto"] = {};
            for (var i in r["htft"]) {
              r["htfto"][r["htft"][i][6]] = r["htft"][i];
            }
            if (r["htft"] != null && r["htft"].length > 0) {
              r["full"] += 1;
            } else {
              r["htft"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("fglg")) {
          if (odds["fglg"]) {
            r["fglg"] = odds["fglg"].filter((v, i, r) => {
              return v[4] == "FGLG";
            });
            r["fglgo"] = {};
            for (var i in r["fglg"]) {
              r["fglgo"][r["fglg"][i][6]] = r["fglg"][i];
            }
            r["fglgh"] = odds["fglg"].filter((v, i, r) => {
              return v[4] == "FGLGH";
            });
            r["fglgho"] = {};
            for (var i in r["fglgh"]) {
              r["fglgho"][r["fglgh"][i][6]] = r["fglgh"][i];
            }
            if (r["fglg"] != null && r["fglg"].length > 0) {
              r["full"] += 1;
            } else {
              r["fglg"] = null;
            }
            if (r["fglgh"] != null && r["fglgh"].length > 0) {
              r["half"] += 1;
            } else {
              r["fglgh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("tg")) {
          if (odds["tg"]) {
            r["tg"] = odds["tg"].filter((v, i, r) => {
              return v[4] == "TG";
            });
            r["tgo"] = {};
            for (var i in r["tg"]) {
              r["tgo"][r["tg"][i][6]] = r["tg"][i];
            }
            r["tgh"] = odds["tg"].filter((v, i, r) => {
              return v[4] == "TGH";
            });
            r["tgho"] = {};
            for (var i in r["tgh"]) {
              r["tgho"][r["tgh"][i][6]] = r["tgh"][i];
            }
            if (r["tg"] != null && r["tg"].length > 0) {
              r["full"] += 1;
            } else {
              r["tg"] = null;
            }
            if (r["tgh"] != null && r["tgh"].length > 0) {
              r["half"] += 1;
            } else {
              r["tgh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("dc")) {
          if (odds["dc"]) {
            r["dc"] = odds["dc"].filter((v, i, r) => {
              return v[4] == "DC";
            });
            r["dco"] = {};
            for (var i in r["dc"]) {
              r["dco"][r["dc"][i][6]] = r["dc"][i];
            }
            r["dch"] = odds["dc"].filter((v, i, r) => {
              return v[4] == "DCH";
            });
            r["dcho"] = {};
            for (var i in r["dch"]) {
              r["dcho"][r["dch"][i][6]] = r["dch"][i];
            }
            if (r["dc"] != null && r["dc"].length > 0) {
              r["full"] += 1;
            } else {
              r["dc"] = null;
            }
            if (r["dch"] != null && r["dch"].length > 0) {
              r["half"] += 1;
            } else {
              r["dch"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("dnb")) {
          if (odds["dnb"]) {
            r["dnb"] = odds["dnb"].filter((v, i, r) => {
              return v[4] == "DNB";
            });
            r["dnbo"] = {};
            for (var i in r["dnb"]) {
              r["dnbo"][r["dnb"][i][6]] = r["dnb"][i];
            }
            r["dnbh"] = odds["dnb"].filter((v, i, r) => {
              return v[4] == "DNBH";
            });
            r["dnbho"] = {};
            for (var i in r["dnbh"]) {
              r["dnbho"][r["dnbh"][i][6]] = r["dnbh"][i];
            }
            if (r["dnb"] != null && r["dnb"].length > 0) {
              r["full"] += 1;
            } else {
              r["dnb"] = null;
            }
            if (r["dnbh"] != null && r["dnbh"].length > 0) {
              r["half"] += 1;
            } else {
              r["dnbh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("anb")) {
          if (odds["anb"]) {
            r["anb"] = odds["anb"].filter((v, i, r) => {
              return v[4] == "ANB";
            });
            r["anbo"] = {};
            for (var i in r["anb"]) {
              r["anbo"][r["anb"][i][6]] = r["anb"][i];
            }
            if (r["anb"] != null && r["anb"].length > 0) {
              r["full"] += 1;
            } else {
              r["anb"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("hnb")) {
          if (odds["hnb"]) {
            r["hnb"] = odds["hnb"].filter((v, i, r) => {
              return v[4] == "HNB";
            });
            r["hnbo"] = {};
            for (var i in r["hnb"]) {
              r["hnbo"][r["hnb"][i][6]] = r["hnb"][i];
            }
            if (r["hnb"] != null && r["hnb"].length > 0) {
              r["full"] += 1;
            } else {
              r["hnb"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("bs")) {
          if (odds["bs"]) {
            r["bs"] = odds["bs"].filter((v, i, r) => {
              return v[4] == "BS";
            });
            r["bso"] = {};
            for (var i in r["bs"]) {
              r["bso"][r["bs"][i][6]] = r["bs"][i];
            }
            if (r["bs"] != null && r["bs"].length > 0) {
              r["full"] += 1;
            } else {
              r["bs"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("cl")) {
          if (odds["cl"]) {
            r["cl"] = odds["cl"].filter((v, i, r) => {
              return v[4] == "CL";
            });
            r["clo"] = {};
            for (var i in r["cl"]) {
              r["clo"][r["cl"][i][6]] = r["cl"][i];
            }
            if (r["cl"] != null && r["cl"].length > 0) {
              r["full"] += 1;
            } else {
              r["cl"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("twtn")) {
          if (odds["twtn"]) {
            r["twtn"] = odds["twtn"].filter((v, i, r) => {
              return v[4] == "TWTN";
            });
            r["twtno"] = {};
            for (var i in r["twtn"]) {
              r["twtno"][r["twtn"][i][6]] = r["twtn"][i];
            }
            if (r["twtn"] != null && r["twtn"].length > 0) {
              r["full"] += 1;
            } else {
              r["twtn"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("oxtou")) {
          if (odds["oxtou"]) {
            r["oxtou"] = odds["oxtou"].filter((v, i, r) => {
              return v[4] == "1X2OU";
            });
            r["oxtouo"] = {};
            for (var i in r["oxtou"]) {
              r["oxtouo"][r["oxtou"][i][6]] = r["oxtou"][i];
            }
            if (r["oxtou"] != null && r["oxtou"].length > 0) {
              r["full"] += 1;
            } else {
              r["oxtou"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("dcou")) {
          if (odds["dcou"]) {
            r["dcou"] = odds["dcou"].filter((v, i, r) => {
              return v[4] == "DCOU";
            });
            r["dcouo"] = {};
            for (var i in r["dcou"]) {
              r["dcouo"][r["dcou"][i][6]] = r["dcou"][i];
            }
            if (r["dcou"] != null && r["dcou"].length > 0) {
              r["full"] += 1;
            } else {
              r["dcou"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("ouoe")) {
          if (odds["ouoe"]) {
            r["ouoe"] = odds["ouoe"].filter((v, i, r) => {
              return v[4] == "OUOE";
            });
            r["ouoeo"] = {};
            for (var i in r["ouoe"]) {
              r["ouoeo"][r["ouoe"][i][6]] = r["ouoe"][i];
            }
            if (r["ouoe"] != null && r["ouoe"].length > 0) {
              r["full"] += 1;
            } else {
              r["ouoe"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("wm")) {
          if (odds["wm"]) {
            r["wm"] = odds["wm"].filter((v, i, r) => {
              return v[4] == "WM";
            });
            r["wmo"] = {};
            for (var i in r["wm"]) {
              r["wmo"][r["wm"][i][6]] = r["wm"][i];
            }
            if (r["wm"] != null && r["wm"].length > 0) {
              r["full"] += 1;
            } else {
              r["wm"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("htftoe")) {
          if (odds["htftoe"]) {
            r["htftoe"] = odds["htftoe"].filter((v, i, r) => {
              return v[4] == "HTFTOE";
            });
            r["htftoeo"] = {};
            for (var i in r["htftoe"]) {
              r["htftoeo"][r["htftoe"][i][6]] = r["htftoe"][i];
            }
            if (r["htftoe"] != null && r["htftoe"].length > 0) {
              r["full"] += 1;
            } else {
              r["htftoe"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("etg")) {
          if (odds["etg"]) {
            r["etg"] = odds["etg"].filter((v, i, r) => {
              return v[4] == "ETG";
            });
            r["etgo"] = {};
            for (var i in r["etg"]) {
              r["etgo"][r["etg"][i][6]] = r["etg"][i];
            }
            // console.log(r["etgo"]);
            r["etgh"] = odds["etg"].filter((v, i, r) => {
              return v[4] == "ETGH";
            });
            r["etgho"] = {};
            for (var i in r["etgh"]) {
              r["etgho"][r["etgh"][i][6]] = r["etgh"][i];
            }
            if (r["etg"] != null && r["etg"].length > 0) {
              r["full"] += 1;
            } else {
              r["etg"] = null;
            }
            if (r["etgh"] != null && r["etgh"].length > 0) {
              r["half"] += 1;
            } else {
              r["etgh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("ehtg")) {
          if (odds["ehtg"]) {
            r["ehtg"] = odds["ehtg"].filter((v, i, r) => {
              return v[4] == "EHTG";
            });
            r["ehtgo"] = {};
            for (var i in r["ehtg"]) {
              r["ehtgo"][r["ehtg"][i][6]] = r["ehtg"][i];
            }
            r["ehtgh"] = odds["ehtg"].filter((v, i, r) => {
              return v[4] == "EHTGH";
            });
            r["ehtgho"] = {};
            for (var i in r["ehtgh"]) {
              r["ehtgho"][r["ehtgh"][i][6]] = r["ehtgh"][i];
            }
            if (r["ehtg"] != null && r["ehtg"].length > 0) {
              r["full"] += 1;
            } else {
              r["ehtg"] = null;
            }
            if (r["ehtgh"] != null && r["ehtgh"].length > 0) {
              r["half"] += 1;
            } else {
              r["ehtgh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("eatg")) {
          if (odds["eatg"]) {
            r["eatg"] = odds["eatg"].filter((v, i, r) => {
              return v[4] == "EATG";
            });
            r["eatgo"] = {};
            for (var i in r["eatg"]) {
              r["eatgo"][r["eatg"][i][6]] = r["eatg"][i];
            }
            r["eatgh"] = odds["eatg"].filter((v, i, r) => {
              return v[4] == "EATGH";
            });
            r["eatgho"] = {};
            for (var i in r["eatgh"]) {
              r["eatgho"][r["eatgh"][i][6]] = r["eatgh"][i];
            }
            if (r["eatg"] != null && r["eatg"].length > 0) {
              r["full"] += 1;
            } else {
              r["eatg"] = null;
            }
            if (r["eatgh"] != null && r["eatgh"].length > 0) {
              r["half"] += 1;
            } else {
              r["eatgh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("cshtft")) {
          if (odds["cshtft"]) {
            r["cshtft"] = odds["cshtft"].filter((v, i, r) => {
              return v[4] == "CSHTFT";
            });
            r["cshtfto"] = {};
            for (var i in r["cshtft"]) {
              r["cshtfto"][r["cshtft"][i][36]] = r["cshtft"][i];
            }
            if (r["cshtft"] != null && r["cshtft"].length > 0) {
              r["full"] += 1;
            } else {
              r["cshtft"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("etghtft")) {
          if (odds["etghtft"]) {
            r["etghtft"] = odds["etghtft"].filter((v, i, r) => {
              return v[4] == "ETGHTFT";
            });
            r["etghtfto"] = {};
            for (var i in r["etghtft"]) {
              r["etghtfto"][r["etghtft"][i][36]] = r["etghtft"][i];
            }
            if (r["etghtft"] != null && r["etghtft"].length > 0) {
              r["full"] += 1;
            } else {
              r["etghtft"] = null;
            }
          }
        }
      }

      r["total"] = r["full"] + r["half"];
      // console.log(r["total"]);
      return r;
    },
    difference(a1, a2) {
      var result = [];
      for (var i = 0; i < a1.length; i++) {
        if (a2.indexOf(a1[i]) === -1) {
          result.push(a1[i]);
        }
      }
      return result;
    },
    symmetric(a1, a2) {
      var result = [];
      for (var i = 0; i < a1.length; i++) {
        if (a2.indexOf(a1[i]) === -1) {
          result.push(a1[i]);
        }
      }
      for (i = 0; i < a2.length; i++) {
        if (a1.indexOf(a2[i]) === -1) {
          result.push(a2[i]);
        }
      }
      return result;
    },
    shallowEqual(objA, objB) {
      if (objA === objB) return true;
      if (typeof objA !== 'object' || objA === null ||
        typeof objB !== 'object' || objB === null) {
        return false;
      }
      const keysA = Object.keys(objA);
      const keysB = Object.keys(objB);
      if (keysA.length !== keysB.length) return false;
      for (let i = 0; i < keysA.length; i++) {
        if (objA[keysA[i]] !== objB[keysA[i]]) return false;
      }
      return true;
    },
    deep(fn, m, a1) {
      for (var i in a1) {
        if (m[fn].hasOwnProperty(i)) {
          if (!this.shallowEqual(a1[i], m[fn][i])) {
            this.$set(m[fn], i, a1[i]);
          }
        } else {
          this.$set(m[fn], i, a1[i]);
        }
      }
      var t1 = Object.keys(m[fn]);
      var t2 = Object.keys(a1);
      var t3 = this.difference(t1, t2);
      for (var n in t3) {
        this.$delete(m[fn], t3[n]);
      }

      var sportsId = parseInt(m["sportsId"]);
      if (fn == "odds") {
        var details = this.details(sportsId, m["matchHeader"], m["odds"], this.cacheData.child[m["matchId"]]);
        var q1 = Object.keys(m["details"]);
        var q2 = Object.keys(details);
        var q3 = this.difference(q1, q2);
        for (var n in q3) {
          this.$delete(m["details"], q3[n]);
        }
        for (var n in q2) {
          if (m["details"].hasOwnProperty(q2[n])) {
            if (!this.shallowEqual(m["details"][q2[n]], details[q2[n]])) {
              this.$set(m["details"], q2[n], details[q2[n]]);
            }
          } else {
            this.$set(m["details"], q2[n], details[q2[n]]);
          }
        }

        if (a1.hasOwnProperty("orz")) {
          if (Object.keys(a1.orz).length) {
            m.sorting = Math.round(a1.orz[0][15] * 10000);
          }
        }
      }

      if (fn == "more") {
        var moreItems = this.moreItems(sportsId, m["odds"], m["more"], m);
        var z1 = Object.keys(m["moreItems"]);
        var z2 = Object.keys(moreItems);
        var z3 = this.difference(z1, z2);
        for (var n in z3) {
          this.$delete(m["moreItems"], z3[n]);
        }
        for (var n in z2) {
          if (m["moreItems"].hasOwnProperty(z2[n])) {
            if (!this.shallowEqual(m["moreItems"][z2[n]], moreItems[z2[n]])) {
              this.$set(m["moreItems"], z2[n], moreItems[z2[n]]);
            }
          } else {
            this.$set(m["moreItems"], z2[n], moreItems[z2[n]]);
          }
        }
      }
    },
    processOdds(fn, e, mkt) {
      const endTiming = this.$perf ? this.$perf.startTiming('processOdds', 'method') : () => {};
      
      var odds = {};
      var st1 = new Date();
      for (var i in e.odds) {
        for (var j in e.odds[i]) {
          for (var k in e.odds[i][j]) {
            if (!this.oddsList.hasOwnProperty(k)) {
              this.oddsList[k] = {};
            }
            if (!this.oddsList[k].hasOwnProperty(j)) {
              this.oddsList[k][j] = {};
            }
            this.oddsList[k][j] = e.odds[i][j][k];

            if (!odds.hasOwnProperty(k)) {
              odds[k] = {};
            }
            if (!odds[k].hasOwnProperty(j)) {
              odds[k][j] = {};
            }
            odds[k][j] = e.odds[i][j][k];
          }
        }
      }

      for (var m in odds) {
        if (this.oddsList.hasOwnProperty(m)) {
          var t1 = Object.keys(this.oddsList[m]);
          var t2 = Object.keys(odds[m]);
          var t3 = this.difference(t1, t2);
          for (var n in t3) {
            this.$delete(this.oddsList[m], t3[n]);
          }
        }
      }

      var a1 = Object.keys(this.oddsList);
      var a2 = Object.keys(this.matchList);
      var a3 = this.difference(a1, a2);

      for (var n in a3) {
        this.$delete(this.oddsList, a3[n]);
      }

      for (var m in this.oddsList) {
        if (this.matchList.hasOwnProperty(m)) {
          this.deep(fn, this.matchList[m], this.oddsList[m]);
        }
      }

      if (this.menu3 == "orz") {
        this.recalcList();
      }

      EventBus.$emit("PROCESS_ODDS");
      
      endTiming(); // End performance monitoring
    },
    deepMMO(fn, m, a1) {
      for (var i in a1) {
        if (m[fn].hasOwnProperty(i)) {
          if (!this.shallowEqual(a1[i], m[fn][i])) {
            this.$set(m[fn], i, a1[i]);
          }
        } else {
          this.$set(m[fn], i, a1[i]);
        }
      }
      var t1 = Object.keys(m[fn]);
      var t2 = Object.keys(a1);
      var t3 = this.difference(t1, t2);
      for (var n in t3) {
        this.$delete(m[fn], t3[n]);
      }

      var sportsId = parseInt(m["sportsId"]);
      if (fn == "mmo") {
        var mmoDetails = this.mmoDetails(sportsId, m["matchHeader"], m["mmo"], this.cacheData.child[m["matchId"]]);
        var q1 = Object.keys(m["mmoDetails"]);
        var q2 = Object.keys(mmoDetails);
        var q3 = this.difference(q1, q2);
        for (var n in q3) {
          this.$delete(m["mmoDetails"], q3[n]);
        }
        for (var n in q2) {
          if (m["mmoDetails"].hasOwnProperty(q2[n])) {
            if (!this.shallowEqual(m["mmoDetails"][q2[n]], mmoDetails[q2[n]])) {
              this.$set(m["mmoDetails"], q2[n], mmoDetails[q2[n]]);
            }
          } else {
            this.$set(m["mmoDetails"], q2[n], mmoDetails[q2[n]]);
          }
        }

        if (a1.hasOwnProperty("orz")) {
          m.sorting = Math.round(a1.orz[0][15] * 10000);
        }
      }
    },
    processMMO(fn, e, mkt) {
      const endTiming = this.$perf ? this.$perf.startTiming('processMMO', 'method') : () => {};
      
      var mmo = {};
      var st1 = new Date();
      for (var i in e.mmo) {
        for (var j in e.mmo[i]) {
          for (var k in e.mmo[i][j]) {
            if (!this.mmoList.hasOwnProperty(k)) {
              this.mmoList[k] = {};
            }
            if (!this.mmoList[k].hasOwnProperty(j)) {
              this.mmoList[k][j] = {};
            }
            this.mmoList[k][j] = e.mmo[i][j][k];

            if (!mmo.hasOwnProperty(k)) {
              mmo[k] = {};
            }
            if (!mmo[k].hasOwnProperty(j)) {
              mmo[k][j] = {};
            }
            mmo[k][j] = e.mmo[i][j][k];
          }
        }
      }

      var isForceUpdate = false;

      for (var m in mmo) {
        if (this.mmoList.hasOwnProperty(m)) {
          var t1 = Object.keys(this.mmoList[m]);
          var t2 = Object.keys(mmo[m]);
          var t3 = this.difference(t1, t2);
          for (var n in t3) {
            this.$delete(this.mmoList[m], t3[n]);
            isForceUpdate = true;
          }
        }
      }

      var t11 = this.refMMO[mkt];
      var t21 = Object.keys(mmo);
      if (t11 != null) {
        var t31 = this.difference(t11, t21);
        for (var n1 in t31) {
          var mmm = t31[n1];
          if (fn == "mmo") {
            this.$delete(this.mmoList, mmm);
            if (this.matchList.hasOwnProperty(mmm)) {
              if (this.matchList[mmm].hasOwnProperty("mmo")) this.matchList[mmm]["mmo"] = {};
              if (this.matchList[mmm].hasOwnProperty("mmoDetails")) this.matchList[mmm]["mmoDetails"] = {};
            }
          } else {
            this.$delete(this.mmoList, mmm);
          }
          isForceUpdate = true;
        }
      }
      this.refMMO[mkt] = t21;

      var a1 = Object.keys(this.mmoList);
      var a2 = Object.keys(this.matchList);
      var a3 = this.difference(a1, a2);
      for (var n in a3) {
        this.$delete(this.mmoList, a3[n]);
        isForceUpdate = true;
      }

      for (var m in this.mmoList) {
        if (this.matchList.hasOwnProperty(m)) {
          this.deepMMO(fn, this.matchList[m], this.mmoList[m]);
        }
      }

      if (this.menu3 == "orz") {
        this.recalcList();
      }

      if (this.menuX) {
        var p1 = {};
        var p2 = {};
        var p3 = {};

        var n1 = {};
        var n2 = {};
        var n3 = {};

        for (var kk in this.marketList) {
          var mm = this.marketList[kk];
          p1[kk] = false;
          n1[kk] = false;
          for (var jj in mm.leagueList) {
            var nn = mm.leagueList[jj];
            p2[jj] = false;
            n2[jj] = false;
            for (var ii in nn.matchList) {
              var gg = nn.matchList[ii];

              p3[ii] = false;
              n3[ii] = false;
              for (var pp in gg.mmo) {
                var qq = gg.mmo[pp];
                n1[kk] = true;
                n2[jj] = true;
                n3[ii] = true;
                for (var tt in qq) {
                  if (qq[tt][13] == 1) {
                    p1[kk] = true;
                    p2[jj] = true;
                    p3[ii] = true;
                    break;
                  }
                }
                if (p3[ii]) {
                  break;
                }
              }
              gg["xp"] = p3[ii];
              gg["xn"] = n3[ii];
            }
            nn["xp"] = p2[jj];
            nn["xn"] = n2[jj];
          }
          mm["xp"] = p1[kk];
          mm["xn"] = n1[kk];
        }
      }

      if (isForceUpdate) {
        this.$forceUpdate();
      }

      EventBus.$emit("PROCESS_MMO");
      
      endTiming(); // End performance monitoring
    },
    recalcList() {
      for (var n in this.marketList) {
        for (var m in this.marketList[n].leagueList) {
          this.marketList[n].leagueList[m].matchList.sort((a, b) => {
            return a.sorting - b.sorting;
          });
        }
      }
    },
    processList() {
      const endTiming = this.$perf ? this.$perf.startTiming('processList', 'method') : () => {};
      
      var sports = this.sports;
      var data = this.cacheData;
      const mki = {
        1: this.$t("ui.early"),
        2: this.$t("ui.today"),
        3: this.$t("ui.live"),
        4: this.$t("ui.parlay"),
      };
      var matchList = {};
      var leagueList = {};
      var marketList = {};

      var groupIndex = 0;
      var matchIndex = 0;
      this.liveMatchCount = 0;
      var sp = Object.keys(data.group);

      var cfgOrder = config.defaultSportOrder;
      if (this.currency_code == "BDT") {
        cfgOrder = config.defaultSportOrder1;
      }
      var spi = [];
      for (var cc = 0; cc < cfgOrder.length; cc++) {
        var ccs = cfgOrder[cc].toString();
        if (sp.includes(ccs)) {
          spi.push(ccs);
        }
      }

      for (var spn = 0; spn < spi.length; spn++) {
        var sportsId = parseInt(spi[spn]);
        var mk = Object.keys(data.group[sportsId]);
        for (var mkn = 0; mkn < mk.length; mkn++) {
          var marketId = parseInt(mk[mkn]);
          var bt = Object.keys(data.group[sportsId][marketId]);
          for (var btn = 0; btn < bt.length; btn++) {
            var betType = bt[btn];
            var scope = marketId + "_" + sportsId + "_" + betType;
            var cid = "x10" + "_" + scope;

            var cc = {
              id: cid,
              ptype: 10,
              sportsId: sportsId,
              sportsType: sports[sportsId],
              marketId: marketId,
              marketType: mki[marketId],
              betTypeId: betType,
              betTypeLayout: this.$t("m.BT_" + betType.toUpperCase()),
              matchList: [],
              leagueList: [],
              xp: false,
              xn: false,
            };

            if (this.marketList.hasOwnProperty(cid)) {
              cc.xp = this.marketList[cid].xp;
              cc.xn = this.marketList[cid].xn;
            }

            var lns = data.group[sportsId][marketId][betType];

            for (var ln = 0; ln < lns.length; ln++) {
              var matches = lns[ln];

              if (matches.length > 0) {
                groupIndex += 1;

                var lni = matches[0][0];

                if (data.league.hasOwnProperty(lni)) {
                  if (this.leagueFiltered == false || (this.leagueFiltered == true && this.selectedLeague.includes(lni) == true)) {
                    var scope1 = groupIndex + "_" + scope + "_" + lni;
                    var gid = "x20_" + scope1;

                    var gg = {
                      id: gid,
                      groupIndex: groupIndex,
                      categoryId: cid,
                      ptype: 20,
                      marketId: marketId,
                      sportsId: sportsId,
                      betTypeId: betType,
                      leagueId: lni,
                      leagueHeader: data.league[lni],
                      leagueName: data.league[lni][4],
                      leagueInfo: data.league[lni][6],
                      gameType: data.league[lni][8] ? data.league[lni][8] : null,
                      isParent: data.league[lni][5] == 1,
                      matches: matches,
                      matchList: [],
                      xp: false,
                      // px: false,
                    };
                    if (leagueList.hasOwnProperty(gid)) {
                      gg = leagueList[gid];
                    } else {
                      leagueList[gid] = gg;
                    }

                    if (this.leagueList.hasOwnProperty(gid)) {
                      gg.xp = this.leagueList[gid].xp;
                      gg.xn = this.leagueList[gid].xn;
                    }

                    if (sportsId == 1 || gg.isParent == true) {
                      for (var mn = 0; mn < matches.length; mn++) {
                        if (marketId == 3) {
                          this.liveMatchCount += 1;
                        }
                        matchIndex += 1;
                        var mi = matches[mn];
                        var mid = mi[2];
                        var scope2 = scope1 + "_" + mid;
                        var mmid = "x30" + "_" + scope2;
                        var mm = {
                          id: mmid,
                          matchIndex: matchIndex,
                          categoryId: cid,
                          groupId: gid,
                          ptype: 30,
                          marketId: marketId,
                          sportsId: sportsId,
                          betTypeId: betType,
                          leagueId: mi[0],
                          leagueHeader: data.league[mi[0]],
                          leagueName: data.league[mi[0]][4],
                          matchItem: mi,
                          matchId: mid,
                          matchHeader: data.head[mid],
                          matchBody: data.match[mid],
                          child: data.child[mid],
                          homeTeam: data.match[mid][5],
                          awayTeam: data.match[mid][6],
                          workingTime: data.match[mid][7],
                          matchTime: data.match[mid][8],
                          runningScore: data.match[mid][11],
                          runningTime: data.match[mid][12],
                          channelId: data.match[mid][13],
                          stats: data.match[mid][14],
                          radarId: [1, 2].includes(sportsId) ? data.match[mid][27] : 0,
                          details: {},
                          moreItems: {},
                          mmoDetails: {},
                          odds: {},
                          mmo: {},
                          more: {},
                          moremmo: {},
                          sorting: data.match[mid][1],
                          xp: false,
                          // px: false,
                        };

                        if (this.matchList.hasOwnProperty(mid)) {
                          mm.odds = this.matchList[mid].odds;
                          mm.mmo = this.matchList[mid].mmo;
                          mm.more = this.matchList[mid].more;
                          mm.moremmo = this.matchList[mid].moremmo;
                          mm.details = this.matchList[mid].details;
                          mm.moreItems = this.matchList[mid].moreItems;
                          mm.mmoDetails = this.matchList[mid].mmoDetails;
                          mm.sorting = this.matchList[mid].sorting;
                          mm.xp = this.matchList[mid].xp;
                          mm.xn = this.matchList[mid].xn;
                        }

                        gg["lastMatchTime"] = data.match[mid][8];
                        gg["matchList"].push(mm);
                        cc["matchList"].push(mm);
                        matchList[mid] = mm;
                      }

                      if (this.menu3 == "orz") {
                        gg["matchList"].sort((a, b) => {
                          return a.sorting - b.sorting;
                        });
                      }

                      if (gg.matchList.length > 0) {
                        cc["leagueList"].push(gg);
                      }
                    }
                  }
                }
              }
            }

            if (cc.leagueList.length > 0) {
              marketList[cid] = cc;
            }
          }
        }
      }

      var x1 = Object.keys(this.matchList);
      if (x1.length > 0) {
        var x2 = Object.keys(matchList);
        var x3 = this.difference(x1, x2);
        for (var n in x3) {
          this.$delete(this.matchList, x3[n]);
        }
        for (var v in matchList) {
          if (!this.shallowEqual(this.matchList[v], matchList[v])) {
            this.$set(this.matchList, v, matchList[v]);
          }
        }
      } else {
        this.$set(this, "matchList", matchList);
      }

      var x1 = Object.keys(this.leagueList);
      if (x1.length > 0) {
        var x2 = Object.keys(leagueList);
        var x3 = this.difference(x1, x2);
        for (var n in x3) {
          this.$delete(this.leagueList, x3[n]);
        }
        for (var v in leagueList) {
          if (!this.shallowEqual(this.leagueList[v], leagueList[v])) {
            this.$set(this.leagueList, v, leagueList[v]);
          }
        }
      } else {
        this.$set(this, "leagueList", leagueList);
      }

      var x1 = Object.keys(this.marketList);
      if (x1.length > 0) {
        var x2 = Object.keys(marketList);
        var x3 = this.difference(x1, x2);
        for (var n in x3) {
          this.$delete(this.marketList, x3[n]);
        }
        for (var v in marketList) {
          if (!this.shallowEqual(this.marketList[v], marketList[v])) {
            this.$set(this.marketList, v, marketList[v]);
          }
        }
      } else {
        this.$set(this, "marketList", marketList);
      }
      
      endTiming(); // End performance monitoring
    },
    loadMoreItems() {
      if (this.pagination.loading || this.pagination.currentPage >= this.totalPages) return;

      this.pagination.loading = true;
      this.pagination.currentPage++;

      // Simulate API call or data loading
      setTimeout(() => {
        this.pagination.loading = false;
      }, 300);
    },
    handleScroll(event) {
      const { scrollTop, scrollHeight, clientHeight } = event.target;
      const scrollBottom = scrollTop + clientHeight;

      // Load more when user scrolls near bottom
      if (scrollHeight - scrollBottom < 200) {
        this.loadMoreItems();
      }
    },
    resetPagination() {
      this.pagination.currentPage = 1;
      this.pagination.totalItems = this.marketKeys3.length;
    },
    setTrackedTimeout(fn, delay) {
      const id = setTimeout(fn, delay);
      this.timeouts.push(id);
      return id;
    },
    showComingSoon() {
      this.$swal("Info", "This feature is coming soon", "info");
    },
  },
};
</script>

<style lang="scss" scoped>
.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  width: 100%;

  .spinner-border {
    width: 2rem;
    height: 2rem;
  }
}

.virtual-scroll-container {
  position: relative;
  height: 100%;
  overflow-y: auto;

  .virtual-scroll-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    will-change: transform;
  }
}

.main-item {
  transition: opacity 0.3s ease;

  &.loading {
    opacity: 0.6;
  }
}
</style>
