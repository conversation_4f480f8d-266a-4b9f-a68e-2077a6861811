<template lang="pug">
.room-bet-value-io(:class="[valueClass]" @click="placeBet($event.target)") {{ value }}
</template>

<script>
import cal from "@/library/_calculation";
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";

export default {
  props: {
    odds: {
      type: Array,
    },
    idx: {
      type: String,
    },
    typ: {
      type: String,
    },
    cls: {
      type: String,
    },
    dataType: {
      type: String,
    },
    roomData: {
      type: Object,
    },
    match: {
      type: Object,
    },
  },
  data() {
    return {
      trigger: false,
      timer: null,
      old: null,
      dir: null,
      newPage: true,
      selected: false,
    };
  },
  computed: {
    betType() {
      return this.$store.state.layout.menu3;
    },
    id() {
      return this.odds[3];
    },
    value() {
      return this.odds[this.idx];
    },
    dv() {
      var idx = parseInt(this.idx);
      if (this.odds[idx]) {
        switch (this.dataType) {
        case "1": // HDP/OU
          idx = parseInt(this.idx) + 16;
          return parseFloat(this.odds[idx]);
          break;
        case "2": // OE/ML/1X2
          idx = parseInt(this.idx) + 16;
          return parseFloat(this.odds[idx]);
          break;
        default:
          return parseFloat(this.odds[idx]);
          break;
        }
      } else {
        return 0;
      }
    },
    valueClass() {
      var result = [];
      if (this.cls) {
        result.push(this.cls);
      }
      if (this.trigger == true) {
        result.push("room-value-highlight");
      }
      if (this.dir != null) {
        result.push(this.dir);
      }
      if (!this.allowBet) {
        result.push("room-value-no-bet");
      }
      if (this.value < 0) {
        result.push("room-value-negative");
      }
      if (this.selected === true) {
        result.push("room-value-selected");
      }
      return result;
    },
    allowBet() {
      switch (this.dataType) {
      case "1":
        return this.odds[15] == 1;
      case "2":
        return this.odds[10] == 1;
      case "3":
        return this.odds[9] == 1;
      case "4":
        return this.odds[9] == 1;
      }
      return false;
    },
    pause() {
      switch (this.dataType) {
      case "1":
        return this.odds[14] == 1;
      case "2":
        return this.odds[9] == 1;
      case "3":
        return false;
      case "4":
        return false;
      }
      return true;
    },
    mixParlay() {
      switch (this.dataType) {
      case "1": // HDP/OU
        return this.odds[13] == 1;
      case "2": // OE/ML/1X2
        return this.odds[8] == 1;
      case "3": // CS/DC...
        return this.odds[8] == 1;
      case "4": // OUTRIGHT
        return this.odds[8] == 1;
      }
      return false;
    },
  },
  watch: {
    dv(newVal, oldVal) {
      var nv = newVal;
      var ov = oldVal;
      if (nv == "" || nv == 0) {
        this.trigger = false;
        this.dir = null;
        return;
      } else {
        if (ov != nv) {
          this.trigger = true;
          if (nv < 0) {
            if (ov < nv) {
              this.dir = "room-value-down";
            } else {
              this.dir = "room-value-up";
            }
          } else {
            if (ov > nv) {
              this.dir = "room-value-down";
            } else {
              this.dir = "room-value-up";
            }
          }
          setTimeout(() => {
            this.trigger = false;
            this.dir = null;
          }, 10000);
        }
      }
      this.old = oldVal;
    },
    id(newVal, oldVal) {
      this.reset();
    },
    typ(newVal, oldVal) {
      this.reset();
    },
    betType(newVal, oldVal) {
      this.reset();
    },
  },
  mounted() {
    EventBus.$on("selectOdds2", this.selectOdds);
  },
  destroyed() {
    EventBus.$off("selectOdds2", this.selectOdds);
  },
  methods: {
    selectOdds(e) {
      if (e.hasOwnProperty(this.roomData.room_id)) {
        if (e[this.roomData.room_id].hasOwnProperty(this.match.match_id)) {
          var id = e[this.roomData.room_id][this.match.match_id].odds[3];
          var idx = e[this.roomData.room_id][this.match.match_id].idx;
          var typ = e[this.roomData.room_id][this.match.match_id].typ;
          if (id == this.id && idx == this.idx && typ == this.typ) {
            this.selected = true;
          } else {
            this.selected = false;
          }
        } else {
          this.selected = false;
        }
      } else {
        this.selected = false;
      }
    },
    convert(v, g, b, bt) {
      if (v == null || v == "") {
        return null;
      }

      var result = v;
      switch (bt) {
      case "HDP":
      case "HDPH":
        if (v.includes("-")) {
          var balls = v.split("-");
          result = (parseFloat(balls[0]) + parseFloat(balls[1])) / 2;
        }
        if (b != g + 1) {
          if (result != 0) {
            result = "-" + result.toString();
          }
        }
        break;
      case "OU":
      case "OUH":
        if (v.includes("-")) {
          var balls = v.split("-");
          result = (parseFloat(balls[0]) + parseFloat(balls[1])) / 2;
        }
        break;
      default:
        result = null;
        break;
      }

      return result;
    },
    reset() {
      this.trigger = false;
      this.old = null;
      this.dir = null;
    },
    placeBet(e) {
      if (this.value) {
        if (this.allowBet) {
          if (EventBus.tournamentAdd2) {
            var HomeAway = 0;
            var homeIdx = 0;
            var awayIdx = 0;
            var originIdx = 24;
            if (["HDP", "HDPH"].includes(this.odds[4].toUpperCase())) {
              homeIdx = 10;
              awayIdx = 9;
            } else {
              homeIdx = 12;
              awayIdx = 11;
            }
            if (this.odds[7] == 1) {
              if (this.idx == homeIdx) {
                HomeAway = 1;
              } else {
                HomeAway = 2;
                originIdx = 23;
              }
            } else {
              if (this.idx == awayIdx) {
                HomeAway = 1;
                originIdx = 23;
              } else {
                HomeAway = 2;
              }
            }

            var ballValue = this.convert(
              this.odds[8],
              this.odds[7],
              HomeAway,
              this.odds[4]
            );

            EventBus.tournamentAdd2({
              odds: this.odds,
              typ: this.typ,
              idx: this.idx,
              value: this.value,
              room: this.roomData,
              match: this.match,
              home_away: HomeAway,
              ball_value: ballValue,
              bet_type: this.odds[4],
              giving: this.odds[7],
              origin: this.odds[originIdx],
              sports_type: this.match.sports_type,
            });
          }
        }
      }
    },
  },
};
</script>
