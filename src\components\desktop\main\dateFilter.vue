<template lang="pug">
  .d-flex.flex-row.filter-block
    .filter-single
      .d-flex.flex-row(@click="handleSelection(0)")
        .filter-date(:class="{ 'active': 0 == parseInt(selectedDays) }")
          .filter-date-title {{ $t("ui.all") }}
          .filter-date-body */*
    .filter-single(v-for="(item, index) in 7")
      .d-flex.flex-row(@click="handleSelection(item)")
        .filter-date(:class="{ 'active': item == parseInt(selectedDays) }")
          .filter-date-title {{ $dayjs().subtract(12, 'hour').add(item, 'day').format("ddd") }}
          .filter-date-body {{ $dayjs().subtract(12, 'hour').add(item, 'day').format("M/DD") }}
    .filter-single
      .d-flex.flex-row(@click="handleSelection(8)")
        .filter-date(:class="{ 'active': 8 == parseInt(selectedDays) }")
          .filter-date-title {{ $dayjs().subtract(12, 'hour').add(8, 'day').format("ddd") }}
          .filter-date-body &ge; {{ $dayjs().subtract(12, 'hour').add(8, 'day').format("M/DD") }}
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";

export default {
  computed: {
    selectedDays() {
      return this.$store.getters.selectedDays;
    }
  },
  methods: {
    handleSelection(e) {
      this.$store.dispatch("layout/setSelectedDays", e.toString());
      EventBus.$emit("INVALIDATE");
    }
  }
};
</script>