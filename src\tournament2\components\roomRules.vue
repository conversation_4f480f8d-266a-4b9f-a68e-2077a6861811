<template lang="pug">
#room-rules.modal.fade.modal-room
  .modal-dialog.modal-xl.modal-dialog-centered.modal-dialog-scrollable
    .modal-content
      .modal-header
        .modal-title
          .modal-title-left {{ $t('ui.rules') }}
      .modal-body
        .m-1
          iframe(:src="rulesUrl" loading="lazy")
      .tournament-room.mx-3.mt-1.mb-3.d-flex.align-items-center
        .select-league-row.flex-fill.border-0
        .tournament-button.tournament-button-secondary.tournament-button-hover(@click="cancel")
          .tournament-text {{ $t('ui.cancel') }}
          .tournament-icon
            i.fas.fa-times
</template>

<script>
import config from "@/config";
import errors from "@/errors";

export default {
  components: {
  },
  data() {
    return { 
    };
  },
  computed: {
    rulesUrl() {
      var lang = "en";
      switch (this.$store.getters.language) {
      case "cn":
        lang = this.$store.getters.language;
        break;
      default:
        lang = "en";
      }
      return config.rulesUrl + "/tournament/rules_" + lang + ".html";
    },
  },
  destroyed() {
  },
  mounted() {
  },
  methods: {
    cancel() {
      $("#room-rules").modal("hide");
    },
  },
};
</script>
