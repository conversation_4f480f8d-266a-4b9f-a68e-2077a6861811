# Vue 2 Preparation Plan for Vue 3 Migration

## Overview
This plan outlines preparation steps that can be done in the existing Vue 2 codebase to make the Vue 3 migration smoother, less risky, and more manageable. All these steps maintain Vue 2 compatibility while preparing for Vue 3.

## Phase 1: Foundation Preparation (2-3 weeks)

### 1.1 Pinia Migration (Vue 2 Compatible)
**Priority: High** | **Risk: Low** | **Impact: High**

Pinia has excellent Vue 2 support and can be migrated incrementally.

**Steps:**
1. Install Pinia with Vue 2 support
2. Set up Pinia alongside existing Vuex
3. Migrate one store at a time
4. Test thoroughly before moving to next store

**Implementation:**
```bash
# Install Pinia for Vue 2
yarn add pinia@^2.1.0
yarn add @pinia/nuxt # if using Nuxt
```

```javascript
// src/main.js - Add Pinia alongside Vuex
import { PiniaVuePlugin, createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

Vue.use(PiniaVuePlugin)
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

new Vue({
  store, // Keep existing Vuex store
  pinia, // Add Pinia
  // ... rest of config
})
```

**Migration Order:**
1. Start with `_user.js` store (least dependencies)
2. Move to `_layout.js` store
3. Migrate betting stores (`_betsingle.js`, `_betparlay.js`)
4. Finally migrate `_cache.js` (most complex)

### 1.2 Event Bus Replacement
**Priority: High** | **Risk: Low** | **Impact: Medium**

Replace the deprecated `new Vue()` event bus with modern patterns.

**Current Issue:**
```javascript
// src/library/_event-bus.js
import Vue from "vue";
export const EventBus = new Vue();
```

**Solution Options:**

**Option 1: Props/Emit Pattern**
```javascript
// Replace EventBus.$emit with proper parent-child communication
// Parent component
<template>
  <child-component @custom-event="handleEvent" />
</template>

// Child component
methods: {
  triggerEvent() {
    this.$emit('custom-event', data)
  }
}
```

**Option 2: Provide/Inject Pattern**
```javascript
// Create a composable for shared events
// src/composables/useEventBus.js
import { reactive } from '@vue/composition-api'

const eventBus = reactive({
  events: {}
})

export function useEventBus() {
  const emit = (event, data) => {
    if (eventBus.events[event]) {
      eventBus.events[event].forEach(callback => callback(data))
    }
  }
  
  const on = (event, callback) => {
    if (!eventBus.events[event]) {
      eventBus.events[event] = []
    }
    eventBus.events[event].push(callback)
  }
  
  const off = (event, callback) => {
    if (eventBus.events[event]) {
      const index = eventBus.events[event].indexOf(callback)
      if (index > -1) {
        eventBus.events[event].splice(index, 1)
      }
    }
  }
  
  return { emit, on, off }
}
```

### 1.3 Composition API Introduction
**Priority: Medium** | **Risk: Low** | **Impact: High**

Install and start using Vue 2 Composition API for new components.

**Installation:**
```bash
yarn add @vue/composition-api
```

**Setup:**
```javascript
// src/main.js
import Vue from 'vue'
import VueCompositionAPI from '@vue/composition-api'

Vue.use(VueCompositionAPI)
```

**Usage Example:**
```vue
<template>
  <div>
    <p>Count: {{ count }}</p>
    <button @click="increment">+</button>
  </div>
</template>

<script>
import { ref, computed } from '@vue/composition-api'

export default {
  setup() {
    const count = ref(0)
    const doubleCount = computed(() => count.value * 2)
    
    const increment = () => {
      count.value++
    }
    
    return {
      count,
      doubleCount,
      increment
    }
  }
}
</script>
```

## Phase 2: Code Modernization (3-4 weeks)

### 2.1 Remove Deprecated Patterns
**Priority: High** | **Risk: Low** | **Impact: Medium**

**A. Replace vue-resource with axios**
```bash
yarn remove vue-resource
yarn add axios
```

```javascript
// Create axios instance
// src/services/api.js
import axios from 'axios'

const api = axios.create({
  baseURL: process.env.VUE_APP_API_URL,
  timeout: 10000,
})

export default api
```

**B. Update main.js to remove vue-resource**
```javascript
// Remove
import VueResource from "vue-resource"
Vue.use(VueResource)

// Add
import api from '@/services/api'
Vue.prototype.$http = api
```

**C. Update service files**
```javascript
// Before (vue-resource)
export function fetchData(context) {
  return Vue.http.get('/api/data')
}

// After (axios)
import api from '@/services/api'
export function fetchData() {
  return api.get('/api/data')
}
```

### 2.2 Component Pattern Modernization
**Priority: Medium** | **Risk: Low** | **Impact: Medium**

**A. Standardize Component Structure**
```vue
<template>
  <!-- Use semantic HTML -->
  <div class="component-name">
    <header class="component-name__header">
      <!-- Header content -->
    </header>
    <main class="component-name__content">
      <!-- Main content -->
    </main>
  </div>
</template>

<script>
export default {
  name: 'ComponentName', // Always include name
  props: {
    // Define props with validation
    title: {
      type: String,
      required: true
    }
  },
  emits: ['update', 'close'], // Explicit emits (Vue 3 preparation)
  data() {
    return {
      // Local state
    }
  },
  computed: {
    // Computed properties
  },
  methods: {
    // Methods
  }
}
</script>

<style scoped>
/* Use scoped styles */
.component-name {
  /* Component styles */
}
</style>
```

**B. Remove jQuery Dependencies**
```javascript
// Before
mounted() {
  $(this.$refs.modal).modal('show')
}

// After - Use Vue refs and events
mounted() {
  this.showModal()
},
methods: {
  showModal() {
    // Use Vue way or vanilla JS
    this.$refs.modal.style.display = 'block'
  }
}
```

### 2.3 Improve Component Organization
**Priority: Medium** | **Risk: Low** | **Impact: Medium**

**A. Create Composables Directory**
```
src/
├── composables/
│   ├── useAuth.js
│   ├── useApi.js
│   ├── useLocalStorage.js
│   └── useEventBus.js
```

**B. Separate Business Logic**
```javascript
// src/composables/useAuth.js
import { ref, computed } from '@vue/composition-api'
import { useUserStore } from '@/stores/user'

export function useAuth() {
  const userStore = useUserStore()
  const isLoading = ref(false)
  
  const isLoggedIn = computed(() => userStore.isLoggedIn)
  const user = computed(() => userStore.playerInfo)
  
  const login = async (credentials) => {
    isLoading.value = true
    try {
      await userStore.doLogin(credentials)
    } finally {
      isLoading.value = false
    }
  }
  
  return {
    isLoggedIn,
    user,
    login,
    isLoading
  }
}
```

## Phase 3: Build System Preparation (1-2 weeks)

### 3.1 Update Build Dependencies
**Priority: Medium** | **Risk: Medium** | **Impact: Medium**

**A. Update Vue CLI (Stay on Vue 2)**
```bash
# Update to latest Vue CLI 4.x (Vue 2 compatible)
yarn global add @vue/cli@^4.5.0
yarn add @vue/cli-service@^4.5.0
```

**B. Update Webpack Configuration**
```javascript
// vue.config.js - Prepare for Vue 3
module.exports = {
  chainWebpack: (config) => {
    // Future-proof configurations
    config.resolve.alias.set('@', path.resolve(__dirname, 'src'))
    
    // Prepare for Vue 3 SFC compilation
    config.module
      .rule('vue')
      .use('vue-loader')
      .tap(options => {
        options.compilerOptions = {
          preserveWhitespace: false
        }
        return options
      })
  }
}
```

**C. Update ESLint Configuration**
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    'plugin:vue/recommended', // Start with recommended rules
    '@vue/prettier'
  ],
  rules: {
    // Prepare for Vue 3 patterns
    'vue/no-deprecated-slot-attribute': 'error',
    'vue/no-deprecated-slot-scope-attribute': 'error',
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/no-v-html': 'warn' // Security preparation
  }
}
```

### 3.2 Performance Optimizations
**Priority: Medium** | **Risk: Low** | **Impact: High**

**A. Implement Code Splitting**
```javascript
// src/router/index.js - Add route-level code splitting
const routes = [
  {
    path: '/desktop',
    component: () => import(/* webpackChunkName: "desktop" */ '@/views/desktop/index.vue')
  },
  {
    path: '/tournament2',
    component: () => import(/* webpackChunkName: "tournament" */ '@/tournament2/views/index.vue')
  }
]
```

**B. Optimize Bundle Size**
```javascript
// vue.config.js
module.exports = {
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial'
          }
        }
      }
    }
  }
}
```

## Phase 4: Testing and Documentation (2-3 weeks)

### 4.1 Testing Framework Updates
**Priority: High** | **Risk: Low** | **Impact: High**

**A. Update Testing Dependencies**
```bash
# Update to latest Vue 2 compatible versions
yarn add -D @vue/test-utils@^1.3.0
yarn add -D vue-jest@^3.0.7
```

**B. Create Component Test Templates**
```javascript
// tests/unit/components/Button.spec.js
import { shallowMount } from '@vue/test-utils'
import Button from '@/components/ui/Button.vue'

describe('Button.vue', () => {
  it('renders props.title when passed', () => {
    const title = 'Click me'
    const wrapper = shallowMount(Button, {
      propsData: { title }
    })
    expect(wrapper.text()).toMatch(title)
  })
  
  it('emits click event when clicked', () => {
    const wrapper = shallowMount(Button)
    wrapper.find('button').trigger('click')
    expect(wrapper.emitted().click).toBeTruthy()
  })
})
```

### 4.2 Documentation Updates
**Priority: Medium** | **Risk: Low** | **Impact: Medium**

**A. Component Documentation**
```vue
<!-- src/components/ui/Button.vue -->
<template>
  <button 
    :class="classes"
    :disabled="disabled"
    @click="$emit('click', $event)"
  >
    <slot>{{ title }}</slot>
  </button>
</template>

<script>
/**
 * Button component for user interactions
 * @displayName Button
 * @example
 * <Button title="Click me" @click="handleClick" />
 */
export default {
  name: 'Button',
  props: {
    /** Button text */
    title: {
      type: String,
      default: ''
    },
    /** Button variant */
    variant: {
      type: String,
      default: 'primary',
      validator: value => ['primary', 'secondary', 'danger'].includes(value)
    },
    /** Disabled state */
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: {
    /** Emitted when button is clicked */
    click: (event) => event instanceof Event
  }
}
</script>
```

## Phase 5: Migration Readiness (1 week)

### 5.1 Pre-Migration Checklist
**Priority: High** | **Risk: Low** | **Impact: High**

- [ ] All stores migrated to Pinia
- [ ] Event bus completely replaced
- [ ] Vue-resource replaced with axios
- [ ] Composition API introduced in new components
- [ ] Component patterns standardized
- [ ] Testing framework updated
- [ ] Build system optimized
- [ ] Documentation updated
- [ ] Performance baseline established

### 5.2 Migration Branch Setup
```bash
# Create migration branch
git checkout -b vue3-migration
git push -u origin vue3-migration

# Create backup branch
git checkout main
git checkout -b vue2-backup
git push -u origin vue2-backup
```

### 5.3 Final Preparation Steps
1. **Audit remaining Vue 2 specific code**
2. **Update dependencies to latest Vue 2 compatible versions**
3. **Run comprehensive testing**
4. **Document current functionality**
5. **Set up monitoring for performance baselines**

## Timeline Summary

| Phase | Duration | Key Tasks | Risk Level |
|-------|----------|-----------|------------|
| Phase 1 | 2-3 weeks | Pinia migration, Event bus replacement, Composition API | Low |
| Phase 2 | 3-4 weeks | Code modernization, Pattern updates | Low |
| Phase 3 | 1-2 weeks | Build system preparation | Medium |
| Phase 4 | 2-3 weeks | Testing and documentation | Low |
| Phase 5 | 1 week | Migration readiness | Low |

**Total: 9-13 weeks**

## Benefits of This Approach

1. **Reduced Migration Risk**: Major patterns already modernized
2. **Incremental Progress**: Can be done alongside regular development
3. **Improved Code Quality**: Modern patterns and better organization
4. **Performance Gains**: Optimizations benefit Vue 2 immediately
5. **Team Learning**: Gradual introduction of Vue 3 concepts
6. **Easier Testing**: Modern testing patterns established

## Success Metrics

- [ ] 100% Vuex stores migrated to Pinia
- [ ] Event bus completely eliminated
- [ ] Vue-resource dependency removed
- [ ] 50%+ of new components using Composition API
- [ ] Test coverage maintained or improved
- [ ] Bundle size reduced by 10-15%
- [ ] Build time improved by 20%+

---

*This preparation plan reduces the actual Vue 3 migration from 13-17 weeks to 6-8 weeks by doing the groundwork in Vue 2 first.* 