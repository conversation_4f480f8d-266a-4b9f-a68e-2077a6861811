{"name": "sportsbook", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint src", "analyze": "vue-cli-service build --report"}, "dependencies": {"@intlify/vue-i18n-loader": "^4.2.0", "@vue/compiler-dom": "^3.2.45", "core-js": "^3.27.1", "dayjs": "^1.8.28", "dayjs-ext": "^2.2.0", "fast-deep-equal": "^3.1.3", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "numeral": "^2.0.6", "pako": "^2.1.0", "sass": "1.69", "sweetalert2": "^9.10.0", "swiper": "5.x", "vue": "^2.6.10", "vue-bootstrap-datetimepicker": "^5.0.1", "vue-i18n": "8", "vue-meta": "^2.4.0", "vue-plain-pagination": "^0.3.0", "vue-resource": "^1.5.1", "vue-router": "^3.0.7", "vue-slider-component": "^3.2.15", "vue-snotify": "^3.2.1", "vue-style-loader": "4.1.3", "vue-sweetalert2": "^3.0.3", "vue-tour": "^2.0.0", "vuelidate": "^0.7.4", "vuex": "3", "vuex-persist": "^3.1.3", "vuex-shared-mutations": "^1.0.2"}, "devDependencies": {"@kazupon/vue-i18n-loader": "^0.5.0", "@types/minimatch": "3", "@volar/vue-language-plugin-pug": "^1.6.5", "@vue/cli-plugin-babel": "^3.9.2", "@vue/cli-plugin-e2e-cypress": "^3.9.0", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-unit-jest": "^3.9.0", "@vue/cli-service": "^3.9.3", "@vue/eslint-config-prettier": "^5.0.0", "@vue/language-plugin-pug": "^2.0.19", "@vue/test-utils": "^1.0.0-beta.29", "babel-core": "^6.26.3", "babel-eslint": "^10.1.0", "babel-jest": "^24.8.0", "babel-plugin-transform-imports": "1.5.0", "critters-webpack-plugin": "^3.0.2", "eslint": "7", "eslint-plugin-vue": "7", "image-webpack-loader": "^8.1.0", "imagemin": "^9.0.1", "imagemin-cli": "^8.0.0", "imagemin-gifsicle": "^7.0.0", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "imagemin-svgo": "^11.0.1", "lint-staged": "^9.2.0", "sass-loader": "10.4.1", "thread-loader": "2.1.3", "vue-cli-plugin-pug": "^2.0.0", "vue-template-compiler": "^2.6.10", "webpack-bundle-analyzer": "^4.10.2", "yorkie": "^2.0.0"}, "resolutions": {"minimatch": "^3.0.5"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": ["git add"], "*.vue": ["git add"]}}