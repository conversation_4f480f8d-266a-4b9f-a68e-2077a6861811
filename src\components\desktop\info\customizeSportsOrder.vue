<template lang="pug">
.col-12.setting-right.py-3
  .w-100.mx-auto
    .font-weight-bold.text-account.normal {{ $t("message.change_sports_menu_order") }}
    hr
    fieldset.number-list
      ul#sortable
        li(:id="index" v-for="index in order" :key="index" v-if="!defaultExclude.includes(index)")
          .form.drag
            //- img(:src="'/v1/images/icon-sport-svg/' + index + '.png'" width="13" v-if="['41'].includes(index.toString())")
            img(:src="'/v1/images/icon-sport-svg/' + index + '.svg'" width="15")
            span(:title="sports[index]") {{ sports[index] }} ({{ index }})
    .text-center.mb-2.mt-4
      SpinButton(type="button" @click="save" :loading="loading" css="btn-primary btn-result active w-25" :text="$t('ui.save')").mr-1
      SpinButton(type="button" @click="restore" :loading="loading" css="btn-primary btn-result w-25" :text="$t('ui.restore')")
</template>

<script>
import SpinButton from "@/components/ui/SpinButton";
import config from "@/config";
import errors from "@/errors";

export default {
  components: {
    SpinButton
  },
  data: () => ({
    loading: false,
    order: []
  }),
  computed: {
    sports() {
      return this.$store.state.layout.sports;
    },
    defineOrder() {
      return this.$store.state.layout.order.sports;
    },
    defaultOrder() {
      var result = this.$store.state.layout.order.default.sports;
      return result;
    },
    defaultExclude() {
      return config.defaultSportExclude;
    }
  },
  mounted() {
    this.initial();

    // console.log(Object.keys(this.sports).map(Number));
  },
  methods: {
    save() {
      this.$store.dispatch("layout/setOrder", { property: "sports", value: this.order }).then(res => {
        this.$store
          .dispatch("user/setSettings", {
            page_display: JSON.stringify(this.$store.state.layout.pageDisplay),
            sports_order: JSON.stringify(this.$store.state.layout.order.sports),
            betting: JSON.stringify(this.$store.state.layout.betting)
          })
          .then(
            res => {
              this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.succeed"), "success");
            },
            err => {
              this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.failed"), "error");
            }
          );
      });
    },
    restore() {
      this.order = Object.keys(this.sports).map(Number);
      if (this.defaultOrder.length != this.sports.length) {
        this.order = Object.keys(this.sports).map(Number);
      } else {
        this.order = this.defaultOrder;
      }
      // this.order = [1, 2, 3, 4];
      $("#sortable").sortable("refreshPositions");
      this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.succeed"), "success");
    },
    initial() {
      if (this.defineOrder.length > 0) {
        if (this.defineOrder.length != this.sports.length) {
          this.order = Object.keys(this.sports).map(Number);
        } else {
          this.order = this.defineOrder;
        }
      } else {
        if (this.defaultOrder.length != this.sports.length) {
          this.order = Object.keys(this.sports).map(Number);
        } else {
          this.order = this.defaultOrder;
        }
      }
      $("#sortable").sortable({
        zIndex: 0
      });
      $("#sortable").disableSelection();
      $("#sortable").sortable({
        update: (event, ui) => {
          this.order = $("#sortable").sortable("toArray");
        }
      });
    }
  }
};
</script>
