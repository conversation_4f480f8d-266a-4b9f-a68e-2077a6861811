<template lang="pug">
.bet-info.parlay-info(:class="{ 'changed': betslip.oddsChanged, 'grey': betslip.marketType != 3, 'live': betslip.marketType == 3 }")
  .head.d-flex
    .head-btn(@click="handleCloseBet(betslip.matchId)")
      i.fal.fa-times
    .head-text.flex-fill.text-ellipsis
      span.ml-1.text-ellipsis(:title="sports[betslip.sportsType] +' - '+betTypeDisplay()") {{ sports[betslip.sportsType] }} - {{ betTypeDisplay() }}
  .bet-detail.bet-parlay
    .name(
      v-if="ballDisplay != null"
      :class="[betslip.betStatus == 'false' ? 'cancelBet':'', ((betslip.giving == 0 && betslip.homeAway == 2) ? 'red' : (betslip.giving == 1 && betslip.homeAway == 1) ? 'red' : '')]"
      )
      | {{ betslip.betDisplay }}
    .oddsdetail(:class="[betslip.betStatus == 'false' ? 'cancelBet': '']")
      .d-flex.justify-content-start
        .selector-name
          span(:class="{ red : ballDisplay.ball_display < 0 }") {{ ballDisplay.ball_display }}
          span(:class="{ red : ballDisplay.criteria2 < 0 }") ({{ ballDisplay.criteria2 }})
        .selector-name(v-if="ballDisplay == null") {{ betslip.betDisplay }}
        .selector-score(v-if="betslip.marketType == 3 && betslip.score != ''") [{{ betslip.score }}]
        .selector-other @
        .selector-odds {{ beton }}
    .team
      span {{ betslip.homeName }} vs {{ betslip.awayName }}
</template>

<script>
import config from "@/config";
import naming from "@/library/_name";
import { setTimeout } from 'timers';

export default {
  props: {
    betslip: {
      type: Object
    }
  },
  computed: {
    debug() {
      return config.debugMode;
    },
    beton() {
      return this.$t("m.BT_" + this.betslip.beton);
    },
    betType() {
      return this.$t("m.BT_" + this.betslip.betType);
    },
    ballDisplay() {
      return this.betslip.fact;
    },
    isBallDisplay() {
      var result = this.ballDisplay;
      if (["HDP", "HDPH"].includes(this.betslip.betType)) {
        return result != null && result != "0";
      } else {
        return false;
      }
    },
    sports() {
      return this.$store.state.layout.sports;
    }
  },
  mounted(){
    setTimeout(()=>{
      $(document).ready(function(){
        $('[data-toggle="tooltip"]').tooltip();
      });
    },10)
  },
  methods: {
    handleCloseBet(e) {
      this.$emit("handleCloseBet", e);
    },
    betTypeDisplay() {
      return this.$t("m.BS_" + this.betslip.betType);
    },
    showDetail(){
      // setTimeout(()=>{
      //   $('[data-toggle="tooltip"]').tooltip();
      //   if($('.tooltip .tooltip-inner').html() != undefined){
      //     $('.tooltip .tooltip-inner').html($('.tooltip .tooltip-inner').html().replace(/\[br\]/g,"<br />"))
      //   }else{
      //     this.showDetail();
      //   }
      // },50)
    }
  }
};
</script>
<style lang="scss" scoped>
.cancelBet{
  text-decoration: line-through;
}
</style>