<template lang="pug">
.e-group
  //- Racing Sports
  template(v-if="racingList.includes(sportsType)")
    //- 45, 46, 49, 50, 51
    template(v-if="racingList1.includes(sportsType)")
      //- 45, 46, 49
      template(v-for="(g1, g2) in group")
        efItem3(:source="g1" :index="parseInt(g2)" :key="g2")
    template(v-else)
      //- 50, 51
      template(v-if="racingList2.includes(sportsType)" v-for="(g1, g2) in group")
        efItem4(:source="g1" :index="parseInt(g2)" :key="g2")
      template(v-if="racingList3.includes(sportsType)" v-for="(g1, g2) in group")
        efItem5(:source="g1" :index="parseInt(g2)" :key="g2")
  template(v-else)
    template(v-for="(g1, g2) in group")
      efItem2(:source="g1" :index="parseInt(g2)" :key="g2")
</template>

<script>
import config from '@/config';
export default {
  components: {
    efItem2: () => import("@/components/desktop/main/efItem2.vue"),
    efItem3: () => import("@/components/desktop/main/efItem3.vue"),
    efItem4: () => import("@/components/desktop/main/efItem4.vue"),
    efItem5: () => import("@/components/desktop/main/efItem5.vue"),
  },
  props: {
    source: {
      type: Array
    },
    sportsType: {
      type: Number
    }
  },
  data() {
    return {};
  },
  computed: {
    racingList() {
      return config.racingList;
    },
    racingList1() {
      return config.racingList1;
    },
    racingList2() {
      return config.racingList2;
    },
    racingList3() {
      return config.racingList3;
    },
    group() {
      var g = {};
      for (var i = 0; i < this.source.length; i++) {
        if (!g.hasOwnProperty(this.source[i].matchBody[9])) {
          g[this.source[i].matchBody[9]] = {
            items: [],
            teams: []
          };
        }

        g[this.source[i].matchBody[9]].items.push(this.source[i]);
        g[this.source[i].matchBody[9]].teams.push(this.source[i].homeTeam);
      }
      return g;
    }
  },
  mounted() {
    // console.log(this.group);
  },
  methods: {}
};
</script>
