<template lang="pug">
.game-row.mb-2
  //- small {{ details['hdp'][0][1] }}
  .game-cell.game-cell-sm.w-40.justify-content-start
    img(src="/v1/images/esports/crown/no1.svg")
  .game-cell.game-cell-sm.flex-fill.justify-content-start
    .game-group
      .game-group-top.game-group-top-1(v-html="$t('ui.no1')")
  .game-cell.game-cell-sm.w-152.bg-01.ef-bet.ef-gh
    template(v-if="details['ou'] != null && details['ou'][0] != null && details['ou'][0][11] != 0 && details['ou'][0][12] != 0 && details['ou'][0][11] != '' && details['ou'][0][12] != ''")
      oddsItem(:odds="details['ou'][0]" idx=12 :typ="oddsType" dataType="1")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-152.bg-01.ef-bet.ef-gh
    template(v-if="details['ou'] != null && details['ou'][0] != null && details['ou'][0][11] != 0 && details['ou'][0][12] != 0 && details['ou'][0][11] != '' && details['ou'][0][12] != ''")
      oddsItem(:odds="details['ou'][0]" idx=11 :typ="oddsType" dataType="1")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-152.bg-01.ef-bet.ef-gh
    template(v-if="details['oe'] != null && details['oe'][0] != null && details['oe'][0][5] != 0 && details['oe'][0][7] != 0 && details['oe'][0][5] != '' && details['oe'][0][7] != ''")
      oddsItem(v-if="details['oe'][0][5] != 0" :odds="details['oe'][0]" cls="" idx=5 :typ="oddsType" dataType="2")
    template(v-else)
      .fad.fa-lock-alt.text-muted
  .game-cell.game-cell-sm.w-152.bg-01.ef-bet.ef-gh
    template(v-if="details['oe'] != null && details['oe'][0] != null && details['oe'][0][5] != 0 && details['oe'][0][7] != 0 && details['oe'][0][5] != '' && details['oe'][0][7] != ''")
      oddsItem(v-if="details['oe'][0][7] != 0" :odds="details['oe'][0]" cls="" idx=7 :typ="oddsType" dataType="2")
    template(v-else)
      .fad.fa-lock-alt.text-muted
</template>

<script>
import mixinHDPOUOdds from "@/library/mixinHDPOUOdds";

export default {
  components: {
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem")
  },
  mixins: [mixinHDPOUOdds],
  props: {
    source: {
      type: Object
    },
    player: {
      type: Number
    }
  },
  computed: {
    team() {
      var n = this.source.homeTeam.split(" - ");
      return n;
    }
  },
  methods: {
    getImage() {
      return "images/esports/marble-number/ball" + String(this.team[0]) +".svg";
    }
  }
};
</script>
