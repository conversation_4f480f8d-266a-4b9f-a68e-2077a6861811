<template lang="pug">
#bet-parlay-mmo-panel.magicY.bet-mmo.bet-parlay-scroll(v-if="isDataExists")
  .new-betslip
    .betslip-content
      template(v-for="(item, index) in betparlaymmo")
        betInfoParlayMMO(:betslip="item", :key="'bi-' + item.matchId", @handleCloseBet="handleCloseBet")
        //- matchInfoParlayMMO(:betslip="item" :key="'mi-' + item.matchId")
      .bet-infosub
        .m-1(v-if="isPlaceBet && !betConfirm")
          label(@click="handleAcceptAnyOdds")
            input(name="odds", type="checkbox", :checked="betting.acceptAnyOdds == 'true'")
            span.text(@click="handleAcceptAnyOdds") {{ $t('ui.accept_any_odds') }}
        .warning.m-1(v-if="oddsIsUpdating")
          i.fal.fa-exclamation-circle.text-danger.mr-1
          span {{ $t('error.oddsIsUpdating') }}
          .clearfix
        .warning.m-1(v-if="errorMessage")
          i.fal.fa-times-circle.text-danger.mr-1
          span {{ errorMessage }}
          .clearfix
      template(v-if="isPlaceBet && !betConfirm")
        .stake-field.py-1
          .d-flex
            .flex-fill.w-50.pl-1(style="padding-top: 3px; font-weight: bold") {{ currency_code }}
            div
              StakeInput(v-model="stake", @handleStake="handleStake", ref="stake", :loadbet="loading.check")
    template(v-if="isPlaceBet")
      template(v-if="!betConfirm")
        .p-1
          table.table-entry(width="100%")
            tbody
              tr
                td(width="50%") {{ $t('ui.payout') }}
                td.text-right(width="50%") {{ $numeral(payout).format('0,0.00[0]') }}
              tr
                td(width="50%") {{ $t('ui.mix_parlay') }}
                td.text-right(width="50%") {{ $numeral(multi).format('0,0.00[0]') }}
              tr
                td {{ $t('ui.min') }}
                td.text-right {{ $numeral(minBet).format('0,0') }}
              tr
                td {{ $t('ui.max') }}
                //- td.text-right {{ $numeral(maxBet).format("0,0") }} {{ maxBet }}
                td.text-right {{ maxBet }}
        .stake.pt-0.mt-0
          .d-flex.justify-content-around
            div
              SpinButton(css="btn btn-block btn-cancel btn-sm btn-secondary", @click="cancelBetClick", :text="$t('ui.cancel')", :loading="loading.cancel")
            .ml-1.flex-fill
              SpinButton(css="btn btn-sm btn-block btn-process btn-warning text-ellipsis", @click="processConfirmBet", :text="$t('ui.process_bet')", :loading="loading.process")
      template(v-else)
        .p-1
          table.table-entry(width="100%")
            tbody
              tr
                td(width="50%") {{ $t('ui.afold') }}
                td.text-right(width="50%") {{ betparlaymmo.length }}
              tr
                td(width="50%") {{ $t('ui.stake') }}
                td.text-right(width="50%") {{ $numeral(stake).format('0,0.00') }}
              tr
                td(colspan="2")
                  i.fad.fa-check-circle.mr-1.text-success
                  span.text-dark {{ $t('message.confirm_bet') }}
        .stake.pt-0.mt-0
          .d-flex.justify-content-around
            div
              SpinButton(css="btn btn-block btn-cancel btn-sm btn-secondary", @click="betConfirm = false", :text="$t('ui.no')", :loading="loading.cancel")
            .ml-1.flex-fill
              SpinButton(css="btn btn-sm btn-block btn-process btn-warning text-ellipsis", @click="processBet", :text="$t('ui.yes')", :loading="loading.process")
</template>

<script>
import { EventBus } from "@/library/_event-bus.js";
import xhrBet from "@/library/_xhr-betmmo.js";
import calc from "@/library/_calculation.js";
import config from "@/config";
import betInfoParlayMMO from "@/components/desktop/left/betInfoParlayMMO";
import matchInfoParlayMMO from "@/components/desktop/left/matchInfoParlayMMO";
import StakeInput from "@/components/desktop/left/stakeInput";
import SpinButton from "@/components/ui/SpinButton";
import mixinBetType from "@/library/mixinBetType";
import naming from "@/library/_name";

export default {
  components: {
    betInfoParlayMMO,
    matchInfoParlayMMO,
    SpinButton,
    StakeInput,
  },
  mixins: [mixinBetType],
  data() {
    return {
      errorMessage: "",
      loading: {
        check: false,
        cancel: false,
        process: false,
      },
      league: {},
      match: {},
      child: {},
      defaultCounter: 10,
      counter: 10,
      placeSlip: {},
      stake: null,
      payout: null,
      invalidOdds: false,
      oddsIsUpdating: false,
      autoCloseOddsIsUpdating: null,
      parlayMatch: {},
      betConfirm: false,
      isMMO: false,
    };
  },
  computed: {
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
    commType() {
      return this.$store.getters.commType;
    },
    multi() {
      var result = 1;
      for (var n in this.betparlaymmo) {
        result = result * 2; //- parseFloat(this.betparlaymmo[n].val);
      }
      var f = parseFloat(calc.fm(result, false, 4)).toFixed(3);
      // console.log(f, result);
      return f;
    },
    maxBet() {
      // var max = calc.truncateInteger(this.maxParlayPayout / this.multi);

      var c = Object.keys(this.betparlaymmo).length;
      var d = 0.85;
      if (c > 3) {
        d = 0.8;
      }

      var max = calc.truncateInteger(this.maxParlayPayout / this.multi / d);
      for (var n in this.betparlaymmo) {
        if (max > parseFloat(this.betparlaymmo[n].maxBet)) {
          max = parseFloat(this.betparlaymmo[n].maxBet);
        }
      }

      // var a = max / d;
      // var b = a.toString();
      // var q = b.indexOf(".");
      // if (q >= 0) {
      //   b = b.slice(0, b.indexOf("."));
      // }

      return max;
    },
    minBet() {
      var result = 0;
      for (var n in this.betparlaymmo) {
        if (result == 0) {
          result = this.betparlaymmo[n].minBet;
        } else {
          if (result < this.betparlaymmo[n].minBet) {
            result = this.betparlaymmo[n].minBet;
          }
        }
      }
      return result;
    },
    maxPayout() {
      var result = 0;
      for (var n in this.betparlaymmo) {
        if (result == 0) {
          result = this.betparlaymmo[n].maxPayout;
        } else {
          if (result < this.betparlaymmo[n].maxPayout) {
            result = this.betparlaymmo[n].maxPayout;
          }
        }
      }
      return result;
    },
    maxParlayPayout() {
      var result = 0;
      for (var n in this.betparlaymmo) {
        if (result == 0) {
          result = this.betparlaymmo[n].maxParlayPayout;
        } else {
          if (result > this.betparlaymmo[n].maxParlayPayout) {
            result = this.betparlaymmo[n].maxParlayPayout;
          }
        }
      }
      return result;
    },
    debug() {
      return config.debugMode;
    },
    betparlaymmo() {
      var tempOrder = [];
      tempOrder = Object.values(this.$store.state.betparlaymmo.data).sort(function (
        a,
        b
      ) {
        return a.bettime < b.bettime ? -1 : a.bettime > b.bettime ? 1 : 0;
      });
      return tempOrder;
    },
    pageDisplay() {
      return this.$store.getters.pageDisplay;
    },
    betting() {
      return this.$store.getters.betting;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
    isDataExists() {
      if (Object.keys(this.betparlaymmo).length > 0) {
        return true;
      } else {
        return false;
      }
    },
    isPlaceBet() {
      if (Object.keys(this.betparlaymmo).length >= config.mmoParlayMinTicket) {
        return true;
      } else {
        return false;
      }
    },
    menu2() {
      return this.$store.getters.menu2;
    },
  },
  watch: {
    stake(newVal) {
      this.handlePayout();
    },
    multi(newVal) {
      this.handlePayout();
    },
  },
  destroyed() {
    clearInterval(this.autoRefresh);
  },
  mounted() {
    setInterval(this.autoRefresh, this.defaultCounter * 1000);

    EventBus.$on("BETPARLAYMMO", this.runbet);
    EventBus.betParlayMMO = this.triggerRunBet;
    EventBus.closeParlayMMO = this.handleCloseBet;
    EventBus.cancelParlayMMO = this.cancelBet;
  },
  methods: {
    getBallDisplay(b, g, ha, bt) {
      return naming.ballDisplay2(b, g, ha, bt, this);
    },
    triggerRunBet(odds, typ, idx, val, bt, amt, isMMO, e, pos, giving) {
      if (EventBus.cancelParlay) EventBus.cancelParlay();
      EventBus.$emit("BETPARLAYMMO", odds, typ, idx, val, bt, amt, isMMO, e, pos, giving);
    },
    existsInSlip(match_id) {
      return Object.keys(this.$store.state.betparlaymmo.data).includes(match_id);
    },
    runbet(odds, typ, idx, val, bt, amt, isMMO, e, pos, giving) {
      // console.log("runbetBetParlayMMO", odds, isMMO);

      if (!isMMO && !this.mmoMode) {
        this.$helpers.showDialog(
          this.$t("ui.action"),
          this.$t("error.invalidCurrency"),
          "error"
        );
        return;
      }
      if (EventBus.cancelParlay) EventBus.cancelParlay();
      this.isMMO = isMMO;
      // console.log("betParlayMMO", odds, typ, idx, val, bt, amt, isMMO, e, pos, giving);

      this.showMe();

      if (!this.existsInSlip(odds[1].toString())) {
        if (
          Object.keys(this.$store.state.betparlaymmo.data).length + 1 >
          config.mmoParlayMaxTicket
        ) {
          this.$helpers.showDialog(
            this.$t("ui.action"),
            this.$t("error.maxParlayTicket"),
            "info"
          );
          return;
        }
      }

      this.stake = amt;
      this.handleStake();
      this.checkParlayBet(odds, typ, idx, val, bt, e, pos, giving);
      this.xFocus();
      clearTimeout(this.autoCloseOddsIsUpdating);
      this.invalidOdds = false;
      this.oddsIsUpdating = false;
    },
    cache() {
      return this.$store.getters.data;
    },
    processConfirmBet() {
      this.errorMessage = "";
      this.betConfirm = true;
      document.onkeyup = (event) => {
        if (event.which == 13 || event.keyCode == 13) {
          this.scrollToBottom();
          this.processBet();
        } else {
          if (event.which == 27 || event.keyCode == 27) {
            this.betConfirm = false;
          }
        }
      };
    },
    xFocusTouchEnd() {
      // if (this.$refs.stake) {
      //   if (this.$refs.stake.hasOwnProperty("focus")) {
      //     this.$refs.stake.focus();
      //   }
      // }
      // document.body.removeEventListener("touchend", this.xFocusTouchEnd);
    },
    xFocus() {
      // console.log("betparlaymmo_focus");
      EventBus.$emit("STAKE_FOCUS");

      // if (this.$refs.stake) {
      //   if (this.$refs.stake.hasOwnProperty("focus")) {
      //     this.$refs.stake.focus();
      //   }
      //   document.body.addEventListener("touchend", this.xFocusTouchEnd);
      //   setTimeout(() => {
      //     if (this.$refs.stake) {
      //       if (this.$refs.stake.hasOwnProperty("focus")) {
      //         this.$refs.stake.focus();
      //       }
      //     }
      //   }, 1000);
      // }
    },
    scrollToBottom() {
      var e = document.getElementById("bet-parlay-panel");
      e.scrollTop = e.scrollHeight - e.clientHeight;
    },
    handleCloseBet(e) {
      this.$store.dispatch("betparlaymmo/removeData", e);
      this.parlayMatch[e] = 0;
    },
    processBet() {
      // if (this.loading.process == true) return;

      if (!this.betConfirm || Object.keys(this.betparlaymmo).length == 0) return;

      this.errorMessage = "";
      this.betConfirm = false;
      this.loading.process = true;
      var old = this.placeSlip;
      var validBet = true;
      this.placeSlip = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        bet_member: this.stake,
        accept_any_odds: this.betting.acceptAnyOdds,
        bet_detail: [],
        operator_type: this.$store.getters.operatorType,
        parent_id: this.$store.getters.parentId,
      };

      if (this.stake > this.maxBet) {
        this.$helpers.showDialog(
          this.$t("ui.action"),
          this.$t("error.betOverMaxBet"),
          "info"
        );
        this.loading.process = false;
        this.betConfirm = true;
        return;
      }

      for (var n in this.betparlaymmo) {
        var item = {};
        item["a"] = this.betparlaymmo[n].oddsId;
        item["b"] = this.betparlaymmo[n].subMatchId;
        item["c"] = this.betparlaymmo[n].matchId;
        item["d"] = this.betparlaymmo[n].betType;
        item["e"] = this.betparlaymmo[n].betTeamId;
        item["f"] = this.betparlaymmo[n].homeAway;
        item["g"] = parseFloat(this.betparlaymmo[n].val);
        item["h"] = parseFloat(this.betparlaymmo[n].origin);
        item["j"] = this.betparlaymmo[n].ballDisplay;
        item["k"] = this.betparlaymmo[n].fact.criteria2;
        this.placeSlip.bet_detail.push(item);
        if (this.betparlaymmo[n].betStatus == "false") {
          validBet = false;
        }
      }

      if (!validBet) {
        this.$helpers.showDialog(
          this.$t("ui.action"),
          this.$t("error.matchHide"),
          "info"
        );
        this.loading.process = false;
        this.betConfirm = true;
        return;
      }

      old = null;
      xhrBet.betParlay(this.placeSlip).then(
        (res) => {
          if (res.success) {
            // EventBus.$emit("GET_BALANCE");
            if (this.$store.state.layout.betting.defaultStake == "1") {
              this.$store.dispatch("layout/setSingleBetting", {
                property: "defaultStakeAmount",
                value: this.stake,
              });
            }
            if (res.data.pending == false || res.data.pending == undefined) {
              if (EventBus.betListAccept) {
                EventBus.betListAccept();
              }
            } else {
              if (EventBus.betListPending) {
                EventBus.betListPending();
              }
            }
            this.cancelBet();
            if (EventBus.getBalance) {
              EventBus.getBalance();
            }
          } else {
            this.$helpers.handleFeedback(res.status);
          }
        },
        (err) => {
          this.loading.process = false;
          switch (err.status) {
          case "insufficient_balance":
            this.errorMessage = this.$t("error.insufficient_balance");
            break;
          case "invalidOdds":
            this.invalidOdds = true;
            break;
          case "oddsIsUpdating":
            this.oddsIsUpdating = true;
            clearTimeout(this.autoCloseOddsIsUpdating);
            this.autoCloseOddsIsUpdating = setTimeout(() => {
              this.oddsIsUpdating = false;
            }, this.defaultCounter * 1000);
            break;
          default:
            this.$helpers.handleFeedback(err.status);
            break;
          }
        }
      );
    },
    handlePayout() {
      var c = Object.keys(this.betparlaymmo).length;
      var d = 0.85;
      if (c > 3) {
        d = 0.8;
      }
      var a = this.stake * this.multi * d;
      var b = this.maxBet * this.multi;
      if (a > b) {
        a = b;
      }
      // this.payout = parseFloat(calc.fm(a, false, 4)).toFixed(3);
      this.payout = a.toFixed(3);
    },
    handleStake() {
      if (this.stake % 1 != 0) {
        this.stake = Math.round(this.stake);
      }

      if (this.stake > this.maxBet) {
        this.stake = this.maxBet;
      } else {
        if (this.stake < this.minBet) {
          this.stake = this.minBet;
        }
      }
    },
    // checkMatchExists() {
    //   var result = true;
    //   if (this.betparlaymmo != null) {
    //     var cache = this.cache();
    //     var otflag1 = false;
    //     var otflag2 = false;

    //     if (cache != null && cache.hasOwnProperty("match")) {
    //       for (var n in this.betparlaymmo) {
    //         if (!cache.match.hasOwnProperty(this.betparlaymmo[n].matchId)) {
    //           this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: "false" });
    //         } else {
    //           // check odds exist if match have more than 1 ot
    //           if (cache.match.hasOwnProperty(this.betparlaymmo[n].matchId)) {
    //             if (cache.odds[1] != undefined) {
    //               if (cache.odds[1]["hdp"] != undefined) {
    //                 if (cache.odds[1]["hdp"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[1]["hdp"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[1]["hdp"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag1 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }

    //               if (cache.odds[1]["ou"] != undefined) {
    //                 if (cache.odds[1]["ou"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[1]["ou"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[1]["ou"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag1 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }

    //               if (cache.odds[1]["oe"] != undefined) {
    //                 if (cache.odds[1]["oe"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[1]["oe"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[1]["oe"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag1 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }

    //               if (cache.odds[1]["oxt"] != undefined) {
    //                 if (cache.odds[1]["oxt"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[1]["oxt"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[1]["oxt"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag1 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }

    //               if (cache.odds[1]["ml"] != undefined) {
    //                 if (cache.odds[1]["ml"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[1]["ml"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[1]["ml"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag1 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }

    //               if (cache.odds[1]["orz"] != undefined) {
    //                 if (cache.odds[1]["orz"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[1]["orz"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[1]["orz"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag1 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }
    //             }

    //             if (cache.odds[2] != undefined) {
    //               if (cache.odds[2]["hdp"] != undefined) {
    //                 if (cache.odds[2]["hdp"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[2]["hdp"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[2]["hdp"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag2 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }

    //               if (cache.odds[2]["ou"] != undefined) {
    //                 if (cache.odds[2]["ou"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[2]["ou"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[2]["ou"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag2 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }

    //               if (cache.odds[2]["oe"] != undefined) {
    //                 if (cache.odds[2]["oe"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[2]["oe"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[2]["oe"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag2 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }

    //               if (cache.odds[2]["oxt"] != undefined) {
    //                 if (cache.odds[2]["oxt"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[2]["oxt"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[2]["oxt"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag2 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }

    //               if (cache.odds[2]["ml"] != undefined) {
    //                 if (cache.odds[2]["ml"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[2]["ml"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[2]["ml"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag2 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }

    //               if (cache.odds[2]["orz"] != undefined) {
    //                 if (cache.odds[2]["orz"][this.betparlaymmo[n].matchId]) {
    //                   for (var m in cache.odds[2]["orz"][this.betparlaymmo[n].matchId]) {
    //                     if (cache.odds[2]["orz"][this.betparlaymmo[n].matchId][m][3] == this.betparlaymmo[n].oddsId) {
    //                       otflag2 = true;
    //                     }
    //                   }
    //                   this.$store.dispatch("betparlaymmo/setSingleData", { matchid: this.betparlaymmo[n].matchId, property: "betStatus", value: otflag1 });
    //                   if (otflag1) return result;
    //                 }
    //               }
    //             }
    //           }
    //         }
    //       }
    //     } else {
    //       result = false;
    //     }
    //   }
    //   return result;
    // },
    autoRefresh() {
      this.errorMessage = "";
      if (this.isDataExists) {
        if (
          (this.betting.autoRefreshOdds == true ||
            this.betting.autoRefreshOdds == "true") &&
          this.isDataExists
        ) {
          for (var n in this.betparlaymmo) {
            this.requestSingleBet(this.betparlaymmo[n], true);
          }
        }
      }
    },
    handleAutoRefresh() {
      this.$store.dispatch("layout/setSingleBetting", {
        property: "autoRefreshOdds",
        value: this.betting.autoRefreshOdds == "true" ? "false" : "true",
      });
      this.counter = this.defaultCounter;
    },
    handleAcceptAnyOdds() {
      this.$store.dispatch("layout/setSingleBetting", {
        property: "acceptAnyOdds",
        value: this.betting.acceptAnyOdds == "true" ? "false" : "true",
      });
    },
    refreshOddsYes() {
      this.invalidOdds = false;
      this.counter = this.defaultCounter;
      for (var n in this.betparlaymmo) {
        this.requestSingleBet(this.betparlaymmo[n]);
      }
    },
    refreshOddsNo() {
      this.invalidOdds = false;
    },
    getBetLimit() {
      return this.$store.getters.playerBetLimit["PARLAY"];
    },
    showMe() {
      $("#collapse-betslip").collapse("show");
      $("#pills-parlay-tab").tab("show");
    },
    cancelBetClick() {
      this.cancelBet();
      setTimeout(() => {
        if (config.vg1.includes(this.menu2)) {
          $("#collapse-vgames").collapse("show");
        } else {
          $("#collapse-allsports").collapse("show");
        }
      }, 100);
    },
    cancelBet() {
      // console.log("cancelBetMMO");
      clearTimeout(this.autoCloseOddsIsUpdating);
      this.invalidOdds = false;
      this.oddsIsUpdating = false;
      this.loading.cancel = true;
      this.loading.check = false;
      this.league = {};
      this.match = {};
      this.counter = this.defaultCounter;
      this.$store.dispatch("betparlaymmo/clearData").then(() => {
        this.loading.cancel = false;
        this.loading.process = false;
      });
    },
    checkParlayBet(odds, typ, idx, val, bt, e, pos, giving) {
      this.counter = this.defaultCounter;
      this.loading.check = true;
      var cache = this.cache();
      if (cache.hasOwnProperty("league") && cache.hasOwnProperty("match")) {
        var sd = {};

        sd.matchId = odds[1];
        sd.child = cache.child[sd.matchId];
        sd.parent = cache.parent[sd.matchId];

        if (sd.child != undefined) {
          for (var n in sd.child) {
            this.$store.dispatch("betparlaymmo/removeData", sd.child[n][0]);
          }
        }

        if (sd.parent != undefined) {
          this.$store.dispatch("betparlaymmo/removeData", sd.parent);
        }

        sd.typ = typ;
        sd.idx = idx;
        sd.val = val;
        sd.pos = pos;
        sd.beton = giving;

        sd.leagueId = odds[0];
        sd.subMatchId = odds[2];
        sd.oddsId = odds[3];
        sd.betType = odds[4];
        sd.target = bt;
        // sd.criteria1 = odds[6];
        // sd.criteria2 = odds[7];

        this.league = cache.league[sd.leagueId];
        this.match = cache.match[sd.matchId];

        sd.sportsType = this.league[1];
        // sd.leagueId = this.league[0];
        sd.leagueName = this.league[4];

        var limit = this.getBetLimit();
        sd.minBet = limit.min_bet;
        sd.maxBet = limit.max_bet;
        sd.maxPayout = limit.max_payout;
        sd.maxParlayPayout = 0;
        sd.marketType = odds[20]; // this.match[4];
        sd.homeName = this.match[5];
        sd.awayName = this.match[6];
        sd.workingDate = this.match[7];
        sd.matchTime = this.match[8];
        sd.homeId = this.match[22];
        sd.awayId = this.match[23];
        sd.score = this.match[11] ? this.match[11].toString().trim() : "";
        // sd.awayId = this.match[23];
        sd.typId = config.oddsTypeId[sd.typ];
        sd.ballDisplay = "";
        sd.betDisplay = "";

        this.setBetTypeMMO(sd, odds);
        this.requestSingleBet(sd, false);

        this.league = null;
        this.match = null;
      }
    },
    requestSingleBet(data, refresh) {
      if (data == null) {
        this.cancelBet("requestSingleBetNoData");
        return;
      }
      if (!data.betType || !data.oddsId || !data.sportsType) {
        this.cancelBet("requestSingleBetInvalid");
        return;
      }

      var slip = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        sports_type: data.sportsType,
        parlay: data.target == "parlay",
        odds_id: data.oddsId,
        submatch_id: data.subMatchId,
        bet_type: data.betType,
        bet_team_id: data.betTeamId,
        home_away: data.homeAway,
        odds_display: parseFloat(data.val),
        odds_mo: parseFloat(data.origin),
        odds_type: data.typId,
        ball_display: data.ballDisplay,
        criteria2: data.fact.criteria2,
        market_type: data.marketType,
      };

      var cache = this.cache();
      xhrBet.betSingleOddsCheck(slip).then(
        (res) => {
          if (res.success) {
            if (data != null) {
              var sd = Object.assign({}, data);

              sd.oddsChanged = res.data[0].odds_change;
              sd.ldiffChanged = res.data[0].ldiff_change;
              sd.ballChanged = res.data[0].ball_change;
              if (res.data[0].home_giving == true) {
                sd.giving = 1;
              } else {
                sd.giving = 0;
              }
              if (res.data[0].ball_display_new != null) {
                sd.ballChangedText = this.getBallDisplay(
                  sd.ballDisplay.toString(),
                  sd.giving,
                  sd.homeAway,
                  sd.betType
                );
                sd.ballDisplay = res.data[0].ball_display_new;
              }
              if (res.data[0].ldiff_new != null) {
                sd.ldiffChangedText = sd.fact.criteria2;
                sd.criteria2 = res.data[0].ldiff_ori;
                sd.fact = naming.ballDisplayMMO(
                  sd.ballDisplay.toString(),
                  sd.giving,
                  sd.homeAway,
                  sd.betType,
                  sd.criteria2
                );

                // sd.fact.criteria2 = res.data[0].ldiff_new;

                // var fact = {
                //   ball_display: sd.fact.ball_display,
                //   criteria2: res.data[0].ldiff_new,
                // };
                // sd.fact = fact;
              }
              if (res.data[0].odds_display_new != null) {
                sd.oddsChangedText = sd.val;
                sd.val = calc.fmtType(
                  res.data[0].odds_display_new,
                  this.commType,
                  sd.betType
                );
              }
              if (res.data[0].odds_new != null) {
                sd.origin = calc.fmt(res.data[0].odds_new);
              }
              if (sd.minBet < res.data[0].min_bet) {
                sd.minBet = res.data[0].min_bet;
              }
              if (sd.maxBet > res.data[0].max_bet) {
                sd.maxBet = res.data[0].max_bet;
              }
              sd.maxParlayPayout = res.data[0].max_parlay_payout;
              var flag = 1;
              if (!refresh) {
                Object.keys(this.parlayMatch).forEach(
                  function (i) {
                    if (i == sd.matchId) {
                      flag = 0;
                      sd.bettime = this.parlayMatch[sd.matchId];
                    }

                    if (i == sd.matchId && this.parlayMatch[sd.matchId] == 0) {
                      flag = 1;
                    }
                  }.bind(this)
                );

                if (flag) {
                  sd.bettime = new Date().getTime();
                  this.parlayMatch[sd.matchId] = sd.bettime;
                }
              }
              sd.betStatus = true;
              sd.multi = 2;
              this.$store.dispatch("betparlaymmo/setData", sd);
              this.loading.check = false;
              this.invalidOdds = false;
              // console.log(sd);
            }
          } else {
            this.$helpers.handleFeedback(res.status);
          }
        },
        (err) => {
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
  },
};
</script>
