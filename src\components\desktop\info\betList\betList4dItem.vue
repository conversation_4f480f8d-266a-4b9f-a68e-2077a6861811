<template lang="pug">
table.table-info(width='100%' :id="'bet-' + status + '-accordion'")
  tbody
    tr
      th(scope='col', width='4%').text-left {{ $t("ui.no/") }}
      th(scope='col', width='20%').text-left {{ $t("ui.date") }}
      th(scope='col', width='22%').text-left {{ $t("ui.choice") }}
      th(scope='col', width='37%').text-left {{ $t("ui.betting") }}
      th(scope='col', width='7%').text-right {{ $t("ui.stake") }}
      th(scope='col', width='10%').text-center {{ $t("ui.status") }}
    tr.grey(v-if="betList == 'undefined' || betList.length <= 0")
      td(colspan="6").text-center
        span {{ $t('message.no_information_available') }}
    tr(v-for="(item, index) in betList" :class="{ grey: index % 2 === 0 }")
      td.text-center(valign='top') {{ getNo(index) }}
      td.text-left(valign='top')
        div {{ $t("ui.ref_no") }}: {{ item.ticket_id }} / {{ item.bet_id }}
        div {{ $dayjs(item.bet_time).format("MM/DD/YYYY hh:mm:ss A") }}
      td.text-left(valign='top')
        .bet-info
          .bet-type.blue {{ $t("m.D_" + item.bet_type) }}
          .bet-detail.blue
            .name.red {{ item.comp_name }} - {{ item.num_type }}
            .oddsdetail
              .d-flex.justify-content-start
                .selector-name {{ $dayjs(item.match_date).format("MM/DD/YYYY") }}
      td.text-left(valign='top')
        .bet-info
          .font-weight-bold {{ item.number }}
          .bet-detail.blue
            .small(v-if="parseFloat(item.big) > 0")
              span {{ item.num_type == "4D" ? $t("m.D_BIG") : "AE" }}
              span  x {{ $numeral(item.big).format("0,0.00") }}
            .small(v-if="parseFloat(item.small) > 0")
              span {{ $t("m.D_SMALL") }}
              span  x {{ $numeral(item.small).format("0,0.00") }}
            .small(v-if="parseFloat(item.sa) > 0")
              span {{ item.num_type == "4D" ? "SA" : "3A" }}
              span  x {{ $numeral(item.sa).format("0,0.00") }}
            .small(v-if="parseFloat(item.sb) > 0")
              span B
              span  x {{ $numeral(item.sb).format("0,0.00") }}
            .small(v-if="parseFloat(item.sc) > 0")
              span C
              span  x {{ $numeral(item.sc).format("0,0.00") }}
            .small(v-if="parseFloat(item.sd) > 0")
              span D
              span  x {{ $numeral(item.sd).format("0,0.00") }}
            .small(v-if="parseFloat(item.se) > 0")
              span E
              span  x {{ $numeral(item.se).format("0,0.00") }}
            .small(v-if="parseFloat(item.aa) > 0")
              span AA
              span  x {{ $numeral(item.aa).format("0,0.00") }}
            .small(v-if="parseFloat(item.abc) > 0")
              span 3C
              span  x {{ $numeral(item.abc).format("0,0.00") }}
      td.text-right(valign='top')
        span(v-if="item.bet_member") {{ $numeral(item.bet_member).format("0,0.00") }}
      td.text-center(valign='top')
        div(v-if="item.bet_status") {{ $t("ui." + item.bet_status.toLowerCase()) }}
    tr.table-total
      td.text-right(colspan="4") {{ $t("ui.total") }}
      td.text-right {{ $numeral(calculateTotalBet).format("0,0.00") }}
      td
</template>

<script>
import config from "@/config";
import xhr from "@/library/_xhr-betlist.js";

export default {
  components: {
  },
  props: {
    betList: {
      type: Array,
      default: []
    },
    status: {
      type: String,
      default: "accept"
    },
    pageIndex: {
      type: Number
    },
    pageSize: {
      type: Number
    }
  },
  data() {
    return {
      parlayItems: [],
      loading: false
    };
  },
  computed: {
    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    },
    calculateTotalBet() {
      var total = 0;

      for (var n in this.betList) {
        total += this.betList[n].bet_member;
      }

      return total.toFixed(3);
    },
    language() {
      return this.$store.getters.language;
    }
  },
  methods: {
    getNo(e) {
      if (this.pageIndex == null) {
        return e + 1;
      } else {
        return ((this.pageIndex - 1) * this.pageSize) + (e + 1);
      }
    }
  }
};
</script>