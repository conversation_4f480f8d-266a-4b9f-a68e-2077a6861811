<template lang="pug">
.hl-content.d-flex.flex-column
  .hl-container
    .hl-filter
      .hl-filter-bar
        .slider-wrap
          slider(ref="slick")
            .hl-league-single.text-uppercase(@click="setLeague()", :class="{ active: selectedLeague == null }") {{ $t('ui.all') }}
            .hl-league-single(v-for="(item, index) in league", @click="setLeague(item)", :class="{ active: selectedLeague == item }")
              .hl-league-logo
                img(v-if="item.league_logo_path", :src="item.league_logo_path")
              .hl-league-name {{ getName('name_', item) }}
          a.hl-select-date(href="javascript:void(0);", data-toggle="dropdown", aria-haspopup="true", aria-expanded="false")
            .hl-logo-calendar.pointable
              i.fad.fa-calendar-alt
            .input-group
              .hl-date-picker
                span(v-if="selectedDate") {{ $dayjs(selectedDate).format('MM/DD') }} - {{ $dayjs(selectedDate).add(6, 'days').format('MM/DD') }}
                span(v-else) {{ $t('ui.all') }}
              .input-group-append
                span.input-group-text
                  #date-picker.fad.fa-chevron-circle-down
          .dropdown-menu.dropdown-menu-right(aria-labelledby="date-picker")
            a.dropdown-item(:class="{ active: selectedDate == null }", @click="setDated()") {{ $t('ui.all') }}
            a.dropdown-item(v-for="item in mondays", :class="{ active: selectedDate == item }", @click="setDated(item)")
              | {{ $dayjs(item).format('MM/DD') }} - {{ $dayjs(item).add(6, 'days').format('MM/DD') }}
  .hl-container.flex-fill.d-flex.flex-column
    .row.flex-fill
      .col-8.d-flex.flex-column
        .hl-video-left.fade-me-in(v-if="selectedHighlight")
          .hl-video-top
            player(v-if="isplay", ref="hlp", :src="selectedHighlight.video_link", @add="addPlayer", @remove="removePlayer", @play="playing")
            .live-video.brigther(v-else, @click="isplay = true")
              .play-btn
              .inner-logo(v-if="selectedHighlight.home_logo_path && selectedHighlight.away_logo_path")
                .inner-logo-team
                  img(v-if="selectedHighlight.home_logo_path", :src="selectedHighlight.home_logo_path")
                span vs
                .inner-logo-team
                  img(v-if="selectedHighlight.away_logo_path", :src="selectedHighlight.away_logo_path")
              .inner-match
                .hl-team-name {{ getName('home_name_', selectedHighlight) }}
                .inner-event
                  .hl-label {{ selectedHighlight.final_home }} : {{ selectedHighlight.final_away }}
                  .hl-label.small(v-if="selectedHighlight.half_home != null")
                    .d-block HT
                    .d-block {{ selectedHighlight.half_home }} : {{ selectedHighlight.half_away }}
                .hl-team-name.text-left {{ getName('away_name_', selectedHighlight) }}
          .hl-video-bottom.flex-fill
            .hl-match-team.d-flex.flex-column
              .hl-league-titlebar
                .hl-league-logo
                  img(v-if="selectedHighlight.league_logo_path", :src="selectedHighlight.league_logo_path")
                .hl-league-name {{ getName('name_', selectedHighlight) }}
              .hl-team-info.d-flex.flex-row.align-items-center.justify-content-center
                .hl-team.hl-team01
                  .hl-team-logo
                    img(v-if="selectedHighlight.home_logo_path", :src="selectedHighlight.home_logo_path")
                  .hl-team-name {{ getName('home_name_', selectedHighlight) }}
                .hl-event
                  .hl-match-time.d-flex.align-items-center.justify-content-center
                    .hl-date {{ $dayjs(selectedHighlight.match_time).format('MM/DD') }}
                    .hl-time {{ $dayjs(selectedHighlight.match_time).format('h:mmA') }}
                  .hl-match-score.d-flex.align-items-center.justify-content-center.flex-column
                    .hl-label {{ selectedHighlight.final_home }} : {{ selectedHighlight.final_away }}
                    .hl-score
                      .label HT
                      .score {{ selectedHighlight.half_home }} : {{ selectedHighlight.half_away }}
                .hl-team.hl-team02
                  .hl-team-logo
                    img(v-if="selectedHighlight.away_logo_path", :src="selectedHighlight.away_logo_path")
                  .hl-team-name {{ getName('away_name_', selectedHighlight) }}
            .hl-match-info
              .hl-title(v-html="getRemark('remark_', selectedHighlight)")
      .col-4.hl-video-right
        .hl-right-banner(v-if="list.length > 0")
          .hl-title-matches {{ $t('ui.upnext') }}
          .hl-matches.magicY
            template(v-for="(item, index) in list")
              preview(:item="item", :idx="index", @selected="handleSelected", :isme="item == selectedHighlight")
        template(v-else)
          .hl-right-banner.d-flex.align-items-center.justify-content-center
            .hl-message {{ $t('ui.no_video') }}
</template>
<script>
import config from "@/config";
import errors from "@/errors";
import service from "@/library/_xhr-ext";

import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {
    slider: () => import("@/components/desktop/slider"),
    player: () => import("@/views/highlight/player"),
    viewer: () => import("@/views/highlight/viewer"),
    preview: () => import("@/views/highlight/preview"),
  },
  data() {
    return {
      isplay: false,
      league: [],
      list: [],
      player: [],
      selectedDate: null,
      mondays: [],
      selectedHighlight: null,
      selectedLeague: null,
      loading: {
        league: false,
        list: false,
      },
      updating: {
        league: false,
      },
      feedback: {
        success: false,
        status: errors.session.invalidSession,
      },
      locale: {
        format: "YYYY-MM-DD",
        useCurrent: true,
        minDate: this.$dayjs(
          this.$dayjs().startOf("day").subtract(1, "year").format("YYYY-MM-DD")
        ).format("YYYY-MM-DD"),
        maxDate: this.$dayjs().format("YYYY-MM-DD"),
      },
      liveTV: {
        schedule: [],
        loading: false,
        count: 0,
        // matches: {}
      },
    };
  },
  computed: {
    language() {
      return this.$store.getters.language;
    },
  },
  destroyed() {},
  mounted() {
    this.getLeague();
    this.getList(true);
    var d = new Date(),
        td = new Date(),
        mondays = [];

    d.setDate(1);
    d.setMonth(d.getMonth() - 2);

    // Get the first Monday in the month
    while (d.getDay() !== 1) {
      d.setDate(d.getDate() + 1);
    }

    // Get all the other Mondays in the month
    while (d < td) {
      var pushDate = new Date(d.getTime());
      mondays.push(pushDate);
      d.setDate(d.getDate() + 7);
    }

    this.mondays = [];
    for (var i = mondays.length - 1; i >= 0; i--) {
      this.mondays.push(mondays[i]);
    }

    // this.mondays = mondays;
  },
  beforeUpdate() {
    if (this.$refs.slick) {
      if (this.updating.league == true) {
        this.$refs.slick.destroy();
      }
    }
  },
  updated() {
    this.$nextTick(() => {
      if (this.$refs.slick) {
        if (this.updating.league == true) {
          this.$refs.slick.create();
          this.updating.league = false;
        }
      }
    });
  },
  methods: {
    setLeague(e) {
      this.selectedLeague = e;
      this.getList();
    },
    setDated(e) {
      this.selectedDate = e;
      this.getList();
    },
    handleSelected(e) {
      this.selectedHighlight = e;
      this.isplay = false;
    },
    playEvent(e, action) {
      if (e) {
        EventBus.$emit("LIVE_CENTER", {
          action: action,
          radar_id: e.radar_id,
          channel: e.channel,
          match_id: e.match_id,
          league_id: e.league_id,
          sports_type: e.sports_type,
          market_type: e.market_type,
          home_team: e.home_name_en,
          away_team: e.away_name_en,
          working_date: e.working_date,
          match_time: e.match_time,
        });
      }
    },
    findEvent(e) {
      this.$router.push("/desktop");
      this.$nextTick(() => {
        EventBus.$emit("FIND_EVENT", e);
        this.$nextTick(() => {
          this.playEvent(e, "channel");
        });
      });
    },
    handleDateChange(e) {
      this.getList();
    },
    playing(e) {
      var i = this.player.indexOf(e);
      for (var j = 0; j < this.player.length; j++) {
        if (j != i) {
          this.player[j].pause();
        }
      }
    },
    addPlayer(e) {
      if (this.player.indexOf(e) < 0) {
        this.player.push(e);
      }
    },
    removePlayer(e) {
      var i = this.player.indexOf(e);
      if (i > -1) {
        this.player.splice(i, 1);
      }
    },
    getName(p, e) {
      var name = e[p + this.language];
      if (name == null || name == "" || !name) {
        name = e[p + "en"];
      }
      return name;
    },
    getRemark(p, e) {
      var name = e[p + this.language];
      if (name == null || name == "" || !name) {
        name = e[p + "en"];
      }
      var html = "";
      var p = name.split(",");
      for (var i = 0; i < p.length; i++) {
        var b = p[i].split("|");
        if (b.length > 1) {
          html += "<div class='remark-item'>";
          for (var j = 0; j < b.length; j++) {
            if (j == 0) {
              html +=
                "<div class='remark-detail " +
                b[j].toLowerCase() +
                "'>" +
                b[j] +
                "</div>";
            } else {
              html += "<div class='remark-detail'>" + b[j] + "</div>";
            }
          }
          html += "</div>";
        } else {
          html += "<div class='remark-item'>" + b[0] + "</div>";
        }
      }
      return html;
    },
    getLeague() {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
      };
      this.loading.league = true;
      service.getAnyList(config.highlightLeagueUrl(), json).then(
        (result) => {
          this.loading.league = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              var data = result.data;
              this.updating.league = true;
              if (data != null && data.length > 0) {
                this.league = data;
                this.selectedLeague = null;
              } else {
                this.league = [];
              }
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading.league = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getList(firstLoad) {
      var league_id = null;
      var match_date = null;

      if (!firstLoad && this.selectedLeague) {
        league_id = this.selectedLeague.league_id;
      }

      if (!firstLoad && this.selectedDate) {
        match_date = this.$dayjs(this.selectedDate).format("YYYY-MM-DD");
      }

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        league_id: league_id,
        match_date: match_date,
        page_number: 1,
        page_size: 100,
      };
      this.loading.list = true;
      this.list = [];
      service.getAnyList(config.highlightListUrl(), json).then(
        (result) => {
          this.loading.list = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              var data = result.data;
              if (data != null && data.length > 0) {
                this.list = data;
                this.selectedHighlight = data[0];
              } else {
                this.list = [];
              }
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.loading.list = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getSchedule(callback) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
      };

      if (this.liveTV.loading != true) {
        this.liveTV.loading = true;
        service.getLiveList(config.liveTvListUrl(), json).then(
          (result) => {
            this.liveTV.loading = false;
            if (result) {
              this.feedback.status = result.status;
              if (result.success == true) {
                var data = result.data;
                this.liveTV.count = data.length;
                var group = data.reduce((r, a) => {
                  r[a.sports_type] = [...(r[a.sports_type] || []), a];
                  return r;
                }, {});
                var gk = Object.keys(group);
                for (var i = 0; i < gk.length; i++) {
                  group[gk[i]].sort((a, b) => {
                    var keyA = new Date(a.match_time),
                        keyB = new Date(b.match_time);
                    if (keyA < keyB) return -1;
                    if (keyA > keyB) return 1;
                  });
                }
                this.liveTV.schedule = group;
                this.liveTV.matches = {};
                for (var i = 0; i < data.length; i++) {
                  this.liveTV.matches[data[i].match_id] = data[i];
                }
                if (callback) {
                  callback(data);
                }
              } else {
                this.$helpers.handleFeedback(this.feedback.status);
              }
            }
          },
          (err) => {
            this.liveTV.loading = false;
            this.feedback.success = false;
            this.feedback.status = errors.request.failed;
            this.$helpers.handleFeedback(err.status);
          }
        );
      }
    },
  },
};
</script>
