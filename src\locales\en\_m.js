export default {
  BT_CSHTFT: "HT/FT Correct Score",
  BT_ETGHTFT: "HT/FT Exact Total Goal",
  BS_CSHTFT: "HT/FT Correct Score",
  BS_ETGHTFT: "HT/FT Time Exact Total Goal",


  BT_O: "Over",
  BT_U: "Under",

  BT_BOTH: "Both",
  BT_ONE: "One",
  BT_HDP: "Handicap",
  BT_OU: "Over/Under",
  BT_ML: "Moneyline",
  BT_1X2: "1X2",
  BT_1X2HDP: "3 Way Handicap",
  BT_CS: "Correct Score",
  BT_OE: "Odd/Even",
  BT_DC: "Double Chance",
  BT_HTFT: "Half Time/Full Time",
  BT_FGLG: "First Goal/Last Goal",
  BT_TG: "Total Goal",
  BT_OR: "Outright",
  BT_ORZ: "Outright",
  BT_ODD: "Odd",
  BT_EVEN: "Even",

  BT_ANB: "Away No Bet",
  BT_BS: "Both/One/Neither Team To Score",
  BT_CL: "Clean Sheet",
  BT_DNB: "Draw No Bet",
  BT_DNBH: "1H. Draw No Bet",
  BT_HNB: "Home No Bet",
  BT_TWTN: "To Win To Nil",

  BS_1X2OU: "1X2 + OU",
  BT_1X2OU: "1X2 + OU",
  BT_1O: "1O",
  BT_1U: "1U",
  BT_2O: "2O",
  BT_2U: "2U",
  BT_XO: "XO",
  BT_XU: "XU",

  BS_DCOU: "Double Chance + OU",
  BT_DCOU: "Double Chance + OU",
  BT_HDO: "HDO",
  BT_HDU: "HDU",
  BT_HAO: "HAO",
  BT_HAU: "HAU",
  BT_DAO: "DAO",
  BT_DAU: "DAU",

  BS_OUOE: "OU + OE",
  BT_OUOE: "OU + OE",
  BK_OO: "OO",
  BK_OE: "OE",
  BK_UO: "UO",
  BK_UE: "UE",

  BS_HTFTOE: "HT/FT + OE",
  BT_HTFTOE: "HT/FT + OE",
  BK_EO: "EO",
  BK_EE: "EE",

  BS_WM: "Winning Margin",
  BT_WM: "Winning Margin",
  BK_H1: "Home 1",
  BK_H2: "Home 2",
  BK_H3Up: "Home 3+",
  BK_A1: "Away 1",
  BK_A2: "Away 2",
  BK_A3Up: "Away 3+",
  BK_D: "Score Draw (Excl. 0-0)",
  BK_NG: "No Goal",

  BS_ETG: "Exact Total Goal",
  BT_ETG: "Exact Total Goal",
  BS_EHTG: "Exact Home Total Goal",
  BT_EHTG: "Exact Home Total Goal",
  BS_EATG: "Exact Away Total Goal",
  BT_EATG: "Exact Away Total Goal",
  BS_ETGH: "FH. Exact Total Goal",
  BT_ETGH: "FH. Exact Total Goal",
  BS_EHTGH: "FH. Exact Home Total Goal",
  BT_EHTGH: "FH. Exact Home Total Goal",
  BS_EATGH: "FH. Exact Away Total Goal",
  BT_EATGH: "FH. Exact Away Total Goal",
  BT_0: "0",
  BT_1: "1",
  BT_2: "2",
  BT_3: "3",
  BT_4: "4",
  BT_5: "5",
  BT_6Up: "6+",
  BT_3Up: "3+",

  BT_HDPH: "1H. Handicap",
  BT_OUH: "1H. Over/Under",
  BT_MLH: "1H. Moneyline",
  BT_1X2H: "1H. 1X2",
  BT_1X2HDPH: "1H. 3 Way Handicap",
  BT_CSH: "1H. Correct Score",
  BT_OEH: "1H. Odd/Even",
  BT_DCH: "1H. Double Chance",
  BT_FGLGH: "1H. First Goal/Last Goal",
  BT_TGH: "1H. Total Goal",

  TB_HDP: "FT. HDP",
  TB_OU: "FT. O/U",
  TB_1X2: "FT. 1X2",
  TB_OE: "FT. O/E",
  TB_HDPH: "1H. HDP",
  TB_OUH: "1H. O/U",
  TB_1X2H: "1H. 1X2",
  TB_OEH: "1H. O/E",

  BT_HDPOU: "HDP & OU",
  BT_PARLAY: "Mix Parlay",
  BT_FT: "FT. ",
  BT_1H: "1H. ",
  BT_2H: "2H. ",

  BT_FG: "First Goal",
  BT_LG: "Last Goal",
  BT_HFG: "HF",
  BT_AFG: "AF",
  BT_HLG: "HL",
  BT_ALG: "AL",
  BT_NG: "NG",
  BT_HY: "Home Yes",
  BT_HN: "Home No",
  BT_AY: "Away Yes",
  BT_AN: "Away No",
  BT_H: "Home",
  BT_A: "Away",
  BT_D: "Draw",
  BT_HD_DC: "HD",
  BT_HA_DC: "HA",
  BT_DA_DC: "DA",
  BT_HH: "HH",
  BT_HD: "HD",
  BT_HA: "HA",
  BT_DH: "DH",
  BT_DD: "DD",
  BT_DA: "DA",
  BT_AH: "AH",
  BT_AD: "AD",
  BT_AA: "AA",

  BT_Y: "Yes",
  BT_N: "No",

  BT_LIVE: "LIVE",
  BT_Live: "LIVE",
  BT_live: "LIVE",
  BT_HT: "H.TIME",
  BT_DELAYED: "DELAY",
  BT_PEN: "PEN",

  BT_FT1: "FT.1",
  BT_FT2: "FT.2",
  BT_FTX: "FT.X",

  TC_FT1: "1",
  TC_FT2: "2",
  TC_FTX: "X",

  BT_HT1: "HT.1",
  BT_HT2: "HT.2",
  BT_HTX: "HT.X",

  F5_INN_HDP: "F5 INN. HDP",
  F5_INN_OU: "F5 INN. O/U",

  BS_HDP: "FT Handicap",
  BS_HDPH: "FH Handicap",
  BS_OU: "FT Over/Under",
  BS_OUH: "FH Over/Under",
  BS_OE: "FT Odd/Even",
  BS_OEH: "FH Odd/Even",
  BS_1X2: "FT 1X2",
  BS_1X2H: "FH 1X2",
  BS_ML: "FT Moneyline",
  BS_MLH: "FH Moneyline",
  BS_1X2HDP: "FT 3 Way HDP",
  BS_1X2HDPH: "FH 3 Way HDP",
  BS_CS: "FT Correct Score",
  BS_CSH: "FH Correct Score",
  BS_DC: "FT Double Chance",
  BS_DCH: "FH Double Chance",
  BS_FGLG: "FT First/Last Goal",
  BS_FGLGH: "FH First/Last Goal",
  BS_TG: "FT Total Goal",
  BS_TGH: "FH Total Goal",
  BS_ANB: "Away No Bet",
  BS_HTFT: "Half Time/Full Time",
  BS_BS: "B/O/N Team To Score",
  BS_CL: "Clean Sheet",
  BS_DNB: "Draw No Bet",
  BS_DNBH: "FH Draw No Bet",
  BS_HNB: "Home No Bet",
  BS_TWTN: "To Win To Nil",
  BS_OR: "Outright",
  BS_ORZ: "Outright",

  LOT_BS: "B/S",
  LOT_BIG: "B",
  LOT_SMALL: "S",
  LOT_OE: "O/E",
  LOT_ODD: "O",
  LOT_EVEN: "E",
  LOT_OU: "O/U",
  LOT_OVER: "O",
  LOT_UNDER: "U",
  LOT_1: "H",
  LOT_0: "A",
  LOT3_OVER: "O",
  LOT3_UNDER: "E",

  D_1: "Single Number",
  D_2: "Number & Reverse",
  D_3: "Box",
  D_4: "iBox",
  D_5: "Box Last 3 Digit",
  D_6: "Box Last 2 Digit",

  D_BIG: "BIG",
  D_SMALL: "SMALL",
  D_AA: "AA",
  D_SA: "A",
  D_SB: "B",
  D_SC: "C",
  D_SD: "D",
  D_SE: "E",
  D_ABC: "ABC",

  GH_1X2: "WIN",
  GH_1X20: "WIN",
  GH_1X2HDP: "PLACE/SHOW",
  GH_1X2HDP2: "PLACE",
  GH_1X2HDP4: "SHOW",
  GH_OVER: "OVER (4,5,6)",
  GH_UNDER: "UNDER (1,2,3)",
  GH_ODD: "ODD (1,3,5)",
  GH_EVEN: "EVEN (2,4,6)",
  GH_OU1: "OVER (4,5,6)",
  GH_OU2: "UNDER (1,2,3)",
  GH_OE1: "ODD (1,3,5)",
  GH_OE2: "EVEN (2,4,6)",

  GX_1X2: "WIN",
  GX_1X20: "WIN",
  GX_1X2HDP: "PLACE/SHOW",
  GX_1X2HDP2: "PLACE",
  GX_1X2HDP4: "SHOW",
  GX_OVER: "OVER (7-12)",
  GX_UNDER: "UNDER (1-6)",
  GX_ODD: "ODD (1,3,5,7,9,11)",
  GX_EVEN: "EVEN (2,4,6,8,10,12)",
  GX_OU1: "OVER (7-12)",
  GX_OU2: "UNDER (1-6)",
  GX_OE1: "ODD (1,3,5,7,9,11)",
  GX_OE2: "EVEN (2,4,6,8,10,12)",

  GC_OU: "O/U",
  GC_OE: "O/E",
  GC_OVER: "OVER (3,4,5)",
  GC_UNDER: "UNDER (0,1,2)",
  GC_ODD: "ODD (1,3,5)",
  GC_EVEN: "EVEN (0,2,4)",
  GC_5_0: "5-0",
  GC_4_1: "4-1",
  GC_3_2: "3-2",
  GC_0_5: "0-5",
  GC_1_4: "1-4",
  GC_2_3: "2-3",
  GC_ML0: "1ST BALL",
  GC_1X20: "TO WIN",
  GC_ML: "1ST BALL",
  GC_1X2: "TO WIN",
  GC_OU1: "OVER (3,4,5)",
  GC_OU2: "UNDER (0,1,2)",
  GC_OE1: "ODD (1,3,5)",
  GC_OE2: "EVEN (0,2,4)",

  ROOM_A: "Open",
  ROOM_0: "Active",
  ROOM_1: "Closed",
  ROOM_2: "Cancel",
  ROOM_3: "Ended",
  ROOM_4: "Stop Betting",
  ROOM_10: "All",

  ROOM_T0: "All",
  ROOM_T1: "Public Room",
  ROOM_T2: "Private Room",
  ROOM_T3: "Free Room",

  joinRoom1: "Join Public Room",
  joinRoom2: "Join Private Room",
  joinRoom3: "Join Free Room",
  createRoom1: "Create Public Room",
  createRoom2: "Create Private Room",
  createRoom3: "Create Free Room",
  Champion: "Champion",
  FirstRunnerUp: "First Runner Up",
  SecondRunnerUp: "Second Runner Up",


  BT_OEHM: "Home Odd/Even",
  BT_OEHMH: "FH. Home Odd/Even",
  BT_OEAW: "Away Odd/Even",
  BT_OEAWH: "FH. Away Odd/Even",

  BS_OEHM: "FT. Home Odd/Even",
  BS_OEHMH: "FH. Home Odd/Even",
  BS_OEAW: "FT. Away Odd/Even",
  BS_OEAWH: "FH. Away Odd/Even",

};