.content .left.active .content-bet {
	width: 240px;
	-webkit-box-shadow: 3px 0 5px 1px rgba(0, 0, 0, 0.5);
	        box-shadow: 3px 0 5px 1px rgba(0, 0, 0, 0.5);
	border: 1px solid #0d3a64;
}
.content .left.active .collapse-box {
	width: 240px;
	border: 1px solid #0d3a64;
	margin-top: -40px;
}
.content .left.active .sport-list:last-child .group {
	border-radius: 0 0 3px 3px;
	border-bottom: 1px solid #395f83;
}
.content .left .side-column {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-pack: distribute;
	    justify-content: space-around;
}
.content .left .side-row {
	display: block;
	border-radius: 5px;
	background: url(/images/tab-bg.png) no-repeat;
    background-size: cover;
}
.content .left.active .side-column {
	display: block;
}
.content .left.active .active-block {
	display: block !important;
}
.content .left.active .active-none {
	display: none !important;
}
.content .left.active .group .arrow-up {
	display: none;
}
.content .left.active .nav-header.nav-left {
	width: 100%;
	border-radius: 0;
}
.content .left.active .nav-header.nav-right {
	width: 100%;
	border-radius: 0 0 5px 5px;
	border-top: 1px solid #5991C1;
}
.content .left.active #collapse-betslip.collapse,.content .left.active #collapse-betslip.collapsing {
	display: none;
	position: absolute;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	margin-top: -80px;
	left: 50px;
}
.content .left.active #collapse-betslip.collapse.show {
	display: block;
	position: absolute;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	margin-top: -80px;
	left: 50px;
}
.content .left.active #collapse-mybet.collapse,.content .left.active #collapse-mybet.collapsing {
	display: none;
	position: absolute;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	margin-top: -40px;
	left: 50px;
}
.content .left.active #collapse-mybet.collapse.show {
	display: block;
	position: absolute;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	margin-top: -40px;
	left: 50px;
}
.content .left.active .collapse-box.collapse,.content .left.active .collapse-box.collapsing {
	display: none;
	position: absolute;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
	left: 50px;
}
.content .left.active .collapse-box.collapse.show {
	display: block;
	position: absolute;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
	left: 50px;
}
.content .left.active .content-bet {
	display: block;
	position: absolute;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
}
.content .left.active #collapse-parlay {
	display: block;
	position: absolute;
	width: 240px;
	background: #0f4f8c;
	-webkit-box-shadow: 3px 0 5px 1px rgba(0, 0, 0, 0.5);
	        box-shadow: 3px 0 5px 1px rgba(0, 0, 0, 0.5);
	border: 1px solid #0d3a64;
}
.content .left.active .mybet .collapsed {
	border-radius: 0;
}
.content .left.active #collapse-live.collapse.show {
	width: 240px;
	background: #0f4f8c;
	-webkit-box-shadow: 3px 0 5px 1px rgba(0, 0, 0, 0.5);
	        box-shadow: 3px 0 5px 1px rgba(0, 0, 0, 0.5);
	border: 1px solid #0d3a64;
	display: block;
	position: absolute;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	margin-top: -40px;
	z-index: 1;
}
.content .left.active .collapse.show .group {
	background: #276FA8;
}
.content .left.active #heading-live[aria-expanded="false"] .group ~ #collapse-live.collapse.show {
	display: none;
}
.content .left.active #market-group {
	width: 50px;
	position: absolute;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
}
.content .left.active #collapse-allsports.collapse.show #market-group {
	display: block;
	overflow: hidden;
	height: 42px;
	margin-top: -42px;
	margin-left: -1px;
	border-left: 1px solid #5991C1;
	border-right: 1px solid #5991C1;
}
.content .left.active #collapse-allsports.collapse.show #market-group .market-menu {
	display: none !important;
	width: 240px;
}
.content .left.active #collapse-allsports.collapse.show #market-group .market-icon {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
	width: 100%;
	height: 42px;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
}
.content .left.active #collapse-allsports.collapse.show #market-group:hover .market-icon {
	display: none !important;
}
.content .left.active #collapse-allsports.collapse.show #market-group:hover .market-menu {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
}
.content .left.active #collapse-allsports.collapse.show #market-group:hover {
	width: 240px;
	-webkit-box-shadow: 3px 0 5px 1px rgba(0, 0, 0, 0.5);
	        box-shadow: 3px 0 5px 1px rgba(0, 0, 0, 0.5);
}
.content .left.active .scroll-sports {
	max-height: inherit;
	overflow: inherit;
}
.content .left.active .scroll-sports .group.changed {
	border-left: 1px solid #395f83 !important;
	border-right: 1px solid #395f83 !important;
}
.content .left.active .new-wrap {
	position: relative;
	margin-top: 42px;
}
.content .left.active #market-tab {
	position: relative;
	margin-top: 42px;
}
.content .left.active #market-tab .heading-collapse {
	position: relative;
	width: 50px;
	-webkit-transition: none;
	-o-transition: none;
	transition: none;
	-webkit-animation: none;
	        animation: none;
}
.content .left #market-tab .tab-pane {
	position: relative;
}
.content .left.active #market-tab .tab-pane {
	position: relative;
}
.sport-list {
	position: static;
}
.content .left.active .collapse-box .group {
	width: 240px;
	background: #0f4f8c;
	-webkit-box-shadow: 3px 0 5px 1px rgba(0, 0, 0, 0.5);
	        box-shadow: 3px 0 5px 1px rgba(0, 0, 0, 0.5);
	border: 1px solid #0d3a64;
}
.content .left.active .group.changed.selected {
	background: #145693 !important;
}
.content .left.active .heading-collapse[aria-expanded="false"] .group.changed:hover {
	background: #145693 !important;
}
.content .left.active .group .sport-type {
	padding: 0;
	line-height: 39px;
}
.content .left .bg-img .group.mini {
	display: none;
}
.content .left.active .bg-img .group.mini {
	display: block;
	text-align: center;
}
.content .left.active .bg-img .group.mini img {
	width: 28px !important;
	height: 28px !important;
	margin: 0;
}
.content .left.active .user-info-wrapper {
	display: none;
}
#desktop {
	position: absolute;
	width: 100%;
}
.x-side {
	position: fixed;
	width: 220px;
	height: calc(100vh - 110px);
	background: #C0CED9;
}
.x-side::before {
	content: "";
	position: absolute;
	top: 0;
	left: -12px;
	bottom: 0;
	border-left: 12px solid #C0CED9;
}
.x-side::after {
	content: "";
	position: absolute;
	top: 0;
	right: -8px;
	bottom: 0;
	border-right: 8px solid #C0CED9;
}
.content .left.active .x-side {
	width: 50px;
	height: calc(100vh - 44px);
}
.pointable {
	cursor: pointer;
	color: #ffffff;
}
.pointable:hover {
	cursor: pointer;
	color: #F6C344;
}
.pointable span {
	margin-left: 4px;
}
.pointable:active {
	cursor: wait;
}
.pointable.dark {
	color: #000000;
}
.pointable.dark i {
	color: #00000080;
}
.pointable.dark:hover {
	color: #000000;
}

.left a:active {
	cursor: wait;

}