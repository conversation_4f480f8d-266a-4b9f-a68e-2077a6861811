<template lang="pug">
.hx-row.w-100
  .hx-col.hx-cols.w-50
    template(v-if="details['ou'] != null && details['ou'][i] != null && details['ou'][i][11] != 0 && details['ou'][i][12] != 0 && details['ou'][i][11] != '' && details['ou'][i][12] != ''")
      .hx
        .hxs.w-43
          .ball-value {{ details['ou'][i][8] }}
        .hxs.w-43r
          oddsItem(:odds="details['ou'][i]" idx=12 :typ="oddsType" dataType="1")
      .hx
        .hxs.w-43
          .ball-value u
        .hxs.w-43r
          oddsItem(:odds="details['ou'][i]" idx=11 :typ="oddsType" dataType="1")
  .hx-col.hx-cols.w-50
    template(v-if="details['oe'] != null && details['oe'][i] != null && details['oe'][i][5] != 0 && details['oe'][i][7] != 0 && details['oe'][i][5] != '' && details['oe'][i][7] != ''")
      .hx
        .hxs.w-43
          .ball-value O
        .hxs.w-43r
          oddsItem(v-if="details['oe'][0][5] != 0" :odds="details['oe'][i]" cls="" idx=5 :typ="oddsType" dataType="2")
      .hx
        .hxs.w-43
          .ball-value E
        .hxs.w-43r
          oddsItem(v-if="details['oe'][0][7] != 0" :odds="details['oe'][i]" cls="" idx=7 :typ="oddsType" dataType="2")
</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";

export default {
  components: {
    oddsItem
  },
  props: {
    details: {
      type: Object
    },
    oddsType: {
      type: String
    },
    item: {
      type: String
    },
    i: {
      type: [String, Number]
    },
  },
  // updated: function() {
  //   this.$nextTick(function() {
  //     console.log("cs", new Date(), this.oddsType, this.item);
  //   });
  // }
};
</script>
