<template lang="pug">
#modal-leagues.modal(tabindex="-1" role="dialog" ref="leagueModal")
  .modal-dialog.modal-dialog-centered.modal-dialog-scrollable.modal-lg(role="document")
    .modal-content.fixed-modal-lg
      .modal-header
        h5.modal-title {{ $t("ui.select_leagues") }}
        button.close(type="button" data-dismiss="modal" aria-label="Close")
          i.fal.fa-times(aria-hidden="true")
      .modal-body
        .d-flex.flex-row
          .filter-option.mr-2.flex-fill
            .input-group
              .input-group-prepend
                span.input-group-text.dark
                  i.fad.fa-search
              input.form-control(type="text" placeholder="Search League" v-model="leagueSearch")
          .filter-option.mr-2
            .input-group
              .input-group-prepend
                .input-group-text(@click="rowSelect('all')")
                  label.checkmarkbox
                    input(type="checkbox" :checked="(selectedLeague.length == leagueIdSelected.length && selectedLeague.length == availableLeague.length && leagueIdSelected.length == availableLeague.length) || leagueIdSelected.length == availableLeague.length" @click="rowSelect('all')")
                    span.checkmark
                    span.blue {{ $t("ui.check_all") }}
          .filter-option
            .input-group
              .input-group-prepend
                .input-group-text
                  label.checkmarkbox
                    input(type="checkbox" v-model="leagueShowSelected")
                    span.checkmark
                    span.blue {{ $t("ui.selected_only") }}
        .d-flex.flex-row.mt-2
          #select-league.accordion.w-100.magicY
            .card
              template(v-for="(sItem, sKey, sIndex) in sports")
                template(v-if="getFilterLeagueList(sKey).length > 0")
                  .card-header(data-toggle="collapse" :data-target="'#collapse-' + sKey"  aria-expanded="true" :aria-controls="'collapse-' + sKey" :id="'type' + sKey")
                    i.fad.fa-chevron-circle-down
                    span.ml-1 {{ sItem }}
                  .sp-header.collapse.show(:aria-labelledby="'type' + sKey" :id="'collapse-' + sKey")
                    .sp-body.card-body
                      label.league-wrap(v-for="(lItem, lKey, lIndex) in getFilterLeagueList(sKey)" @click="rowSelect(lItem)")
                        template(v-if="showLeague(lItem)")
                          input(type="checkbox" :checked="checkLeague(lItem)" :value="lItem[0]" @click="rowSelect(lItem)")
                          span.checkmark
                          span.blue {{ lItem[4] }}
      .modal-footer
        button.btn.btn-primary(type="button" @click="saveLeague(leagueIdSelected)") {{ $t('ui.save') }}
        button.btn.btn-secondary(type="button" data-dismiss="modal") {{ $t('ui.cancel') }}
</template>
<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";

export default {
  data() {
    return {
      loading: false,
      leagueSearch: "",
      leagueIdSelected: [],
      leagueAllSelected: true,
      leagueShowSelected: false,
      feedback: {
        success: false,
        status: errors.session.invalidSession
      }
    };
  },
  computed: {
    sports() {
      return this.$store.state.layout.sports;
    },
    selectedLeague() {
      return this.$store.getters.selectLeague;
    },
    availableLeague() {
      return this.$store.state.layout.league;
    }
  },
  mounted: function() {
    $(this.$refs.leagueModal).on("shown.bs.modal", this.initial);
  },
  methods: {
    leaguesList() {
      if (this.$store.state.layout.league != null) {
        var tempList = Object.values(this.$store.state.layout.league);
        return tempList.filter(item => {
          return item[4].toLowerCase().indexOf(this.leagueSearch.toLowerCase()) > -1;
        });
      } else {
        return null;
      }
    },
    getFilterLeagueList(sportId) {
      var llnn = this.leaguesList();
      // console.log(llnn);
      if (llnn) {
        var list = this.leaguesList().filter(item => {
          return item[1] == sportId;
        });
        return list.sort((a, b) => {
          if (a[4] > b[4]) return 1;
          if (a[4] < b[4]) return -1;
          return 0;
        });
      } else {
        return [];
      }
    },
    rowSelect: function(info) {
      if (info != "all") {
        var index = this.leagueIdSelected.indexOf(info[0]);
        if (index == -1) this.leagueIdSelected.push(info[0]);
        else this.leagueIdSelected.splice(index, 1);
      } else {
        if (this.$store.state.layout.league != null) {
          var tempList = Object.values(this.$store.state.layout.league);

          setTimeout(() => {
            if (tempList.length != this.leagueIdSelected.length) {
              for (var i = 0; i < tempList.length; i++) {
                var index = this.leagueIdSelected.indexOf(tempList[i][0]);
                if (index == -1) this.leagueIdSelected.push(tempList[i][0]);
              }
            } else {
              this.leagueIdSelected = [];
            }
          }, 10);
        }
      }
    },
    initial() {
      this.leagueIdSelected = [];
      if (this.$store.getters.selectLeague != null) {
        if (this.$store.getters.selectLeague.length > 0) this.leagueIdSelected = this.$store.getters.selectLeague.slice();
        else {
          var tempList = Object.values(this.$store.state.layout.league);
          for (var i = 0; i < tempList.length; i++) {
            this.leagueIdSelected.push(tempList[i][0]);
          }
        }
      }
    },
    checkLeague(item) {
      if (this.leagueIdSelected.indexOf(item[0]) > -1) return true;
      else return false;
    },
    showLeague(item) {
      if (this.leagueShowSelected) {
        if (this.leagueIdSelected.indexOf(item[0]) > -1) return true;
        else return false;
      }
      return true;
    },
    saveLeague(payload) {
      if(payload.length < 1){
        this.$helpers.showDialog(this.$t("ui.action"), this.$t("error.selectLeague"), "info");
        return;
      }
      this.$store.dispatch("layout/setSelectLeague", payload);
      EventBus.$emit("INVALIDATE");
      $(this.$refs.leagueModal).modal("hide");
    },
    clearLeague() {
      this.$store.dispatch("layout/resetSelectLeague");
      EventBus.$emit("INVALIDATE");
      $(this.$refs.leagueModal).modal("hide");
    }
  }
};
</script>