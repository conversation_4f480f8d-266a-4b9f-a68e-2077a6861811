/* landing2 */
@font-face {
  font-family: 'riftbold';
  src: url('/font/rift-bold-webfont.woff2') format('woff2'),
    url('/font/rift-bold-webfont.woff') format('woff');
  font-weight: normal;
  font-style: normal;

}

body {
  font-family: "<PERSON><PERSON>", "Microsoft YaHei", "微软雅黑", <PERSON><PERSON><PERSON><PERSON>, "华文细黑", sans-serif;
}

.landing2 {
  background-color: #f6fafd;
  font-size: 16px;
  background-image: url('../../img2/landing2/top-bg-m.jpg'), url('../../img2/landing2/bg-bottom-m.jpg');
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: top, bottom;
}

.landing2 .header2 {
  transition: all 0.1s ease-in-out !important;
  background: rgb(9, 75, 133) !important;
  background: linear-gradient(90deg, rgba(9, 75, 133, 0.9) 0%, rgba(43, 129, 202, 0.9) 100%) !important;
  padding: 5px 0px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
  width: 100% !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 9999 !important;
  transition: all 0.3s ease-in-out !important;
  display: block !important;
  box-sizing: border-box !important;
  position: sticky !important;
  position: -webkit-sticky !important;
  top: 0; /* required */
}

.landing2 .header2.small-top {
  padding: 5px 0px !important;
}

.landing2 .right-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.landing2 .right-wrapper .line {
  width: 1px;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0px 7px;
}

.landing2 .header2.small-top .right-wrapper .line {
  height: 20px !important;
}

.landing2 .container {
  max-width: 1400px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  min-width: auto !important;
}

.landing2 .copyright .container {
  padding: 0 15px;
}

.landing2 .logo {
  width: 100px;
  margin-left: 15px;
}

/* lang dropdown */
.landing2 .lang-wrap {
  width: auto;
  text-align: right;
  height: auto;
}

/* .landing2 .lang-wrap {
  height: ;
} */


.landing2 .langpicker {
  display: none;
}

.landing2 #lang-dropdown-list {
  /* padding-left: 0px; */
  margin-bottom: 0px;
  text-align: left;
}

.landing2 #lang-dropdown-list img,
.landing2 .btn-select img {
  width: 20px;
  padding: 5px 0;
}

.landing2 .btn-select img {
  width: 25px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.landing2 #lang-dropdown-list li {
  list-style: none;
  padding: 0px 10px;
}

.landing2 #lang-dropdown-list li:hover {
  background-color: #e9e9e9;
}

.landing2 #lang-dropdown-list li:hover span {
  color: #000;
}

.landing2 #lang-dropdown-list li span,
.landing2 .btn-select li span {
  margin-left: 10px;
  font-size: 13px;
  color: #fff;
}

.landing2 .btn-select li span {
  display: none;
}

.landing2 .lang-dropdown-wrap {
  display: none;
  width: 120px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  border: 1px solid rgba(0, 0, 0, 0.15);
  padding: 5px 0px;
  border-radius: 0.25rem;
  background-color: #fff;
  position: absolute;
  top: 40px;
  right: 0px;
  box-sizing: border-box;
  z-index: 10;
}

.landing2 .open {
  display: show !important;
}

.landing2 .btn-select,
.landing2 .btn-switch-device {
  border-radius: 100%;
  border: 1px solid rgba(255, 255, 255, 0.5);
  background: rgb(47, 148, 236);
  background: radial-gradient(circle, rgba(47, 148, 236, 1) 25%, rgba(8, 76, 144, 1) 100%);
  padding: 5px;
  box-sizing: border-box;
  color: #fff;
  width: 30px;
  height: 30px;
  line-height: 35px;
  position: relative;
  font-size: 20px;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  box-shadow: 1px 2px 4px 2px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: 1px 2px 4px 2px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 1px 2px 4px 2px rgba(0, 0, 0, 0.3);
  margin: 0 5px;
}

.landing2 .lang-select {
  width: 100%;
  position: relative;
  height: 40px;
}
.landing2 .small-top .lang-select {
  height: 32px;
}

.landing2 .btn-select li {
  list-style: none;
  float: left;
  padding-bottom: 0px;
  font-size: 16px;
}

.landing2 .login-group {
  display: none;
}

.landing2 .input-group .input-group {
  box-shadow: 1px 2px 2px 2px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: 1px 2px 2px 2px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 1px 2px 2px 2px rgba(0, 0, 0, 0.3);
}

.landing2 .input-group .input-group-text {
  background-color: #fff;
  border: 0;
  color: #9C9C9C;
}

.landing2 .input-group>.form-control {
  border: 0;
}

.landing2 .login-field {
  margin: 0 5px;
  position: relative;
}

.landing2 .input-group .form-control {
  padding-left: 5px;
}

.landing2 .input-group>.form-control:not(:last-child) {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.landing2 .login-btn2 {
  background: rgb(255, 229, 113);
  background: linear-gradient(0deg, rgba(255, 229, 113, 1) 0%, rgba(251, 225, 109, 1) 6%, rgba(240, 194, 7, 1) 7%, rgba(255, 236, 161, 1) 94%, rgba(255, 249, 223, 1) 95%, rgba(255, 249, 223, 1) 100%);
  box-shadow: 0px 3px 0px 0px rgba(139, 105, 24, 1);
  -webkit-box-shadow: 0px 3px 0px 0px rgba(139, 105, 24, 1);
  -moz-box-shadow: 0px 0px 3px 0px rgba(139, 105, 24, 1);
  border-radius: 100px;
  padding: 7px 1rem;
  width: 80%;
  max-width: 120px;
  border: none;
  margin: 0 auto 0 5px;
  position: relative;
  font-weight: bold;
  -webkit-appearance: none;
  color: #000000;
}

.landing2 .login-btn2:active {
  border: none;
}

.landing2 .login-btn2 span {
  position: relative;
  z-index: 1;
}

.landing2 .login-btn2:before {
  content: "";
  background: rgb(54, 135, 201);
  background: linear-gradient(45deg, rgba(54, 135, 201, 0.13) 0%, rgba(255, 255, 255, 0.2) 43%, rgba(255, 255, 255, 0) 45%);
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 100px;
}

.landing2 .switch-device-mobile {
  font-size: 20px;
  display: none;
}

.landing2 .switch-device-desktop {
  font-size: 16px;
  display: block;
}

/* swiper base */
.landing2 .swiper-container {
  overflow: hidden;
}

.landing2 .swiper-pagination-bullet {
  border-radius: 0px !important;
  width: 10px !important;
  height: 8px !important;
  transform: skewX(-26deg) !important;
}

.landing2 .swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,
.landing2 .swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 1px !important;
}

.landing2 .swiper-pagination-bullet-active {
  background: #F6C344 !important;
}

.landing2 .swiper-button-next,
.landing2 .swiper-button-prev {
  color: #135a8f2e;
}

.landing2 .swiper-slide .banner-text {
  text-align: center;
  width: 90%;
  max-width: 500px;
  margin: 0 auto;
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0);
  top: 10%;
  opacity: 0;
}

.landing2 .swiper-slide-active .banner-text {
  animation-name: fadeInUpM;
  -webkit-animation-name: fadeInUpM;
  animation-duration: 1s;
  animation-fill-mode: both;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  opacity: 0;
}

@keyframes fadeInUpM {
  from {
    top: 10%;
    opacity: 0;
  }

  to {
    top: 0%;
    opacity: 1;
  }
}

@-webkit-keyframes fadeInUpM {
  from {
    top: 10%;
    opacity: 0;
  }

  to {
    top: 0%;
    opacity: 1;
  }
}

.landing2 .break {
  display: none;
}

.landing2 .swiper-slide img {
  width: 100%;
}

.landing2 .banner-desktop {
  display: none;
}

.landing2 .banner-mobile {
  display: block;
  padding-top: 120px;
}

.landing2 .swiper-slide h1 {
  text-transform: uppercase;
  font-size: 1.8rem;
  color: #1A3B5A;
  font-family: 'riftbold';
  padding: 25px 15px 0 15px;
  margin: 0;
}

.landing2 .swiper-slide p {
  font-size: 0.8rem;
  color: #1A3B5A;
  padding: 0 15px 0 15px;
  margin: 0;
}

.landing2 .event-slider-wrap {
  display: none;
}

.landing2 .event-slider-wrap .container {
  max-width: 1400px;
  position: relative;
}

.landing2 .event-banner-iframe {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
}

.landing2 .mobile-login {
  border: 2px solid #fff;
  border-radius: 10px;
  background: rgb(224, 234, 242);
  background: linear-gradient(0deg, rgba(224, 234, 242, 1) 0%, rgba(224, 234, 242, 0.5) 100%);
  box-shadow: 0px 0px 4px 2px rgba(0, 0, 0, 0.2) inset;
  -webkit-box-shadow: 0px 0px 4px 2px rgba(0, 0, 0, 0.2) inset;
  -moz-box-shadow: 0px 0px 4px 2px rgba(0, 0, 0, 0.2) inset;
  padding: 20px;
  display: block;
  margin: 0 auto 30px;
}

.landing2 .mobile-login .login-header {
  background: rgb(179, 198, 214);
  background: linear-gradient(0deg, rgba(179, 198, 214, 1) 0%, rgba(194, 215, 232, 1) 100%);
  padding: 5px;
  margin: -20px -20px 15px -20px;
  border-radius: 10px 10px 0 0;
  text-align: center;
  text-transform: uppercase;
  font-family: 'riftbold';
  font-size: 20px;
  color: #13598e;
}

.landing2 .mobile-login .login-field-wrapper .login-field {
  max-width: 480px;
  margin: 0 auto;
}

.landing2 .mobile-login .login-field-wrapper .input-group-text {
  background-color: #fff;
  border: none;
  font-size: 0.8rem;
  color: #8397ab;
  width: 35px;
}

.landing2 .mobile-login .login-field-wrapper .form-control {
  border: none;
  padding-left: 0;
}

.landing2 .mobile-login .login-field-wrapper .login-field:first-child {
  border-bottom: 1px #ccc solid;
}

.landing2 .mobile-login .login-field-wrapper .login-field:nth-child(2) .input-group .form-control {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0.25rem;
}

.landing2 .mobile-login .login-field-wrapper .login-field:nth-child(2) .input-group .input-group-text {
  border-top-left-radius: 0;
}

.landing2 .mobile-login .login-field-wrapper .login-field:first-child .input-group .form-control {
  border-bottom-right-radius: 0;
}

.landing2 .mobile-login .login-field-wrapper .login-field:first-child .input-group .input-group-text {
  border-bottom-left-radius: 0;
}

.landing2 .mobile-login .login-btn2 {
  width: 100%;
  max-width: 480px;
  margin: 15px auto 0 auto;
  display: block;
  color: #000;
  -webkit-appearance: none;
  background: rgb(255, 229, 113);
  background: linear-gradient(0deg, rgba(255, 229, 113, 1) 0%, rgba(251, 225, 109, 1) 6%, rgba(240, 194, 7, 1) 7%, rgba(255, 236, 161, 1) 94%, rgba(255, 249, 223, 1) 95%, rgba(255, 249, 223, 1) 100%);
  box-shadow: 0px 3px 0px 0px rgba(139, 105, 24, 1);
  -webkit-box-shadow: 0px 3px 0px 0px rgba(139, 105, 24, 1);
  -moz-box-shadow: 0px 0px 3px 0px rgba(139, 105, 24, 1);
}

.landing2 .mobile-login .invalid-feedback {
  text-align: center;
  margin-bottom: -10px;
}

.landing2 .widget-wrap {
  background-size: 100%;
  background-position: bottom;
  background-repeat: no-repeat;
  padding: 0px 15px 30px 15px;
}

.landing2 .widget-wrap .widget-content-wrapper {
  display: block;
}

.landing2 .widget-box .widget-box-header {
  background-image: url('../../img2/landing2/widget-header-bg.jpg');
  background-size: cover;
  text-align: center;
  color: #fff;
  text-transform: uppercase;
  font-weight: bold;
  padding: 5px;
  font-size: 0.9rem;
}

.landing2 .widget-box {
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0px 0px 4px 2px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  margin-top: 20px;
}

.landing2 .widget-box .widget-content {
  padding: 0 10px 10px 10px;
}

.landing2 iframe {
  border: none;
  width: 100%;
}

.landing2 .hlighlight-iframe {
  height: 300px;
  min-height: 500px;
}

.landing2 .hotmatch-iframe {
  height: 280px;
}

.landing2 .livestream-iframe {
  height: 180px;
}

.landing2 .footer {
  background-color: #1C61A2;
  color: #ffffffb2;
  font-size: 14px;
  overflow: auto;
  padding: 0;
  text-align: left;
}

.landing2 .footer .tnc {
  border-bottom: 1px rgba(255, 255, 255, 0.2) dashed !important;
  padding-top: 15px !important;
}

.landing2 .footer .tnc h4 img {
  width: 30px;
  display: inline-block;
  margin-right: 5px;
}

.landing2 .footer .tnc .icon img {
  width: 100%;
}

.landing2 .footer h4 {
  font-size: 16px;
  margin: 15px 0 10px 0;
  font-weight: normal;
}

.landing2 .license {
  border-right: 1px rgba(255, 255, 255, 0.2) dashed;
}

.landing2 .license .image-wrap2 {
  margin: 5px 10px;
}

.landing2 .license .image-wrap2:first-child {
  margin-left: 0;
}

.landing2 .browser-logo .image-wrap2 {
  margin: 5px 10px;
}

.landing2 .copyright {
  background-color: #215688;
  font-size: 12px;
  padding: 5px 0;
}

.landing2 .footer-wrapper {
  display: none;
}

.landing2 .tutorial {
  position: fixed;
  z-index: 101;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0;
  width: 100%;
  transition: bottom 0.3s;
  line-height: normal;
  max-width: 600px;
  display: none;
}

.landing2 .tutorial a {
  display: block;
}

.landing2 .tutorial .android {
  width: 100%;
  background-color: #fff;
  padding: 10px;
  font-size: 0.9rem;
  ;
  color: #000;
  position: relative;
}

.landing2 .tutorial .ios {
  width: calc(100% - 20px);
  background-color: #fff;
  padding: 10px;
  font-size: 0.9rem;
  ;
  color: #000;
  margin: 0 auto 15px;
  border-radius: 3px;
  position: relative;
}

.landing2 .tutorial .ios:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 0;
  height: 0;
  border-top: 10px solid#fff;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
}

.landing2 .tutorial .icon {
  width: 40px;
  display: block;
  margin-right: 10px;
}

.landing2 .tutorial .remove {
  margin-right: 10px;
  font-size: 1rem;
}

@media screen and (min-width: 500px) {
  .landing2 .swiper-slide p {
    font-size: 0.8rem;
    padding: 0 15px 0 15px;
  }

  .landing2 .banner-mobile {
    padding-top: 100px;
  }
}

@media screen and (min-width: 768px) {
  .landing2 {
    background-image: url('../../img2/landing2/top-bg.jpg');
    background-position: top;
  }

  .landing2 .lang-dropdown-wrap {
    top: 50px;
    right: 5px;
  }

  .landing2 .widget-wrap {
    background-image: url('../../img2/landing2/bg-bottom.jpg');
    padding: 0px 0px 30px 0px;
  }

  .landing2 .widget-wrap .row {
    margin: 0 -15px;
  }

  .landing2 .container {
    padding: 0 15px !important;
  }

  .landing2 .slider-wrap .container {
    padding: 0;
  }

  .landing2 .widget-box {
    margin: 0;
  }

  .landing2 .widget-wrap .widget-content-wrapper {
    display: flex;
  }

  .landing2 .widget-wrap .widget-content-wrapper .left,
  .landing2 .widget-wrap .widget-content-wrapper .right {
    width: 50%;
  }

  .landing2 .widget-wrap .widget-content-wrapper .right {
    padding-left: 30px;
  }

  .landing2 .widget-box .widget-box-header {
    font-size: 1rem;
  }

  .landing2 .hlighlight-iframe {
    height: 780px;
  }

  .landing2 .hotmatch-iframe {
    height: 290px;
  }

  .landing2 .livestream-iframe {
    height: 405px;
  }

  .landing2 .widget-box .widget-content {
    padding: 0 20px 20px 20px;
  }

  .landing2 .swiper-slide p {
    font-size: 1rem;
    padding: 0;
  }
}

@media screen and (min-width: 970px) {
  .landing2 .switch-device-mobile {
    display: block;
  }

  .landing2 .switch-device-desktop {
    display: none;
  }

  .landing2 .swiper-slide h1 {
    font-size: 2rem;
    padding: 0px;
  }

  .landing2 .banner-desktop {
    display: block;
  }

  .landing2 .banner-mobile {
    display: none;
  }

  .landing2 .swiper-slide-active .banner-text {
    animation-name: fadeInUp;
    -webkit-animation-name: fadeInUp;
    animation-duration: 1s;
    animation-fill-mode: both;
    -webkit-animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    opacity: 0;
  }

  @keyframes fadeInUp {
    from {
      top: 60%;
      opacity: 0;
      transform: translateY(-50%);
    }

    to {
      top: 50%;
      opacity: 1;
      transform: translateY(-50%);
    }
  }

  @-webkit-keyframes fadeInUp {
    from {
      top: 60%;
      opacity: 0;
      transform: translateY(-50%);
    }

    to {
      top: 50%;
      opacity: 1;
      transform: translateY(-50%);
    }
  }

  .landing2 .swiper-slide .banner-text {
    position: absolute;
    width: 30%;
    top: 50%;
    transform: translateY(-50%);
    left: auto;
    right: 0;
    text-align: left;
    opacity: 0;
    padding-right: 50px;
  }

  .landing2 .swiper-horizontal>.swiper-pagination-bullets,
  .landing2 .swiper-pagination-bullets.swiper-pagination-horizontal,
  .landing2 .swiper-pagination-custom,
  .landing2 .swiper-pagination-fraction {
    bottom: 50px;
    left: auto;
    width: 30%;
    text-align: left;
    right: 0;
  }

  .landing2 .swiper-slide p {
    font-size: 0.8rem;
    padding: 0;
  }

  .landing2 .break {
    display: block;
  }

  .landing2 .mobile-login {
    display: none;
  }

  .landing2 .login-group {
    display: flex;
  }

  .landing2 .login-group .invalid-feedback {
    margin-bottom: 0px;
    position: absolute;
    width: 100%;
    text-align: right;
    top: -20px;
    color: #ffee00;
    font-size: 11px;
  }

  .landing2 .event-slider-wrap {
    display: block;
  }

  .landing2 .widget-wrap {
    padding: 30px 0px 30px 0px;
  }

  .landing2 .footer-wrapper {
    display: block !important;
  }
}

@media screen and (min-width: 1028px) {
  .landing2 .logo {
    width: 165px;
    margin-left: 0;
  }

  .landing2 .header2.small-top .logo {
    width: 120px !important;
    transition: all 0.3s ease-in-out !important;
  }

  .landing2 .right-wrapper .line {
    height: 30px;
  }

  .landing2 .btn-select,
  .landing2 .btn-switch-device {
    width: 40px;
    height: 40px;
    line-height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.5);
  }

  .landing2 .header2.small-top .btn-select,
  .landing2 .header2.small-top .btn-switch-device {
    width: 32px !important;
    height: 32px !important;
    line-height: 32px !important;
    transition: all 0.3s ease-in-out !important;
  }

  .landing2 .header2.small-top .switch-device-mobile {
    font-size: 16px !important;
    transition: all 0.3s ease-in-out !important;
  }

  .landing2 .header2.small-top .btn-select img {
    width: 20px !important;
    transition: all 0.3s ease-in-out !important;
  }

  .landing2 .header2.small-top .login-btn2 {
    height: 32px !important;
    line-height: 32px !important;
    padding: 0 1rem !important;
    transition: all 0.3s ease-in-out !important;
  }

  .landing2 .header2.small-top .form-control {
    padding: 0.2rem 0.75rem !important;
    line-height: 1.3 !important;
    height: 32px !important;
    transition: all 0.3s ease-in-out !important;
  }

  .landing2 .swiper-slide h1 {
    font-size: 2.5rem;
    padding: 0px;
  }

  .landing2 .swiper-slide .banner-text {
    width: 28%;
  }

  .landing2 .swiper-horizontal>.swiper-pagination-bullets,
  .landing2 .swiper-pagination-bullets.swiper-pagination-horizontal,
  .landing2 .swiper-pagination-custom,
  .landing2 .swiper-pagination-fraction {
    bottom: 40px;
    left: auto;
    width: 28%;
    text-align: left;
    right: 0;
  }

  .landing2 .swiper-slide p {
    font-size: 1rem;
    padding: 0;
  }

  .landing2 .widget-wrap .widget-content-wrapper .left {
    width: 30%;
  }

  .landing2 .widget-wrap .widget-content-wrapper .right {
    width: 70%;
  }
}

@media screen and (min-width: 1400px) {

  .landing2 .swiper-horizontal>.swiper-pagination-bullets,
  .landing2 .swiper-pagination-bullets.swiper-pagination-horizontal,
  .landing2 .swiper-pagination-custom,
  .landing2 .swiper-pagination-fraction {
    bottom: 100px;
  }
}

.landing2 .footer .tnc {
  padding: 0;
  border-top: inherit;
}

.landing2 .footer .license, .landing2 .footer .tnc {
  text-align: left;
  max-width: inherit;
  margin: 0;
}

@media (max-width: 1600px) {

  .landing2 .container,
  .landing2 header .topbar .container,
  .landing2 header .toolbar .container {
    width: 100%;
  }
}

.landing.wbet {
  overflow-x: initial;
}

.landing2 .header2.small-top .logo img {
  width: 120px !important;
  max-width: 100% !important;
}
