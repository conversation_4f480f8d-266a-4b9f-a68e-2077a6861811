import Vue from "vue";
import { EventBus } from "@/library/_event-bus.js";

function k_combinations(set, k) {
  var i, j, combs, head, tailcombs;
  if (k > set.length || k <= 0) {
    return [];
  }
  if (k == set.length) {
    return [set];
  }
  if (k == 1) {
    combs = [];
    for (i = 0; i < set.length; i++) {
      combs.push([set[i]]);
    }
    return combs;
  }
  combs = [];
  for (i = 0; i < set.length - k + 1; i++) {
    head = set.slice(i, i + 1);
    tailcombs = k_combinations(set.slice(i + 1), k - 1);
    for (j = 0; j < tailcombs.length; j++) {
      combs.push(head.concat(tailcombs[j]));
    }
  }
  return combs;
}

function combinations(set) {
  var k, i, combs, k_combs;
  combs = {};
  if (set.length > 10) {
    k = set.length;
    combs[k] = [];
    k_combs = k_combinations(set, k);
    for (i = 0; i < k_combs.length; i++) {
      combs[k].push(k_combs[i]);
    }
  } else {
    for (k = 2; k <= set.length; k++) {
      combs[k] = [];
      k_combs = k_combinations(set, k);
      for (i = 0; i < k_combs.length; i++) {
        combs[k].push(k_combs[i]);
      }
    }
  }
  return combs;
}

export default {
  namespaced: true,
  state: {
    combo: {},
    data: {}
  },
  mutations: {
    updateCombo(state, payload) {
      var sets = Object.keys(state.data);

      state.combo = combinations(sets);
      // console.log(sets, state.combo);
    },
    updateData(state, payload) {
      var matchid = payload.parent != undefined && typeof payload.parent != "object" ? payload.parent : payload.matchId;
      Vue.set(state.data, matchid, payload);
    },
    deleteData(state, payload) {
      // console.log("deleteData");
      Vue.delete(state.data, payload);
    },
    purgeData(state, payload) {
      // console.log("purgeData");
      for (var n in state.data) {
        Vue.delete(state.data, n);
      }
      state.combo = {};
    },
    updateSingleData(state, payload) {
      if (payload.property != undefined) {
        state.data[payload.matchid][payload.property] = payload.value;
      }
    }
  },
  actions: {
    setCombo(context, payload) {
      context.commit("updateCombo", payload);
    },
    setData(context, payload) {
      context.commit("updateData", payload);
    },
    removeData(context, payload) {
      context.commit("deleteData", payload);
    },
    clearData(context, payload) {
      context.commit("purgeData");
    },
    setSingleData(context, payload) {
      context.commit("updateSingleData", payload);
    }
  }
};
