<template lang="pug">
.match-info-single
  template(v-if="racingList.includes(betslip.sportsType)")
    template(v-if="racingList1.includes(betslip.sportsType)")
      .team01.text-center.py-2 {{ betslip.homeName }}
      .league.text-center.py-0(v-if="racingList.includes(betslip.sportsType) && betslip.matchTime") No. {{ $dayjs(betslip.matchTime).format("MMDDhhmm") }}
    template(v-if="racingList2.includes(betslip.sportsType)")
      //- template(v-if="['CS'].includes(betslip.betType)")
      //-   .team01.text-center.red {{ betslip.homeName }}
      //-   .text-vs.text-center - VS -
      //-   .team02.text-center  {{ betslip.awayName }}
      template(v-if="['ML','1X2'].includes(betslip.betType)")
        .team01.text-center(v-if="betslip.idx == 5") {{ betslip.homeName }}
        .team02.text-center(v-else)  {{ betslip.awayName }}
      template(v-if="['OU','OE'].includes(betslip.betType)")
        .team01.text-center.py-2 {{ betslip.homeName }}
      .league.text-center.py-0(v-if="racingList.includes(betslip.sportsType) && betslip.matchTime") No. {{ $dayjs(betslip.matchTime).format("MMDDhhmm") }}
    template(v-if="racingList3.includes(betslip.sportsType)")
      .team01.text-center.py-2 {{ betslip.homeName }}
      .league.text-center.py-0(v-if="racingList.includes(betslip.sportsType) && betslip.matchTime") No. {{ $dayjs(betslip.matchTime).format("MMDDhhmm") }}
  template(v-else)
    .team01.text-center(:class="betslip.giving == 1 && isBallDisplay ? 'red' : ''") {{ betslip.homeName }}
    template(v-if="betslip.betType != 'OR'")
      .text-vs.text-center - VS -
      .team02.text-center(:class="betslip.giving == 0 && isBallDisplay ? 'red' : ''")  {{ betslip.awayName }}
    .league.text-center {{ betslip.leagueName }}
</template>

<script>
import naming from "@/library/_name";
import config from "@/config";

export default {
  props: {
    betslip: {
      type: Object
    }
  },
  computed: {
    racingList() {
      return config.racingList;
    },
    racingList1() {
      return config.racingList1;
    },
    racingList2() {
      return config.racingList2;
    },
    racingList3() {
      return config.racingList3;
    },
    isBallDisplay() {
      var result = naming.ballDisplay2(this.betslip.ballDisplay, this.betslip.giving, this.betslip.homeAway, this.betslip.betType, this);
      if (["HDP", "HDPH"].includes(this.betslip.betType)) {
        return result != null && result != "0";
      } else {
        return false;
      }
    }
  }
};
</script>