<template lang="pug">
#desktop
  .container.viewport(:class="{ active: !header }")
    .content.d-flex.flex-row
      leftBar
      mainPanel
      rightBar.flex-fill
</template>

<script>
export default {
  components: {
    leftBar: () => import("@/components/desktop/leftBar"),
    rightBar: () => import("@/components/desktop/rightBar"),
    mainPanel: () => import("@/components/desktop/mainPanel")
  },
  data() {
    return {};
  },
  computed: {
    header() {
      return this.$store.getters.header;
    }
  },
  mounted() {
    // Operator type logging removed for production
  }
};
</script>
