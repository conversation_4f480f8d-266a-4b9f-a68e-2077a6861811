<template lang="pug">
header.short
  .topbar.info-top
    .hl-container.d-flex.align-items-center.justify-content-center
      template(v-if="whiteLabel")
        router-link(to="/desktop")
          i.fas.fa-chevron-left
          span {{ $t("ui.back")  }}
      .d-flex.info-content.align-items-center.hl-mod1.flex-fill
        template(v-if="!whiteLabel")
          .logo(@click="hardRefresh()")
            logo
        .nav-info
          a(href="javascript:void(0);" @click="go('/info/betlist')" :class="{ active: pageName == 'betlist' }").nav-link  {{ $t("ui.bet_list") }}
        .nav-info
          a(href="javascript:void(0);" @click="go('/info/statement')" :class="{ active: pageName == 'statement' }").nav-link {{ $t("ui.statement") }}
        .nav-info
          a(href="javascript:void(0);" @click="go('/info/sysparlay')" :class="{ active: pageName == 'sysparlay' }").nav-link {{ $t("ui.sysparlay") }}
        .nav-info
          a(href="javascript:void(0);" @click="go('/info/result')" :class="{ active: pageName == 'result' }").nav-link {{ $t("ui.result") }}
        .nav-info
          a(href="javascript:void(0);" @click="go('/info/message')" :class="{ active: pageName == 'message' }").nav-link {{ $t("ui.message") }}
        .nav-info
          a(href="javascript:void(0);" @click="go('/info/rules')" :class="{ active: pageName == 'rules' }").nav-link {{ $t("ui.rules") }}
        .nav-info
          a(href="javascript:void(0);" @click="go('/info/settings')" :class="{ active: pageName == 'settings' }").nav-link {{ $t("ui.settings") }}

</template>

<script>
import config from "@/config";


export default {
  components: {
    logo: () => import("@/components/desktop/logo")
  },
  data() {
    return {
      page: location.pathname.split("/").pop()
    };
  },
  computed: {
    whiteLabel() {
      return config.whiteLabel;
    },
    pageName() {
      return this.page;
    },
    language() {
      return this.$store.getters.language;
    }
  },
  methods: {
    hardRefresh() {
      window.location.reload(true);
    },
    go(tab) {
      // var routeData = this.$router.resolve('/' + tab);
      // this.$helpers.info(routeData.href);
      if (tab.split("/").pop() === "statement" && this.pageName === "statement") window.location.reload();
      if (this.page != tab.split("/").pop()) {
        this.$router.push(tab);
        this.page = tab.split("/").pop();
      }
    }
  }
};
</script>