# Game Integration Guide for Member-SPA

This guide provides step-by-step instructions for AI assistants to integrate new game providers into the member-spa project.

## Overview

The member-spa project supports multiple game providers with a standardized integration approach. Each game provider requires configuration in several files and follows specific patterns.

## Prerequisites

- Understanding of Vue.js components
- Knowledge of the project's file structure
- Access to the game provider's API documentation

## Integration Steps

### 1. Game Provider Configuration

#### 1.1 Add Provider Configuration
**File:** `src/config/game-providers.js`

Add the new provider configuration following the existing pattern:

```javascript
// Example for a new provider "NEWGAME"
NEWGAME: {
  name: "NewGame",
  pattern: "username/sessionid", // or "playerid/password"
  apiEndpoint: "https://api.newgame.com/launch",
  requiredParams: ["username", "sessionid", "lang", "mobile"],
  optionalParams: ["currency", "game_id"],
  validation: {
    // Add any specific validation rules
  }
}
```

**Pattern Types:**
- `"username/sessionid"`: Uses username and session token
- `"playerid/password"`: Uses player ID and password

#### 1.2 Add Environment Variables
**File:** `config.js`

Add the provider's URL configuration:

```javascript
// Add to the config object
newgameUrl() {
  return process.env.VUE_APP_NEWGAME_URL || "https://api.newgame.com";
}
```

**File:** `.env.development`

Add the environment variable:

```env
VUE_APP_NEWGAME_URL=https://newgame.uat001.com
```

### 2. Game Launch Function

#### 2.1 Add Launch Function
**File:** `src/library/_xhr-game.js`

Add the launch function using the utility system:

```javascript
// For username/sessionid pattern
launchNewGame(args) {
  return this.launchGameWithUtility("NEWGAME", args);
}

// For playerid/password pattern
launchNewGame(args) {
  return this.launchGameWithPassword("NEWGAME", args);
}
```

#### 2.2 Add Loading State
**File:** `src/library/_xhr-game.js`

Add to the loading states object:

```javascript
loading: {
  // ... existing states
  newgame: false
}
```

### 3. Menu Integration

#### 3.1 Add Menu Entry
**File:** `src/config/game-menu.js`

Add to the appropriate section (slots, casino, others, etc.):

```javascript
{
  name: "NewGame",
  isHidden: false,
  isNewFeatures: false,
  toPath: "/newgame",
  popup: "window.open(this.href,'newgame','top=10,height=572,width=1024,status=no,toolbar=no,menubar=no,location=no');return false;",
  thumbnail: "img/lobby/newgame.png?v=5",
  title: "ui.newgame",
  exclude: [],
  currency: ['CNY', 'IDR', 'THB', 'MYR', 'USD', 'SGD', 'MMK']
}
```

### 4. Vue Component Creation

#### 4.1 Create Game Component
**File:** `src/views/game/newgame.vue`

Create a new Vue component following the pattern:

```vue
<template lang="pug">
.wrapper.h-100v.p-3
  .preloader.d-flex.align-items-center.justify-content-center(v-if="error")
    .game-warning
      i.fad.fa-exclamation-triangle.mr-1
      span {{ error }}
  .lds-overlay(v-else)
    .lds-roller
      div
      div
      div
      div
      div
      div
      div
      div
</template>

<script>
import config from "@/config";
import xhr from "@/library/_xhr-game.js";
import mixinTypes from "@/library/mixinTypes";
import errors from "@/errors";

export default {
  data() {
    return {
      url: "",
      error: "",
    };
  },
  computed: {
    whiteLabel() {
      return this.$store.getters.whiteLabel.mode;
    },
  },
  beforeCreate() {
    $("html").addClass("minimal noscrollbar");
    $("body").addClass("minimal noscrollbar");
  },
  created() {
    $("html").addClass("minimal noscrollbar");
    $("body").addClass("minimal noscrollbar");

    if (this.whiteLabel) return;

    this.launchGame();
  },
  methods: {
    isMobileTablet() {
      // Mobile detection logic (reuse from existing components)
    },
    launchGame() {
      var q = this.$route.query;
      var game = "0";
      if (q.game) {
        game = q.game;
      }
      var mobile = false;
      if (this.isMobileTablet()) {
        mobile = true;
      }
      var args = {
        username: this.$store.getters.accountId,
        sessionid: this.$store.getters.sessionToken,
        lang: this.$store.getters.language,
        mobile: mobile.toString(),
      };

      xhr.launchNewGame(args).then(
        (res) => {
          this.loading = false;
          if (res) {
            if (res.success == true) {
              this.url = res.data;
              if (this.url.includes("http")) {
                setTimeout(() => {
                  window.location.replace(this.url);
                }, 200);
              } else {
                // Handle error cases
                this.handleError(res.status);
              }
            } else {
              this.handleError(res.status);
            }
          }
        },
        (err) => {
          this.loading = false;
          this.handleError(err.status);
        }
      );
    },
    handleError(status) {
      switch (status) {
        case "lotteryDisabled":
        case "currencyNotSupported":
        case "liveCasinoDisabled":
        case "EsportsDisabled":
        case "MiniGameDisabled":
          this.error = this.$t("error." + status);
          break;
        case "account_status_suspend":
          this.error = this.$t("error.SUSPEND");
          break;
        default:
          this.error = this.$t("error.game_maintenance");
          break;
      }
    },
  },
};
</script>
```

#### 4.2 Add Router Route
**File:** `src/router/index.js`

Add the route for the new game:

```javascript
{
  path: "/newgame",
  component: () => import("@/views/game/newgame.vue"),
  meta: { requiredLoggedIn: true },
},
```

### 5. Game Result Integration (Optional)

If the game provider supports result viewing, add result functionality:

#### 5.1 Add Result Template
**File:** `src/components/desktop/info/statement/slotsGameResult.vue`

Add to the template section:

```pug
template(v-if="item.game_provider == 'NG'")
  a.icon-info(v-if="!loading.newgame" href="javascript:void(0);" title='Result' @click="getResultNewGame(item.bet_id)")
    i.fad.fa-poll
  a.icon-info(v-else href="javascript:void(0);")
    i.fa.fa-spin.fa-spinner
```

#### 5.2 Add Loading State
**File:** `src/components/desktop/info/statement/slotsGameResult.vue`

Add to the loading object:

```javascript
loading: {
  // ... existing states
  newgame: false
}
```

#### 5.3 Add Result Method
**File:** `src/components/desktop/info/statement/slotsGameResult.vue`

Add the result method:

```javascript
getResultNewGame(e) {
  if (this.loading.newgame) return;
  var id = e;
  var url = "";
  const args = {
    resultid: id.toString(),
    userid: this.account_id,
    currency: this.currency_code,
  };
  this.loading.newgame = true;
  this.$http.post(config.newgamePath, args).then(
    (res) => {
      this.loading.newgame = false;
      if (res.data) {
        url = res.data.detail_url;
        if (url) {
          const newWindow = window.open("", "resultnewgame", "top=10,height=240,width=640,status=no,toolbar=no,menubar=no,location=no");
          if (newWindow) {
            newWindow.document.head.innerHTML = `
              <title>Game Result</title>
              <link rel="stylesheet" href="/css/bootstrap.min.css">
              <link rel="stylesheet" href="/css/app.css">
              <link rel="stylesheet" href="/css/desktop/info.css">
              <style>
                body { 
                  margin: 20px; 
                  background: #F4F7FC;
                }
              </style>
            `;
            newWindow.document.body.innerHTML = `<div class="result-container">${url}</div>`;
          }
        } else {
          this.$swal("Error", res.data.errorMessage, "error");
        }
      } else {
        this.$swal("Error", res.data.errorMessage, "error");
      }
    },
    (err) => {
      this.loading.newgame = false;
      this.$swal("Error", err, "error");
    }
  );
},
```

#### 5.4 Add Config Path
**File:** `config.js`

Add the result API path:

```javascript
newgamePath() {
  return this.apiPath() + "/newgame/result";
}
```

### 6. Translation Keys

#### 6.1 Add Translation Keys
**File:** `src/locales/en/_ui.js`

Add the UI translation:

```javascript
newgame: "NewGame",
```

**File:** `src/locales/en/_message.js`

Add any message translations:

```javascript
newgame_maintenance: "NewGame is currently under maintenance",
```

### 7. Assets

#### 7.1 Add Thumbnail Image
**File:** `public/img/lobby/newgame.png`

Add the game provider's thumbnail image.

## Common Patterns

### Username/Session ID Pattern
Used by most providers:
- Pragmatic Play (PP)
- Joker Gaming (JK)
- PG Soft (PGS)
- Simple Play (SP)
- JILI
- UUSL
- NextSpin (NEXT)
- WOW Gaming (WOW)
- Live22 (OSG)
- WF Gaming (WF)
- EpicWin (EPW)
- AIO (BI)

### Player ID/Password Pattern
Used by some providers:
- DBPoker (DB)
- Arcardia (AR)

## Error Handling

Standard error handling includes:
- `lotteryDisabled`
- `currencyNotSupported`
- `liveCasinoDisabled`
- `EsportsDisabled`
- `MiniGameDisabled`
- `account_status_suspend`
- `game_maintenance` (default)

## Testing Checklist

- [ ] Game launches successfully
- [ ] Mobile detection works
- [ ] Error handling displays correct messages
- [ ] Menu entry appears correctly
- [ ] Route navigation works
- [ ] Result viewing works (if applicable)
- [ ] Loading states work properly
- [ ] Translation keys are displayed correctly

## Notes

- Always follow the existing naming conventions
- Use the utility system for game launches when possible
- Maintain consistency with existing code patterns
- Test thoroughly before deployment
- Update this guide when adding new patterns or requirements

## Example Integration

See the AIO (BI) integration as a complete example:
- Provider ID: `BI`
- Pattern: `username/sessionid`
- Files modified: All files mentioned above
- Component: `others1.vue`
- Route: `/others1`

## Code Optimization and Maintainability

### Current Architecture Analysis

The current integration approach has evolved from individual launch functions to a centralized utility system, providing significant benefits:

#### ✅ **Strengths**
- **Centralized Configuration**: All provider configs in `src/config/game-providers.js`
- **Utility System**: Reduces code duplication by ~85%
- **Standardized Patterns**: Two main patterns (username/sessionid, playerid/password)
- **Consistent Error Handling**: Unified error management across providers
- **Reusable Components**: Common Vue component structure

#### ⚠️ **Areas for Improvement**
- **Component Duplication**: Each provider still requires a separate Vue component
- **Menu Configuration**: Manual menu entry creation for each provider
- **Result Integration**: Repetitive result viewing code
- **Translation Management**: Manual translation key addition

### Optimization Strategies

#### 1. **Dynamic Component Generation**

**Current Issue**: Each provider needs a separate Vue component file
**Solution**: Create a generic game launcher component

```javascript
// src/views/game/GameLauncher.vue
<template lang="pug">
.wrapper.h-100v.p-3
  .preloader.d-flex.align-items-center.justify-content-center(v-if="error")
    .game-warning
      i.fad.fa-exclamation-triangle.mr-1
      span {{ error }}
  .lds-overlay(v-else)
    .lds-roller
      div(v-for="i in 8" :key="i")
</template>

<script>
export default {
  props: {
    providerId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      url: "",
      error: "",
    };
  },
  computed: {
    whiteLabel() {
      return this.$store.getters.whiteLabel.mode;
    },
    providerConfig() {
      return this.$store.getters.gameProviders[this.providerId];
    }
  },
  methods: {
    launchGame() {
      const args = this.buildLaunchArgs();
      const launchMethod = this.getLaunchMethod();
      
      launchMethod(args).then(
        this.handleSuccess,
        this.handleError
      );
    },
    buildLaunchArgs() {
      // Build args based on provider pattern
    },
    getLaunchMethod() {
      // Return appropriate launch method based on provider
    }
  }
};
</script>
```

#### 2. **Dynamic Menu Generation**

**Current Issue**: Manual menu entry creation
**Solution**: Generate menu from provider configuration

```javascript
// src/menuList.js - Enhanced version
const generateMenuFromProviders = () => {
  const { GAME_PROVIDERS_CONFIG } = require('./config/game-providers.js');
  const menu = { slots: [], casino: [], others: [] };
  
  Object.entries(providers).forEach(([id, config]) => {
    const menuItem = {
      name: config.name,
      isHidden: config.isHidden || false,
      isNewFeatures: config.isNewFeatures || false,
      toPath: `/game/${id.toLowerCase()}`,
      popup: `window.open(this.href,'${id.toLowerCase()}','top=10,height=572,width=1024,status=no,toolbar=no,menubar=no,location=no');return false;`,
      thumbnail: `img/lobby/${id.toLowerCase()}.png?v=5`,
      title: `ui.${id.toLowerCase()}`,
      exclude: config.exclude || [],
      currency: config.currency || ['CNY', 'IDR', 'THB', 'MYR', 'USD', 'SGD', 'MMK']
    };
    
    // Categorize based on provider type
    if (config.category === 'slots') menu.slots.push(menuItem);
    else if (config.category === 'casino') menu.casino.push(menuItem);
    else menu.others.push(menuItem);
  });
  
  return menu;
};
```

#### 3. **Unified Result System**

**Current Issue**: Repetitive result viewing code
**Solution**: Create a generic result handler

```javascript
// src/library/_game-result-handler.js
export class GameResultHandler {
  constructor(providerId, config) {
    this.providerId = providerId;
    this.config = config;
  }
  
  async getResult(betId, userData) {
    const args = this.buildResultArgs(betId, userData);
    const response = await this.makeRequest(args);
    return this.handleResponse(response);
  }
  
  buildResultArgs(betId, userData) {
    const baseArgs = {
      resultid: betId.toString(),
      userid: userData.accountId,
      currency: userData.currencyCode
    };
    
    // Merge with provider-specific args
    return { ...baseArgs, ...this.config.resultArgs };
  }
  
  handleResponse(response) {
    if (response.data?.detail_url) {
      this.openResultWindow(response.data.detail_url);
    } else {
      this.showError(response.data?.errorMessage);
    }
  }
  
  openResultWindow(url) {
    const windowName = `result${this.providerId.toLowerCase()}`;
    const newWindow = window.open("", windowName, "top=10,height=240,width=640,status=no,toolbar=no,menubar=no,location=no");
    
    if (newWindow) {
      newWindow.document.head.innerHTML = this.getResultWindowStyles();
      newWindow.document.body.innerHTML = `<div class="result-container">${url}</div>`;
    }
  }
  
  getResultWindowStyles() {
    return `
      <title>Game Result</title>
      <link rel="stylesheet" href="/css/bootstrap.min.css">
      <link rel="stylesheet" href="/css/app.css">
      <link rel="stylesheet" href="/css/desktop/info.css">
      <style>
        body { 
          margin: 20px; 
          background: #F4F7FC;
        }
      </style>
    `;
  }
}
```

#### 4. **Enhanced Provider Configuration**

**Current Issue**: Limited configuration options
**Solution**: Extended configuration schema

```javascript
// Enhanced provider configuration
NEWGAME: {
  name: "NewGame",
  pattern: "username/sessionid",
  category: "slots", // auto-categorize for menu
  apiEndpoint: "https://api.newgame.com/launch",
  requiredParams: ["username", "sessionid", "lang", "mobile"],
  optionalParams: ["currency", "game_id"],
  validation: {
    // Validation rules
  },
  result: {
    enabled: true,
    endpoint: "/newgame/result",
    args: {
      // Provider-specific result args
    }
  },
  ui: {
    windowSize: {
      height: 572,
      width: 1024
    },
    thumbnail: "newgame.png",
    excludeFrom: [], // White labels to exclude from
    supportedCurrencies: ['CNY', 'IDR', 'THB', 'MYR', 'USD', 'SGD', 'MMK']
  },
  features: {
    mobileSupport: true,
    resultViewing: true,
    gameHistory: true
  }
}
```

#### 5. **Automated Translation Management**

**Current Issue**: Manual translation key addition
**Solution**: Auto-generate translation keys

```javascript
// src/utils/translation-generator.js
export class TranslationGenerator {
  static generateProviderTranslations(providers) {
    const translations = {
      ui: {},
      message: {},
      error: {}
    };
    
    Object.entries(providers).forEach(([id, config]) => {
      const key = id.toLowerCase();
      
      // Generate UI translations
      translations.ui[key] = config.name;
      
      // Generate message translations
      translations.message[`${key}_maintenance`] = `${config.name} is currently under maintenance`;
      
      // Generate error translations
      translations.error[`${key}_error`] = `${config.name} encountered an error`;
    });
    
    return translations;
  }
  
  static updateTranslationFiles(translations) {
    // Auto-update all locale files
    const locales = ['en', 'cn', 'th', 'id', 'my', 'jp', 'kr', 'tw', 'vi'];
    
    locales.forEach(locale => {
      this.updateLocaleFile(locale, translations);
    });
  }
}
```

### Implementation Roadmap

#### Phase 1: Foundation (Week 1-2)
- [ ] Create generic GameLauncher component
- [ ] Implement dynamic menu generation
- [ ] Create GameResultHandler class
- [ ] Update provider configuration schema

#### Phase 2: Migration (Week 3-4)
- [ ] Migrate existing providers to new system
- [ ] Update router to use dynamic routes
- [ ] Implement automated translation generation
- [ ] Create migration scripts

#### Phase 3: Enhancement (Week 5-6)
- [ ] Add provider-specific customization options
- [ ] Implement advanced error handling
- [ ] Create provider management dashboard
- [ ] Add automated testing for new providers

#### Phase 4: Optimization (Week 7-8)
- [ ] Performance optimization
- [ ] Code splitting for large provider lists
- [ ] Caching strategies
- [ ] Documentation updates

### Benefits of Optimization

#### 🚀 **Performance Improvements**
- **Reduced Bundle Size**: Eliminate duplicate component code
- **Faster Loading**: Dynamic imports and code splitting
- **Better Caching**: Optimized asset loading

#### 🛠️ **Development Efficiency**
- **Faster Integration**: New providers in minutes, not hours
- **Reduced Errors**: Standardized patterns reduce bugs
- **Easier Testing**: Centralized logic simplifies testing

#### 🔧 **Maintenance Benefits**
- **Single Source of Truth**: All provider configs in one place
- **Automatic Updates**: Changes propagate automatically
- **Consistent UI**: Standardized styling and behavior

#### 📈 **Scalability**
- **Unlimited Providers**: No limit on number of providers
- **Easy Customization**: Provider-specific overrides
- **Future-Proof**: Extensible architecture

### Migration Strategy

#### For Existing Providers
1. **Backward Compatibility**: Maintain existing functionality during migration
2. **Gradual Migration**: Migrate one provider at a time
3. **Feature Flags**: Use feature flags to control new vs old system
4. **Testing**: Comprehensive testing at each step

#### For New Providers
1. **Use New System**: All new providers use optimized system
2. **Documentation**: Updated guides and examples
3. **Training**: Team training on new patterns

### Monitoring and Metrics

#### Key Performance Indicators
- **Integration Time**: Time to add new provider
- **Bundle Size**: Impact on application size
- **Error Rate**: Reduction in integration errors
- **Maintenance Time**: Time spent on provider updates

#### Monitoring Tools
- **Bundle Analyzer**: Track bundle size changes
- **Error Tracking**: Monitor integration errors
- **Performance Monitoring**: Track loading times
- **Usage Analytics**: Monitor provider usage

This optimization plan transforms the current manual integration process into an automated, scalable system that reduces development time, improves maintainability, and ensures consistency across all game providers. 