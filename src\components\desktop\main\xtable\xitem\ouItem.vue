<template lang="pug">
  .hx-col.hx-cols(:class="cls ? cls : 'w-98'")
    template(v-if="details[betType] != null && details[betType][i] != null && details[betType][i][11] != 0 && details[betType][i][12] != 0 && details[betType][i][11] != '' && details[betType][i][12] != ''")
      .hx
        .hxs.w-43
          .ball-value {{ details[betType][i][8] }}
        .hxs.w-43r
          oddsItem(:odds="details[betType][i]" idx=12 :typ="oddsType" dataType="1")
      .hx
        .hxs.w-43
          .ball-value u
        .hxs.w-43r
          oddsItem(:odds="details[betType][i]" idx=11 :typ="oddsType" dataType="1")
</template>

<script>
// import oddsItem from "@/components/desktop/main/xtable/oddsItem";

export default {
  components: {
    oddsItem: () => import("@/components/desktop/main/xtable/oddsItem")
  },
  props: {
    cls: {
      type: String
    },
    details: {
      type: Object
    },
    oddsType: {
      type: String
    },
    item: {
      type: String
    },
    i: {
      type: [String, Number]
    },
    betType: {
      type: String
    }
  },
  // updated: function() {
  //   this.$nextTick(function() {
  //     console.log("cs", new Date(), this.oddsType, this.item);
  //   });
  // }
};
</script>
