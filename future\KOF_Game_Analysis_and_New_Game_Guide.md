# KOF (King of Fighters) Game Analysis and New Game Integration Guide

## Overview
This document provides a comprehensive analysis of KOF (sports id 43) implementation in the sb-desktop system and serves as a guide for adding new esports games.

## KOF (Sports ID 43) Implementation Analysis

### 1. Game Configuration

#### Core Configuration (src/config.js)
```javascript
// Game Parameters
params: {
  43: {
    delay: 0,
    duration: 5,
    live: 1,
    thumbnail: "images/esports/game-thumb/kof-thumb.jpg",
  }
}

// Game Stream Configuration
gameStream: {
  43: "vDYS8-N7FsO", // KOF
  143: "vDYS8-N7FsO", // KOF (alternative ID)
}

// Game Lists
gameList: ["50", "52", "49", "51", "43", "41", "44", "42", "45", "46", "47"]
efightList: [41, 42, 43, 44, 47, 52], // Fighter games
special: [40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52]
day1: [1, 20, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52]
```

#### Sport Order Configuration
```javascript
defaultSportOrder: [
  1, 40, 2, 7, 3, 5, 12, 4, 11, 8, 30, 9, 13, 6, 19, 35, 14, 15, 24, 34, 25, 23, 17, 31, 16, 27, 10, 18, 26, 36, 37, 32, 21, 28, 38, 29, 20, 22, 39, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52,
]
```

### 2. Visual Assets

#### Required Image Files
- **Thumbnail**: `public/images/esports/game-thumb/kof-thumb.jpg`
- **Video Background**: `public/images/esports/video-bg/video-bg-kof.png`
- **Sport Icon**: `public/images/icon-sport-svg/43.svg` (referenced via `config.getSportsImage()`)

#### CSS Styling
```css
/* Video background styling */
.esports-heroes.sports-43 {
  background: url(/images/esports/video-bg/video-bg-kof.png) no-repeat;
  background-size: cover;
}
```

### 3. Game Type Classification

KOF is classified as a **Fighter Game** and belongs to the `efightList` category, which includes:
- 41: SF4 (Street Fighter 4)
- 42: UFC3
- 43: KOF (King of Fighters)
- 44: MK (Mortal Kombat)
- 47: TEKKEN 8
- 52: DRAGON BALL

### 4. UI Components and Rendering

#### Main Display Components
- **efSlider.vue**: Game thumbnail carousel
- **efBlock.vue**: Main esports block with live streaming
- **efItem1.vue**: Individual match display for fighter games
- **efGroup.vue**: Group handling for different game types

#### Special Handling for Fighter Games
```javascript
// In efBlock.vue
template(v-if="efightList.includes(parseInt(ll.sportsId))")
  //- Fighting Sports
  template(v-for="(mml, mmk) in ll.matchList")
    efItem1(:source="mml", :index="parseInt(mmk)", :key="mmk + mml.id")
```

### 5. Betting System

#### Supported Bet Types
KOF supports standard esports betting types:
- **HDP** (Handicap)
- **OU** (Over/Under)
- **OE** (Odd/Even)
- **ML** (Money Line)

#### Betting Components
- **oddsItem.vue**: Individual odds display
- **hdpouMMO.vue**: MMO (Multi-Market Odds) handling
- **betSingle.vue**: Single bet processing
- **betParlay.vue**: Parlay bet processing

### 6. Live Streaming Integration

#### Stream URL Construction
```javascript
// Base URL pattern
bintuUrl() {
  return this.liveUrl + "/bintu?v=";
}

// KOF specific stream
// https://wlive888.com/bintu?v=vDYS8-N7FsO&u=mymymymy005&t=b0443b641e9ce9c3daf3fc40dba225cf&product=43
```

#### Stream Parameters
- **Stream ID**: `vDYS8-N7FsO`
- **Product ID**: `43`
- **User**: Dynamic user session
- **Token**: Authentication token

### 7. Result Handling

#### Result Display
- **efresult.vue**: Match result display
- **esports2GameResult.vue**: Game result processing
- **esports2Details.vue**: Detailed bet information

#### Special Result Logic
```javascript
// In efresult.vue - KOF specific handling
template(v-if="!['41','47','52'].includes(sportsType)")
  // KOF shows Over/Under results
  .digits.rounded-circle.mx-auto.text-white(:class="item.over_under == 'over' ? 'ball-over' : 'ball-under'")
    {{ $t('m.LOT_'+item.over_under.toUpperCase()) }}
```

## New Game Integration Guide

### Step 1: Configuration Setup

#### 1.1 Add Game Parameters
```javascript
// In src/config.js
params: {
  [NEW_GAME_ID]: {
    delay: 0,           // Stream delay in seconds
    duration: 5,        // Match duration in minutes
    live: 1,           // Live status (1 = live, 0 = not live)
    thumbnail: "images/esports/game-thumb/[game-name]-thumb.jpg",
  }
}
```

#### 1.2 Add Game Stream Configuration
```javascript
gameStream: {
  [NEW_GAME_ID]: "vDYS8-[STREAM_ID]", // Main stream ID
  [NEW_GAME_ID + 100]: "vDYS8-[STREAM_ID]", // Alternative stream ID
}
```

#### 1.3 Update Game Lists
```javascript
// Add to appropriate game lists
gameList: [...existing_games, NEW_GAME_ID.toString()]
efightList: [...existing_fighter_games, NEW_GAME_ID] // If fighter game
racingList: [...existing_racing_games, NEW_GAME_ID] // If racing game
special: [...existing_special_games, NEW_GAME_ID]
day1: [...existing_day1_games, NEW_GAME_ID]
```

#### 1.4 Update Sport Order
```javascript
defaultSportOrder: [
  // ... existing sports, NEW_GAME_ID
]
```

### Step 2: Visual Assets

#### 2.1 Required Images
Create the following files:
- `public/images/esports/game-thumb/[game-name]-thumb.jpg` (270x95px recommended)
- `public/images/esports/video-bg/video-bg-[game-name].png` (540x305px recommended)
- `public/images/icon-sport-svg/[NEW_GAME_ID].svg` (22x22px)

#### 2.2 CSS Styling
Add to `public/css/app.css`:
```css
.esports-heroes.sports-[NEW_GAME_ID] {
  background: url(/images/esports/video-bg/video-bg-[game-name].png) no-repeat;
  background-size: cover;
}
```

### Step 3: Game Type Classification

#### 3.1 Determine Game Category
- **Fighter Games**: Add to `efightList` - uses `efItem1.vue` component
- **Racing Games**: Add to `racingList` - uses `efGroup.vue` with specific item components
- **Other Games**: May need custom component development

#### 3.2 Component Selection
```javascript
// Fighter games use efItem1.vue
// Racing games use efGroup.vue with:
// - efItem3.vue for greyhound-like games
// - efItem4.vue for marble clash games  
// - efItem5.vue for marble survival games
```

### Step 4: Betting System Integration

#### 4.1 Bet Type Support
Ensure the game supports standard betting types:
- HDP (Handicap)
- OU (Over/Under)
- OE (Odd/Even)
- ML (Money Line)

#### 4.2 MMO Support (if applicable)
If the game supports Multi-Market Odds:
```javascript
// Add to MMO handling components
// Update hdpouMMO.vue, betParlayMMO.vue as needed
```

### Step 5: Result Handling

#### 5.1 Result Display Logic
Update result handling in `efresult.vue`:
```javascript
// Add game-specific result logic
template(v-if="![GAME_IDS_TO_EXCLUDE].includes(sportsType)")
  // Custom result display logic
```

#### 5.2 Game Result Processing
Update `esports2GameResult.vue` and related components for proper result handling.

### Step 6: Testing and Validation

#### 6.1 Component Testing
- Test game thumbnail display in efSlider
- Test live stream integration
- Test betting functionality
- Test result display

#### 6.2 Integration Testing
- Verify game appears in correct sport order
- Test MMO functionality (if applicable)
- Validate result processing
- Check mobile responsiveness

### Step 7: Documentation

#### 7.1 Update Comments
```javascript
/*
  [NEW_GAME_ID]: [GAME_NAME]
  // Add to the game ID mapping comments
*/
```

#### 7.2 Update Stream Comments
```javascript
// In efSlider.vue
/*
  [NEW_GAME_ID]: "vDYS8-[STREAM_ID]", [GAME_NAME]
  // Add to stream mapping comments
*/
```

## Example: Adding a New Fighter Game (Game ID 53 - NARUTO)

### ✅ COMPLETED CONFIGURATION
```javascript
// Already added to config.js
params: {
  53: {
    delay: 1,
    duration: 5,
    live: 1,
    thumbnail: "images/esports/game-thumb/naruto-thumb.jpg",
  }
}

gameStream: {
  53: "vDYS8-v2kwn", // NARUTO
  153: "vDYS8-v2kwn", // NARUTO (alternative)
}

efightList: [41, 42, 43, 44, 47, 52, 53], // ✅ Added
gameList: ["50", "52", "49", "51", "43", "41", "44", "42", "45", "46", "47", "53", "54"] // ✅ Added
```

### ✅ COMPLETED FILES
- ✅ `public/images/esports/game-thumb/naruto-thumb.jpg`
- ✅ `public/images/esports/video-bg/video-bg-naruto.png`
- ✅ `public/images/icon-sport-svg/53.svg` (just created)

### ✅ COMPLETED CSS
```css
.esports-heroes.sports-53 {
  background: url(/images/esports/video-bg/video-bg-naruto.png) no-repeat;
  background-size: cover;
}
```

### 🎉 GAME ID 53 (NARUTO) IS NOW FULLY INTEGRATED!

**Status**: ✅ **COMPLETE** - Ready for testing and deployment

**What was implemented**:
1. ✅ Game parameters configured (delay: 1, duration: 5, live: 1)
2. ✅ Stream configuration added (vDYS8-v2kwn)
3. ✅ Game lists updated (efightList, gameList, special, day1)
4. ✅ Sport order updated (defaultSportOrder)
5. ✅ Thumbnail image available
6. ✅ Video background image available
7. ✅ Sport icon SVG created
8. ✅ CSS styling added
9. ✅ Comments updated in config.js

## Important Notes

1. **Stream IDs**: Must be unique and follow the `vDYS8-[ID]` pattern
2. **Image Sizes**: Maintain consistent dimensions for thumbnails and backgrounds
3. **Game Categories**: Proper classification affects UI rendering and betting logic
4. **Testing**: Thoroughly test all betting scenarios and result displays
5. **Mobile Support**: Ensure new games work properly on mobile devices
6. **Performance**: Monitor impact on system performance with new games

## Troubleshooting

### Common Issues
1. **Game not appearing**: Check `gameList` and `defaultSportOrder` arrays
2. **Thumbnail not loading**: Verify file path and image format
3. **Stream not working**: Validate stream ID and URL construction
4. **Betting errors**: Check bet type support and MMO configuration
5. **Result display issues**: Verify result handling logic in efresult.vue

### Debug Steps
1. Check browser console for JavaScript errors
2. Verify network requests for images and streams
3. Test betting functionality step by step
4. Validate result processing with sample data
5. Check mobile responsiveness across devices 