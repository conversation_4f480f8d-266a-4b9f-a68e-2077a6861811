<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 50 50">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
      }

      .cls-2 {
        fill: url(#radial-gradient);
      }
    </style>
    <radialGradient id="radial-gradient" cx="-548.1" cy="-1173.6" fx="-548.1" fy="-1173.6" r="19.5" gradientTransform="translate(61.9 -1269.8) rotate(-23.4) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fbeee5"/>
      <stop offset="0" stop-color="#fbeec5"/>
      <stop offset=".3" stop-color="#fbee92"/>
      <stop offset=".4" stop-color="#fbee68"/>
      <stop offset=".6" stop-color="#fbee46"/>
      <stop offset=".7" stop-color="#fbee2f"/>
      <stop offset="1" stop-color="#fbb61c"/>
    </radialGradient>
  </defs>
  <!-- Generator: Adobe Illustrator 28.6.0, SVG Export Plug-In . SVG Version: 1.2.0 Build 709)  -->
  <g>
    <g id="Layer_1">
      <g id="Layer_1-2" data-name="Layer_1">
        <g id="Layer_1-2">
          <g id="Layer_1-2-2" data-name="Layer_1-2">
            <g id="Layer_1-2-2">
              <circle class="cls-1" cx="25" cy="25" r="25"/>
              <circle class="cls-2" cx="24.9" cy="24.8" r="19.5"/>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>