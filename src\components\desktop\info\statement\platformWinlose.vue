<template lang="pug">
  table.table-info(width='100%')
    tbody
      tr
        th(scope='col', width='10%') {{ $t("ui.date") }}
        th(scope='col', width='46%') {{ $t("ui.remark") }}
        th.text-right(scope='col', width='10%') {{ $t("ui.turnover") }}
        th.text-right(scope='col', width='10%') {{ $t("ui.win") }} / {{ $t("ui.lost") }}
        th.text-right(scope='col', width='10%') {{ $t("ui.commission") }}
        th.text-right(scope='col', width='10%') {{ $t("ui.balance") }}
        th(scope='col', width='4%')
      tr.grey(v-if="platformWinloseList.length == 0")
        td(colspan="7").text-center
          span {{ $t('message.no_information_available') }}
      tr.clickable(v-for="(item, index) in platformWinloseList" @click="onClick(item.product)" :class="{ grey: index % 2 === 0 }")
        td
          div {{ $t("ui." + $dayjs(currDate).format("dddd").toLowerCase())  }}
          div {{ $dayjs(currDate).format("MM/DD/YYYY") }}
        td
          span {{ $t("ui." + item.product.toLowerCase()) }} {{ $t("ui.betting_statement") }}
        td.text-right
          span {{ $numeral(item.turnover).format("0,0.00") }}
        td.text-right
          span(:class="{ red: parseFloat(item.winlose) < 0 }") {{ $numeral(item.winlose).format("0,0.00") }}
        td.text-right
          span {{ $numeral(item.comm).format("0,0.00") }}
        td.text-right
          span(:class="{ red: parseFloat(item.winlose + item.comm) < 0 }") {{ $numeral(item.winlose + item.comm).format("0,0.00") }}
        td.text-center
          .icon-next
            i.fad.fa-caret-square-right
</template>
<script>
export default {
  props: {
    platformWinloseList: {
      type: Array,
      default: []
    },
    currDate: {
      type: String,
      default: null
    }
  },
  methods: {
    onClick: function (product) {
      this.$emit("getGameResult", product);
    }
  }
}
</script>