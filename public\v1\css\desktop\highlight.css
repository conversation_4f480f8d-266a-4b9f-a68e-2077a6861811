body.hl {
	font-family: "<PERSON>o", "<PERSON>homa", -apple-system, system-ui, BlinkMacSystemFont, "Microsoft YaHei", STXihei, "Helvetica Neue", sans-serif;
	background-color: #C0CED9;
	background-attachment: fixed;
	background-size: cover;
}
.hl-content {
	margin: 0 auto;
	padding: 8px 0;
	font-size: 13px;
	position: relative;
	font-family: "Roboto", "Tahoma", -apple-system, system-ui, BlinkMacSystemFont, "Microsoft YaHei", STXihei, "Helvetica Neue", sans-serif;
	min-width: 900px;
	width: 100%;
	max-width: 1340px;
	display: block;
	height: calc(100vh - 45px);
	min-height: 700px;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	will-change: transform;
	margin-top: 45px;
}
.hl-container {
	padding: 0;
	width: 1280px;
	max-width: 1280px;
	margin: 0 auto;
}
.hl-video {
}
.hl-video-left {
	width: 100%;
	overflow: hidden;
	border-radius: 3px;
}
.hl-video-bottom {
	background-color: #ffffff;
}
.hl-video-bottom .hl-league-titlebar .hl-league-name { 
	color: #fff;
}
.hl-right-banner {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	width: 100%;
	height: 660px;
	border-radius: 10px;
}
.hl-banner-single {
	width: 100%;
	margin-bottom: 5px;
}
.hl-banner-single:last-child {
	margin-bottom: 0;
}
.hl-banner-single img {
	width: 100%;
	border-radius: 10px;
}
.hl-matches-wrap {
	overflow: hidden;
}
.hl-matches {
	padding-right: 12px;
}
.hl-title-matches {
	font-size: 14px;
	color: #2F4A75;
	font-weight: 500;
	padding-bottom: 2px;
}
.hl-match-single {
	margin-bottom: 9px;
}
.hl-match-top {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	background-color: #2f4a75;
	padding: 6px 8px;
}
.hl-match-top a, .hl-match-top a:hover {
	text-decoration: none;
	color: #f9d040;
}
.hl-match-top-left {
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
	font-size: 12px;
	font-weight: 500;
	color: #fff;
}
.hl-match-top-right {
	color: #f9d040;
	background-color: #1f3354;
	font-size: 11px;
	font-weight: 400;
	padding: 0 12px;
	border-radius: 15px;
	line-height: 1;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}
.hl-match-bottom {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	background-color: #ffffff;
}
.hl-match-bottom .hl-match-team {
	background-color: transparent;
	-webkit-box-shadow: none;
	box-shadow: none;
	height: 48px;
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
}
.hl-match-bottom .hl-match-team .hl-team02 .hl-team-name {
	color: #061d52;
}
.hl-match-bottom .hl-match-team .hl-team {
	padding: 8px 0;
	background-color: transparent;
	font-size: 12px;
	white-space: normal;
}
.hl-text-vs {
	padding: 0 4px;
	font-size: 12px;
	color: #585858;
	font-weight: 500;
}
.hl-match-team {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.16);
	box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.16);
	width: 100%;
}
.hl-team {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
	font-size: 1rem;
	padding: 6px 8px;
	background-color: #fff;
	white-space: normal;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
	height: 100%;
	width: 40%;
}
.hl-team-name {
	font-weight: 500;
}
.hl-team01 .hl-team-name {
	color: #014273;
}
.hl-team02 {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: reverse;
	-ms-flex-direction: row-reverse;
	flex-direction: row-reverse;
}
.hl-team02 .hl-team-name {
	color: #C11A00;
}
.hl-team01 .hl-team-logo {
	margin-right: 0.3rem;
}
.hl-team02 .hl-team-logo {
	margin-left: 0.3rem;
}
.hl-event {
	padding: 0;
	text-align: center;
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
	overflow: hidden;
	height: 100%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	background: #f5f5f5;
}
.hl-score {
	font-size: 12px;
	color: #070707;
	font-weight: 500;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	line-height: 1;
	border: 1px solid #88ABC8;
}
.hl-score .score {
	padding: 4px;
	background: #fff;
	line-height: 1;
	white-space: nowrap;
	color: #4F85B3;
}
.hl-score .label {
	padding: 4px;
	background: #88ABC8;
	color: #fff;
	line-height: 1;
}
.hl-label {
	font-size: 18px;
	color: #b53f39;
	font-weight: 700;
	line-height: 1;
	padding-bottom: 2px;
}
.hl-match-info {
	padding: 12px;
	height: 75px;
}
.hl-match-time {
	font-size: 11px;
	color: #000;
	font-weight: 500;
	width: 100%;
	padding: 0;
	-webkit-box-flex: 1;
	    -ms-flex-positive: 1;
	        flex-grow: 1;
}
.hl-date {
	padding: 0 2px;
	line-height: 1;
}
.hl-time {
	padding: 0 2px;
	line-height: 1;
}
.hl-title {
	font-size: 18px;
	font-weight: 700;
	margin-bottom: 1rem;
}
.hl-filter {
}
.hl-select-date {
	background-color: #276FA8;
	border-radius: 3px;
	font-size: 14px;
	font-weight: 500;
	color: #fff;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	min-width: 227px;
	height: 44px;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	padding: 0 1rem;
	cursor: pointer;
	margin-left: 1rem;
	width: 20%;
}
.hl-select-date:hover {
	text-decoration: none;
}
.hl-logo-calendar {
	margin: 0 4px;
}
.hl-league-single {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	padding: 2px 8px;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	font-size: 12px;
	font-weight: 500;
	cursor: pointer;
	height: 42px;
	min-width: 60px;
	line-height: 1;
	width: 100%;
	background: #fff;
}
.hl-league-logo {
	margin-right: 6px;
	width: 32px;
	min-width: 32px;
	max-width: 32px;
	height: 32px;
	min-height: 32px;
	max-height: 32px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	overflow: hidden;
}
.hl-league-logo img {
	height: 32px;
}
.hl-league-name {
	color: #2F4A75;
}
.hl-filter-row {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}
.hl-small-single {
	margin-bottom: 8px;
	overflow: hidden;
	border-collapse: collapse;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	border-radius: 3px;
}
.hl-small-single.active .hl-filter-small {
	background-color: #FFF0C9;
}
.hl-small-single.active .hl-filter-small .hl-league-titlebar {
	background-color: #EBDDA4;
}
.hl-small-single.active .hl-filter-small .hl-team{
	background-color: #FFF0C9;
}
.hl-small-single.active .hl-filter-small .hl-score {
	border: 1px solid #E8AB47;
}
.hl-small-single.active .hl-filter-small .hl-score .label {
	background: #E8AB47;
}
.hl-small-single.active .hl-filter-small .hl-score .score {
	color: #E8AB47;
}
.hl-video-small, .hl-video-small img, .hl-video-small .embed-responsive {
	height: 100%;
	width: 100%;
}
.hl-filter-small {
	background-color: #ffffff;
	width: calc(100% - 178px);
	height: 100px;
	overflow: hidden;
}
.hl-filter-small .hl-team {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	    -ms-flex-direction: column;
	        flex-direction: column;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: start;
	    -ms-flex-pack: start;
	        justify-content: flex-start;
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
	font-size: 11px;
	font-weight: 400;
	padding: 0 4px;
	background-color: #ffffff88;
	white-space: normal;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
	height: auto;
	width: 100%;
	height: 78px;
	min-height: 78px;
	max-height: 78px;
	line-height: 1;
}
.hl-filter-small .hl-team .hl-team-name {
	width: 100%;
	text-align: center;
	font-family: "Roboto Condensed";
}
.hl-filter-small .hl-team-logo {
	width: 48px;
	min-width: 48px;
	max-width: 48px;
	height: 48px;
	min-height: 48px;
	max-height: 48px;
	overflow: hidden;
	padding: 8px 4px 4px 4px;
}
.hl-filter-small .hl-team01 .hl-team-logo {
	margin: 0;
}
.hl-filter-small .hl-team02 .hl-team-logo {
	margin: 0;
}
.hl-filter-small .hl-team-logo img {
	height: 36px;
}
.hl-filter-small .hl-score {
	font-size: 10px;
}
.hl-filter-small .hl-label {
	font-size: 14px;
	white-space: nowrap;
}
.hl-filter-small .hl-match-info {
	font-size: 13px;
}
.hl-filter-small .hl-title {
	font-size: 15px;
	margin-bottom: 0;
}
.hl-content .slider-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	margin-bottom: 8px;
}
.hl-content .slider-nav {
	padding: 0 2.5rem;
	width: 80%;
}
.hl-content .slider-content {
	padding-bottom: 50px;
}
.hl-content .slider-nav .slick-prev, .hl-content .slider-nav .slick-next {
	width: 36px;
	height: 36px;
}
.hl-content .slider-nav .slick-prev {
	left: 0;
}
.hl-content .slick-prev:before, .hl-content .slick-next:before {
	font-size: inherit;
	width: 36px;
	height: 36px;
	opacity: 1;
	display: block;
}
.hl-content .slick-prev:before {
	background: url('../../sample/arrow-left-dark.svg') top center no-repeat;
	background-size: contain;
}
.hl-content .slick-next:before {
	background: url('../../sample/arrow-right-dark.svg') top center no-repeat;
	background-size: contain;
}
.hl-content .slider-nav .slick-next {
	right: 0;
}
.hl-content .slider-nav .slick-slide {
	margin: 0 4px;
	background-color: #fff;
	border-radius: 3px;
	border-collapse: collapse;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	overflow: hidden;
}
.hl-content .slider-nav .slick-slide:focus {
	outline: none;
}
.hl-content .slider-nav .slick-slide.slick-current {
}
.hl-league-single.active {
	background-color: #F9D040;
	color: #014273;
}
.hl-match-info .hl-league-single {
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	padding: 0;
	cursor: inherit;
}
.hl-team-logo {
	width: 40px;
	min-width: 40px;
	max-width: 40px;
	height: 40px;
	min-height: 40px;
	max-height: 40px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	overflow: hidden;
}
.hl-team-logo img {
	height: 40px;
}
.hl-video-top {
	width: 100%;
	height: 486px;
	background: #000000;
	overflow: hidden;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}
.hl-video-top video {
	width: 100%;
	height: 100%;
}
.hl-video-small {
	width: 178px;
	height: 100px;
	background: #000000;
	overflow: hidden;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}
.hl-video-small .plyr {
	max-width: 100%;
	min-width: 0;
}
.plyr {
	width: 100%;
	height: 100%;
}
.hl-video-small video {
	width: 100%;
	height: 100%;
}
.hl-filter-small .hl-match-team {
	width: 100%;
	height: 100%;
	padding: 0;
}
.hl-select-date .input-group-text {
	background-color: transparent;
	border: 0;
	padding: 0;
	margin: 0 0 0 4px;
}
.hl-select-date .form-control {
	background-color: #0f4d8a;
	border: 0;
	padding: 0;
	margin: 0;
	color: #ffffff;
	font-size: 16px;
	font-weight: 600;
	line-height: 1;
	text-align: center;
	display: none;
}
.hl-date-picker {
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}
.hl-select-date .input-group {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}
.hl-select-date i {
	color: #ffffff;
	font-size: 22px;
}
.hl-select-date .active i {
	color: #f9d040;
}
.hl-date-picker {
	font-size: 17px;
	font-weight: 600;
}
.hl-none {
	width: 100%;
	line-height: 1;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	font-size: 17px;
	font-weight: 600;
	height: 100%;
	padding-left: 0.5rem;
}
.hl-none.message {
	color: #ffffffcc;
	padding: 4rem 1rem;
}
.hl-select-date .datepicker i {
	color: #212529;
}
.hl-title {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}
.remark-item {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	margin-right: 8px;
}
.remark-item:last-child {
	margin-right: 0;
}
.remark-detail {
	padding: 4px 8px;
	background: #585858;
	color: #ffffff;
	-webkit-box-shadow: 1px 1px 4px #00000088;
	box-shadow: 1px 1px 4px #00000088;
}
.remark-detail:first-child {
	padding-left: 12px;
	background: #1f3354;
	color: #ffffff;
	border-radius: 30px 0 0 30px;
}
.remark-detail:last-child {
	padding-right: 12px;
	background: #585858;
	color: #ffffff;
	border-radius: 0 30px 30px 0;
}
.remark-detail.pen:first-child {
	padding-left: 12px;
	background: #b53f39;
}
.hl-sports-type {
	width: 48px;
	height: 48px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}
.hl-sports-type img {
	width: 28px;
	height: 28px;
}
.hl-right-banner .hl-match-bottom .hl-match-team {
	padding-right: 8px;
}
.hl-right-banner .hl-match-bottom .hl-match-team .hl-team.hl-team01 {
	text-align: left;
}
.hl-right-banner .hl-match-bottom .hl-match-team .hl-team.hl-team02 {
	text-align: right;
}
.hl-right-banner .hl-match-single {
	cursor: pointer;
}
.hl-right-banner .hl-match-single .hl-match-bottom {
	background: #dddddd;
}
.hl-right-banner .hl-match-single:hover .hl-match-bottom {
	background: #ffffff;
}
.hl-right-banner .hl-match-single .hl-match-top {
	background-color: #0f4d8a;
}
.hl-right-banner .hl-match-single:hover .hl-match-top {
	background-color: #1460ac;
}
.nav-info.nav-title {
	font-family: "Lato";
	color: #fff;
	font-size: 16px;
	text-transform: uppercase;
	font-weight: 600;
	/* text-shadow: 0 0 5px #ffffff88; */
}
.hl-filter-small .hl-league-single {
}
.hl-league-titlebar {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: flex-start;
	-ms-flex-pack: flex-start;
	justify-content: flex-start;
	cursor: pointer;
	min-width: 60px;
	line-height: 1;
	width: 100%;
	padding: 4px 6px;
	font-size: 11px;
	font-weight: 500;
	height: auto;
	width: 100%;
	background: url(/images/market-head-bg.png) no-repeat;
	background-size: cover;
}
.hl-filter-small .hl-league-titlebar {
	padding: 4px 6px 2px 6px;
	font-size: 11px;
	font-weight: 500;
	height: auto;
	background: #DCE6EF;
}
.hl-filter-small .hl-league-single .hl-league-name {
	overflow: hidden;
	white-space: nowrap;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
}
.hl-league-titlebar .hl-league-name {
 color: #000;
}
.hl-video-left .hl-league-logo {
	margin-right: 6px;
	width: 16px;
	min-width: 16px;
	max-width: 16px;
	height: 16px;
	min-height: 16px;
	max-height: 16px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	overflow: hidden;
}
.hl-video-left .hl-league-logo img {
	height: 16px;
}
.hl-filter-small .hl-league-logo {
	margin-right: 6px;
	width: 16px;
	min-width: 16px;
	max-width: 16px;
	height: 16px;
	min-height: 16px;
	max-height: 16px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	overflow: hidden;
}
.hl-filter-small .hl-league-logo img {
	height: 16px;
}
.hl-filter-small .hl-match-time {
	font-size: 10px;
}
.hl-event-info {
	padding: 0;
	background: #f5f5f5cc;
	width: 72px;
	min-width: 72px;
	max-width: 72px;
}
.hl-team-info {
	margin: 0;
	background: #ffffffaa;
}
.hl-video-left .hl-team-info {
	width: 100%;
	-webkit-box-flex: 1;
	    -ms-flex-positive: 1;
	        flex-grow: 1;
	height: 75px;
}
.hl-team-vs {
	color: #212529;
	font-size: 9px;
	text-align: center;
}
.highlight-btn {
	width: 100%;
	overflow: hidden;
	border-radius: 3px;
}
.highlight-btn img {
	width: 100%;
}
.z-side {
	padding-right: 0;
}
#date-picker {
	color: #ffffff;
	font-size: 24px;
	text-decoration: none;
}
.hl-filter-bar .dropdown-menu {
	background: #276FA8;
	color: #f5f5f5;
	border-radius: 3px;
}
.hl-filter-bar .dropdown-menu .dropdown-item {
	color: #f5f5f5;
	font-size: 14px;
	font-weight: 600;
	padding: 12px 8px;
	line-height: 1;
	cursor: pointer;
}
.hl-filter-bar .dropdown-menu .dropdown-item:hover {
	background: #145693;
}
.hl-filter-bar .dropdown-menu .dropdown-item.active:hover {
	background: #1460ac;
}
.hl .col-8, .hl .col-4 {
	position: relative;
	width: 100%;
	padding-right: 4px;
	padding-left: 4px;
}
.hl-date-picker span {
	color: #ffffff;
	text-decoration: none;
}
.hl-match-score {
	background: #00000008;
	width: 100%;
	padding: 6px 0;
}
.live-video {
	background-image: url('../../img/soccer.jpg');
	background-position: center;
	background-size: 100% auto;
	width: 100%;
	height: 100%;
	text-align: center;
	text-align: center;
	color: #ffffff;
	font-size: 24px;
	text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.25);
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: end;
	    -ms-flex-pack: end;
	        justify-content: flex-end;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	    -ms-flex-direction: column;
	        flex-direction: column;
	overflow: hidden;
	-webkit-box-shadow: inset 0 7px 5px -5px rgba(0, 0, 0, 1), inset 0 -7px 5px -5px rgba(0, 0, 0, 1);
	        box-shadow: inset 0 7px 5px -5px rgba(0, 0, 0, 1), inset 0 -7px 5px -5px rgba(0, 0, 0, 1);
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	position: relative;
	cursor: pointer;
}
.live-video.brigther {
	background-image: url('../../img/soccer_90.jpg');
}
.live-video .play-btn {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 60px;
	height: 43px;
	background-image: url('../../img/play_icon.svg');
	cursor: pointer;
}
.hl-video-small .live-video .play-btn {
	width: 30px;
	height: 21px;
}
.hl-small-single:hover .live-video .play-btn, .live-video .play-btn:hover, .hl-video-small .live-video .play-btn:hover {
	background-image: url('../../img/play_icon_hover.svg');
}
.live-video .inner-logo {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	    -ms-flex-direction: row;
	        flex-direction: row;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	width: 100%;
}
.live-video .inner-logo .inner-logo-team {
	width: 120px;
	overflow: hidden;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
}
.live-video .inner-logo .inner-logo-team img {
	width: 120px;
}
.live-video .inner-logo span {
	width: 160px;
	min-width: 160px;
	max-width: 160px;
	font-size: 40px;
	font-family: "Oswald";
	text-align: center;
	margin: 0;
}
.live-video .inner-match {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	    -ms-flex-direction: row;
	        flex-direction: row;
	width: 100%;
	height: 200px;
}
.live-video .inner-match .hl-team-name {
	width: calc(50% - 50px);
	line-height: 1;
	overflow: hidden;
	border-collapse: collapse;
	text-align: right;
	-webkit-box-flex: 1;
	    -ms-flex-positive: 1;
	        flex-grow: 1;
	margin: 15px 10px;
}
.live-video .inner-match .inner-event {
	width: 140px;
	min-width: 140px;
	max-width: 140px;
	overflow: hidden;
}
.live-video .inner-match .inner-event .hl-label {
	font-size: 28px;
	color: #ffeca8;
	white-space: nowrap;
	margin-bottom: 5px;
}
.live-video .inner-match .inner-event .hl-label.small {
	font-size: 18px;
	color: #ffffffcc;
}
.hl-video-small .live-video {
	font-size: 10px;
}
.hl-video-small .live-video .inner-match {
	height: 36px;
}
.hl-video-small .live-video .inner-logo .inner-logo-team {
	width: 32px;
	overflow: hidden;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
}
.hl-video-small .live-video .inner-logo .inner-logo-team img {
	width: 32px;
}
.hl-video-small .live-video .inner-logo span {
	width: 52px;
	min-width: 52px;
	max-width: 52px;
	font-size: 16px;
	font-family: "Oswald";
	text-align: center;
	margin: 0;
}
.hl-video-small .live-video .inner-match .hl-team-name {
	width: calc(50% - 19px);
	margin: 4px;
}
.hl-video-small .live-video .inner-match .inner-event {
	width: 48px;
	min-width: 48px;
	max-width: 48px;
}
.hl-video-small .live-video .inner-match .inner-event .hl-label {
	font-size: 10px;
	margin-bottom: 0;
}
.hl-video-small .live-video .inner-match .inner-event .hl-label.small {
	font-size: 9px;
}

.hl-message {
	color: #fff;
	font-size: 16px;
	padding: 8px 32px;
	background: #00000088;
	border-radius: 32px;
}