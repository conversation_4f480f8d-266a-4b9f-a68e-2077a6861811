<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="manifest" href="<%= BASE_URL %><%= VUE_APP_MANIFEST %>?v=muQbdTxYGU" />
    <meta name="theme-color" content="#1252a5" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="#1252a5" />
    <link rel="apple-touch-icon" sizes="180x180" href="v1/img/icons/apple-touch-icon.png?v=muQbdTxYGU" />
    <link rel="mask-icon" href="v1/img/icons/safari-pinned-tab.svg" color="#4DBA87" />
    <meta name="msapplication-TileColor" content="#ffffff" />
    <meta http-equiv="cache-control" content="no-cache, must-revalidate, post-check=0, pre-check=0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=1440, initial-scale=0.5, maximum-scale=1, user-scalable=yes, minimal-ui" />
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />
    <!--[if IE]>
      <meta http-equiv="Page-Enter" content="blendTrans(duration=0)" />
      <meta http-equiv="Page-Exit" content="blendTrans(duration=0)" />
    <![endif]-->
    <link rel="icon" href="<%= BASE_URL %><%= VUE_APP_FAVICON %>?v=muQbdTxYGU" />
    <title><%= VUE_APP_META %></title>
    <meta name="description" content="<%= VUE_APP_DESCRIPTION %>" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css?family=Lato:300,400,700,900|Oswald:300,400,500,600,700|Roboto+Condensed:400,700|Roboto:300,400,500,700,900|Open+Sans:300,400,500,600,700&display=swap"
      rel="preload"
      as="style"
    />
    <link
      href="https://fonts.googleapis.com/css?family=Lato:300,400,700,900|Oswald:300,400,500,600,700|Roboto+Condensed:400,700|Roboto:300,400,500,700,900|Open+Sans:300,400,500,600,700&display=swap"
      rel="stylesheet"
    />
    <style>
      html,
      body {
        font-family: "Tahoma", -apple-system, system-ui, BlinkMacSystemFont, "Tahoma", "Microsoft YaHei", "Helvetica Neue", sans-serif;
      }
    </style>
    <link rel="stylesheet" href="/v1/css/bootstrap.min.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/pro/css/all.min.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/sweetalert2.min.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/slick.min.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/slick-theme.min.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/bootstrap-datetimepicker.min.css?v=muQbdTxYGU" />

    <link rel="stylesheet" href="/v1/css/desktop/app.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/header.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/group.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/content.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/content-main.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/modal.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/dropdown.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/more.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/info.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/mini.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/app.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/header.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/group.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/content.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/content-main.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/modal.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/dropdown.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/more.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/info.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/mini.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/swal.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/dark.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/match.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/color1.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/color2.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/color3.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/color4.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/color5.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/hxtable.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/highlight.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/esports.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/tournament.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/app.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/landing.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/landingbt.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/desktop/landing2.css?v=muQbdTxYGU" />
    <link rel="stylesheet" href="/v1/css/swiper-bundle.min.css?v=muQbdTxYGU" />

    <script src="/v1/js/jquery-3.5.1.min.js?v=muQbdTxYGU"></script>
    <script src="/v1/js/jquery-ui.min.js?v=muQbdTxYGU"></script>
    <script src="/v1/js/popper.min.js?v=muQbdTxYGU"></script>
    <script src="/v1/js//bootstrap.min.js?v=muQbdTxYGU"></script>
    <script src="/v1/js/slick.min.js?v=muQbdTxYGU"></script>
    <script src="/v1/js/TweenMax.min.js?v=muQbdTxYGU"></script>
    <script src="/v1/js/swiper-bundle.min.js?v=muQbdTxYGU"></script>

    <link rel="stylesheet" href="/v1/css/festival/landing-responsive.css?v=muQbdTxYGU" />
    <!-- <link rel="stylesheet" href="/v1/css/festival/festival.css?v=muQbdTxYGU" /> -->
    <!-- <script src="/v1/css/festival/festival.js?v=muQbdTxYGU"></script> -->

    <link rel="stylesheet" href="/v1/css/plyr.css?v=muQbdTxYGU" />
    <script src="/v1/js/plyr.js?v=muQbdTxYGU"></script>
    <link rel="stylesheet" href="/v1/css/splide.min.css?v=muQbdTxYGU" />
    <script src="/v1/js/splide.min.js?v=muQbdTxYGU"></script>

    <link rel="preload" href="/v1/font/rift-bold-webfont.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/v1/css/app.css?v=muQbdTxYGU" as="style">
    <link rel="prefetch" href="/v1/images/bg1.png" as="image">
    <link rel="prefetch" href="/v1/images/bg2.png" as="image">

    <!-- <script>
      window.onscroll = function () {
        if (document.body.scrollTop > 50 || document.documentElement.scrollTop > 50) {
          if (!$(".landing2 .header2").hasClass("small-top")) {
            $(".landing2 .header2").addClass("small-top");
          }
        } else {
          if ($(".landing2 .header2").hasClass("small-top")) {
            $(".landing2 .header2").removeClass("small-top");
          }
        }
      };
    </script> -->
  </head>

  <body>
    <noscript> <strong>We're sorry but our web application doesn't work properly without JavaScript enabled. Please enable it to continue.</strong> </noscript>
    <div id="app"></div>
    <script></script>
  </body>
</html>
