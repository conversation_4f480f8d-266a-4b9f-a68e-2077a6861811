<template lang="pug">
.morebet-wrapper.morePanelX(v-if="[1].includes(sportsType)")
  .body-bet
    ul#nav-morebet.nav.nav-tabs(role="tablist", v-if="(layoutIndex == 3 && [1].includes(sportsType)) || layoutIndex != 3", :class="{ 'live-tab': layoutIndex == 3 }")
      li.nav-item(v-if="isChild1 && checkTabMode3")
        a.nav-link(:class="{ active: mode == 3 }", href="javascript:void(0);", @click="goToTab(3)") {{ $t('ui.corners') }}
      li.nav-item(v-if="isChild2 && checkTabMode4")
        a.nav-link(:class="{ active: mode == 4 }", href="javascript:void(0);", @click="goToTab(4)") {{ $t('ui.specials') }}
    .tab-content(:id="'content-' + uid")
      .tab-pane.active(:id="'accordian-more-' + uid")
        //- Corners/Booking
        .row.mx-0(v-if="mode == 3 && [1].includes(sportsType)")
          template(v-for="(sitem, skey) in child1Ids", v-if="isDataEmptyModeMore(hdpou(sitem[0]))")
            hdpouMoreMatchX(
              :uid="uid",
              :childId="sitem[0]",
              :details="hdpou(sitem[0])",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )

        //- Special
        .row.mx-0(v-if="mode == 4 && [1].includes(sportsType)")
          template(v-for="(sitem, skey) in child2Ids", v-if="isDataEmptyModeMore(hdpou(sitem[0]))")
            hdpouMoreMatchX(
              :uid="uid",
              :childId="sitem[0]",
              :details="hdpou(sitem[0])",
              :matchId="matchId",
              :leagueId="leagueId",
              :marketType="marketType",
              :sportsType="sportsType",
              :betType="betType",
              :layoutIndex="layoutIndex"
            )
</template>

<script>
import config from "@/config";

import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {
    hdpouMoreMatchX: () => import("@/components/desktop/main/moremore/hdpouMoreMatchX"),
    ouSpecialMatch: () => import("@/components/desktop/main/moremore/ouSpecialMatch"),
  },
  props: {
    uid: {
      type: String,
    },
    child1Ids: {
      type: Array,
    },
    child2Ids: {
      type: Array,
    },
    details: {
      type: Object,
    },
    matchId: {
      type: Number,
    },
    leagueId: {
      type: Number,
    },
    marketType: {
      type: Number,
    },
    sportsType: {
      type: Number,
    },
    betType: {
      type: String,
    },
    layoutIndex: {
      type: Number,
    },
    mainOdds: {
      type: Object,
    },
  },
  data() {
    return {
      mode: 0,
      vHide: true,
    };
  },
  computed: {
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
    isDataExists() {
      if (this.details != null) {
        if (this.details.hasOwnProperty("total")) {
          return (
            this.details.total > 0 ||
            this.child1Ids.length > 0 ||
            this.child2Ids.length > 0
          );
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    selectedMatch() {
      return this.$store.getters.selectedMatch;
    },
    isDetailsTotal() {
      return this.details.total > 0;
    },
    isDetailsFull() {
      return this.details.full > 0;
    },
    isDetailsHalf() {
      return this.details.half > 0;
    },
    isChild1() {
      return this.child1Ids.length > 0;
    },
    isChild2() {
      return this.child2Ids.length > 0;
    },
    menu3() {
      return this.$store.state.layout.menu3;
    },
    language() {
      return this.$store.getters.language;
    },
    pageType() {
      return this.$store.getters.pageDisplay.pageType;
    },
    checkTabMode3() {
      var flag = false;
      this.child1Ids.forEach((v, k) => {
        if (this.isDataEmptyModeMore(this.hdpou(v[0]))) {
          flag = true;
        }
      });
      return flag;
    },
    checkTabMode4() {
      var flag = false;
      this.child2Ids.forEach((v, k) => {
        if (this.isDataEmptyModeMore(this.hdpou(v[0]))) {
          flag = true;
        }
      });
      return flag;
    },
  },
  watch: {},
  destroyed() {
    EventBus.$off("PROCESS_ODDS", this.forceUpdate);
    EventBus.$off("PROCESS_MMO", this.forceUpdate);
  },
  mounted() {
    this.setDefaultTab();
    EventBus.$on("PROCESS_ODDS", this.forceUpdate);
    EventBus.$on("PROCESS_MMO", this.forceUpdate);
  },
  methods: {
    forceUpdate() {
      this.$forceUpdate();
    },
    isDataEmptyModeMore(details) {
      var hdp = details["hdp"] != null && details["hdp"][0] != null;
      var hdph = details["hdph"] != null && details["hdph"][0] != null;
      var ou = details["ou"] != null && details["ou"][0] != null;
      var ouh = details["ouh"] != null && details["ouh"][0] != null;
      var oxt = details["oxt"] != null && details["oxt"][0] != null;
      var oxth = details["oxth"] != null && details["oxth"][0] != null;
      var oe = details["oe"] != null && details["oe"][0] != null;
      var oeh = details["oeh"] != null && details["oeh"][0] != null;
      var ml = details["ml"] != null && details["ml"][0] != null;
      var mlh = details["mlh"] != null && details["mlh"][0] != null;
      return hdp || ou || oe || oxt || hdph || ouh || oxth || oeh || ml || mlh;
    },
    isDataEmptyEsportMore(details) {
      var hdp = details["hdp"] != null && details["hdp"][0] != null;
      var ou = details["ou"] != null && details["ou"][0] != null;
      var ml = details["ml"] != null && details["ml"][0] != null;
      var oe = details["oe"] != null && details["oe"][0] != null;
      return hdp || ou || oe || ml;
    },
    isDataEmptyHDPSFVMore(details) {
      var hdp = details["hdp"] != null && details["hdp"][0] != null;

      return hdp;
    },
    isDataEmptyOUSFVMore(details) {
      var ou = details["ou"] != null && details["ou"][0] != null;

      return ou;
    },
    show4dStats(e) {
      alert(e);
    },
    handleDefaultTab() {
      switch (this.mode) {
      case 0:
        if (this.child1Ids.length > 0) {
          this.goToTab(3);
        } else {
          if (this.child2Ids.length > 0) {
            this.goToTab(0);
          }
        }
        break;
      case 1:
        if (this.details.full <= 0) {
          this.goToTab(0);
        }
        break;
      case 2:
        if (this.details.half <= 0) {
          this.goToTab(0);
        }
        break;
      case 3:
        if (this.child1Ids.length <= 0) {
          this.goToTab(0);
        }
        break;
      case 4:
        if (this.child2Ids.length <= 0) {
          this.goToTab(0);
        }
        break;
      }
    },
    setDefaultTab() {
      setTimeout(() => {
        if (
          (this.child1Ids != null && this.child1Ids.length > 0) ||
          (this.child2Ids != null && this.child2Ids.length > 0)
        ) {
          this.handleDefaultTab();
        } else {
          this.setDefaultTab();
        }
      }, 1000);
    },
    league() {
      return this.$store.getters.data.league;
    },
    head() {
      return this.$store.getters.data.head;
    },
    match() {
      return this.$store.getters.data.match;
    },
    leagueName(data) {
      var result = data["league"].split(" - ");
      if (result.length >= 2) {
        return result[result.length - 1].toLowerCase().trim();
      }
      return this.details["league"].toLowerCase().trim();
    },
    odds() {
      if (this.$store.getters.data.hasOwnProperty("odds")) {
        if (this.$store.getters.data.odds[this.layoutIndex] != null) {
          return this.$store.getters.data.odds[this.layoutIndex];
        } else {
          return {};
        }
      }
      return {};
    },
    mmo() {
      if (this.$store.getters.data.hasOwnProperty("mmo")) {
        if (this.$store.getters.data.mmo[this.layoutIndex] != null) {
          return this.$store.getters.data.mmo[this.layoutIndex];
        } else {
          return {};
        }
      }
      return {};
    },
    hdpouMMO(e) {
      var odds = this.mmo();
      var r = {};
      r["tn"] = 0;
      r["team"] = null;
      r["oxtn"] = 0;
      r["more"] = 0;

      if (odds != null) {
        if (odds.hasOwnProperty("hdp")) {
          if (odds["hdp"][e] != null) {
            r["hdp"] = odds["hdp"][e].filter((v, i, r) => {
              return v[4] == "HDP";
            });
            r["hdph"] = odds["hdp"][e].filter((v, i, r) => {
              return v[4] == "HDPH";
            });
            if (r["hdp"] ? r["hdp"].length : 0 > r["tn"]) {
              r["tn"] = r["hdp"].length;
              if (r["hdp"][0][8] != null && r["hdp"][0][8] != "0") {
                r["team"] = r["hdp"][0][7];
              } else {
                r["team"] = 2;
              }
            } else {
              r["hdp"] = null;
            }
            if (r["hdph"] ? r["hdph"].length : 0 > r["tn"]) {
              if (r["tn"] < r["hdph"].length) {
                r["tn"] = r["hdph"].length;
              }
              if (r["team"] == null) {
                if (r["hdph"][0][8] != null && r["hdph"][0][8] != "0") {
                  r["team"] = r["hdph"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["hdph"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("ou")) {
          if (odds["ou"][e]) {
            r["ou"] = odds["ou"][e].filter((v, i, r) => {
              return v[4] == "OU";
            });
            r["ouh"] = odds["ou"][e].filter((v, i, r) => {
              return v[4] == "OUH";
            });
            if (r["ou"] ? r["ou"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ou"].length) {
                r["tn"] = r["ou"].length;
              }
              if (r["team"] == null) {
                if (r["ou"][0][8] != null && r["ou"][0][8] != "0") {
                  r["team"] = r["ou"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["ou"] = null;
            }
            if (r["ouh"] ? r["ouh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ouh"].length) {
                r["tn"] = r["ouh"].length;
              }
              if (r["team"] == null) {
                if (r["ouh"][0][8] != null && r["ouh"][0][8] != "0") {
                  r["team"] = r["ouh"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["ouh"] = null;
            }
          }
        }
      }
      return r;
    },
    hdpou(e) {
      var odds = this.odds();
      // console.log(odds);
      var r = {};
      r["tn"] = 0;
      r["team"] = null;
      r["oxtn"] = 0;
      r["more"] = 0;

      r["mmo"] = {};

      if (odds != null) {
        if (odds.hasOwnProperty("hdp")) {
          if (odds["hdp"][e] != null) {
            r["hdp"] = odds["hdp"][e].filter((v, i, r) => {
              return v[4] == "HDP";
            });
            r["hdph"] = odds["hdp"][e].filter((v, i, r) => {
              return v[4] == "HDPH";
            });
            if (r["hdp"] ? r["hdp"].length : 0 > r["tn"]) {
              r["tn"] = r["hdp"].length;
              if (r["hdp"][0][8] != null && r["hdp"][0][8] != "0") {
                r["team"] = r["hdp"][0][7];
              } else {
                r["team"] = 2;
              }
            } else {
              r["hdp"] = null;
            }
            if (r["hdph"] ? r["hdph"].length : 0 > r["tn"]) {
              if (r["tn"] < r["hdph"].length) {
                r["tn"] = r["hdph"].length;
              }
              if (r["team"] == null) {
                if (r["hdph"][0][8] != null && r["hdph"][0][8] != "0") {
                  r["team"] = r["hdph"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["hdph"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("ou")) {
          if (odds["ou"][e]) {
            r["ou"] = odds["ou"][e].filter((v, i, r) => {
              return v[4] == "OU";
            });
            r["ouh"] = odds["ou"][e].filter((v, i, r) => {
              return v[4] == "OUH";
            });
            if (r["ou"] ? r["ou"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ou"].length) {
                r["tn"] = r["ou"].length;
              }
              if (r["team"] == null) {
                if (r["ou"][0][8] != null && r["ou"][0][8] != "0") {
                  r["team"] = r["ou"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["ou"] = null;
            }
            if (r["ouh"] ? r["ouh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ouh"].length) {
                r["tn"] = r["ouh"].length;
              }
              if (r["team"] == null) {
                if (r["ouh"][0][8] != null && r["ouh"][0][8] != "0") {
                  r["team"] = r["ouh"][0][7];
                } else {
                  r["team"] = 2;
                }
              }
            } else {
              r["ouh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("oxt")) {
          if (odds["oxt"][e]) {
            r["oxt"] = odds["oxt"][e].filter((v, i, r) => {
              return v[4] == "1X2";
            });
            r["oxth"] = odds["oxt"][e].filter((v, i, r) => {
              return v[4] == "1X2H";
            });
            if (r["oxt"] ? r["oxt"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oxt"].length) {
                r["tn"] = r["oxt"].length;
              }
              r["oxtn"] = 1;
            } else {
              r["oxt"] = null;
            }
            if (r["oxth"] ? r["oxth"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oxth"].length) {
                r["tn"] = r["oxth"].length;
              }
              r["oxtn"] = 1;
            } else {
              r["oxth"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("oe")) {
          if (odds["oe"][e]) {
            r["oe"] = odds["oe"][e].filter((v, i, r) => {
              return v[4] == "OE";
            });
            r["oeh"] = odds["oe"][e].filter((v, i, r) => {
              return v[4] == "OEH";
            });
            if (r["oe"] ? r["oe"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oe"].length) {
                r["tn"] = r["oe"].length;
              }
              r["oen"] = 1;
            } else {
              r["oe"] = null;
            }
            if (r["oeh"] ? r["oeh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["oeh"].length) {
                r["tn"] = r["oeh"].length;
              }
              r["oehn"] = 1;
            } else {
              r["oeh"] = null;
            }
          }
        }

        if (odds.hasOwnProperty("ml")) {
          if (odds["ml"][e]) {
            r["ml"] = odds["ml"][e].filter((v, i, r) => {
              return v[4] == "ML";
            });
            r["mlh"] = odds["ml"][e].filter((v, i, r) => {
              return v[4] == "MLH";
            });
            if (r["ml"] ? r["ml"].length : 0 > r["tn"]) {
              if (r["tn"] < r["ml"].length) {
                r["tn"] = r["ml"].length;
              }
              r["mln"] = 1;
            } else {
              r["ml"] = null;
            }
            if (r["mlh"] ? r["mlh"].length : 0 > r["tn"]) {
              if (r["tn"] < r["mlh"].length) {
                r["tn"] = r["mlh"].length;
              }
              r["mlhn"] = 1;
            } else {
              r["mlh"] = null;
            }
          }
        }

        var head = this.head();
        var match = this.match();
        if (head[e] == undefined) {
          r["more"] = 0;
        } else {
          r["head"] = head[e][0];
          r["match"] = match[e];
          if (r["head"] != null) {
            r["more"] = head[e][0][7];
          }
          if (r["match"] != null) {
            r["league"] = this.league()[r["match"][1]][4];
          }
        }
      }

      if (this.mmoMode) {
        r["mmo"] = this.hdpouMMO(e);
      }

      this.result = null;
      this.result = r;
      return r;
    },
    goToTab(mode) {
      this.mode = mode;
    },
    checkOdds(e, t) {
      var result = false;

      switch (t) {
      case 1:
        e.forEach((value) => {
          if (
            value[5] != null &&
            value[5] != "" &&
            value[7] != null &&
            value[7] != ""
          ) {
            result = this.menu3 != "parlay" ? true : value[8] == 1;
          }
        });
        break;
      case 2:
        e.forEach((value) => {
          if (
            value[5] != null &&
            value[5] != "" &&
            value[6] != null &&
            value[6] != "" &&
            value[7] != null &&
            value[7] != ""
          ) {
            result = this.menu3 != "parlay" ? true : value[8] == 1;
          }
        });
        break;
      default:
        e.forEach((value) => {
          if (value[5] != null && value[5] != "") {
            result = this.menu3 != "parlay" ? true : value[8] == 1;
          }
        });
        break;
      }

      return result;
    },
  },
};
</script>
