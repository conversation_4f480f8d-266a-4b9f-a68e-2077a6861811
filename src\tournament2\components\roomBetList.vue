<template lang="pug">
#room-bet-list.modal.fade.modal-room
  .modal-dialog.modal-xl.modal-dialog-centered.modal-dialog-scrollable
    .modal-content
      .modal-header
        .modal-title
          .modal-title-left {{ $t('ui.bet_list') }}
          .modal-title-right {{ $t('ui.room_id') }}: {{ roomId }}
      .modal-body
        table.table-info(width='100%')
          tbody
            tr
              th.text-center(scope='col', width='4%') {{ $t("ui.no/") }}
              th.text-left(scope='col', width='18%') {{ $t("ui.trans_time") }}
              th.text-left(scope='col', width='34%') {{ $t("ui.event") }}
              th.text-right(scope='col', width='16%') {{ $t("ui.place_bet") }}
              th.text-right(scope='col', width='10%') {{ $t("ui.stake") }}
              th.text-right(scope='col', width='10%') {{ $t("ui.player") }}
              th(scope='col', width='8%') {{ $t("ui.status") }}
            tr.grey(v-if="betList == 'undefined' || betList.length <= 0")
              td(colspan="8").text-center
                span {{ $t('message.no_information_available') }}
            tr(v-for="(item, index) in betList" :class="{ grey: index % 2 === 0 }")
              td.text-center(valign='top')
                span {{ index + 1 }}
              td.text-left(valign='top')
                div {{ $t("ui.id") }}: {{ item.bet_id }}
                div {{ $dayjs(item.created_on).format("MM/DD/YYYY hh:mm:ss A") }}
              td.text-left(valign='top')
                .bet-info
                  .bet-type.blue.mb-1(style="font-size: 13px") {{ sports[item.sports_type] }} - {{ $t("m.BT_" + item.bet_type) }}
                  .match-info.d-flex.flex-column.pl-2.mb-1(style="border-left: 4px solid #cc9966cc; border-radius: 4px; overflow: hidden;")
                    .name-league.font-weight-bold {{ item.league_name }}
                    .d-flex
                      .name-home {{ item.home_team_name }}
                      small.text-muted.mx-1 -vs-
                      .name-away {{ item.away_team_name }}
                    .name-league {{ $dayjs(item.match_time).format("MM/DD/YYYY HH:mm A") }}
              td.text-right(valign='top')
                div
                  template(v-if="['HDP','HDPH'].includes(item.bet_type)")
                    span(v-if="item.home_away === 1") {{ getTeamName("home", item) }}
                    span(v-else) {{ item.away_team_name }}
                  template(v-else)
                    span(v-if="item.home_away === 1") {{ $t('ui.over') }}
                    span(v-else) {{ $t('ui.under') }}
                div {{ item.ball_display }} @ {{ item.odds_display }}
              td.text-right(valign='top')
                span {{ $numeral(item.bet_member).format("0,0") }}
              td.text-right(valign='top')
                div {{ item.member_id }}
              td.text-left(valign='top')
                div {{ $t("ui." + item.bet_status.toLowerCase()) }}
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import service from "@/tournament2/library/_xhr.js";
import vPagination from "vue-plain-pagination";

export default {
  components: {},
  props: {},
  data() {
    return {
      roomId: null,
      betList: [],
    };
  },
  computed: {
    sports() {
      return this.$store.state.layout.sports;
    },
    language() {
      return this.$store.getters.language;
    },
  },
  mounted() {
    EventBus.$on("ROOM_BETLIST2", this.show);
  },
  destroyed() {
    EventBus.$off("ROOM_BETLIST2", this.show);
  },
  methods: {
    show(e) {
      this.roomId = e.room_id;
      this.betList = e.data;
      $("#room-bet-list").modal("show");
    },
    getTeamName(p, e) {
      var name = e[p + "_name_" + this.language];
      if (name == null || name == "" || !name) {
        name = e[p + "_team_name"];
      }
      return name;
    },
  },
};
</script>
