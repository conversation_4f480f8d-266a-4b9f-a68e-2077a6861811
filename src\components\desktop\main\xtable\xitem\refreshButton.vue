<template lang="pug">
  .filter-item.pointable.w-44.refresh(
    v-if="!state"
    :class="cls"
    @click="refresh()"
    )
    i.fas.fa-redo
    .timer {{ counter }}
  .filter-item.w-44(
    v-else
    :class="cls"
    )
    i.fad.fa-spinner.fa-spin
</template>

<script>
export default {
  props: {
    cls: {
      type: String
    },
    state: {
      type: Boolean
    },
    counter: {
      type: Number
    }
  },
  methods: {
    refresh() {
      this.$emit("refresh");
    }
  }
}
</script>