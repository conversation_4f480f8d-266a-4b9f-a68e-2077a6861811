.group-hover {
  margin-top: 12px;
  display: none;
  position: absolute;
  -webkit-transform: translate3d(0, 52px, 0px) !important;
  transform: translate3d(0, 52px, 0px) !important;
  top: 0px;
  left: 0;
  width: 100%;
  z-index: 2;
  background-image: url(/images/nav_bg_left.png), url(/images/nav_bg_right.png);
  background-size: auto 100%;
  background-position: left, right;
  background-repeat: no-repeat;
  -webkit-box-shadow: inset 0px 7px 5px -4px rgba(0, 0, 0, 0.5), 0px 7px 5px -4px rgba(0, 0, 0, 0.5);
  box-shadow: inset 0px 7px 5px -4px rgba(0, 0, 0, 0.5), 0px 7px 5px -4px rgba(0, 0, 0, 0.5);
  font-family: "Lato", sans-serif;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.group-hover .product-wrap {
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  text-align: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.group-hover .product-wrap .product {
  padding: 8px 8px;
  font-family: "Lato";
  font-size: 12px;
  font-weight: 600;
}
.group-hover .product-wrap .product.coming-soon {
  opacity: 0.6;
}
.group-hover .product-wrap .product:last-child {
  border-right: none;
}
.group-hover .product-wrap .product .suit-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  display: block;
}
.group-hover .product-wrap .product .suit-wrap .suite {
  font-size: 14px;
  font-weight: 500;
  background: url(/images/bg2.png);
  background-size: auto 100%;
  color: #014273;
  line-height: 1;
  font-family: "Oswald";
  padding: 8px 8px 10px 8px;
}
.group-hover .product-wrap .product .suit-wrap:hover .suite {
  background: #f9d040;
  color: #0d3a64;
}
.group-hover .product-wrap .product .suit-wrap .suit-image {
  position: relative;
  display: block;
  padding: 8px 0 0 0;
}
.group-hover .product-wrap .suit-wrap .suit-overlay {
  background: #00000088;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  display: none;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.group-hover .product-wrap .suit-wrap:hover .suit-overlay {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.group-hover .product-wrap .suit-wrap .suit-overlay .play-now {
  color: #ffc107;
  border: 1px solid #ffc107;
  padding: 4px 8px;
  font-size: 16px;
  font-weight: 900;
  font-family: "Lato";
}
.group-hover .product-wrap .suit-wrap {
  -webkit-transition: 0.5s all ease-in-out;
  -o-transition: 0.5s all ease-in-out;
  transition: 0.5s all ease-in-out;
  position: relative;
}
.group-hover .product-wrap .product a,
.group-hover .product-wrap .product a:hover {
  text-decoration: none;
}
.hover-drop:hover + .group-hover {
  display: block;
}
.group-hover:hover {
  display: block;
}
.pagenumber {
  background-color: #f5eeb8;
  color: #545454;
  padding: 0.5rem;
}
.pagenumber ul {
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  margin-bottom: 0.5rem;
  margin-right: 1rem;
}
.pagenumber ul li {
  list-style: none;
  padding: 4px;
}
.pagenumber ul li span {
  padding: 0 4px;
}
.pagenumber ul li a {
  background-color: #0f4f8c;
  color: #ffffff;
  padding: 4px 6px;
  border-radius: 1rem;
  line-height: 1;
}
.pagenumber ul li a:hover {
  color: #ffffff;
  background-color: #007bff;
}
.pagenumber ul li a:active {
  color: #f9d040;
  background-color: #007bff;
}
.page-dropdown {
  font-size: 12px;
}
.page-items {
  font-size: 12px;
  padding: 0 4px;
}
.page-button {
  padding: 4px 6px;
  border-radius: 1rem;
  line-height: 1;
  border: 1px solid #0f4f8c;
}
.btn-refresh {
  font-size: 11px;
  cursor: pointer;
  background: #f9d040;
  border: 1px solid #b28705;
  font-weight: bold;
  margin: 1px 1px 1px 1px;
  padding: 0;
  left: 0;
  line-height: 23px;
  width: calc(100% - 2px);
}
.btn-refresh:hover {
  background: #ffc107;
}
.btn-refresh:active {
  background: #b28705;
}
.ng-result {
  width: 100vw;
  height: 100vh;
}
.ng-result .topbar {
  background: #ffffffcc;
}
.ng-result .logo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.ng-result .logo span {
  padding: 0 8px;
  color: #0d3a64;
  font-weight: 900;
  line-height: 1;
  font-size: 20px;
}
.ng-result header .topbar .info-content .nav-info a {
  color: #0d3a64;
  text-transform: capitalize;
  -webkit-box-shadow: none;
  box-shadow: none;
  background-color: #ffffff;
  border: 0;
  text-shadow: none;
}
.ng-result header .topbar .info-content .nav-info a:hover,
.ng-result header .topbar .nav-info a.active {
  color: #007bff;
  -webkit-box-shadow: none;
  box-shadow: none;
  background-color: #ffffff;
  border-left: 0;
  border-right: 0;
  text-shadow: none;
  text-decoration: underline;
}
.table-digits {
  font-size: 13px;
  table-layout: fixed;
}
.table-digits th,
.table-digits td,
.table-digits th,
.table-digits th {
  padding: 3px;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
.table-digits tr.bg-color01 {
  background: url(/images/market-head-bg.png) no-repeat;
  background-size: cover;
}
.table-digits tr.bg-color02 {
  background: #b7c8e2;
}
.table-digits tr.bg-color03 {
  background: #f6f6f6;
}
.digits {
  width: 20px;
  height: 20px;
  line-height: 1;
  font-size: 12px;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-weight: 300;
}
.side-frame {
  padding: 0;
  border: 0;
  margin: 0;
  width: 100%;
  min-height: 80px;
  height: auto;
  overflow-y: hidden;
  font-size: 12px;
  color: #00000044;
  text-align: center;
  vertical-align: middle;
  background-image: url(/images/nav_bg_left.png), url(/images/nav_bg_right.png);
  background-size: auto 100%;
  background-position: left, right;
  background-repeat: no-repeat;
  position: relative;
}
.side-frame iframe {
  padding: 0;
  border: 0;
  margin: 0;
  width: 100%;
  height: 100%;
}
#tv-widget {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  -webkit-box-shadow: inset 0px 7px 5px -5px rgba(0, 0, 0, 0.5);
  box-shadow: inset 0px 7px 5px -5px rgba(0, 0, 0, 0.5);
}
.side-frame .wrapper-frame {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  color: #ffffffcc;
  font-family: "Oswald", "Tahoma", sans-serif;
  font-size: 20px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.25);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.side-frame .wrapper-frame a {
  display: none !important;
}
.frame-header {
  background: #145693;
  border-bottom: 1px solid #5991C1;
  border-left: 1px solid #5991C1;
  border-right: 1px solid #5991C1;
  height: auto;
  padding: 0;
  margin: 0;
  color: #f1f1f1;
  font-size: 12px;
  font-weight: 300;
  font-family: "Roboto Condensed";
  height: 32px;
}
.right .card-header {
  height: 34px;
}
.right .tools {
  padding: 0;
  height: 34px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.right .text-icon {
  font-size: 12px;
  font-weight: 400;
  width: 50%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  cursor: pointer;
  color: #f1f1f1;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 0;
  margin: 0;
  padding: 0 4px;
  border-right: 1px solid #ffffff22;
}
.right .text-icon:last-child {
  border-right: 0;
}
.right .text-icon span {
  margin-top: -1px;
}
.right .text-icon b {
  font-size: 11px;
  padding: 0;
  background: #F6C344;
  border-radius: 2px;
  color: #010101;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin-left: 4px;
  height: 17px;
  width: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-top: -1px;
	padding-top: 1px;
}
.right .text-icon.selected {
  color: #f9d040;
}
.right .text-icon.full-w {
  width: 100%;
}
.right .text-icon i,
.right .text-icon svg {
  font-size: 12px;
}
.right .text-icon.selected i,
.right .text-icon.selected svg {
  fill: #f9d040;
  color: #f9d040;
}
.right .text-actions {
  padding: 1px;
  height: 32px;
}
.right .text-icon i,
.right .text-icon svg {
  margin-right: 6px;
}
.right .icon .pointable {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.frame-header .tracker-button {
  width: 31px;
  height: 31px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  cursor: pointer;
  color: #f1f1f1;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 0;
  margin: 0;
  border-right: 1px solid #1b4c7c;
}
.frame-header .tracker-button svg {
  fill: #ffffff;
}
.frame-header .tracker-button.selected {
  background-color: #1b4c7c;
}
.frame-header .tracker-sport {
  padding: 6px;
}
.frame-header .tracker-menu {
  padding: 6px;
}
.frame-header .tracker-menu i {
  line-height: 20px;
}
.frame-header .tracker-sport img {
  height: 14px;
  line-height: 20px;
}
.frame-header .tracker-team {
  line-height: 20px;
  padding: 6px 0;
  color: #ffc107;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: calc(100% - 35px);
}
#tracker-header,
.frame-header {
  width: 100%;
}
#tracker-header,
.frame-header .tracker-team {
  width: calc(100% - 1px);
  max-width: calc(100% - 1px);
}
.frame-dropdown {
  width: calc(100% - 96px);
  max-width: calc(100% - 96px);
}
.frame-parent .tracker-dropdown {
  font-size: 12px;
  border-radius: 0;
  padding: 0 0 0 0;
  height: calc(100% - 33px);
  z-index: 200;
  overflow: auto;
  -webkit-transform: none !important;
  -ms-transform: none !important;
  transform: none !important;
  left: 1px !important;
  top: 32px !important;
  width: calc(100% - 2px);
  background: #000000;
  font-family: "Roboto Condensed";
  -webkit-transform: translate3d(0, 0, 0) !important;
  transform: translate3d(0, 0, 0) !important;
  will-change: transform !important;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.frame-parent .tracker-dropdown .dropdown-item {
  padding: 4px 8px;
  color: #fff;
}
.frame-parent .tracker-dropdown .dropdown-item:hover {
  background: #0d3a64;
  color: #f9d040;
}
.frame-parent .tracker-dropdown .dropdown-item.active {
  background: #bb4038;
}
.frame-parent .tracker-dropdown .dropdown-item .live {
  font-size: 9px;
  text-transform: uppercase;
  color: #fff;
  background: #be3a36;
  border-radius: 2px;
  padding: 0 2px 1px 2px;
  margin-right: 3px;
  border: 1px solid #ffffff88;
  font-family: "Roboto";
  font-weight: 600;
}
.frame-parent .tracker-dropdown .dropdown-item img {
  height: 14px;
  line-height: 14px;
  margin: 0 2px;
}
.frame-parent .tracker-dropdown .dropdown-item svg {
  fill: #ffffff;
  margin: 0 2px;
}
.frame-parent .tracker-dropdown .dropdown-item i {
  color: #ffffff;
  margin: 0 2px;
}
.frame-parent .tracker-dropdown .pointable:hover {
  color: #17a2b8;
  fill: #17a2b8;
}
.frame-parent .tracker-dropdown .pointable:hover svg {
  color: #17a2b8;
  fill: #17a2b8;
}
.h-19 {
  min-width: 19px;
  content: " ";
}
.h-22 {
  min-width: 22px;
  content: " ";
}
.frame-wrapper {
  height: auto;
  padding: 0;
  margin: 0;
  background: url(/images/bg2.png);
  background-size: auto 100%;
  border-radius: 0 0 5px 5px;
}
#tracker-frame .side-frame {
  background-image: url("/images/tracker-base.png");
  background-position: center;
  background-size: 100% auto;
  min-height: 80px;
  height: 100%;
}
#tracker-frame .side-frame.no-bg {
  background-image: none;
}
#tracker-frame .side-frame #widget {
  min-height: 80px;
  height: 100%;
}
#overlay {
  position: fixed;
  display: none;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  z-index: 2000;
}
#overlay #overlay-widget,
#overlay .tv-widget,
#overlay .wrapper-frame,
#overlay iframe {
  width: 100vw !important;
  height: 100vh !important;
  margin: 0;
  border: 0;
}
#overlay .wrapper-frame {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}
#overlay iframe {
  width: 100% !important;
  height: 100% !important;
  margin: 0;
  border: 0;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}
#overlay #overlay-widget {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
#overlay #overlay-title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
#overlay #overlay-title img {
  height: 20px;
  padding-right: 8px;
}
#overlay-close {
  display: block;
  cursor: pointer;
  color: white;
  padding: 10px;
  font-size: 20px;
}
body {
  overflow-x: auto !important;
  overflow-y: scroll !important;
}
.magicY {
  overflow-x: hidden !important;
  overflow-y: overlay !important;
}
.magicY::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
.magicY::-webkit-scrollbar-track {
  background: #f1f1f1;
}
.magicY::-webkit-scrollbar-thumb {
  background: #5078af;
}
.magicY:hover::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
.hx-frame {
  padding: 0;
  border: 0;
  margin: 0;
  width: 100vw;
  height: 100vh;
  overflow-y: hidden;
  background: #dddddd;
  -webkit-box-shadow: inset 0px 7px 5px -5px rgba(0, 0, 0, 0.5);
  box-shadow: inset 0px 7px 5px -5px rgba(0, 0, 0, 0.5);
  font-size: 12px;
  color: #00000044;
  text-align: center;
  vertical-align: middle;
  background-image: url(/images/nav_bg_left.png), url(/images/nav_bg_right.png);
  background-size: auto 100%;
  background-position: left, right;
  background-repeat: no-repeat;
  position: relative;
  font-family: "Oswald";
}
.content .main .single-match {
  background: #0d3a64;
  color: #ffffffcc;
  font-size: 14px;
  padding: 0;
  border-left: 1px solid #38648d;
  border-right: 1px solid #264e74;
  border-top: 1px solid #4f7eab;
  border-bottom: 1px solid #264e74;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-collapse: collapse;
  border-radius: 3px;
  display: block;
  line-height: 1;
  font-family: "Roboto";
  margin-top: 3px;
}
.content .main .single-match .header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}
.content .main .single-match .header img {
  height: 16px;
  margin-right: 6px;
}
.content .main .single-match .header.border-1 {
  border-bottom: 1px solid #1b4c7c;
}
.content .main .single-match .content {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 160px;
}
.content .main .single-match .content .bg-block {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.content .main .single-match .content .bg-block.s1,
.content .main .single-match .content .bg-block.s7 {
  top: auto;
  bottom: 0;
}
.content .main .single-match .content .info-block {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
}
.content .main .single-match .action-block {
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  line-height: 1;
  color: #ffffffcc;
  width: 28px;
  height: 28px;
  border-right: 1px solid #264e74;
}
.content .main .single-match .name-block {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  line-height: 1;
  padding: 0 8px;
  font-size: 12px;
}
.content .main .single-match .name-block small,
.content .main .single-match .name-block span {
  line-height: 1;
}
.content .main .single-match .name-block span {
  font-weight: 600;
}
.content .main .single-match .league-block {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  font-size: 14px;
  color: #ffffffaa;
  padding: 6px 0;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
.content .main .single-match .league-block .name-part,
.content .main .single-match .league-block .date-part {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
.content .main .single-match .league-block .name-part {
  font-size: 20px;
  color: #ffffffcc;
  padding-top: 12px;
  font-weight: 600;
  text-shadow: 1px 1px 3px #00000088;
}
.content .main .single-match .league-block .date-part {
  font-size: 14px;
  color: #ffffffaa;
  padding-top: 6px;
  text-shadow: 1px 1px 2px #00000088;
}
.content .main .single-match .event-block {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
.content .main .single-match .home-block,
.content .main .single-match .away-block {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 38%;
  overflow: hidden;
  word-break: break-word;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 1px 1px 1px #00000088, 0 0 3px #ffffffaa;
}
.content .main .single-match .home-block {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  margin-left: 8px;
}
.content .main .single-match .away-block {
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  margin-right: 8px;
}
.content .main .single-match .match-block {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  font-size: 12px;
  font-weight: 500;
  color: #ffffff88;
  padding: 6px 18px;
}
.content .main .single-match .running-time-block {
  font-family: "Oswald";
  font-size: 16px;
  font-weight: 600;
  padding: 6px 12px;
  color: #ffffff;
  margin-bottom: 6px;
  background: #000000cc;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 3px;
}
.content .main .single-match .running-time-block.danger {
  color: #ffffff;
  background: #ff0000cc;
}
.content .main .single-match .running-time-block.info {
  color: #ffffff;
  background: #2556b3;
}
.content .main .single-match .running-time-block.warn {
  color: #ffffff;
  background: #d88200;
}
.content .main .single-match .running-time-block.label {
  color: #ffffff;
  background: #b53f39;
}
.content .main .single-match .running-score-block {
  font-family: "Oswald";
  font-size: 30px;
  color: #ffffff;
  padding: 8px 0;
  background: #00000066;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 6px;
  text-shadow: 0 0 5px #000000;
}
.content .main .single-match .scoreboard .info-block {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 66px;
}
.content .main .single-match .scoreboard .league-block {
  font-size: 14px;
  padding: 6px 0;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  background: #00000044;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}
.content .main .single-match .scoreboard .event-block {
  width: 75%;
  background: #ffffff22;
}
.content .main .single-match .scoreboard .home-block,
.content .main .single-match .away-block {
  font-size: 14px;
  font-weight: 600;
}
.content .main .single-match .scoreboard .league-block .name-part {
  font-size: 14px;
  color: #ffffffcc;
  padding: 4px 12px;
  width: 100%;
  white-space: normal;
  text-align: center;
}
.content .main .single-match .scoreboard .league-block .date-part {
  font-size: 11px;
  color: #ffffffaa;
  padding: 0 12px;
  width: 100%;
}
.content .main .single-match .scoreboard .home-block,
.content .main .single-match .away-block {
  font-size: 14px;
}
.content .main .single-match .scoreboard .running-time-block {
  font-size: 14px;
  padding: 4px 12px;
  margin-bottom: 4px;
}
.content .main .single-match .scoreboard .running-score-block {
  font-size: 20px;
  padding: 4px 0;
  font-weight: 900;
}
.scoreboard-frame {
  width: 100%;
  position: absolute;
  top: 66px;
  left: 0;
  z-index: 3;
  border: 0;
  padding: 0;
  background: #ffffff00;
}
.scoreboard-frame iframe {
  width: 100%;
  height: 100%;
}
.content .main .single-match .content.scoreboard {
  height: 166px;
}
.content .main .single-match .content.scoreboard .scoreboard-frame {
  height: 100px;
}