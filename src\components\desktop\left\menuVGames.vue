<template lang="pug">
.bg
  .side-row.xb.vgames
    #heading-vgames.heading-collapse.collapsed(
      data-toggle="collapse"
      data-target="#collapse-vgames"
      aria-expanded="false"
      aria-controls="collapse-vgames"
      @click="setGame"
      )
      .group(:title="$t('ui.vgames')" :class="{ 'selected' : menu0 == 'event' && menuY == '1' }")
        .d-flex.justify-content-center.align-items-center
          .live-icon
            img(src="/v1/images/icon-sport-svg/v-games.svg" style="width: 21px; margin-left: 1px; margin-top: -1px;")
          .flex-fill.active-none
            span {{ $t("ui.vgames") }}
          .arrow-up.active-none
            i.far.fa-chevron-up
  #collapse-vgames.collapse.xb(aria-labelledby="heading-vgames" data-parent="#accordion-sports")
    .group.sub.group-mmo
      ul.subgroup
        template(v-for="item in getGameList")
          li.hot
            a.vgames-link(
              href="javascript:void(0);"
              @click="setMenu2(parseInt(item), 'today')"
            )
              .d-flex(:class="{ 'active' : menu0 == 'all' && menu1 == 'today' && menu2 == item }")
                .subtitle.flex-fill
                  img.icon(:src="'/v1/images/icon-sport-svg/' + getImage(item)")
                  span.title.active-none(style="text-transform: capitalize;" :class="item == '50' ? '' : ''") {{ sportsName[item] }}
                .number.active-none.vgames(:class="'vgames-' + item")
</template>

<script>
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";

export default {
  name: 'MenuVGames',
  props: {
    menu0: {
      type: String,
      required: true
    },
    menu1: {
      type: String,
      required: true
    },
    menu2: {
      type: [String, Number],
      required: true
    },
    menuY: {
      type: String,
      required: true
    },
    menuList: {
      type: Object,
      required: true
    }
  },
  computed: {
    getGameList() {
      if (this.menuList.menu && this.menuList.menu.hasOwnProperty("2")) {
        var m = Object.keys(this.menuList.menu["2"]);
        var n = config.gameList.filter((value) => m.includes(value));
        return n;
      } else {
        return [];
      }
    },
    sportsName() {
      if (!this.menuList) {
        return {};
      }

      if (!this.menuList.sports) {
        return {};
      }

      return this.menuList.sports;
    }
  },
  methods: {
    getImage(e) {
      return config.getSportsImage(e);
    },
    setGame() {
      var el = $(".vgames-link");
      if (el[0]) {
        setTimeout(() => {
          el[0].click();
        }, 100);
      }
    },
    setMenu2(e, f) {
      this.$emit('setMenu2', e, f);
    }
  }
}
</script> 