body.color1 {
	background: #E3E3E3;
}
.color1 header,.color1 header .topbar {
	background: #00967B;
}
.color1 header .toolbar {
	background-color: #00967B;
	border-top: 0;
	border-bottom: 8px solid #E3E3E3;
	-webkit-box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.2);
    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.2);
	top: 0;
}
.color1 header .toolbar.active.white-label .new-timezone {
	min-width: 256px;
}
.color1 header .toolbar.active.white-label .menu {
	min-width: 256px;
}
.color1 header .toolbar.active .container .logo {
	padding-top: 1px;
	display: none !important;
}
.color1 header .toolbar .menu .nav {
	justify-content: flex-end;
}
.color1 .content .right .z-side .card .card-header {
	color: #fff;
	background: url(/images/color1/tab-bg.png) no-repeat;
    background-size: cover;
}
.color1 .content .right .z-side .card .card-header {
	border: 1px solid #00a084;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.color1 .right .text-icon.selected {
	color: #fff;
}
.color1 .right .text-icon.selected i, .color1 .right .text-icon.selected svg {
	color: #fff;
}
.color1 .content .right .text-icon.selected {
}
.color1 .frame-header {
	background: #077461ee;
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
	border-left: 1px solid rgba(255, 255, 255, 0.2);
	border-right: 1px solid rgba(255, 255, 255, 0.2);
}
.color1 .frame-wrapper {
	background: #fff;
}
.color1 .mini-game-container .carousel-control-prev-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23078871' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
}
.color1 .mini-game-container .carousel-control-next-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23078871' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
}
.color1 .mini-game-container .carousel-indicators .active {
	background-color: #078871;
}
.color1 .mini-bg1 {
	background: #fff;
}
.color1 .luckybox {
	color: #000;
}
.color1 .luckyslider-label {
	color: #000;
}
.color1 .luckybox .btn-1 {
	background-color: rgba(0, 0, 0, 0.3);
	color: #fff;
}
.color1 .luckybox .btn-2 {
	background: transparent linear-gradient(90deg, #077461 0%, #00967B 100%) 0% 0% no-repeat padding-box;
}
.color1 .luckybox .switch-wrap .switch .slider {
	background-color: #AEAEAE;
}
.color1 header .toolbar .show-user {
	display: none !important;
}
.color1 header .toolbar .menu ul.nav li.nav-item a.nav-link {
	height: 32px;
	border-radius: 3px;
	margin: 1px;
	background-color: #077461;
	border: 1px solid #016654;
	-webkit-box-shadow: 0 1px 0 #00b695cc inset, 1px 1px 0 #00967a44 inset;
	        box-shadow: 0 1px 0 #00b695cc inset, 1px 1px 0 #00967a44 inset;
}
.color1 .dropdown-panel {
	background: #fff;
	box-shadow: none;
	border-radius: 0 0 3px 3px !important;
}
.color1 .dropdown-li {
	border-left: 0;
	border-right: 0;
}
.color1 .dropdown-li:last-child {
	border-bottom: 0;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0;
}
.color1 .dropdown-li .caption {
	font-size: 10px;
	color: #545454;
}
.color1 .dropdown-li .value .unit {
	font-size: 10px;
	color: #545454;
}
.color1 .dropdown-li .value {
	font-size: 12px;
}
.color1 .user-info-wrapper {
	border-radius: 3px;
	margin-bottom: 4px;
	font-size: 11px;
	font-family: "Lato", sans-serif;
}
.color1 .profile .dropdown-li {
	padding: 2px 2px;
	border-top: 1px solid rgba(0, 0, 0, 0.05);
	border-bottom: 0;
}
.color1 .user-info-wrapper {
	background-color: #077461;
	border: 1px solid rgba(255, 255, 255, 0.2);
	color: #fff;
}
.color1 .user-info-wrapper .user {
	color: #fff;
}
.color1 .user-info-wrapper .balance-drop {
	border-top: 1px solid rgba(255, 255, 255, 0.2);
	background-color: #eeeff4;
	color: #078871;
}
.color1 .user-info-wrapper .details {
	background-color: #078871;
	color: #fff;
	border-top: 1px solid rgba(255, 255, 255, .1);
}
.color1 .user-info-wrapper .details .balance-text {
	cursor: pointer;
	padding: 0.5rem;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	    -ms-flex-pack: justify;
	        justify-content: space-between;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	text-align: left;
	position: relative;
}
.color1 .user-info-wrapper .details .balance-text::after {
	content: "";
	height: 19px;
	border-right: 1px solid rgba(255, 255, 255, .1);
	padding-right: 16px;
}
.color1 .user-info-wrapper .details .balance .caption {
	font-size: 9px;
	color: #fff;
}
.color1 .user-info-wrapper .details .balance {
	padding: 0.5rem;
	-webkit-box-flex: 1;
	    -ms-flex-positive: 1;
	        flex-grow: 1;
	text-align: right;
	background-color: #078871;
	color: #F6C344;
	font-weight: bold;
}
.color1 .content .main .header-wrap {
}
.color1 .content .main .filter-area {
}
.color1 .content .main .filter-area .filter-item {
	color: #505050;
}
.color1 .content .main .filter-area .filter-item .filter-icon {
	color: #505050;
}
.color1 .content .main .filter-area .filter-item .filter-icon.page-button:hover {
	background-color: #00967a;
}
.color1 #new-searchbar.searchbar .input-group .input-group-prepend .input-group-text {
	color: #505050;
}
.color1 #new-searchbar.searchbar .input-group .form-control {
	color: #505050;
}
.color1 #new-searchbar.filter-item.searchbar .input-group .form-control::-webkit-input-placeholder {
	color: #505050 !important;
}
.color1 #new-searchbar.filter-item.searchbar .input-group .form-control:-ms-input-placeholder {
	color: #505050 !important;
}
.color1 #new-searchbar.filter-item.searchbar .input-group .form-control:-moz-placeholder {
	color: #505050 !important;
}
.color1 .page-button {
	border: 1px solid rgba(255, 255, 255, 0.2);
}
.color1 .x-accordion.accordion .bg {
	background: #078871;
}
.color1 .language-selector .dropdown-menu {
	background: #078871 !important;
	border: 1px solid rgba(0, 0, 0, 0.3) !important;
	-webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset !important;
	        box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset !important;
}
.color1 .x-btn-default,.color1 .x-btn {
	border: 1px solid rgba(0, 0, 0, 0.5);
	-webkit-box-shadow: 0 1px 0 #ffffff3d inset, 1px 1px 0 #ffffff2e inset;
	        box-shadow: 0 1px 0 #ffffff3d inset, 1px 1px 0 #ffffff2e inset;
}
.color1 .x-btn-default:hover {
	-webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 1px 1px 0 rgba(255, 255, 255, 0.2) inset, -1px -1px 1px rgba(0, 0, 0, 0.2) inset, 0 0 3px rgba(0, 0, 0, 0.2);
	        box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 1px 1px 0 rgba(255, 255, 255, 0.2) inset, -1px -1px 1px rgba(0, 0, 0, 0.2) inset, 0 0 3px rgba(0, 0, 0, 0.2);
}
.color1 .content .left.active .nav-header.nav-right {
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.color1 .content .left .nav-header {
	color: #078871;
}
.color1 .content .left .nav-header .collapsed {
	background: #078871;
}
.color1 .content .left .nav-header [aria-expanded="true"] {
	background: #077461;
}
.color1 .content .left .nav-header [aria-expanded="false"] {
	background: #078871;
}
.color1 .content .left .content-bet .nav li.nav-item a.nav-link {
	background: #eeeff4;
	color: #333;
}
.color1 .content .left .content-bet .nav li.nav-item a.nav-link.active {
	background: #f9d040;
}
.color1 .content .left .group.selected {
	background: url(/images/color1/tab-bg.png) no-repeat;
    background-size: auto 100%;
	color: #fff;
}
.color1 .content .left .side-row {
    background: url(/images/color1/tab-bg.png) no-repeat;
    background-size: cover;
}
.color1 .content .left .vgames {
}
.color1 .content .left.active .collapse.show .group {
	background: #078871;
}
.color1 .content .left.active .group.changed.selected {
	background: #077461 !important;
}
.color1 .content .left.active .heading-collapse[aria-expanded="false"] .group.changed:hover {
	background: #077461 !important;
}
.color1 .content .left .group {
	color: #fff;
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.color1 .content .left .tb {
	border-top: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 3px 3px 0 0;
}
.color1 .content .left .xb {
	border-left: 1px solid rgba(255, 255, 255, 0.1);
	border-right: 1px solid rgba(255, 255, 255, 0.1);
}
.color1 .content .left .xb a:hover {
	text-decoration: none;
}
.color1 .content .left ul.subgroup li.small {
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.color1 .content .left ul.subgroup li.small:last-child {
	border-bottom: 0;
}
.color1 .content .left .group .sport-type {
	background: #01886e;
}
.color1 .content .left .group .sport-type a {
	color: #e8f9ff;
}
.color1 .content .left .group.changed.selected,.color1 .content .left .group .sport-type.active {
	background: #077461;
}
.color1 .content .main .filter-block {
}
.color1 .content .main .filter-block .filter-single .filter-date {
	border: 1px solid rgba(0, 0, 0, 0.1);
}
.color1 .content .main .filter-block .filter-single .filter-date .filter-date-title {
	background-color: #00967B;
}
.color1 .content .main .filter-block .filter-single .filter-date .filter-date-body {
	color: #3E3E3E;
}
.color1 .content .main .filter-block .filter-single .filter-date.active .filter-date-title {
	background-color: #F6C344;
}
.color1 .round-alert {
	background: #00000044;
	border: 1px solid #077461;
}
.color1 .text-info {
	color: #077461 !important;
}
.color1 .content .main .filter-area .filter-item .dropdown .dropdown-menu a.dropdown-item,.color1 .content .main .filter-area .filter-item .dropdown .dropdown-menu .dropdown-item .filter-icon {
	color: #077461;
}
.color1 .content .main .filter-area .filter-item .dropdown .dropdown-menu a:hover.dropdown-item {
	background-color: #00967a;
	color: #fff;
}
.color1 .x-accordion.accordion .bg:last-child {
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.color1 .modal .modal-dialog .modal-content .modal-header {
	background: #00967a !important;
}
.color1 .modal .modal-dialog .modal-content .modal-body #select-league .card .card-header {
	background: #deeef5;
	color: #227190;
}
.color3 .modal .modal-dialog .modal-content .modal-footer .btn-primary {
	background: #077461;
}
.color1 .table-info tr th,.color1 .table-betresult tr th {
	background: #077461;
}
.color1 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link.active {
	background: #078871;
}
.color1 header .topbar .info-content .nav-info a:hover,.color1 header .topbar .nav-info a.active {
	background-color: #077461;
	border-left: 1px #078871 solid;
	border-right: 1px #078871 solid;
	text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
}
.color1 .info-wrapper .info-tablewrap .nav-tabs {
	border-bottom: 1px solid #00967a;
}
.color1 .info-wrapper .info-tablewrap .nav-tabs li.nav-item {
	background: rgba(0, 0, 0, 0.05);
	border-top: 1px solid #00967a;
	border-left: 1px solid #00967a;
	border-right: 1px solid #00967a;
}
.color1 .result-selection .form-control.datepicker {
	background: #078871;
}
.color1 .accordion-rules .card .card-header {
	padding: 8px;
	background: #078871;
	color: #efdd00;
	border-radius: 0 0 0 0;
}
.color1 .info-tablewrap .setting-right .btn-result.active {
	color: #fff;
	border: 1px solid #ffffff;
	border-radius: 0 0 0 0;
}
.color1 .modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap:hover input ~ .checkmark {
	background-color: #077461;
	border: 1px solid #078871;
}
.color1 .hx-league {
	background: #ECECEC;
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.color1 .hx-league.live {
	/* background: #bfded8; */
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.color1 .hx-table {
	background: url(/images/color1/market-head-bg.png);
    background-size: auto 100%;
	border-bottom: 1px solid #c4c4c4;
}
.color1 .hx-cell {
	border-left: 1px solid #0daa8d;
}
.color1 .hx-row {
	border-top: 1px solid #0daa8d;
}
.color1 .hx-col {
	border-left: 1px solid #0daa8d;
}
.color1 .hx-more {
	background: #00967a;
}
.color1 .morebet-wrapper .body-bet .nav-tabs li.nav-item a.nav-link.active {
	background: #0daa8d;
	border-bottom: 2px solid #00967a;
}
.color1 .morebet-wrapper .body-bet .tab-content .tab-pane .card .card-header {
	background: #0daa8d82;
}
.color1 .morebet-wrapper .body-bet .nav-tabs.live-tab li.nav-item a.nav-link.active {
	background: #C85F3F;
	border-bottom: 2px solid #C85F3F;
}
.color1 .morebet-wrapper .body-bet .tab-content .tab-pane .card .card-header.live {
	background: #F4D1C5;
}
.color1 .profile .dropdown-li a {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	padding: 6px;
	margin: 2px;
	color: #a5cae5;
	border: 1px solid #00362d;
	-webkit-box-shadow: 0 1px 0 #00967a88 inset, 1px 1px 0 #00967a44 inset;
	        box-shadow: 0 1px 0 #00967a88 inset, 1px 1px 0 #00967a44 inset;
	border-radius: 3px;
	text-align: center;
	text-decoration: none;
	line-height: 1;
}
.color1 .profile .dropdown-li a i {
	margin-right: 4px;
	font-size: 12px;
}
.color1 .profile .dropdown-li a:hover {
	color: #ffc107;
}
.color1 .content .left ul.subgroup input[type="checkbox"] {
	position: relative;
	width: 15px !important;
	height: 15px !important;
	color: #00362d;
	border: 1px solid #fff;
	border-radius: 3px;
	-webkit-appearance: none;
	   -moz-appearance: none;
	        appearance: none;
	outline: 0;
	cursor: pointer;
	-webkit-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	-o-transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
	transition: background 175ms cubic-bezier(0.1, 0.1, 0.25, 1);
}
.color1 .content .left ul.subgroup input[type="checkbox"]::before {
	position: absolute;
	content: "";
	display: block;
	top: 0;
	left: 4px;
	width: 5px !important;
	height: 10px !important;
	border-style: solid;
	border-color: #fff;
	border-width: 0 2px 2px 0;
	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	        transform: rotate(45deg);
	opacity: 0;
}
.color1 .content .left ul.subgroup input[type="checkbox"]:checked {
	color: #fff;
	border-color: #00362d;
	background: #00362d;
}
.color1 .content .left ul.subgroup input[type="checkbox"]:checked::before {
	opacity: 1;
}
.color1 .content .left ul.subgroup input[type="checkbox"]:checked ~ label::before {
	-webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
	        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}
.color1 .hx-table.hx-match.live .morebet-wrapper {
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	border-bottom: 1px solid #b5d0db;
	border-top: 1px solid #b5d0db;
	border-collapse: collapse;
}
.color1 .content .main .single-match {
	background: #078871;
	color: #ffffffcc;
	border: 1px solid rgba(255, 255, 255, 0.2);
}
.color1 .content .main .single-match .action-block {
	border-right: 1px solid rgba(255, 255, 255, 0.2);
}
.color1 .content .main .single-match .content img {
	-webkit-filter: hue-rotate(280deg);
	        filter: hue-rotate(280deg);
}
.color1 .hx-table.alternate.hx-match {
	background: #F6F6F6;
}
.color1 .content .left.active .nav-header .collapsed, .content .left.active .nav-header [aria-expanded="true"], 
.color1 .content .left.active .nav-header [aria-expanded="false"] {
    background: transparent;
}
.color1 .content .left.active .nav-header.nav-left, .color1 .content .left.active .nav-header.nav-right {
	background: #078871;
}
.color1 .content .left ul.subgroup li.hot { 
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.color1.europeview .content .main .single-match {
	background: #078871;
    border: 1px solid #078871;
}
.color1.europeview .content .main .single-match .action-block {
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}
.color1.europeview .hx-table.hx-match .bet-value {
	background-color: #ECECEC;
}
.color1 .x-side {
	background: #E3E3E3;
}
.color1 .x-side::before {
	border-left: 12px solid #E3E3E3;
}
.color1 .x-side::after {
	border-right: 8px solid #E3E3E3;
}
.color1 .luckybox .btn-1:hover {
	background: #078871;
}
.color1 .luckydelete {
	background: rgba(0, 0, 0, 0.3);
}
.color1 .luckydelete:hover {
	background: #078871;
}
.color1 .luckyodds:last-child {
	color: #0A4782;
}
.color1 .luckyeven {
	background: rgba(0,0,0,.05);
}
.color1 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link {
	color: #000;
}
.color1 .info-wrapper .info-tablewrap .nav-tabs li.nav-item a.nav-link.active {
    color: #F6C344;
}
.color1 .info-tablewrap .setting-right .btn-result.active {
	background: #077461 !important;
}
.color1 .result-selection .btn-result {
	border: 1px solid #00967a;
}
.color1 .result-selection .btn-result:hover, 
.color1 .result-selection .btn-result.active {
	background: #077461 !important;
	border: 1px solid #00967a;
}
.color1 .info-tablewrap .pagination .page-item .page-link {
	border: 1px solid #00967a;
	color: #00967a;
}
.color1 .info-tablewrap .pagination .page-item .page-link.active {
	background: #077461;
	color: #fff;
}
.color1 .info-tablewrap .setting-right .custom-control-input:checked ~ .custom-control-label::before {
    background: #077461;
    border: 1px solid #00967a;
}
.color1 .modal .modal-dialog .modal-content .modal-footer .btn-primary {
    background: #077461;
}
.color1 .result-selection .dropdown-menu .dropdown-item:hover, 
.color1 .result-selection .dropdown-menu .dropdown-item:focus, 
.color1 .result-selection .dropdown-menu .dropdown-item.active {
	background: #9f9f9f;
}
.color1 .tournament-top, 
.color1 .tournament-top-plus {
	background: #00967B;
}
.color1 .tournament-btn-search {
	background-color: #01886e;
}
.color1 .tournament-btn-search:hover, 
.color1 .tournament-btn-search:focus {
	background-color: #01886e;
}
.color1 .tournament-user {
	background: transparent linear-gradient(90deg, #077461 0%, #00967B 100%) 0% 0% no-repeat padding-box;
}
.color1 .tournament-pagination ul li .page-link.disable,
.color1 .tournament-pagination ul li .page-link,
.color1 .tournament-pagination ul li .page-link:hover {
	border: 1px solid #00967a;
}
.color1 .tournament-pagination ul li .page-link i,
.color1 .tournament-pagination ul li .page-link {
	color: #00967a;
}
.color1 .tournament-pagination ul li .page-link.active {
	background-color: #01886e;
	border: 1px solid #00967a;
	color: #fff;
}
.color1 .tournament-pagination ul li .page-link:hover {
	color: #000;
}
.color1 .tournament-btn {
	background: url('../../img/tn/bg-match-color1.png') top center no-repeat;
}
.color1 .tournament-pool-fee {
	background: rgba(0,0,0,0.5);
	border: 1px rgba(255,255,255,0.2) solid;
}
.color1 .tournament-pool-result {
    background-color: #01886e;	
}
.color1 .tournament-page-wrapper ul li.nav-item .nav-link {
	color: #01886e;
}
.color1 .select-day-top {
	color: #000;
}
.color1 .select-day-bottom {
	color: #008a6f;
}
.color1 .select-day.today .select-day-bottom {
	color: #F6C344;
}
.color1 .select-league-title {
	color: #000;
}
.color1 .select-league-teams {
	color: #000;
}
.color1 .select-league-time {
	color: #000;
}
.color1 .select-league .date-check input:checked ~ .checkmark-date {
	background-color: #01886e;
	border: 1px solid #01886e;
}
.color1 .room-bottom {
	color: #000;
}
.color1 .room-rate-select select option, 
.color1 .room-limit-select select option,
.color1 .tournament-search select option {
	background-color: #01886e;
}
.color1 .room-rate-select select,
.color1 .room-limit-select select {
	color: #000;
}
.color1 .tournament-point {
	background-color: #00967B;
}
.color1 .tournament-menu ul li.active {
	background-color: #F6C344;
}
.color1 .tournament-menu ul li.active a {
	color: #000;
}
.color1 .bet-info .bet-type.blue {
	color: #007963;
}
.color1 .tournament-mybet-inner {
	background: #D1D9E1;
}
.color1 .tournament-details-icon {
	filter: brightness(0%);
    opacity: 0.5;
}
.color1 .player-prize-icon {
	filter: brightness(0%);
    opacity: 0.5;
}
.color1 .tournament-details-bottom {
	color: #007963;
}
.color1 .tournament-betslip-room {
	background: #077461;
}
.color1 .tournament-betslip {
    background: #D1D9E1;
}
.color1 .alert-tournament {
	background-color: #fff;
	color: #000;
}
.color1 .tournament-odds {
	background-color: #fff;
}
.color1 .tournament-odds::before {
	filter: brightness(0) invert(1);
}
.color1 .tournament-odds::after {
	filter: brightness(0) invert(1);
}
.color1 .tournament-betslip-matches {
	color: #000;
}
.color1 .tournament-odds-text {
	color: #000;
}
.color1 .tournament-betslip-matches .tn-team {
	color: #000;
}
.color1 .tournament-betslip-matches .tn-vs {
	color: #333;
}
.color1 .tournament-betslip-header-title {
	background: #078871;
}
.color1 .player-betslip-top {
	background-color: #00967B;
}
.color1 .player-prize-bottom {
	color: #008a6f;
}
.color1 .player-betslip-room {
    color: #000;
}
.color1 .player-betslip-content .tournament-mybet-single .tournament-mybet-date {
    color: #007963;
}
.color1 .player-betslip-content .tournament-mybet-single .tournament-mybet-small span {
	color: #007963;
}
.color1 .tournament-mybet-status {
	color: #50AE1B;
}
.color1 .tournament-betslip-wrapper ul li.nav-item .nav-link {
	background-color: #077461;
}
.color1 .hl-select-date {
	background-color: #078871;
}
.color1 .hl-filter-bar .dropdown-menu {
	background: #00967a;
}
.color1 .hl-filter-bar .dropdown-menu .dropdown-item:hover {
	background: #078871;	
}
.color1 .hl-filter-bar .dropdown-menu .dropdown-item.active,
.color1 .hl-filter-bar .dropdown-menu .dropdown-item:active,
.color1 .hl-filter-bar .dropdown-menu .dropdown-item.active:hover {
	background-color: #077461;
}
.color1 .hl-league-titlebar {
	background: #077461;
}
.color1 .tournament-betslip-matches .text-center {
	color: #000 !important;
}
.color1 .tournament-table-entry .table-tournament {
	color: #000;
}
.color1 .tournament-stake-field .input-group-text {
	background: #01886e;
}
.color1 .swal2-confirm {
	background: #078871 !important;
    border: 1px solid #00967a !important;
}
.color1 .tournament-betslip-league span {
	color: #000;
}
.color1 .tournament-odds2 .tournament-odds-text {
	color: #fff;
}