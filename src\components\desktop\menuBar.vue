<template lang="pug">
.main-nav
  debugTools
  ul.nav.d-flex.justify-content-start.align-items-center.h-100
    li.nav-item
      a.nav-link(href="javascript:void(0);", @click="setMenu2(1)")
        img.icon-menu(src="/v1/images/menu/sport.png")
        img.icon-menu-hover(src="/v1/images/menu/sport_hover.png")
        span {{ $t('ui.sports') }}

    li#casino.nav-item(v-if="!whiteLabel && !mmoMode && casinoList.length > 0")
      a#casino-menu.nav-link.hover-drop(href="javascript:void(0);", data-toggle="dropdown", aria-haspopup="true", aria-expanded="false")
        img.icon-menu(src="/v1/images/menu/casino.png")
        img.icon-menu-hover(src="/v1/images/menu/casino_hover.png")
        span {{ $t('ui.casino') }}
      .dropdown-menu.group-hover(aria-labelledby="casino-menu")
        //--------------------------------------------------------------------
        //- Casino
        //--------------------------------------------------------------------
        .product-wrap(v-for="row in casinoList")
          .product(v-for="item in row")
            router-link.suit-wrap(
              :to="item.toPath",
              target="_blank",
              :onclick="item.popup",
            )
              .suit-image
                img(:src="item.thumbnail")
                .suit-overlay
                  .play-now {{ $t('ui.play_now') }}
              .suite {{ $t(item.title) }}

    li#slots.nav-item(v-if="!whiteLabel && slotsList.length > 0")
      a#slots-menu.nav-link.hover-drop(href="javascript:void(0);", data-toggle="dropdown", aria-haspopup="true", aria-expanded="false")
        img.icon-menu(src="/v1/images/menu/slot.png")
        img.icon-menu-hover(src="/v1/images/menu/slot_hover.png")
        span {{ $t('ui.slots') }}
      .dropdown-menu.group-hover(aria-labelledby="slots-menu")
        //--------------------------------------------------------------------
        //- Slots
        //--------------------------------------------------------------------
        .product-wrap(v-for="row in slotsList")
          .product(v-for="item in row")
            router-link.suit-wrap(
              :to="item.toPath",
              target="_blank",
              :onclick="item.popup",
            )
              .suit-image
                img(:src="item.thumbnail")
                .suit-overlay
                  .play-now {{ $t('ui.play_now') }}
              .suite {{ $t(item.title) }}


    li#others.nav-item(v-if="!whiteLabel && othersList.length > 0")
      a#others-menu.nav-link.hover-drop(href="javascript:void(0);", data-toggle="dropdown", aria-haspopup="true", aria-expanded="false")
        img.icon-menu(src="/v1/images/menu/others.png")
        img.icon-menu-hover(src="/v1/images/menu/others_hover.png")
        span {{ $t('ui.others') }}
      .dropdown-menu.group-hover(aria-labelledby="others-menu")
        //--------------------------------------------------------------------
        //- Others
        //--------------------------------------------------------------------
        .product-wrap(v-for="row in othersList")
          .product(v-for="item in row")
            router-link.suit-wrap(
              :to="item.toPath",
              target="_blank",
              :onclick="item.popup",
            )
              .suit-image
                img(:src="item.thumbnail")
                .suit-overlay
                  .play-now {{ $t('ui.play_now') }}
              .suite {{ $t(item.title) }}

    li.nav-item(v-if="!whiteLabel && (brand == 'WBET')")
      router-link.nav-link(to="/esports2", target="_blank", onclick="window.open(this.href,'esports2','top=10,height=768,width=1366,status=no,toolbar=no,menubar=no,location=no');return false;")
        img.icon-menu(src="/v1/images/menu/esport.png")
        img.icon-menu-hover(src="/v1/images/menu/esport_hover.png")
        span {{ $t('ui.e_sports') }}

    li.nav-item(v-if="!whiteLabel && w4dSupport && !mmoMode")
      router-link.nav-link(to="/w4d", target="_blank", onclick="window.open(this.href,'w4d','top=10,height=576,width=1248,status=no,toolbar=no,menubar=no,location=no');return false;")
        img.icon-menu(src="/v1/images/menu/4d.png")
        img.icon-menu-hover(src="/v1/images/menu/4d_hover.png")
        span {{ $t('ui.w4d') }}
</template>

<script>
import config from "@/config";
import { GAME_MENU_CONFIG } from "@/config/game-menu";
import mixinTypes from "@/library/mixinTypes";
import naming from "@/library/_name.js";
import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {
    debugTools: () => import("@/components/desktop/debugTools"),
  },
  mixins: [mixinTypes],
  data() {
    return {
      loading: false,
    };
  },
  computed: {
    casinoList() {
      const chunkSize = 8;
      if (GAME_MENU_CONFIG.casino) {
        const visibleItems = GAME_MENU_CONFIG.casino.filter(this.isMenuItemVisible);
        return visibleItems.reduce((resultArray, item, index) => {
          const chunkIndex = Math.floor(index / chunkSize);
          if (!resultArray[chunkIndex]) {
            resultArray[chunkIndex] = [];
          }
          resultArray[chunkIndex].push(item);
          return resultArray;
        }, []);
      } else {
        return [];
      }
    },
    slotsList() {
      const chunkSize = 8;
      if (GAME_MENU_CONFIG.slots) {
        const visibleItems = GAME_MENU_CONFIG.slots.filter(this.isMenuItemVisible);
        return visibleItems.reduce((resultArray, item, index) => {
          const chunkIndex = Math.floor(index / chunkSize);
          if (!resultArray[chunkIndex]) {
            resultArray[chunkIndex] = [];
          }
          resultArray[chunkIndex].push(item);
          return resultArray;
        }, []);
      } else {
        return [];
      }
    },
    othersList() {
      const chunkSize = 8;
      if (GAME_MENU_CONFIG.others) {
        const visibleItems = GAME_MENU_CONFIG.others.filter(this.isMenuItemVisible);
        return visibleItems.reduce((resultArray, item, index) => {
          const chunkIndex = Math.floor(index / chunkSize);
          if (!resultArray[chunkIndex]) {
            resultArray[chunkIndex] = [];
          }
          resultArray[chunkIndex].push(item);
          return resultArray;
        }, []);
      } else {
        return [];
      }
    },

    brand() {
      return config.brand;
    },
    newFeatures() {
      return config.newFeatures;
    },
    currentList() {
      return config.currentList;
    },
    currency_code() {
      return this.$store.getters.currencyCode;
    },
    w4dSupport() {
      return config.w4dSupport;
    },
    mmoMode() {
      return this.$store.getters.mmoMode;
    },
  },
  destroyed() {},
  mounted() {},
  methods: {
    // Helper function to check if a menu item should be visible
    isMenuItemVisible(item) {
      return (
        !item.isHidden &&
        (item.isNewFeatures ? this.newFeatures : true) &&
        !item.exclude.includes(this.brand) &&
        (item.currency.includes(this.currency_code) || item.currency.length <= 0) &&
        (item.urlName ? config.isGameProviderAvailable(item.urlName) : true)
      );
    },
    // Helper function to open a window
    openWindow(name, height, width) {
      window.open(this.$router.resolve({ name }).href, name, `top=10,height=${height},width=${width},status=no,toolbar=no,menubar=no,location=no`);
    },
    // Helper function to set the menu
    setMenu2(e) {
      var mi5 = {};
      mi5["menuX"] = false;
      mi5["menuY"] = "0";
      mi5["menu0"] = "all";
      mi5["menu1"] = "today";
      mi5["menu2"] = e;
      mi5["menu3"] = "hdpou";
      var changed = false;
      if (mi5["menuX"] != this.menuX) {
        changed = true;
      }
      if (mi5["menuY"] != this.menuY) {
        changed = true;
      }
      if (mi5["menu0"] != this.menu0) {
        changed = true;
      }
      if (mi5["menu1"] != this.menu1) {
        changed = true;
      }
      if (mi5["menu2"] != this.menu2) {
        changed = true;
      }
      if (mi5["menu3"] != this.menu3) {
        changed = true;
      }

      if (changed) {
        this.resetSelectLeague();
        this.clearSearch();
        mi5["src"] = "menuBar.setMenu2";
        this.$store.dispatch("layout/setMenuItems", mi5);
        EventBus.$emit("INVALIDATE");
        $(".market-menu").removeClass("active");
      }
    },
    // Helper function to clear the search
    clearSearch() {
      this.$store.dispatch("layout/setSearch", null);
    },
    // Helper function to reset the select league
    resetSelectLeague() {
      this.$store.dispatch("layout/resetSelectLeague");
    },
  },
};
</script>
