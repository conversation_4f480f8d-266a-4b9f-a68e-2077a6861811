.hx-market {
  cursor: pointer;
}

.hx-market:active {
  cursor: wait;
}

.hx-top-rounded {
  border-radius: 3px 3px 0 0;
  overflow: hidden;
}

.hx-mt-1 {
  margin-top: 1px;
}

.hx-mt-2 {
  margin-top: 3px;
}

.hx-main {
  overflow: hidden;
  /* overflow: visible; */
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-collapse: collapse;
}

.hx-flex-c {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-align: center !important;
  -ms-flex-align: center !important;
  align-items: center !important;
  -webkit-box-pack: center !important;
  -ms-flex-pack: center !important;
  justify-content: center !important;
}

.hx-table {
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-collapse: collapse;
  line-height: 1;
  color: #fff;
  box-sizing: border-box;
  background: url(/images/market-head-bg.png);
  background-size: auto 100%;
}

.hx-table.live {
  border-collapse: collapse;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background: url(/images/market-head-bg-live.png);
  background-size: auto 100%;
}

.hx-table.bt-1 {
  border-top: 1px solid #4273a1;
}

.hx-cell {
  display: block;
  border-left: 1px solid #4F85B3;
  border-collapse: collapse;
  line-height: 1;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.hx-row {
  line-height: 1;
  border-top: 1px solid #4F85B3;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.hx-col {
  border-collapse: collapse;
  border-left: 1px solid #4F85B3;
  line-height: 1;
  display: inline-block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  height: 22px;
  font-weight: 400;
  flex: 1;
}

.hx-col:first-child {
  border-left: 0;
}

.hx {
  display: block;
  padding: 1px;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  line-height: 20px;
}

.hx-header-title {
  font-size: 16px;
  color: #014273;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 0;
  position: relative;
  z-index: 1;
  line-height: 24px;
  margin: 0 4px 0 8px;
  text-align: center;
  min-width: 70px;
  transform: skewX(-15deg);
  font-family: 'Roboto';
}

.hx-header-title::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: #FDB338;
  width: 70px;
  height: 24px;
  z-index: -1;
}

.hx-header-title.live {
  color: #FFFFFF;
}

.hx-header-title.live::before {
  background-color: #C11A00;
}

.hx-header-subtitle {
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 700;
  padding-left: 4px;
  font-family: 'Roboto';
}

.hx-table.live .hx-cell {
  border-left: 1px solid #ffffff23;
}

.hx-table.live .hx-row {
  border-top: 1px solid #ffffff23;
}

.hx-table.live .hx-col {
  border-left: 1px solid #ffffff23;
}

.hx-table.live .hx-col:first-child {
  border-left: 0;
}

.hx-table .b-0 {
  border: 0 !important;
  border-collapse: collapse;
  box-sizing: border-box;
}

.hx-table.hx-match {
  background: #FFFFFF;
  color: #212529;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.hx-table.alternate.hx-match {
  background: #F4F7FC;
  color: #212529;
}

.hx-table.live.hx-match {
  background: #FBE9E1;
  color: #212529;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.hx-table.live.alternate.hx-match {
  background: #FFF0EA;
  color: #212529;
}

.hx-table.hx-match:hover,
.hx-table.alternate.hx-match:hover,
.hx-table.live.hx-match:hover,
.hx-table.live.alternate.hx-match:hover,
.hx-table.hx-match.hx-morebet-body:hover,
.hx-table.hx-match.hx-morebet-body.live:hover {
  background: #f5eeb8;
}

.hx-table.hx-match .hx-cell {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
}

.hx-table.hx-match.live .hx-cell {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
}

.hx-table.hx-match .bb-l {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
}

.hx-table.hx-match.live .bb-l {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 1px solid #e0b1a2;
}

.hx-table.hx-match.hx-morebet {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.hx-table.hx-match.hx-morebet.live {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.hx-table.hx-match .hx-row,
.hx-table.hx-match.live .hx-row {
  border: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.hx-table.hx-match .hx-col,
.hx-table.hx-match.live .hx-col {
  border: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.hx-table.hx-match .hx,
.hx-table.hx-match.live .hx {
  line-height: 18px;
  min-height: 20px;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.hx-table.hx-match .hx.text-center,
.hx-table.hx-match.live .hx.text-center {
  justify-content: center;
}

.hx-table.hx-match.hx-more-bet .hx-more-col.header .hx,
.hx-table.hx-match.hx-more-bet .hx-more-col.header.live .hx {
  justify-content: center;
}

.hx-table.hx-match.live .hx span.card {
  padding: 2px 3px;
  margin-left: 5px;
  background: #b53f39 !important;
  color: #fff;
  border-radius: 3px;
  -webkit-transform: rotate(15deg) !important;
  -ms-transform: rotate(15deg) !important;
  transform: rotate(15deg) !important;
  font-weight: 500;
  font-size: 9px;
  display: inline-block;
  line-height: 1;
  font-family: "Roboto";
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.hx.team-black {
  color: #01122b;
  white-space: normal;
  font-weight: 600;
  padding: 1px 0 1px 8px !important;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  line-height: 18px !important;
}

.hx.team-red {
  color: #912817;
  white-space: normal;
  font-weight: 600;
  padding: 1px 0 1px 8px !important;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  line-height: 18px !important;
}

.hx.draw {
  color: #545454;
  white-space: normal;
  font-weight: 600;
  padding: 1px 0 1px 8px !important;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  line-height: 18px !important;
}

.hx-table.hx-match .hx-rows {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.hx-table.hx-match.live .hx-rows {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.hx-table.hx-match .hx-rows:last-child,
.hx-table.hx-match.live .hx-rows:last-child {
  border-bottom: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.hx-table.hx-match .hx-rows,
.hx-table.hx-match.live .hx-rows {
  min-height: 44px;
  height: auto;
}

.hx-table.hx-match.hx-compact .hx-rows,
.hx-table.hx-match.hx-compact.live .hx-rows {
  min-height: 44px;
}

.hx-table.hx-match.hx-compact.hx-sl .hx-rows,
.hx-table.hx-match.hx-compact.hx-sl.live .hx-rows {
  min-height: 42px;
}

.hx-table.hx-match.hx-compact.hx-sl .hxs,
.hx-table.hx-match.hx-compact.hx-sl.live .hxs {
  width: 100%;
  height: 28px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.hx-table.hx-match.hx-compact.hx-sl .bet-value,
.hx-table.hx-match.hx-compact.hx-sl.live .bet-value {
  width: 100%;
}

.hx-table.hx-match .hx-cols,
.hx-table.hx-match.live .hx-cols {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: inline-block;
  height: 100%;
  padding: 1px 1px 1px 1px;
}

.hx-table.hx-match .hx-cols.bb-end,
.hx-table.hx-match.live .hx-cols.bb-end {
  min-height: 44px;
  height: 100%;
  border-right: 1px solid #e0b1a2;
}

.hxs {
  display: inline-block;
}

.hx-table .ball-value {
  color: #545454;
  font-weight: 400;
  font-size: 11px;
  text-align: left;
  margin: 0;
  padding: 0 0 0 3px;
  line-height: 1;
  border: 0;
}

.hx-table .ball-value.ball-mmo {
  color: #0765be;
}

.hx-table .ball-value.ball-mmo .red {
  color: #b53f39;
}

.hx-table .bet-value:hover,
.hx-table .text-red:hover {
  background: #0000001A;
  border: 1px solid #0000001F;
}

.hx-table .bet-value.selected {
  background: #fff;
}

.hx-table .bet-value.highlighted.up {
  border: 1px solid #C0DDC6;
  /* background: #EDF2EE; */
  background: transparent;
}

.hx-table .bet-value.highlighted.down {
  border: 1px solid #D9B8B2;
  /* background: #FFE6E2; */
  background: transparent;
}

.hx-table .bet-value.highlighted:hover {
  background: #F4D1C5;
}

.hx-table .bet-value.highlighted {
  background: #ffaf96;
  color: #b53f39;
  position: relative;
  color: #000;
}

.hx-table .bet-value.highlighted .fa-sort-down {
  position: absolute;
  right: 2px;
  top: 2px;
  color: red;
}

.hx-table .bet-value.highlighted .fa-sort-up {
  position: absolute;
  right: 2px;
  top: 6px;
  color: #5dad00;
}

.hx-table .bet-value.highlighted.text-red {
  color: #b53f39 !important;
}

.hx-table .text-red {
  color: #b53f39 !important;
}

.hx-table .bet-value {
  cursor: pointer;
  border-radius: 0;
  color: #01122b;
  display: block;
  line-height: 16px;
  height: 18px;
  padding-right: 8px;
  text-align: right;
  font-weight: 600;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 1px;
  border: 1px solid transparent;
  min-width: 50px;
  float: right;
}

.hx-table .bet-value.bet-mmo {
  color: #00651e;
}

.hx-table .bet-value.bet-mmox {
  color: #00651e;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 4px;
  padding-right: 8px;
}

.hx-table .bet-value.bet-mmox .red {
  color: #b53f39;
}

.hx-table .bet-value.bet-mmox .giving {
  color: #01122b;
  color: #0765be;
  font-weight: 400;
  padding-right: 8px;
}

.hx-table .bet-value.bet-mmo.highlighted:hover {
  background: #f3ffe5;
}

.hx-table .bet-value.bet-mmo.highlighted {
  background: #e2f9c8;
}

.hx-event {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 1px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.hx-event .score {
  color: #b53f39;
  font-weight: 700;
  line-height: 1rem;
  font-size: 12px;
}

.hx-event .label {
  color: #000;
  font-weight: 500;
  line-height: 1rem;
  font-size: 12px;
}

.hx-event .duration {
  color: #545454;
  font-weight: 500;
  line-height: 1rem;
  font-size: 12px;
}

.hx-event .info {
  color: #2556b3;
  font-weight: 500;
  border: 1px solid #2556b3;
  padding: 0 4px;
  border-radius: 3px;
  line-height: 1rem;
  font-size: 10px;
  width: 56px;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  display: flex;
}


.hx-event .danger {
  color: #b53f39;
  font-weight: 500;
  line-height: 1rem;
  font-size: 12px;
}

.hx-event .warn {
  color: #d88200;
  font-weight: 500;
  border: 1px solid #d88200;
  padding: 0 4px;
  border-radius: 3px;
  line-height: 1rem;
  font-size: 11px;
}

.hx-footer {
  width: 100%;
  height: 66px;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.hx-league {
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background: #DCE6EF;
  color: #000;
  padding: 0;
  cursor: auto;
  font-weight: 600;
  line-height: 26px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: start;
  margin: 0;
  height: 25px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.hx-league.live {
  background: #F4D1C5;
  border-bottom: 1px solid #ffffff23;
}

.hx-league .hx-corner-btn {
  width: 30px;
  height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border-radius: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  color: #000000;
  cursor: pointer;
  border-collapse: collapse;
}

.hx-league .hx-button {
  color: #fff;
  height: 24px;
  width: 24px;
  position: relative;
  border-radius: 0;
  text-align: center;
  line-height: 1;
  cursor: pointer;
  margin: 0;
  padding: 0;
  vertical-align: 1px;
  font-size: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.hx-league .hx-button.hx-fav {
  width: 33px;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-collapse: collapse;
}

.hx-league .hx-button i {
  font-size: 14px;
  vertical-align: 0;
}

.hx-league .hx {
  padding: 1px 4px;
}

.hx-info {
  color: #264e74;
  font-size: 14px;
  display: inline-block;
  line-height: 1;
  padding-top: 1px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.hx-league.live .hx-info {
  color: #85360b;
}

.hx-action {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.hx-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 20px;
  height: 20px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  cursor: pointer;
  font-size: 13px;
}

.hx-icon i {
  color: #00000099;
}

.hx-icon i.selected {
  color: #ffc107;
  text-shadow: 0 0 0 #000000, 0 0 1px #000000, 0 0 3px #00000044;
}

.hx-more {
  background: #3E74A2;
  color: #fff;
  width: 41px;
  max-width: 100%;
  text-align: center;
  line-height: 18px;
  display: block;
  border-radius: 0;
  cursor: pointer;
  font-weight: 500;
  font-size: 11px;
  border-radius: 30px;
}

.hx-more i {
  font-size: 9px;
}

.hx-match.live .hx-more {
  background: #CB5A34;
}

.hx-table.hx-match.hx-more-bet .hx-cell {
  border: 0;
}

.hx-table.hx-match.hx-more-bet .hx-row.header {
  background: #d0d0d0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
}

.hx-table.hx-match.hx-more-bet.live .hx-row.header {
  background: #F6E3DC;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.hx-table.hx-match .hx-more-col {
  background: #FFFFFF;
  color: #0d0d0d;
  display: flex;
}

.hx-table.hx-match.live .hx-more-col {
  background: #FFF0EA;
  color: #0d0d0d;
}

.hx-table.hx-match .hx-more-col .hx-col {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
  flex: 1;
}

.hx-table.hx-match.live .hx-more-col .hx-col {
  border-left: 1px solid rgba(0, 0, 0, 0.05);
}

.hx-table.hx-match .hx-more-col.header .hx-col {
  line-height: 1;
  height: auto;
  flex: 1;
}

.hx-table.hx-match .hx-more-col .hx-col:first-child {
  border-left: 0;
}

.hx-more-col .hx-col .hx {
  text-align: center;
  line-height: 16px;
}

.hx-more-col .hx-col .hx .hxs {
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 1px 1px 0 1px;
}

.hx-table.hx-match .hx-more-col.hx-more-col1 .hx-col:first-child {
  min-width: 74px;
  width: 74px;
  max-width: 74px;
}

.hx-table.hx-match .hx-more-col.hx-more-col1 .hx-col {
  min-width: 75px;
  width: 75px;
  max-width: 75px;
}

.hx-table.hx-match .hx-more-col.hx-more-col2 .hx-col {
  /* min-width: 94px;
  width: 94px;
  max-width: 94px; */
}

.hx-table.hx-match .hx-more-col.hx-more-col3 .hx-col {
  min-width: 50%;
  width: 50%;
  max-width: 50%;
}

.hx-table.hx-match .hx-more-col.hx-more-col4 .hx-col {
  min-width: 33.33%;
  width: 33.33%;
  max-width: 33.33%;
}

.hx-table.hx-match .hx-more-col.hx-more-col5 .hx-col {
  min-width: 25%;
  width: 25%;
  max-width: 25%;
}

.hx-table.hx-match .hx-more-col.hx-more-col6 .hx-col {
  min-width: 20%;
  width: 20%;
  max-width: 20%;
}

.hx-table.hx-match .hx-more-col.hx-more-col7 .hx-col {
  min-width: 16.66%;
  width: 16.66%;
  max-width: 16.66%;
}

.hx-table.hx-match .hx-more-col.hx-more-col8 .hx-col {
  min-width: 14.28%;
  width: 14.28%;
  max-width: 14.28%;
}

.hx-table.hx-match .hx-more-col.hx-more-col9 .hx-col {
  min-width: 12.5%;
  width: 12.5%;
  max-width: 12.5%;
}

.hx-table.hx-match.hx-more-bet .hx-cell.bl-1 {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
}

.hx-table.hx-match.hx-more-bet.live .hx-cell.bl-1 {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
}

.hx-table.hx-match.hx-more-bet .hx-cell.br-1 {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
}

.hx-table.hx-match.hx-more-bet.live .hx-cell.br-1 {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-right: 1px solid #caa9a0;
}

.hx-table.hx-match.hx-more-bet .bet-value.more-value {
  padding-left: 8px;
  text-align: center;
  margin: 0 auto;
  float: none;
}

.hx-table.hx-match .bet-value.long {
  padding-left: 8px;
  text-align: center;
}

.hx-more-row {
  padding: 0 2px 2px 0;
}

.hx-title {
  height: 40px;
  border-top: 1px solid rgba(0, 0, 0, .1);
  padding-top: 4px;
}

.hx-match .selected-odds {
  background-color: #db9c44;
}

.hx-match .bet-mmo.selected-odds {
  background-color: #e6ae60;
}

.hx-outright-date {
  font-weight: 700;
  color: #545454;
  padding: 0 4px;
}

.hx-w80 {
  width: 80px;
}

.hx-match.hx-orz .hx-row {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.hx-match.hx-orz .hx-row,
.hx-match.hx-orz .hx-col,
.hx-table.hx-match.hx-orz .hx,
.hx-table.hx-match.hx-orz.live .hx {
  height: 24px;
  line-height: 24px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.hx-table.hx-match.hx-orz .hx-cols,
.hx-table.hx-match.hx-orz.live .hx-cols {
  padding: 0;
}

.hx-table.hx-morebet-header {
  background: #d0d0d0;
  color: #000000;
}

.hx-table.hx-morebet-header.live {
  background: #F6E3DC;
}

.hx-table.hx-morebet-header .hx-cell {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 0;
}

.hx-table.hx-morebet-header .hx-cell:last-child,
.hx-table.hx-morebet-header.live .hx-cell:last-child {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-right: 0;
}

.hx-table.hx-morebet-header .hx-cell:first-child,
.hx-table.hx-morebet-header.live .hx-cell:first-child {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 0;
}

.hx-table.hx-morebet-header .hx-col {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 18px;
}

.hx-table.hx-morebet-header .bl-1 {
  border-left: 1px solid rgba(0, 0, 0, 0.05);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-collapse: collapse;
}

.hx-table.hx-morebet-header.live .bl-1 {
  border-left: 1px solid #eba28d;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-collapse: collapse;
}

.hx-table.hx-morebet-header .br-1 {
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-collapse: collapse;
}

.hx-table.hx-morebet-header.live .br-1 {
  border-right: 1px solid #eba28d;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-collapse: collapse;
}

.hx-table.hx-morebet-header .hx,
.hx-table.hx-morebet-header.live .hx {
  padding: 1px 1px 1px 8px;
  font-size: 11px;
}

.hx-table.hx-match.hx-morebet-body {
  /* background: #e5ecf5; */
  background: #fff;
}

.hx-table.hx-match.hx-morebet-body.live {
  background: #ffe8e0;
}

.hx-table.hx-morebet-body .hx-cell:last-child,
.hx-table.hx-morebet-body.live .hx-cell:last-child {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-right: 0;
}

.hx-table.hx-morebet-body .hx-cell:first-child,
.hx-table.hx-morebet-body.live .hx-cell:first-child {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 0;
}

.up::after {
  position: absolute;
  right: -4px;
  top: -1px;
  transform: rotate(45deg);
  content: "";
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #00C626;
}

.down::after {
  position: absolute;
  right: -4px;
  bottom: -1px;
  transform: rotate(135deg);
  content: "";
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #C11A00;
}

.nb {
  cursor: auto !important;
}

.main table {
  table-layout: fixed !important;
  -webkit-box-sizing: content-box !important;
  box-sizing: content-box !important;
  border-collapse: collapse !important;
}

.spinner-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 10000;
  font-size: 24px;
  padding: 0;
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.spinner-overlay .frame {
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.25);
  border-radius: 10px;
}

.spinner-overlay i {
  color: rgba(255, 255, 255, 0.8);
}

.clickable {
  cursor: pointer;
  color: #014273;
  font-weight: bold;
}

.clickable:hover {
  color: #6c757d;
}

input.is-invalid {
  border: 1px solid #880000;
}

input.is-valid {
  border: 1px solid #008888;
}

#sortable {
  overflow: hidden;
  position: relative;
}

.bg-empty .fas.fa-star {
  width: 12px;
  color: #008888;
  text-shadow: 0 0 3px white;
  cursor: pointer;
}

.bg-empty .fas.fa-star.selected {
  color: #f0d400;
  text-shadow: 0 0 3px white;
}

.table-betresult .badge {
  font-size: 9px;
  width: 20px;
  padding: 1px 0;
  margin: 0;
}

.selected-odds,
.cs-selected-odds {
  background-color: #db9c44;
}

.table-entry {
  margin: 0 auto;
}

.table-entry tr td {
  padding: 1px 6px;
  font-size: 12px;
}

.team-table {
  padding: 0px !important;
  vertical-align: top;
}

.team-tr1 {
  height: -webkit-max-content;
  height: -moz-max-content;
  height: max-content;
}

.team-tr2 {
  vertical-align: top;
}

.align-item-margin {
  padding-top: 3px;
}

.w-624 {
  width: 624px;
  min-width: 624px;
  max-width: 624px;
}

.w-549 {
  width: 549px;
  min-width: 549px;
  max-width: 549px;
}

.w-429 {
  width: 429px;
  min-width: 429px;
  max-width: 429px;
}

.w-457 {
  width: 457px;
  min-width: 457px;
  max-width: 457px;
}

.w-394 {
  width: 394px;
  min-width: 394px;
  max-width: 394px;
}

.w-392 {
  width: 392px;
  min-width: 392px;
  max-width: 392px;
}

.w-390 {
  width: 390px;
  min-width: 390px;
  max-width: 390px;
}

.w-372 {
  width: 372px;
  min-width: 372px;
  max-width: 372px;
}

.w-360 {
  width: 360px;
  min-width: 360px;
  max-width: 360px;
}

.w-344 {
  width: 344px;
  min-width: 344px;
  max-width: 344px;
}

.w-328 {
  width: 328px;
  min-width: 328px;
  max-width: 328px;
}

.w-328r {
  width: calc(100% - 328px);
}
.w-320 {
  width: 320px;
  min-width: 320px;
  max-width: 320px;
}

.w-300 {
  width: 300px;
  min-width: 300px;
  max-width: 300px;
}

.w-298 {
  width: 298px;
  min-width: 298px;
  max-width: 298px;
}

.w-264 {
  width: 264px;
  min-width: 264px;
  max-width: 264px;
}

.w-262 {
  width: 262px;
  min-width: 262px;
  max-width: 262px;
}

.w-262r {
  width: calc(100% - 262px);
}

.w-261 {
  width: 261px;
  min-width: 261px;
  max-width: 261px;
}

.w-240 {
  width: 240px;
  min-width: 240px;
  max-width: 240px;
}

.w-234 {
  width: 234px;
  min-width: 234px;
  max-width: 234px;
}

.w-232 {
  width: 232px;
  min-width: 232px;
  max-width: 232px;
}

.w-226 {
  width: 226px;
  min-width: 226px;
  max-width: 226px;
}


.w-206 {
  width: 206px;
  min-width: 206px;
  max-width: 206px;
}

.w-196 {
  width: 196px;
  min-width: 196px;
  max-width: 196px;
}

.w-195 {
  width: 195px;
  min-width: 195px;
  max-width: 195px;
}

.w-193 {
  width: 193px;
  min-width: 193px;
  max-width: 193px;
}

.w-186 {
  width: 186px;
  min-width: 186px;
  max-width: 186px;
}

.w-180 {
  width: 180px;
  min-width: 180px;
  max-width: 180px;
}

.w-172 {
  width: 172px;
  min-width: 172px;
  max-width: 172px;
}

.w-168 {
  width: 168px;
  min-width: 168px;
  max-width: 168px;
}

.w-164 {
  width: 164px;
  min-width: 164px;
  max-width: 164px;
}

.w-164r {
  width: calc(100% - 164px);
}

.w-150 {
  width: 150px;
  min-width: 150px;
  max-width: 150px;
}

.w-138 {
  width: 138px;
  min-width: 138px;
  max-width: 138px;
}

.w-133 {
  width: 133px;
  min-width: 133px;
  max-width: 133px;
}

.w-130 {
  width: 130;
  min-width: 130;
  max-width: 130;
}

.w-128 {
  width: 128px;
  min-width: 128px;
  max-width: 128px;
}

.w-117 {
  width: 117px;
  min-width: 117px;
  max-width: 117px;
}

.w-103 {
  width: 103px;
  min-width: 103px;
  max-width: 103px;
}

.w-98 {
  width: 98px;
  min-width: 98px;
  max-width: 98px;
}

.w-98r {
  width: calc(100% - 98px);
}

.w-88 {
  width: 88px;
  min-width: 88px;
  max-width: 88px;
}

.w-86 {
  width: 86px;
  min-width: 86px;
  max-width: 86px;
}

.w-83 {
  width: 83px;
  min-width: 83px;
  max-width: 83px;
}

.w-79 {
  width: 79px;
  min-width: 79px;
  max-width: 79px;
}

.w-77 {
  width: 77px;
  min-width: 77px;
  max-width: 77px;
}

.w-72 {
  width: 74px;
  min-width: 74px;
  max-width: 74px;
}

.w-70 {
  width: 70px;
  min-width: 70px;
  max-width: 70px;
}

.w-69 {
  width: 69px;
  min-width: 69px;
  max-width: 69px;
}

.w-66 {
  width: 66px;
  min-width: 66px;
  max-width: 66px;
}

.w-62 {
  width: 62px;
  min-width: 62px;
  max-width: 62px;
}

.w-61 {
  width: 61px;
  min-width: 61px;
  max-width: 61px;
}

.w-60 {
  width: 60px;
  min-width: 60px;
  max-width: 60px;
}

.w-55 {
  width: 55px;
  min-width: 55px;
  max-width: 55px;
}

.w-53 {
  width: 53px;
  min-width: 53px;
  max-width: 53px;
}

.w-51 {
  width: 51px;
  min-width: 51px;
  max-width: 51px;
}

.w-50px {
  width: 50px;
  min-width: 50px;
  max-width: 50px;
}

/* .w-50 {
  width: 50px;
  min-width: 50px;
  max-width: 50px;
} */
.w-48 {
  width: 48px !important;
  min-width: 48px !important;
  max-width: 48px !important;
}

.h-46 {
  height: 46px;
  min-height: 46px;
  max-height: 46px;
}

.h-18 {}

.w-49 {
  width: 49px;
  min-width: 49px;
  max-width: 49px;
}

.w-49r {
  width: calc(100% - 49px);
}

.w-44 {
  width: 44px !important;
  min-width: 44px !important;
  max-width: 44px !important;
}

.w-43 {
  width: 43px;
  min-width: 43px;
  max-width: 43px;
}

.w-43r {
  width: calc(100% - 43px);
}

.w-40 {
  width: 40px;
  min-width: 40px;
  max-width: 40px;
}

.w-40r {
  width: calc(100% - 40px);
}

.w-39 {
  width: 39px;
  min-width: 39px;
  max-width: 39px;
}

.w-15 {
  width: 15px;
  min-width: 15px;
  max-width: 15px;
}

.w-15r {
  width: calc(100% - 15px);
}

.h-66 {
  min-height: 66px !important;
}

.hx-table.hx-match .bl-1 {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
  border-collapse: collapse;
}

.hx-table.hx-match.live .bl-1 {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
  border-collapse: collapse;
}

.hx-table.hx-match .br-1 {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  border-collapse: collapse;
}

.hx-table.hx-match.live .br-1 {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  border-collapse: collapse;
}

.hx-table.hx-match .bb-1 {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-collapse: collapse;
}

.hx-table.hx-match.live .bb-1 {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-collapse: collapse;
}

.hx-table.hx-match .morebet-wrapper {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-collapse: collapse;
}

.hx-table.hx-match.live .morebet-wrapper {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #caa9a0;
  border-top: 1px solid #caa9a0;
  border-collapse: collapse;
}

.hx-ellipsis {
  white-space: nowrap !important;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

.hx-table.hx-match.hx-mmo .hx-cell {
  border-left: 1px solid #97c09b;
}

.hx-table.hx-match.hx-mmo.live .hx-cell {
  border-left: 1px solid #abbe94;
}

.hx-col.hx-cols.hx-mmo {
  background: #aee4b3;
}

.hx-table.live.hx-match .hx-col.hx-cols.hx-mmo {
  background: #cbe4ae;
}

.hx-table.hx-match.hx-mmo,
.hx-table.hx-match.hx-morebet-body.hx-mmo {
  background: #aee4b3;
}

.hx-table.alternate.hx-match.hx-mmo,
.hx-table.alternate.hx-match.hx-morebet-body.hx-mmo {
  background: #aee4b3;
}

.hx-table.live.hx-match.hx-mmo,
.hx-table.live.hx-match.hx-morebet-body.hx-mmo {
  background: #cbe4ae;
}

.hx-table.live.alternate.hx-match.hx-mmo,
.hx-table.live.alternate.hx-match.hx-morebet-body.hx-mmo {
  background: #cbe4ae;
}

/* NON MMO modifier */
.hx-table.hx-match.hx-non-mmo,
.hx-table.hx-match.hx-morebet-body.hx-non-mmo {
  /* background: #edf2fc; */
}

.hx-table.alternate.hx-match.hx-non-mmo,
.hx-table.alternate.hx-match.hx-morebet-body.hx-non-mmo {
  /* background: #edf2fc; */
}

.hx-table.live.hx-match.hx-non-mmo,
.hx-table.live.hx-match.hx-morebet-body.hx-non-mmo {
  /* background: #ffe8e0; */
}

.hx-table.live.alternate.hx-match.hx-non-mmo,
.hx-table.live.alternate.hx-match.hx-morebet-body.hx-non-mmo {
  /* background: #ffe8e0; */
}


.hx-table.hx-match.hx-mmo2 .hx-rows.hx-mmo,
.hx-table.hx-match.hx-mmo2 .live .hx-rows.hx-mmo {
  min-height: 44px;
}

.hx-table.hx-match.hx-mmo2 .hx-cols.hx-mmo,
.hx-table.hx-match.hx-mmo2 .live .hx-cols.hx-mmo {
  min-height: 44px;
}

.hx-card-row {
  padding: 0 15px;
}

.hx-card-col {
  padding: 0 4px 0 0;
}

.hx-card-col:last-child {
  padding: 0;
}