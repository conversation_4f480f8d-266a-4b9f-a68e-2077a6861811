import { EventBus } from "@/library/_event-bus.js";

export default {
  computed: {
    favorite() {
      return this.$store.getters.favorite;
    },
    menu0() {
      return this.$store.getters.menu0;
    }
  },
  methods: {
    // isFav(item) {
    //   if (this.favorite) {
    //     if (this.favorite.indexOf(item[2]) != -1) {
    //       return true;
    //     }
    //   }
    //   return false;
    // },
    // setFav(item, evt) {
    //   var favList = [];
    //   if (favList.indexOf(item[2]) == -1) {
    //     favList.push(item[2]);
    //   }
    //   if (this.isFav(item)) {
    //     this.$store.dispatch("layout/delFavorite", favList);
    //   } else {
    //     this.$store.dispatch("layout/setFavorite", favList);
    //   }
    // },
    xIsFav(matchId) {
      if (this.favorite) {
        if (this.favorite.indexOf(matchId) != -1) {
          return true;
        }
      }
      return false;
    },
    xSetFav(matchId, evt) {
      var favList = [];
      if (favList.indexOf(matchId) == -1) {
        favList.push(matchId);
      }
      if (this.xIsFav(matchId)) {
        this.$store.dispatch("layout/delFavorite", favList);
      } else {
        this.$store.dispatch("layout/setFavorite", favList);
      }
      if (this.menu0 == "favorite") {
        setTimeout(() => {
          EventBus.$emit("GET_MARKET");
        }, 500);
      }
    }
  }
};
