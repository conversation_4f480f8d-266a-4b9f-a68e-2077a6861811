module.exports = {
  root: true,

  env: {
    node: true
  },

  rules: {
    'no-console': 0,
    'no-debugger': 0,
    'vue/attribute-hyphenation': 2,
    'vue/html-end-tags': 2,
    'vue/html-indent': 2,
    'vue/html-self-closing': 1,
    'vue/mustache-interpolation-spacing': 2,
    'vue/name-property-casing': 2,
    'vue/no-multi-spaces': 2,
    'vue/require-default-prop': 0,
    'vue/require-prop-types': 2,
    'vue/v-bind-style': 2,
    'vue/v-on-style': 2,
    'vue/attributes-order': 1,
    'vue/html-quotes': 2,
    'vue/no-confusing-v-for-v-if': 2,
    'vue/order-in-components': 2,
    'vue/this-in-template': 2,
    'vue/no-v-html': 2,
    'vue/no-use-v-if-with-v-for': 2,
    'vue/require-valid-default-prop': 0,
    'vue/prop-name-casing': 2,
    'vue/script-indent': 1,
    'vue/max-attributes-per-line': 1
  },

  parserOptions: {
    parser: 'babel-eslint'
  },

  extends: ["plugin:vue/strongly-recommended"]
};
