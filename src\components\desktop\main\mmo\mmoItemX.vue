<template lang="pug">
.bet-value.bet-mmox(:class="[valueClass]", @click="placeBet($event.target)")
  .giving {{ homeaway }}
  .mmo(:class="{ red: mmo < 0 }") {{ mmo }}
  .percent(:class="{ red: percent < 0 }") &nbsp;({{ percent }})

  //- small {{ dv }}
</template>

<script>
import cal from "@/library/_calculation";
import config from "@/config";
import { EventBus } from "@/library/_event-bus.js";

export default {
  props: {
    odds: {
      type: Array
    },
    idx: {
      type: String
    },
    pos: {
      type: String
    },
    typ: {
      type: String
    },
    cls: {
      type: String
    },
    dataType: {
      type: String
    },
    homeaway: {
      type: String
    },
    giving: {
      type: Number
    }
  },
  data() {
    return {
      trigger: false,
      timer: null,
      old: null,
      dir: null,
      defStake: 0,
      newPage: true
    };
  },
  computed: {
    betType() {
      return this.$store.state.layout.menu3;
    },
    id() {
      return this.odds[3];
    },
    value() {
      return this.odds[this.pos];
    },
    mmo() {
      var result = this.odds[25];
      switch (this.homeaway) {
      case "H":
        if (this.giving == 1) {
          result = 0 - this.odds[25];
        }
        break;
      case "A":
        if (this.giving != 1) {
          result = 0 - this.odds[25];
        }
        break;
      case "O":
        break;
      case "U":
        break;
      }
      return result;
    },
    percent() {
      var result = this.odds[24];
      switch (this.homeaway) {
      case "H":
        if (this.giving != 1) {
          result = 0 - this.odds[24];
        }
        break;
      case "A":
        if (this.giving == 1) {
          result = 0 - this.odds[24];
        }
        break;
      case "O":
        break;
      case "U":
        result = 0 - this.odds[24];
        break;
      }
      return result;
    },
    dv() {
      // if (this.odds[parseInt(this.pos)]) {
      //   return parseFloat(this.odds[27]);
      // } else {
      //   return 0;
      // }
      // if (parseInt(this.percent)) {
      //   return parseInt(this.percent);
      // } else {
      //   return 0;
      // }
      if (this.percent != null) {
        return parseInt(this.percent);
      } else {
        return 0;
      }
    },
    valueClass() {
      var result = [];
      if (this.cls) {
        result.push(this.cls);
      }
      if (this.trigger == true) {
        result.push("highlighted");
      }
      if (this.dir != null) {
        result.push(this.dir);
      }
      if (!this.allowBet) {
        result.push("nb");
      }
      // if (this.value < 0) {
      //   result.push("text-red");
      // }
      return result;
    },
    allowBet() {
      return this.odds[15] == 1;
    },
    pause() {
      return this.odds[14] == 1 || this.odds[21] == 1;
    },
    mixParlay() {
      return this.odds[13] == 1;
    }
  },
  watch: {
    dv(newVal, oldVal) {
      var nv = newVal;
      var ov = oldVal;
      if (nv === "") {
        this.trigger = false;
        this.dir = null;
        return;
      } else {
        if (ov != nv) {
          this.trigger = true;
          // if (nv < 0) {
          //   if (ov > nv) {
          //     this.dir = "down";
          //   } else {
          //     this.dir = "up";
          //   }
          // } else {
          //   if (ov > nv) {
          //     this.dir = "down";
          //   } else {
          //     this.dir = "up";
          //   }
          // }
          if (ov > nv) {
            this.dir = "down";
          } else {
            this.dir = "up";
          }
          setTimeout(() => {
            this.trigger = false;
            this.dir = null;
          }, 10000);
        }
      }
      this.old = oldVal;
    },
    id(newVal, oldVal) {
      this.reset();
    },
    typ(newVal, oldVal) {
      this.reset();
    },
    betType(newVal, oldVal) {
      this.reset();
    }
  },
  mounted() {},
  destroyed() {},
  methods: {
    reset() {
      this.trigger = false;
      this.old = null;
      this.dir = null;
    },
    placeBet(e) {
      if (this.value) {
        $(".bet-value").removeClass("selected-odds");
        if (this.allowBet) {
          if (this.$store.state.layout.betting.defaultStake != "3") {
            this.defStake = this.$store.state.layout.betting.defaultStakeAmount;
          }
          if (this.betType == "parlay") {
            if (this.mixParlay) {
              // if (Object.keys(this.$store.state.betparlaymmo.data).length + 1 <= config.mmoParlayMaxTicket) {
              if (EventBus.betParlayMMO) {
                // EventBus.betParlay(this.odds, this.typ, this.idx, this.value, this.betType, this.defStake);
                EventBus.betParlayMMO(this.odds, this.typ, this.idx, this.value, this.betType, this.defStake, true, e, this.pos, this.homeaway, this.giving);
              }
              // } else {
              //   this.$helpers.showDialog(this.$t("ui.action"), this.$t("error.maxParlayTicket"), "info");
              // }
            }
          }
        }
      }
    }
  }
};
</script>
