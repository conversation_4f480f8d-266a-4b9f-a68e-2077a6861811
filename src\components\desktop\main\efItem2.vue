<template lang="pug">
.esports-gamelist(v-if="source.items != null && source.items[0] != null && source.items.length == 6")
  //- small {{ source }}
  .esports-row.game-bottom-line(:class="source.items[0].marketId == 3 ? 'game-top-line' : ''")
    .esports-cell.esports-cell-sm.flex-grow-1.justify-content-start.w-130.pl-2(v-if="source.items[0].marketId == 3")
      .live-now
        img(src="/v1/images/esports/live-icon.svg")
        span.text-uppercase live
        i.fas.fa-circle
    .esports-cell.esports-cell-sm(v-else)
      efTime(:source="source.items[0]" :horiz="true")
  .game-group
    .game-table
      .game-table-left
        .game-row
          .game-cell.w-40
          .game-cell.flex-fill
          .game-cell.game-state.w-60 {{ $t("m.GH_1X20") }}
          .game-cell.game-state.w-60 {{ $t("m.GH_1X2HDP2") }}
          .game-cell.game-state.w-60 {{ $t("m.GH_1X2HDP4") }}
        efItemChild(:source="source.items[0]" :player="1")
        .game-line
        efItemChild(:source="source.items[1]" :player="2")
        .game-line
        efItemChild(:source="source.items[2]" :player="3")

      .game-table-right
        .game-row
          .game-cell.w-40
          .game-cell.flex-fill
          .game-cell.game-state.w-60 {{ $t("m.GH_1X20") }}
          .game-cell.game-state.w-60 {{ $t("m.GH_1X2HDP2") }}
          .game-cell.game-state.w-60 {{ $t("m.GH_1X2HDP4") }}
        efItemChild(:source="source.items[3]" :player="4")
        .game-line
        efItemChild(:source="source.items[4]" :player="5")
        .game-line
        efItemChild(:source="source.items[5]" :player="6")



</template>

<script>
export default {
  components: {
    efTime: () => import("@/components/desktop/main/efTime"),
    efItemChild: () => import("@/components/desktop/main/efItemChild"),
  },
  props: {
    source: {
      type: Object
    },
    index: {
      type: Number
    }
  },
  methods: {
  }
};
</script>
