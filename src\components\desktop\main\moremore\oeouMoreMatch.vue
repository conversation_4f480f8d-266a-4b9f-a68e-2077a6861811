<template lang="pug">
.col-6.hx-more-row(v-if="details['ou'] != null || details['oe'] != null")
  .card
    .card-header(
      :id="'heading-oeou-more-' + uid + childId"
      data-toggle="collapse"
      :data-target="'#collapse-oeou-more-' + uid + childId"
      :aria-controls="'collapse-oeou-more-' + uid + childId"
      aria-expanded=true
      :class="layoutIndex == 3 ? 'live': 'non-live'"
    )
      i.fad.fa-chevron-circle-down
      span.header-bettype {{ leagueName.trim() }}
    .collapse.show(
      :aria-labelledby="'heading-oeou-more-' + uid + childId"
      :id="'collapse-oeou-more-' + uid + childId"
    )
      .card-body.p-0(:id="'accordian-oeou-more-' + uid + childId")
        .hx-table.hx-morebet-header(:class="marketType == 3 ? 'live' : 'non-live'").bl-1.br-1.bb-1.h-18
          .hx-cell.flex-fill
            .hx-row
              .hx-col
                .hx.text-left {{ $t("ui.full_time") }}
          .hx-cell.w-164.bl-1
            .hx-row
              .hx-col.w-98
                .hx.w-100.text-center {{ $t("ui.ou") }}
              .hx-col.w-66.bl-1
                .hx.w-100.text-center {{ $t("ui.oe") }}
        .hx-table.hx-match.hx-morebet-body(:class="{ 'live': marketType == 3 }").bl-1.br-1.bb-1
          .hx-cell.w-164r.flex-fill
            .hx-row.h-100.hx-rows
              .hx-col.d-block.h-100.w-100.text-ellipsis
                .hx.hx-ellipsis(:class="details['team'] == 1 ? 'team-red' : 'team-black'") {{ homeTeam }}
                .hx.hx-ellipsis(:class="details['team'] == 0 ? 'team-red' : 'team-black'") {{ awayTeam }}
          .hx-cell.w-164
            .hx-row.hx-rows(v-for="(dn, i) in details['tn']")
              ouItem(:details="details" :oddsType="oddsType" :i="i" betType="ou")
              oeItem1(:details="details" :oddsType="oddsType" :i="i" betType="oe")
        template(v-if="details['ouh'] != null || details['oeh'] != null")
          .hx-table.hx-morebet-header(:class="marketType == 3 ? 'live' : 'non-live'").bl-1.br-1.bb-1.h-18
            .hx-cell.flex-fill
              .hx-row
                .hx-col
                  .hx.text-left {{ $t("ui.half_time") }}
            .hx-cell.w-164.bl-1
              .hx-row
                .hx-col.w-98
                  .hx.w-100.text-center {{ $t("ui.ou") }}
                .hx-col.w-66.bl-1
                  .hx.w-100.text-center {{ $t("ui.oe") }}
          .hx-table.hx-match.hx-morebet-body(:class="{ 'live': marketType == 3 }").bl-1.br-1.bb-1
            .hx-cell.w-164r.flex-fill
              .hx-row.h-100.hx-rows
                .hx-col.d-block.h-100.w-100.text-ellipsis
                  .hx.hx-ellipsis(:class="details['team'] == 1 ? 'team-red' : 'team-black'") {{ homeTeam }}
                  .hx.hx-ellipsis(:class="details['team'] == 0 ? 'team-red' : 'team-black'") {{ awayTeam }}
            .hx-cell.w-164
              .hx-row.hx-rows(v-for="(dn, i) in details['tn']")
                ouItem(:details="details" :oddsType="oddsType" :i="i" betType="ouh")
                oeItem1(:details="details" :oddsType="oddsType" :i="i" betType="oeh")
</template>

<script>
import ouItem from "@/components/desktop/main/xtable/xitem/ouItem";
import oeItem1 from "@/components/desktop/main/xtable/xitem/oeItem1";

export default {
  components: {
    ouItem,
    oeItem1
  },
  props: {
    customTeam: {
      type: Boolean
    },
    childId: {
      type: Number
    },
    uid: {
      type: String
    },
    details: {
      type: Object
    },
    matchId: {
      type: Number
    },
    leagueId: {
      type: Number
    },
    marketType: {
      type: Number
    },
    sportsType: {
      type: Number
    },
    betType: {
      type: String
    },
    layoutIndex: {
      type: Number
    }
  },
  computed: {
    homeTeam() {
      if (this.customTeam == true) {
        var result = this.source.homeTeam.split(" - ");
        if (result.length >= 2) {
          if (result.length - 2 != 0) {
            if (result.length <= 5) {
              return result[result.length - 2] + " - " + result[result.length - 1];
            }
            if (result.length <= 6) {
              return result[result.length - 3] + " - " + result[result.length - 2] + " - " + result[result.length - 1];
            }
          }
          return result[result.length - 1];
        }
        return this.source.homeTeam;
      } else {
        return this.source.homeTeam;
      }
    },
    awayTeam() {
      if (this.customTeam == true) {
        var result = this.source.awayTeam.split(" - ");
        if (result.length >= 2) {
          if (result.length - 2 != 0) {
            if (result.length <= 5) {
              return result[result.length - 2] + " - " + result[result.length - 1];
            }
            if (result.length <= 6) {
              return result[result.length - 3] + " - " + result[result.length - 2] + " - " + result[result.length - 1];
            }
          }
          return result[result.length - 1];
        }
        return this.source.awayTeam;
      } else {
        return this.source.awayTeam;
      }
    },
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
    leagueName() {
      var result = this.details["league"].split(" - ");
      if (result.length >= 2) {
        if (result.length - 2 != 0) {
          if (result.length <= 5) {
            return result[result.length - 2] + " - " + result[result.length - 1];
          }
          if (result.length <= 6) {
            return result[result.length - 3] + " - " + result[result.length - 2] + " - " + result[result.length - 1];
          }
        }
        return result[result.length - 1];
      }
      return this.details["league"];
    },
    source() {
      return {
        marketId: this.details.match[4],
        matchTime: this.details.match[8],
        runningScore: this.details.match[11],
        runningTime: this.details.match[12],
        homeTeam: this.details.match[5],
        awayTeam: this.details.match[6]
      };
    }
  }
};
</script>
