<template lang="pug">
  .hx-col.hx-cols(:class="cls ? cls : 'w-98'").hx-mmo
    template(v-if="details[betType] != null && details[betType][i] != null && details[betType][i][11] != 0 && details[betType][i][12] != 0 && details[betType][i][11] != '' && details[betType][i][12] != ''")
      .hx
        .hxs.w-49
          .ball-value.ball-mmo.d-flex
            .mmo(:class="{ red : details[betType][i][25] < 0 }") {{ details[betType][i][25] }}
            .percent(:class="{ red : details[betType][i][24] < 0 }") ({{ details[betType][i][24] }})
        .hxs.w-49r
          mmoItem(:odds="details[betType][i]" idx=12 pos=23 :typ="oddsType" dataType="1")
      .hx
        .hxs.w-49
          .ball-value u
        .hxs.w-49r
          mmoItem(:odds="details[betType][i]" idx=11 pos=23 :typ="oddsType" dataType="1")
    template(v-else)
      .hx
        .hxs.w-49 &nbsp;
        .hxs.w-49r &nbsp;
      .hx
        .hxs.w-49 &nbsp;
        .hxs.w-49r &nbsp;
</template>

<script>

export default {
  components: {
    mmoItem: () => import("@/components/desktop/main/mmo/mmoItem")
  },
  props: {
    cls: {
      type: String
    },
    details: {
      type: Object
    },
    oddsType: {
      type: String
    },
    item: {
      type: String
    },
    i: {
      type: [String, Number]
    },
    betType: {
      type: String
    }
  },
  // updated: function() {
  //   this.$nextTick(function() {
  //     console.log("cs", new Date(), this.oddsType, this.item);
  //   });
  // }
};
</script>
