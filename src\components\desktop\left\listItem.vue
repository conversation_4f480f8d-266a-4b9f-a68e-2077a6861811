<template lang="pug">
.sport-list(v-if="item && item.tn > 0")
  .heading-collapse(
    ref="heading"
    :id="'heading-' + uid"
    data-toggle="collapse"
    :data-target="'#collapse-' + uid"
    aria-expanded="false"
    :aria-controls="'collapse-' + uid"
    @click="setMenu2(item.st)")
    .group.changed(:title="name" :class="{ selected: menu2 == item.st }")
      .d-flex.justify-content-center.align-items-center
        .sport-icon
          img(:src="'/v1/images/icon-sport-svg/' + getImage(item.st)")
        .subtitle.flex-fill.active-none
          span.menu-item(:class="![42].includes(item.st) ? 'text-capitalize' : 'text-uppercase'") {{ name.toLowerCase() }}
        template(v-if="(isLive && menu1 != 'early') || vg1.includes(item.st)")
          .text-hot.active-none(v-if="vg1.includes(item.st)").d-flex.justify-content-center.align-items-center
            img(src="/v1/images/fire.png")
          .text-live.active-none(v-else) LIVE
        .game-number.active-none {{ item.tn }}
  .collapse.collapse-box(
    v-if="item.tn && item.mt != 4 && item.st != 40"
    :data-type="item.st"
    :id="'collapse-' + uid"
    :aria-labelledby="'heading-' + uid"
    data-parent="#accordion-allsports"
    )
    .group.sub
      ul.subgroup
        li.small(v-if="item.hdpou" @click="setMenu3('hdpou')")
          a(href="javascript:void(0);")
            .d-flex(:class="{ active : menu3 == 'hdpou' }")
              .subtitle.flex-fill
                span {{ $t("m.BT_HDPOU") }}
              .game-number {{ item.hdpou }}
        template(v-if="!vg1.includes(item.st)")
          li.small(v-if="item.oxt" @click="setMenu3('oxt')")
            a(href="javascript:void(0);")
              .d-flex(:class="{ active : menu3 == 'oxt' }")
                .subtitle.flex-fill
                  span {{ $t("m.BT_1X2") }}
                .game-number {{ item.oxt }}
          li.small(v-if="item.cs" @click="setMenu3('cs')")
            a(href="javascript:void(0);")
              .d-flex(:class="{ active : menu3 == 'cs' }")
                .subtitle.flex-fill
                  span {{ $t("m.BT_CS") }}
                .game-number {{ item.cs }}
          li.small(v-if="item.oe && item.st != 20" @click="setMenu3('oe')")
            a(href="javascript:void(0);")
              .d-flex(:class="{ active : menu3 == 'oe' }")
                .subtitle.flex-fill
                  span {{ $t("m.BT_OE") }}
                .game-number {{ item.oe }}
          li.small(v-if="item.dc" @click="setMenu3('dc')")
            a(href="javascript:void(0);")
              .d-flex(:class="{ active : menu3 == 'dc' }")
                .subtitle.flex-fill
                  span {{ $t("m.BT_DC") }}
                .game-number {{ item.dc }}
          li.small(v-if="item.tg" @click="setMenu3('tg')")
            a(href="javascript:void(0);")
              .d-flex(:class="{ active : menu3 == 'tg' }")
                .subtitle.flex-fill
                  span {{ $t("m.BT_TG") }}
                .game-number {{ item.tg }}
          li.small(v-if="item.htft" @click="setMenu3('htft')")
            a(href="javascript:void(0);")
              .d-flex(:class="{ active : menu3 == 'htft' }")
                .subtitle.flex-fill
                  span {{ $t("m.BT_HTFT") }}
                .game-number {{ item.htft }}
          li.small(v-if="item.fglg" @click="setMenu3('fglg')")
            a(href="javascript:void(0);")
              .d-flex(:class="{ active : menu3 == 'fglg' }")
                .subtitle.flex-fill
                  span {{ $t("m.BT_FGLG") }}
                .game-number {{ item.fglg }}
        li.small(v-if="item.pl" @click="setMenu3('parlay')")
          a(href="javascript:void(0);")
            .d-flex(:class="{ active : menu3 == 'parlay' }")
              .subtitle.flex-fill
                span {{ $t("m.BT_PARLAY") }}
              .game-number {{ item.pl }}
        li.small(v-if="item.orz" @click="setMenu3('orz')")
          a(href="javascript:void(0);")
            .d-flex(:class="{ active : menu3 == 'orz' }")
              .subtitle.flex-fill
                span {{ $t("m.BT_OR") }}
              .game-number {{ item.orz }}
  </template>

<script>
import config from "@/config";

export default {
  props: {
    uid: {
      type: String
    },
    item: {
      type: Object,
      default: () => {}
    },
    name: {
      type: String,
      default: ""
    },
    index: {
      type: Number,
      default: 0
    },
    isLive: {
      type: Boolean,
      default: false
    },
    menu0: {
      type: String
    },
    menu1: {
      type: String
    },
    menu2: {
      type: Number
    },
    menu3: {
      type: String
    },
    sportsType: {
      type: Number
    }
  },
  data() {
    return {};
  },
  computed: {
    vg1() {
      return config.vg1;
    },
    gameNumber() {
      return this.item.tn;
    },
    lang() {
      return this.$store.getters.language;
    }
  },
  mounted() {
    $(".sport-list").on("mouseover", function() {
      var $menuItem = $(this);
      var $submenuWrapper = $("> .new-wrap", $menuItem);
      var menuItemPos = $menuItem.position();
      $submenuWrapper.css({
        top: menuItemPos.top,
        left: 50
      });
    });
  },
  methods: {
    setFilterMode(e) {
      this.$store.dispatch("cache/setFilterMode", e);
      // EventBus.$emit("INVALIDATE");
    },
    getImage(e) {
      return config.getSportsImage(e);
    },
    setMenu2(e) {
      // console.log(this.item.tn);
      // if (this.item.st == 1 && this.item.tn > 200) {
      //   this.setFilterMode(1);
      // } else {
      //   this.setFilterMode(0);
      // }
      this.$emit("setMenu2", e);
    },
    setMenu3(e) {
      // console.log(this.item[e]);
      // if (this.item.st == 1 && this.item[e] > 200) {
      //   this.setFilterMode(1);
      // } else {
      //   this.setFilterMode(0);
      // }
      this.$emit("setMenu3", e, this.sportsType);
    },
    getLive(e) {
      if (!this.menu) {
        return 0;
      }

      if (!this.menu) {
        return 0;
      }

      if (!this.menu["3"]) {
        return 0;
      }

      if (!this.menu["3"][e]) {
        return 0;
      }

      return this.menu["3"][e]["tn"];
    }
  }
};
</script>
