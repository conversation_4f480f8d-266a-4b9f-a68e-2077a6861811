<template lang="pug">
.tournament-betslip-wrapper
  //- small {{ roomData }}
  template(v-if="[2,3].includes(roomData.room_status)")
    .tournament-tab-content.mb-3
      .tournament-betslip-title
        i.fad.fa-list.mr-2
        span {{ $t('ui.result') }}
      .tournament-mybet-inner
        template(v-if="betResultFinal.length > 0 && (betResultFinal[0].position != null && betResultFinal[0].position != 0)")
          .tournament-mybet-single.tournament-mybet-compact(v-for="bl in betResultFinal")
            template(v-if="bl.position != null && bl.position != 0")
              .tournament-mybet-text {{ $t('ui.winner_rank') }} {{ '#' + bl.position }}
              .tournament-mybet-odds.mb-4
                .tournament-mybet-odds-left
                  .tournament-mybet-text.mb-0 {{ bl.currency }}&nbsp;
                  .tournament-mybet-text.mb-0 {{ bl.payout }}
                .tournament-mybet-odds-right
                  .tournament-mybet-text.mb-0 {{ bl.total_points }}
                .tournament-mybet-small {{ $t('ui.points') }}
              .tournament-mybet-details
                .tournament-mybet-tiny {{ $t('ui.room_id')}} : {{ bl.room_id }}
                .tournament-mybet-status(v-if="bl.result_on") {{ $dayjs(bl.result_on).format("MM/DD/YYYY HH:mm A") }}
        template(v-else)
          .tournament-mybet-single.d-flex.align-items-center.justify-content-center
            template(v-if="loading.getBetResultFinal")
              i.fad.fa-spinner-third.fa-spin
            template(v-else)
              .empty.text-center {{ $t("message.not_winning_prize_in_this_tournament") }}

  template(v-if="[0,1,4].includes(roomData.room_status)")
    ul.nav.nav-pills
      li.nav-item()
        .nav-link(@click="tabMode = 0" :class="tabMode == 0 ? 'active' : ''")
          i.fas.fa-cart-plus.mr-2
          | {{ $t('ui.bet_slip') }}
      li.nav-item()
        .nav-link(@click="tabMode = 1" :class="tabMode == 1 ? 'active' : ''")
          i.fas.fa-receipt.mr-2
          | {{ $t('ui.my_bet') }}

    template(v-if="tabMode == 0")
      .tournament-tab-content(v-if="[0,1].includes(roomData.room_status)")
        //- .tournament-betslip-title
        //-   i.fad.fa-cart-plus.mr-2
        //-   span {{ $t('ui.bet_slip') }}
        .tournament-betslip
          .tournament-betslip-room(style="padding: 4px 12px;")
            span {{ $t('ui.room') }}:&nbsp;
            b {{ roomData.room_id }}
          template(v-if="data.length <= 0")
            .tournament-betslip-single
              .tournament-betslip-body
                .tournament-betslip-matches.d-flex.flex-column.mt-2
                  span.m-2.text-center(style="color: #ffffffcc;") {{ $t('message.bet_empty') }}
          template(v-else)
            template(v-for="m in data")
              .tournament-betslip-single(
                v-if="bs.hasOwnProperty(m)"
                :key="'tournament-betslip-single-' + bs[m].match.match_id"
                :id="'tournament-betslip-single-' + bs[m].match.match_id"
                )
                .tournament-betslip-header
                  .tournament-betslip-header-title {{ sportsType[bs[m].sports_type] }} - {{ $t('m.BS_' + bs[m].bet_type) }}
                .tournament-betslip-body
                  .tournament-betslip-matches.d-flex.flex-column.mt-2
                    .tn-team {{ getName('home_name', bs[m].match) }}
                    .tn-vs vs
                    .tn-team {{ getName('away_name', bs[m].match) }}
                  .tournament-betslip-league
                    span {{ getLeagueName(bs[m].match) }}

                  .tournament-odds2.align-items-end
                    template(v-if="['HDP','HDPH'].includes(bs[m].bet_type)")
                      b(v-if="bs[m].home_away == 1") {{ getName('home_name', bs[m].match) }}
                      b(v-else) {{ getName('away_name', bs[m].match) }}
                    template(v-else)
                      b(v-if="bs[m].home_away == 1") {{ $t('ui.over') }}
                      b(v-else) {{ $t('ui.under') }}
                  .tournament-odds2.align-items-start.mb-2
                    .tournament-odds-text {{ bs[m].ball_value }}
                    .tournament-color-red.mx-1 @
                    .tournament-odds-text {{ bs[m].value }}

          .tournament-betslip-footer
            .tournament-better-odds
              .alert.alert-tournament
                | {{ $t('message.must_accept_better_odds') }}

            template(v-if="roomData.room_join == 1")
              .tournament-stake-field(v-if="!isConfirm")
                .input-group
                  .input-group-prepend
                    span.input-group-text
                      img(src="img/tn/icon-point.svg" style="width: 24px; height: 24px;")
                  StakeInput2(
                    v-model="stake",
                    @handleStake="handleStake()",
                    ref="stake",
                    :loadbet="loading.betPlus"
                    )
              .tournament-betslip-btn.mb-2(v-if="!isConfirm")
                button.tournament-btn-refresh(@click="fastInput(100)")
                  span 100
                button.tournament-btn-refresh(@click="fastInput(300)")
                  span 300
                button.tournament-btn-refresh(@click="fastInput(500)")
                  span 500
                //- button.tournament-btn-bet(style="width: 200px;" @click="fastInput()")
                //-   span ALL IN
              .tournament-table-entry
                table.table-tournament(width="100%")
                  tbody
                    tr
                      td(width="50%") {{ $t("ui.payout") }}
                      td.text-right(width="50%") {{ $numeral(payout).format("0,0.00") }}
                    tr
                      td  {{ $t("ui.min") }}
                      td.text-right {{ $numeral(minBet).format("0,0.00") }}
                    tr
                      td  {{ $t("ui.max") }}
                      td.text-right {{ $numeral(maxBet).format("0,0.00") }}

            template(v-if="roomData.room_join == 1")
              .tournament-better-odds(v-if="isConfirm")
                i.fad.fa-check-circle.mr-1.text-success
                span {{ $t('message.confirm_bet') }}
              .tournament-betslip-btn(v-if="isConfirm")
                button.tournament-btn-cancel(@click="isConfirm = false;")
                  span {{ $t('ui.no') }}
                button.tournament-btn-bet(@click="placeBet" :disabled="(total <= 0) || (stake <= 0) || loading.betPlus")
                  template(v-if="loading.betPlus")
                    i.fad.fa-spinner-third.fa-spin
                  template(v-else)
                    span {{ $t('ui.yes') }}
              .tournament-betslip-btn.mt-3(v-else)
                button.tournament-btn-cancel(@click="cancel")
                  span {{ $t('ui.cancel') }}
                button.tournament-btn-bet(@click="confirmBet" :disabled="(total <= 0) || (stake <= 0) || loading.betPlus")
                    span {{ $t('ui.place_bet') }}
            template(v-else)
              .tournament-betslip-btn
                button.tournament-btn-bet(v-if="roomData.room_count < roomData.room_limit" @click="joinRoom(roomData)" :disabled="roomData.room_status != 0")
                  span {{ $t('ui.join_room') }}
      .tournament-tab-content(v-else)
        .tournament-betslip
          .tournament-mybet-inner
            .tournament-mybet-single.d-flex.align-items-center.justify-content-center
              .empty.text-center {{ $t("message.no_more_bets") }}

    template(v-if="tabMode == 1")
      .tournament-tab-content
        //- .tournament-betslip-title
        //-   i.fad.fa-receipt.mr-2
        //-   span {{ $t('ui.my_bet') }}
        .tournament-mybet-inner
          template(v-if="betList.length > 0")
            .tournament-mybet-single(v-for="(bl, bi) in betList")
              .tournament-mybet-title
                .badge {{ bi + 1 }}.
                | {{ sportsType[bl.sports_type] }} -  {{ $t('m.BT_' + bl.bet_type) }}
              template(v-if="['HDP','HDPH'].includes(bl.bet_type)")
                .tournament-mybet-small(v-if="bl.home_away === 1") {{ getTeamName("home", bl) }}
                .tournament-mybet-small(v-else) {{ bl.away_team_name }}
              template(v-else)
                .tournament-mybet-small(v-if="bl.home_away === 1") {{ $t('ui.over') }}
                .tournament-mybet-small(v-else) {{ $t('ui.under') }}
              .tournament-mybet-odds
                .tournament-mybet-odds-left
                  .tournament-mybet-text.mb-0(v-if="['HDP','HDPH'].includes(bl.bet_type)") {{ bl.ball }}
                  .tournament-mybet-text.mb-0(v-else) {{ Math.abs(bl.ball) }}
                  .tournament-color-red.mx-1 @
                  .tournament-mybet-text.mb-0 {{ bl.odds_display }}
                .tournament-mybet-odds-right
                  .tournament-mybet-text.mb-0 {{ bl.bet_member }}
              .tournament-mybet-odds
                .tournament-mybet-odds-left
                  .tournament-mybet-matches
                    .tn-team {{ getTeamName("home", bl) }}
                    .tn-team {{ getTeamName("away", bl) }}
                    .tn-team(style="color: #92d2ff;") {{ getName("name", bl) }}
                .tournament-mybet-odds-right
                  .tournament-mybet-text.mb-0.text-glass
                    span {{ $t("ui.room") }}:&nbsp;
                    span {{ bl.room_id }}
              .tournament-mybet-details
                .tournament-mybet-tiny ID: {{ bl.bet_id }}
                template(v-if="bl.wl_status")
                  .tournament-mybet-status(v-if="bl.wl != null") {{ $t('ui.' + bl.wl_status.toLowerCase().replace(" ", "_")) }} ({{ $numeral(bl.wl).format("0,0.00") }})
                template(v-else)
                  .tournament-mybet-status(v-if="bl.bet_status") {{ $t('ui.' + bl.bet_status.toLowerCase()) }}
          template(v-else)
            .tournament-mybet-single.d-flex.align-items-center.justify-content-center
              template(v-if="loading.betPlus || loading.getMemberBetList")
                i.fad.fa-spinner-third.fa-spin
              template(v-else)
                .empty.text-center(v-if="roomData.room_join == 1") {{ $t("message.no_bets") }}
                .empty.text-center(v-else) {{ $t("message.you_did_not_join_this_room") }}
          .tournament-betslip-footer(v-if="!loading.getMemberBetList")
            .tournament-betslip-btn
              button.tournament-btn-refresh(@click="refreshList" :disabled="loading.getMemberBetList")
                template(v-if="loading.getMemberBetList")
                  i.fad.fa-spinner-third.fa-spin
                template(v-else)
                  span {{ $t('ui.refresh') }}

</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import service from "@/tournament2/library/_xhr.js";
import xhrBet from "@/library/_xhr-bet.js";
import calc from "@/library/_calculation.js";
import StakeInput2 from "@/tournament2/library/stakeInput2";

const _INTERVAL = 60;

export default {
  components: {
    StakeInput2,
  },
  props: {
    roomId: {
      type: Number,
    },
  },
  data() {
    return {
      isConfirm: false,
      roomData: {},
      debList: null,
      debResult: null,
      debCheck: null,
      loading: {
        betPlus: false,
        getMemberBetList: false,
        getBetResultFinal: false,
      },
      betList: [],
      betResultFinal: [],
      // betMode: false,
      betSlip: {},
      acceptBetterOdds: true,
      // resultMode: false,
      // listMode: false,
      tabMode: 0,
      data: [],
      defaultMinBet: 100,
      defaultMaxBet: 500,
      minBet: this.defaultMinBet,
      maxBet: this.defaultMaxBet,
      stake: this.defaultMinBet,
    };
  },
  computed: {
    payout() {
      if (this.data.length == 1) {
        // console.log(this.data);
        var v = this.bs[this.data[0]].value;
        var a = this.stake * v;
        var b = this.maxBet * v;
        if (a > b) {
          a = b;
        }
        return a.toFixed(3);
      } else {
        return 0;
      }
    },
    bs() {
      var e = this.roomData.room_id;
      if (e == undefined) {
        return {};
      }

      if (this.betSlip.hasOwnProperty(e)) {
        return this.betSlip[e];
      } else {
        return {};
      }
    },
    total() {
      var e = this.roomData.room_id;
      if (e == undefined) {
        return -1;
      }
      // return total count of betSlip keys
      if (this.betSlip[e] == null) {
        return -1;
      } else {
        return Object.keys(this.betSlip[e]).length;
      }
    },
    commType() {
      return this.$store.getters.commType;
    },
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    },
    language() {
      return this.$store.getters.language;
    },
    currency_code() {
      var res = this.$store.getters.currencyCode;
      if (res != null) {
        if (res == "UST") {
          return res.replace("UST", "USDT");
        } else {
          return res;
        }
      } else {
        return "";
      }
    },
    sportsType() {
      return this.$store.state.layout.sports;
    },
  },
  destroyed() {
    EventBus.$off("ROOM_DATA", this.setRoomData);
    // EventBus.$off("roomBetSlipRefresh2", this.getData);
    EventBus.$emit("selectOdds2", this.betSlip);
  },
  mounted() {
    EventBus.tournamentAdd2 = this.addSlip;
    this.debList = this.debounce(this.getMemberBetList, 250);
    this.debResult = this.debounce(this.getBetResultFinal, 250);
    this.debCheck = this.debounce(this.getMultiBetCheck, 250);
    this.getData();
    EventBus.$on("ROOM_DATA", this.setRoomData);
    // EventBus.$on("roomBetSlipRefresh2", this.getData);
    EventBus.$emit("selectOdds2", this.betSlip);

    this.handleTimer();
    this.handleStake(true);
  },
  methods: {
    fastInput(e, skip) {
      if (this.roomData.total_points == null) {
        this.minBet = 0;
        this.maxBet = 0;
        this.stake = 0;
      } else {
        this.minBet = this.defaultMinBet;
        var max = Number(this.roomData.total_points);
        if (max > this.defaultMaxBet) {
          max = this.defaultMaxBet;
        }
        this.maxBet = max;
        if (this.minBet > this.maxBet) {
          this.minBet = this.maxBet;
        }

        var val = e;
        if (val == undefined) {
          val = this.maxBet;
        } else {
          if (val < this.minBet) {
            val = this.minBet;
          }
          if (val > this.maxBet) {
            val = this.maxBet;
          }
        }

        this.stake = val;

        if (this.stake > this.maxBet) {
          this.stake = this.maxBet;
          if (skip == undefined) {
            this.$swal("Warning", this.$t("error.changeStakeMax"), "warning");
          }
        } else {
          if (this.stake < this.minBet) {
            this.stake = this.minBet;
            if (skip == undefined) {
              this.$swal("Warning", this.$t("error.changeStakeMin"), "warning");
            }
          }
        }
      }
    },
    handleStake(skip) {
      if (this.roomData.total_points == null) {
        this.minBet = 0;
        this.maxBet = 0;
        this.stake = 0;
      } else {
        this.minBet = this.defaultMinBet;
        var max = Number(this.roomData.total_points);
        if (max > this.defaultMaxBet) {
          max = this.defaultMaxBet;
        }
        this.maxBet = max;
        if (this.minBet > this.maxBet) {
          this.minBet = this.maxBet;
        }
        if (this.stake > this.maxBet) {
          this.stake = this.maxBet;
          if (skip == undefined) {
            this.$swal("Warning", this.$t("error.changeStakeMax"), "warning");
          }
        } else {
          if (this.stake < this.minBet) {
            this.stake = this.minBet;
            if (skip == undefined) {
              this.$swal("Warning", this.$t("error.changeStakeMin"), "warning");
            }
          }
        }
      }
    },
    handleTimer() {
      this.handler = setTimeout(() => {
        this.refreshCheck();
        this.handleTimer();
      }, 5000);
    },
    leaveRoom() {
      this.$emit("room-leave2");
    },
    joinRoom(room) {
      this.$emit("room-join2", room);
    },
    refreshResult() {
      if (this.debResult) {
        this.debResult();
      }
    },
    refreshList() {
      if (this.debList) {
        this.debList();
      }
    },
    refreshCheck() {
      if (this.debCheck) {
        this.debCheck();
      }
    },
    refreshData() {
      this.refreshResult();
      this.refreshList();
      this.refreshCheck();
    },
    getData() {
      this.getMemberBetList();
      this.getBetResultFinal();
      this.getMultiBetCheck();
    },
    setRoomData(e) {
      // console.log("setRoomData", e);
      if (e.room_id == this.roomId) {
        this.$set(this, "roomData", e);
        this.getData();
        this.handleStake(true);
      } else {
        this.roomData = {};
      }
    },
    debounce(func, wait, immediate) {
      var timeout;

      return function executedFunction() {
        var context = this;
        var args = arguments;

        var later = function () {
          timeout = null;
          if (!immediate) func.apply(context, args);
        };

        var callNow = immediate && !timeout;

        clearTimeout(timeout);

        timeout = setTimeout(later, wait);

        if (callNow) func.apply(context, args);
      };
    },
    confirmBet() {
      if (![0, 1].includes(this.roomData.room_status)) {
        this.$swal("Warning", this.$t("error.roomEndedOrCancelled"), "warning");
        return;
      }
      var e = this.roomData.room_id;
      if (e == undefined) {
        this.$swal("Error", this.$t("error.invalidRoom"), "error");
        return;
      }

      this.isConfirm = true;
    },
    placeBet() {
      this.isConfirm = false;

      if (![0, 1].includes(this.roomData.room_status)) {
        this.$swal("Warning", this.$t("error.roomEndedOrCancelled"), "warning");
        return;
      }

      var e = this.roomData.room_id;
      if (e == undefined) {
        this.$swal("Error", this.$t("error.invalidRoom"), "error");
        return;
      }

      for (var n in this.betSlip[e]) {
        var m = this.betSlip[e][n];
        var item = {
          account_id: this.$store.getters.accountId,
          session_token: this.$store.getters.sessionToken,
          room_id: e,
          bet_member: this.stake,
          accept_better_odds: true,

          odds_id: m.odds[3],
          submatch_id: m.odds[2],
          match_id: m.odds[1],
          bet_type: m.odds[4],
          bet_member: this.stake,
          bet_team_id: m.home_away == 1 ? m.match.home_team_id : m.match.away_team_id,
          home_away: m.home_away,
          odds_display: parseFloat(m.value),
          odds_mo: parseFloat(m.origin),
          odds_type: config.oddsTypeId[m.typ],
          ball_display: m.odds[8],
        };
        this.betPlus(item);
      }

      this.cancel();
    },
    betPlus(json) {
      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      // console.log(json);
      // this.betMode = false;
      this.loading.betPlus = true;
      // this.loading.getMemberBetList = true;
      service.bet(config.tournamentUrl().betplus, json).then(
        (result) => {
          this.loading.betPlus = false;
          EventBus.$emit("roomGameRefresh2");
          // EventBus.$emit("roomBetSlipRefresh2");

          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == false) {
              this.$helpers.handleFeedback(feedback.status);
            } else {
              this.tabMode = 1;
            }
          }
          // setTimeout(() => {
          //   this.cancel();
          //   this.getMemberBetList();
          // }, 5000);
        },
        (err) => {
          // console.log(err);
          this.loading.betPlus = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
          // setTimeout(() => {
          //   this.getMemberBetList();
          // }, 5000);
        }
      );
    },
    cancel() {
      var e = this.roomData.room_id;
      if (e == undefined) {
        return {};
      }

      // use $set to set betSlip to a new object by room_id
      this.data = [];
      this.$set(this.betSlip, e, {});
      EventBus.$emit("selectOdds2", this.betSlip);

      // this.$emit("room-leave2");
    },
    highlighting(match_id, timing) {
      $(".tournament-betslip-single").removeClass("tournament-betslip-highlight");
      setTimeout(() => {
        $("#tournament-betslip-single-" + match_id).addClass("tournament-betslip-highlight");
        var vTop = $(".tournament-content-right").scrollTop();
        var vBottom = vTop + $(".tournament-content-right").height();
        var eTop = $("#tournament-betslip-single-" + match_id).offset().top;
        var eBottom = eTop + $("#tournament-betslip-single-" + match_id).outerHeight();
        if (eBottom > vTop && eTop < vBottom) {
        } else {
          $(".tournament-content-right").animate(
            {
              scrollTop: $("#tournament-betslip-single-" + match_id).offset().top - $(".tournament-content-right").offset().top + $(".tournament-content-right").scrollTop(),
            },
            100
          );
        }
      }, 100);
      setTimeout(() => {
        $("#tournament-betslip-single-" + match_id).removeClass("tournament-betslip-highlight");
      }, timing);
    },
    addSlip(e) {
      if (this.maxBet <= 0) {
        return;
      }
      if (![0, 1].includes(this.roomData.room_status)) {
        return;
      }
      if (e.home_away < 0) {
        return;
      }
      var roomId = this.roomData.room_id;
      if (roomId == undefined) {
        return {};
      }
      if (roomId != this.roomId) {
        return;
      }

      // check if betSlip is empty by room_id, if yes, create a new object
      if (this.betSlip[roomId] == null) {
        this.$set(this.betSlip, roomId, {});
      } else {
        this.betSlip[roomId] = {};
      }

      this.$set(this.betSlip[roomId], e.match.match_id, e);
      this.highlighting(e.match.match_id, 2000);
      EventBus.$emit("selectOdds2", this.betSlip);

      this.data = [e.match.match_id];

      this.tabMode = 0;
    },
    getLeagueName(e) {
      var name = e["name_" + this.language];
      if (name == null || name == "" || !name) {
        name = e.league_name;
      }
      return name;
    },
    getTeamName(p, e) {
      var name = e[p + "_name_" + this.language];
      if (name == null || name == "" || !name) {
        name = e[p + "_team_name"];
      }
      return name;
    },
    getName(p, e) {
      // console.log(p, e);
      var name = e[p + "_" + this.language];
      if (name == null || name == "" || !name) {
        name = e[p + "_en"];
      }
      return name;
    },
    getLogo(p, e) {
      return config.flagPath + p + "/" + e;
    },
    getBetResultFinal() {
      var e = this.roomData.room_id;
      if (e == undefined) {
        return {};
      }

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        room_id: e,
        page_number: 1,
        page_size: 100,
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      this.loading.getBetResultFinal = true;
      service.betResult(config.tournamentUrl().betresultfinal, json).then(
        (result) => {
          this.loading.getBetResultFinal = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              this.betResultFinal = result.data.value;
              // if (this.betResultFinal == null || this.betResultFinal.length <= 0) {
              //   this.resultMode = false;
              // } else {
              //   this.resultMode = true;
              // }
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          this.loading.getBetResultFinal = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getMemberBetList() {
      var e = this.roomData.room_id;
      if (e == undefined) {
        return;
      }

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        room_id: e,
        page_number: 1,
        page_size: 100,
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      this.loading.getMemberBetList = true;
      // console.log("roomBetSlip.memberbetlist");
      service.memberBetList(config.tournamentUrl().memberbetlist, json).then(
        (result) => {
          this.loading.getMemberBetList = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              this.betList = result.data.value;
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          this.loading.getMemberBetList = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getMultiBetCheck() {
      var e = this.roomData.room_id;
      if (e == undefined) {
        return {};
      }

      var parlay = [];
      for (var n in this.data) {
        var data = this.betSlip[e][this.data[n]];
        if (data && data.hasOwnProperty("sports_type")) {
          var item = {
            sports_type: data.sports_type,
            parlay: false,
            odds_id: data.odds[3],
            submatch_id: data.odds[2],
            bet_type: data.bet_type,
            bet_team_id: data.home_away == 1 ? data.match.home_team_id : data.match.away_team_id,
            home_away: data.home_away,
            odds_display: parseFloat(data.value),
            odds_mo: parseFloat(data.origin),
            odds_type: config.oddsTypeId[data.typ],
            ball_display: data.odds[8],
          };
          parlay.push(item);
        }
      }

      if (parlay.length <= 0) {
        return;
      }

      var slip = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        parlay: parlay,
      };

      xhrBet.betMultiOddsCheck(slip).then(
        (res) => {
          if (res.success) {
            var results = res.data;
            for (var n in this.data) {
              var m = this.betSlip[e][this.data[n]];
              var r = results[n];

              var d = r.odds_check_details[0];
              var success = false;
              if (typeof r.status == "string") {
                success = r.status == "1";
              } else {
                success = r.status == 1;
              }

              if (success) {
                if (m.odds[3] && m.odds[3] == d.odds_id) {
                  if (d.home_giving == true) {
                    m.giving = 1;
                  } else {
                    m.giving = 0;
                  }
                  if (d.ball_display_new != null) {
                    m.odds[8] = d.ball_display_new;
                  }
                  if (d.ball_new != null) {
                    var bd = d.ball_new;
                    if (m.home_away != m.giving + 1) {
                      if (bd != 0) {
                        bd = "-" + bd.toString();
                      }
                    }
                    m.ball_value = bd;
                  }
                  if (d.odds_display_new != null) {
                    m.value = calc.fmtType(d.odds_display_new, "A", m.bet_type);
                  }
                  if (d.odds_new != null) {
                    m.origin = calc.fmt(d.odds_new);
                  }
                  if (d.ball_new != null || d.odds_new != null) {
                    this.highlighting(m.match.match_id, 2000);
                  }
                }
              }
            }
          } else {
            this.$helpers.handleFeedback(res.status);
          }
        },
        (err) => {
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
  },
};
</script>
