<template lang="pug">
.col-12.setting-right.py-3
  .w-100.mx-auto
    .row.mx-0.my-2
      .col-4.px-0.text-right.font-weight-bold.text-account {{ $t("ui.change_account_password") }}
      .col-8
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.current_password") }}
      .col-8
        input.form-control.w-75(
          type="password"
          v-model="currPwd"
          maxlength="20"
          autofocus
          autocomplete="new-password"
        )
        small.form-text.text-danger.w-75(v-if="feedback.currPwd") {{ feedback.currPwd }}
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account  {{ $t("ui.new_password") }}
      .col-8
        input.form-control.w-75(
          type="password"
          v-model="newPwd"
          maxlength="20"
          autocomplete="new-password"
        )
        small.form-text.text-danger.w-75(v-if="feedback.newPwd") {{ feedback.newPwd }}
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account  {{ $t("ui.confirm_new_password") }}
      .col-8
        input.form-control.w-75(
          type="password"
          v-model="confirmNewPwd"
          maxlength="20"
          autocomplete="new-password"
        )
        small.form-text.text-danger.w-75(v-if="feedback.confirmNewPwd") {{ feedback.confirmNewPwd }}
        .form-text.text-muted.w-75 {{ $t("message.password_rule") }}
    .row.mx-0.my-2(v-if="!hasSecondaryAccount")
      .col-4.px-0.text-right.text-account  {{ $t("ui.set_nickname") }}
      .col-8
        input.form-control.w-75(
          type="text"
          v-model="nickName"
          maxlength="15"
          autocomplete="new-password"
        )
        small.form-text.text-danger.w-75(v-if="feedback.nickName") {{ feedback.nickName }}
        .form-text.text-muted.w-75
          |  {{ $t("message.nickname_rule") }}
    .text-center.mb-2.mt-4
      SpinButton(type="button" @click="submit" :loading="loading" css="btn-primary btn-result active w-25" :text="$t('ui.save')")
</template>

<script>
import SpinButton from "@/components/ui/SpinButton";
import { required, alphaNum, sameAs, requiredUnless, minLength, maxLength } from "vuelidate/lib/validators";
import config from "@/config";
import errors from "@/errors";
import service from "@/library/_xhr-account";
import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {
    SpinButton
  },
  data: () => ({
    loading: false,
    currPwd: "",
    newPwd: "",
    confirmNewPwd: "",
    nickName: "",
    feedback: {
      success: false,
      status: errors.request.failed,
      currPwd: "",
      newPwd: "",
      confirmNewPwd: "",
      nickName: "",
      timeout: null
    }
  }),
  validations: {
    currPwd: {
      required
    },
    newPwd: {
      required,
      minLength: minLength(6),
      maxLength: maxLength(10)
    },
    confirmNewPwd: {
      required,
      sameAs: sameAs("newPwd")
    },
    nickName: {
      required: requiredUnless("hasSecondaryAccount"),
      alphaNum,
      minLength: minLength(6),
      maxLength: maxLength(15)
    }
  },
  computed: {
    balance() {
      return this.$store.getters.balance;
    },
    hasSecondaryAccount() {
      return this.$store.getters.hasSecondaryAccount;
    }
  },
  destroyed() {
    EventBus.$off("RESET_INPUTS", this.clearInput);
  },
  mounted() {
    EventBus.$on("RESET_INPUTS", this.clearInput);
  },
  methods: {
    resetFeedbank() {
      this.feedback.currPwd = "";
      this.feedback.newPwd = "";
      this.feedback.confirmNewPwd = "";
      this.feedback.nickName = "";
    },
    clearInput() {
      this.currPwd = "";
      this.newPwd = "";
      this.confirmNewPwd = "";
      this.nickName = "";
    },
    submit() {
      if (this.validate()) {
        this.changePassword();
      }
    },
    validate() {
      this.$v.$touch();
      if (!this.$v.$invalid) {
        return true;
      } else {
        this.loading = false;
        this.resetFeedbank();
        clearTimeout(this.feedback.timeout);
        if (!this.$v.currPwd.required) {
          this.feedback.currPwd = this.$t("error.currPasswordRequired");
        }

        if (!this.$v.newPwd.required) {
          this.feedback.newPwd = this.$t("error.newPasswordRequired");
        }

        if (!this.$v.newPwd.minLength) {
          this.feedback.newPwd = this.$t("error.isMinValue", { fname: this.$t("ui.new_password"), fvalue: "6" });
        }

        if (!this.$v.newPwd.maxLength) {
          this.feedback.newPwd = this.$t("error.isMaxValue", { fname: this.$t("ui.new_password"), fvalue: "10" });
        }

        if (!this.$v.confirmNewPwd.required) {
          this.feedback.confirmNewPwd = this.$t("error.confirmNewPasswordRequired");
        }

        if (!this.$v.confirmNewPwd.sameAs) {
          this.feedback.confirmNewPwd = this.$t("error.passwordsNotMatch");
        }

        if (!this.$v.nickName.required) {
          this.feedback.nickName = this.$t("error.nickNameRequired");
        }

        if (!this.$v.nickName.alphaNum) {
          this.feedback.nickName = this.$t("error.alphaNumOnly");
        }

        if (!this.$v.nickName.minLength) {
          this.feedback.nickName = this.$t("error.isMinValue", { fname: this.$t("ui.nickname"), fvalue: "6" });
        }

        if (!this.$v.nickName.maxLength) {
          this.feedback.nickName = this.$t("error.isMaxValue", { fname: this.$t("ui.nickname"), fvalue: "15" });
        }

        this.feedback.timeout = setTimeout(() => {
          this.$v.$reset();
          this.resetFeedbank();
        }, 10000);
      }

      return false;
    },
    changePassword() {
      this.resetFeedbank();

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        password: this.currPwd,
        new_password: this.newPwd
      };

      this.loading = true;
      service.changePassword(json).then(
        result => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              if (!this.hasSecondaryAccount) {
                this.addSecondary();
              } else {
                this.clearInput();
                this.$helpers.showDialog(this.$t("ui.change_account_password"), this.$t("message.change_password_succeed"), "success");
              }
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        err => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          console.log(err);
          if (err.status == "invalid_password") {
            this.feedback.currPwd = this.$t("error.invalid_password");
          } else {
            this.$helpers.handleFeedback(err.status);
          }
        }
      );
    },
    addSecondary() {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        new_account_id: this.nickName
      };

      this.loading = true;
      service.addSecondary(json).then(
        result => {
          this.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.clearInput();
              this.$v.$reset();
              this.$store.dispatch("user/reLogin").then(
                res => {
                  this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.succeed"), "success");
                },
                err => {
                  this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.failed"), "error");
                }
              );
              // this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.succeed"), "success");
            } else {
              this.$store.dispatch("user/reLogin").then(
                res => {
                  this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.succeed"), "success");
                },
                err => {
                  this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.failed"), "error");
                }
              );
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        err => {
          this.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          switch (err.status) {
          case "invalid_password":
          case "new_login_id_exists":
            this.feedback.currPwd = this.$t("error." + err.status);
            break;
          default:
            this.$helpers.handleFeedback(err.status);
            break;
          }
        }
      );
    }
  }
};
</script>
