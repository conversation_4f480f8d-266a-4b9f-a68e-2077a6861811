<template lang="pug">
input#stake-input2.form-control.form-control-sm(
  type="text"
  :value="checkLoading"
  @input="handleInput"
  @keypress="validateStake($event)"
  @blur="handleStake"
  autofocus
  ref="stake"
  autocomplete="off"
  style="font-weight: 900;text-align: right;"
  )
</template>

<script>
import StakeCheck from "@/tournament2/library/_stakeinput2.js";
import { EventBus } from "@/library/_event-bus.js";
export default {
  props: {
    value: {
      type: [Number, String],
    },
    loadbet: {
      type: Boolean,
    },
  },
  data() {
    return {
      autoSelect: true,
    };
  },
  computed: {
    checkLoading() {
      if (!this.loadbet) {
        if (this.autoSelect) {
          this.xSelect();
        }
        return this.value;
      }
      return '';
    },
  },
  mounted() {
    this.xFocus();
    this.xSelect();
  },
  methods: {
    xFocusTouchEnd() {
      var si3 = $("#stake-input2");
      if (si3) {
        si3.focus();
      }
      document.body.removeEventListener("touchend", this.xFocusTouchEnd);
    },
    xFocus() {
      var si = $("#stake-input2");
      if (si) {
        si.focus();
        document.body.addEventListener("touchend", this.xFocusTouchEnd);
        setTimeout(() => {
          var si2 = $("#stake-input2");
          if (si2) {
            si2.focus();
          }
        }, 1000);
      }
    },
    getCaret(element) {
      if (element.selectionStart) return element.selectionStart;
      else if (document.selection) {
        //IE specific
        element.focus();
        element.select();

        var r = document.selection.createRange();
        if (r == null) return 0;

        var re = element.createTextRange(),
            rc = re.duplicate();
        re.moveToBookmark(r.getBookmark());
        rc.setEndPoint("EndToStart", re);
        return rc.text.length;
      }

      return 0;
    },
    validateStake(evt) {
      StakeCheck.validate(evt);
    },
    handleInput(e) {
      var chk = StakeCheck.validateWord(e);
      // console.log(chk);
      if (!chk) {
        if ($("#stake-input2") != undefined) {
          $("#stake-input2").value = this.value;
        }
      }
      this.autoSelect = false;
      this.$emit("input", e.target.value);
    },
    handleStake() {
      this.autoSelect = true;
      this.$emit("handleStake");
    },
    xSelect() {
      setTimeout(() => {
        if ($("#stake-input2") != undefined) {
          $("#stake-input2").select();
        }
      }, 100);
    },
  },
};
</script>
