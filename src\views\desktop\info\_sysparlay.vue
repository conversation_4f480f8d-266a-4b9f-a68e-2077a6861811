<template lang="pug">
.info-wrapper
  .info-title
    .info-icon
      i.fad.fa-receipt
    .page-title(aria-label='breadcrumb')
      ol.breadcrumb.p-0.m-0
        li.breadcrumb-item(aria-current='page')
          a.clickable(@click="backHome()") {{ $t("ui.sysparlay") }}
        li.breadcrumb-item(v-if="mode == 1 && selectedId" aria-current='page') {{ selectedId }}
    SpinButton(text="" :loading="feedback.loading" css="btn-sm btn-info" @click="refreshList" img="fad fa-sync-alt w-16px")
  .info-tablewrap.magicZ
    .result-selection.p-2
      .d-flex.flex-row.mb-1
        .mr-2
          .input-group
            .input-group-prepend
              span.input-group-text
                i.fad.fa-calendar-alt
            date-picker.datepicker(v-model="fromDate", :config="locale", @input="getReport()")

        .mr-2
          .input-group
            .input-group-prepend
              span.input-group-text
                i.fad.fa-calendar-alt
            date-picker.datepicker(v-model="toDate", :config="locale", @input="getReport()")

        .mr-2
          button.btn.btn-secondary.btn-result(type="button", @click="setDate(12)") {{ $t('ui.today') }}
        .mr-2
          button.btn.btn-secondary.btn-result(type="button", @click="setDate(36)") {{ $t('ui.yesterday') }}

      template(v-if="mode == 0")
        table.mt-2(width='100%' v-if="this.feedback.loading")
          tbody
            tr
              .empty.match-info.text-center.p-1
                i.fad.fa-spinner.fa-spin(style="font-size: 16px;")
        table.table-info.mt-2(width='100%' v-else :id="'bet-sysparlay1-accordion'")
          tbody
            template(v-if="data1 != null && data1.length > 0")
              tr
                th.text-center(scope='col', width='4%') #
                th.text-left(scope='col', width='16%') {{ $t("ui.id") }}
                th.text-left(scope='col', width='55%') {{ $t("ui.match") }}
                th.text-right(scope='col', width='5%') {{ $t("ui.fold") }}
                th.text-right(scope='col', width='8%') {{ $t("ui.combination") }}
                th.text-right(scope='col', width='12%') {{ $t("ui.stakeperbet") }}

              tr(v-for="(item, index) in data1" :class="{ grey: index % 2 === 0 }")
                td.text-center(valign='top') {{ ((page_number - 1) * $store.getters.pageSize + index + 1) }}
                td.text-left(valign='top' @click="view2(item.system_parlay_id)")
                  div.clickable {{ $t("ui.ref_no") }}: {{ item.system_parlay_id }}
                  div(v-if="item.created_on") {{ $dayjs(item.created_on).format("MM/DD/YYYY hh:mm:ss A") }}
                  div(v-else) -
                td.text-left(valign='top')
                  .bet-info
                    .d-flex.justify-content-between.align-content-center(:id="'heading1-' + item.system_parlay_id")
                      .bet-type.blue {{ item.total_match }} {{ $t("ui.match") }}
                      .bg-blue.x-blue.btn-inner.collapsed(
                        data-toggle="collapse"
                        :data-target="'#collapse1-' + item.system_parlay_id" role="button" aria-expanded="false"
                        :aria-controls="'collapse1-' + item.system_parlay_id"
                        @click="getMatch(item.system_parlay_id)"
                        :ref="'collapse1-' + item.system_parlay_id"
                        )
                        i.far.fa-chevron-up
                  .bet-list-scroll.match-info.collapse.magicY(
                    :id="'collapse1-' + item.system_parlay_id"
                    :data-parent="'#bet-sysparlay1-accordion'"
                    :aria-labelledby="'heading1-' + item.system_parlay_id"
                    )
                    .empty.match-info.white.text-center(v-if="data2.length <= 0")
                      i.fad.fa-spinner.fa-spin
                    betDetails(:items="data2")
                td.text-right(valign='top') {{ item.fold }}
                td.text-right(valign='top') {{ item.combination }}
                td.text-right(valign='top') {{ $numeral(item.bet_member).format("0,0.00") }}
      template(v-if="mode == 1")
        table.mt-2(width='100%' v-if="this.feedback.loading")
          tbody
            tr
              .empty.match-info.text-center.p-1
                i.fad.fa-spinner.fa-spin(style="font-size: 16px;")
        table.table-info.mt-2(width='100%' v-else :id="'bet-sysparlay2-accordion'")
          tbody
            template(v-if="data3 != null && data3.length > 0")
              tr
                th.text-center(scope='col', width='4%') #
                th.text-left(scope='col', width='16%') {{ $t("ui.id") }}
                th.text-right(scope='col', width='10%') {{ $t("ui.odds") }}
                th.text-left(scope='col', width='31%') {{ $t("ui.choice") }}
                th.text-right(scope='col', width='13%') {{ $t("ui.stake") }}
                th.text-right(scope='col', width='13%') {{ $t("ui.win") }}/{{ $t("ui.lose") }}
                th.text-center(scope='col', width='13%') {{ $t("ui.status") }}
              tr(v-for="(item, index) in data3" :class="{ grey: index % 2 === 0 }")
                td.text-center(valign='top') {{ ((page_number2 - 1) * $store.getters.pageSize + index + 1) }}
                td.text-left(valign='top')
                  div {{ $t("ui.ref_no") }}: {{ item.bet_id }}
                  div(v-if="item.created_on") {{ $dayjs(item.created_on).format("MM/DD/YYYY hh:mm:ss A") }}
                  //- div(v-else) -
                td.text-right(valign='top')
                  div {{ item.odds_display }}
                td.text-left(valign='top')
                  .bet-info
                    .d-flex.justify-content-between.align-content-center(:id="'heading2-' + item.bet_id")
                      .bet-type.blue
                        span {{ $t("ui.mix_parlay") }}
                      .bg-blue.x-blue.btn-inner.collapsed(
                        data-toggle="collapse"
                        :data-target="'#collapse2-' + item.bet_id" role="button" aria-expanded="false"
                        :aria-controls="'collapse2-' + item.bet_id"
                        @click="getDetails(item)"
                        :ref="'collapse2-' + item.bet_id"
                        )
                        i.far.fa-chevron-up
                  .bet-list-scroll.match-info.collapse.magicY(
                    :id="'collapse2-' + item.bet_id"
                    :data-parent="'#bet-sysparlay2-accordion'"
                    :aria-labelledby="'heading2-' + item.bet_id"
                    )
                    .empty.match-info.white.text-center(v-if="data4.length <= 0")
                      i.fad.fa-spinner.fa-spin
                    betDetails(:items="data4")
                td.text-right(valign='top' width='15%') {{ $numeral(item.bet_member).format("0,0.00") }}
                td.text-right(valign='top' width='15%')
                  span(v-if="item.winlose" :class="getNumberClass1(item.winlose)") {{ $numeral(item.winlose).format("0,0.00") }}
                  span(v-else) -
                td.text-center(valign='top' width='10%') {{ $t("ui." + item.bet_status.toLowerCase()) }}

              tr.table-total(v-if="data3.length > 0")
                td.text-left(valign='top' colspan="2")
                  div
                    span {{ $t("ui.total") }} &nbsp;
                    b {{ data3[0].total_accept }}&nbsp;
                    span {{ $t("ui.accepted") }}
                  div
                    span {{ $t("ui.total") }} &nbsp;
                    b {{ data3[0].total_reject }}&nbsp;
                    span {{ $t("ui.rejected") }}
                td.text-right(valign='top' colspan="2") {{ $t("ui.total") }}
                td.text-right(valign='top')
                  b {{ $numeral(data3[0].sum_bet).format("0,0.00") }}
                td.text-right(valign='top')
                  b(v-if="data3[0].pending==0" :class="getNumberClass1(data3[0].sum_wl)") {{ $numeral(data3[0].sum_wl).format("0,0.00") }}
                  b(v-else) -
                td.text-center(valign='top')
      .mt-2(v-if="mode == 0 && data1 != null && data1.length > 0")
        v-pagination(
          v-model="page_number"
          :page-count="page_total"
          :classes="bootstrapPaginationClasses"
          :labels="paginationAnchorTexts"
          @input="changedPage1($event)"
          v-if="page_total"
        )
      .mt-2(v-if="mode == 1 && data3 != null && data3.length > 0")
        v-pagination(
          v-model="page_number2"
          :page-count="page_total2"
          :classes="bootstrapPaginationClasses"
          :labels="paginationAnchorTexts"
          @input="changedPage2($event)"
          v-if="page_total2"
        )
  .notes
    p.mb-0 {{ $t("ui.note") }}:
    ul
      li {{ $t("message.time_display_in_gmt_plus_8") }}
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import SpinButton from "@/components/ui/SpinButton";
import naming from "@/library/_name";
import vPagination from "vue-plain-pagination";
import statement from "@/library/_xhr-statement";
import betlist from "@/library/_xhr-betlist.js";
import betDetails from "@/components/desktop/info/betList/betDetails";

export default {
  components: {
    SpinButton,
    vPagination,
    betDetails,
  },
  data() {
    return {
      mode: 0,
      selectedId: null,
      feedback: {
        loading: false,
        success: false,
        status: errors.session.invalidSession,
        source: "",
      },
      start_date: null,
      end_date: null,
      page_number: 1,
      page_size: 10,
      page_total: 10,
      page_number2: 1,
      page_size2: 10,
      page_total2: 10,
      locale: {
        format: "YYYY-MM-DD",
        useCurrent: true,
        minDate: this.$dayjs(
          this.$dayjs
            .utc()
            .tz("Asia/Kuala_Lumpur")
            .startOf("day")
            .subtract(14, "day")
            .format("YYYY-MM-DD")
        ).format("YYYY-MM-DD"),
        maxDate: this.$dayjs.utc().tz("Asia/Kuala_Lumpur").format("YYYY-MM-DD"),
      },
      bootstrapPaginationClasses: {
        ul: "pagination justify-content-center",
        li: "page-item",
        liActive: "active",
        liDisable: "disabled",
        button: "page-link",
        buttonActive: "active",
        buttonDisable: "disable",
      },
      paginationAnchorTexts: {
        first: "<i class='fas fa-angle-double-left'></i>",
        prev: "<i class='fas fa-angle-left'></i>",
        next: "<i class='fas fa-angle-right'></i>",
        last: "<i class='fas fa-angle-double-right'></i>",
      },
      data1: [],
      data2: [],
      data3: [],
      data4: [],
      data5: [],
    };
  },
  computed: {
    sports() {
      return this.$store.state.layout.sports;
    },
    w4dSupport() {
      return config.w4dSupport;
    },
    whiteLabel() {
      return config.whiteLabel;
    },
    getPageSize() {
      return this.$store.getters.pageSize;
    },
    language() {
      return this.$store.getters.language;
    },
    fromDate: {
      get() {
        if (this.start_date) return this.start_date;
        else
          return this.$dayjs
            .utc()
            .tz("Asia/Kuala_Lumpur")
            .subtract(12, "hour")
            .subtract(7, "day")
            .format("YYYY-MM-DD");
      },
      set(value) {
        this.start_date = value;
      },
    },
    toDate: {
      get() {
        if (this.end_date) return this.end_date;
        else
          return this.$dayjs
            .utc()
            .tz("Asia/Kuala_Lumpur")
            .add("12", "hours")
            .format("YYYY-MM-DD");
      },
      set(value) {
        this.end_date = value;
      },
    },
  },
  destroyed() {
    EventBus.$off("SYSPARLAY_REFRESH", this.refreshReport);
  },
  mounted() {
    EventBus.$on("SYSPARLAY_REFRESH", this.refreshReport);
    this.backHome();
  },
  methods: {
    refreshReport() {
      if (this.mode == 0) {
        this.getReport();
      } else {
        if (this.selectedId) {
          this.getBet(this.selectedId, true);
        }
      }
    },
    getDetails(e, loading) {
      if (e.bet_status.toLowerCase() == "accepted") {
        this.getAccepted(e.bet_id, loading);
      } else {
        this.getRejected(e.bet_id, loading);
      }
    },
    getAccepted(e, loading) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        bet_id: e,
      };
      if (loading) this.feedback.loading = true;
      this.data4 = [];

      betlist.getParlayAcceptDetails(json).then(
        (result) => {
          if (loading) this.feedback.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.data4 = result.data;
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          if (loading) this.feedback.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getRejected(e, loading) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        bet_id: e,
      };
      if (loading) this.feedback.loading = true;
      this.data4 = [];

      betlist.getParlayRejectDetails(json).then(
        (result) => {
          if (loading) this.feedback.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.data4 = result.data;
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          if (loading) this.feedback.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    view2(e) {
      this.mode = 1;
      this.selectedId = e;
      this.page_number2 = 1;
      this.page_size2 = parseInt(this.getPageSize);
      this.page_total2 = 10;
      this.getBet(e, true);
    },
    backHome() {
      this.mode = 0;
      this.selectedId = null;
      this.page_number = 1;
      this.page_size = parseInt(this.getPageSize);
      this.page_total2 = 10;
      this.refreshList();
    },
    ballDisplayMMO(e) {
      return naming.ballDisplayMMO2(e, this);
    },
    getHomeTeam(e) {
      var r = e["home_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.home_team_name;
    },
    getAwayTeam(e) {
      var r = e["away_name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.away_team_name;
    },
    getLeague(e) {
      var r = e["name_" + this.language];
      if (r != null && r != "") {
        return r;
      }
      return e.league_name;
    },
    getNumberClass(e) {
      return parseFloat(e) >= 0 ? "" : "text-red";
    },
    getNumberClass1(e) {
      return parseFloat(e) >= 0 ? "" : "red";
    },
    getBetTypeName(e) {
      return this.$t("m.BT_" + e);
    },
    betDisplay(bet) {
      return naming.betDisplay(bet, this, this.language);
    },
    ballDisplay(e) {
      return naming.ballDisplay(e, this);
    },
    // getParlayMatchResult(e) {
    //   return naming.parlayResult(e.payout_odds, e.odds_display, this);
    // },
    // getMMOParlayMatchResult(e) {
    //   return naming.mmoParlayResult(e.payout_odds, e.odds_display, this);
    // },
    changedPage1(pageNo) {
      this.page_number = pageNo;
      this.refreshList();
    },
    changedPage2(pageNo) {
      this.page_number2 = pageNo;
      this.getBet(this.selectedId, true);
    },
    setDate(offset) {
      this.fromDate = this.$dayjs
        .utc()
        .tz("Asia/Kuala_Lumpur")
        .subtract(offset, "hour")
        .format("YYYY-MM-DD");
      this.toDate = this.$dayjs
        .utc()
        .tz("Asia/Kuala_Lumpur")
        .subtract(offset - 12, "hour")
        .format("YYYY-MM-DD");
    },

    getBet(e, loading) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        system_parlay_id: e,
        page_number: this.page_number2,
        page_size: this.page_size,
      };

      if (loading) this.feedback.loading = true;
      this.data3 = [];

      statement.getBetParlayBet(json).then(
        (result) => {
          if (loading) this.feedback.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.data3 = result.data;
              if (this.data3 == null) {
                this.page_total2 = 1;
              } else {
                if (this.data3.length <= 0) {
                  this.page_total2 = 1;
                } else {
                  var totalrows = this.data3[0].totalrows;
                  if (totalrows % this.page_size2 != 0)
                    this.page_total2 = Math.ceil(
                      parseFloat(totalrows) / parseFloat(this.page_size2)
                    );
                  else
                    this.page_total2 =
                      parseFloat(totalrows) / parseFloat(this.page_size2);
                }
              }
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          if (loading) this.feedback.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getMatch(e, loading) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        system_parlay_id: e,
      };

      this.data2 = [];

      statement.getBetParlayMatch(json).then(
        (result) => {
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.data2 = result.data;
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    getReport(loading) {
      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        start_date: this.fromDate,
        end_date: this.toDate,
        page_number: this.page_number,
        page_size: this.page_size,
      };

      this.feedback.loading = true;
      this.data1 = [];

      statement.getBetParlay(json).then(
        (result) => {
          this.feedback.loading = false;
          if (result) {
            this.feedback.status = result.status;
            if (result.success == true) {
              this.data1 = result.data;
              if (this.data1 == null) {
                this.page_total = 1;
              } else {
                if (this.data1.length <= 0) {
                  this.page_total = 1;
                } else {
                  var totalrows = this.data1[0].totalrows;
                  if (totalrows % this.page_size != 0)
                    this.page_total = Math.ceil(
                      parseFloat(totalrows) / parseFloat(this.page_size)
                    );
                  else
                    this.page_total = parseFloat(totalrows) / parseFloat(this.page_size);
                }
              }
            } else {
              this.$helpers.handleFeedback(this.feedback.status);
            }
          }
        },
        (err) => {
          this.feedback.loading = false;
          this.feedback.success = false;
          this.feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
    refreshList() {
      EventBus.$emit("SYSPARLAY_REFRESH");
    },
  },
};
</script>

<style>
.bbtop {
  border-top: 1px solid #215380;
  border-color: #215380 !important;
}

.table-info tr td .clickable {
  color: #345684 !important;
  text-decoration: underline;
}
</style>
