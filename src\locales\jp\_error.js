export default {
  OK: "OK",
  CLOSE: "アカウントが閉鎖されました!",
  BLOCK: "アカウントがブロックされました!",
  FREEZE: "アカウントが凍結されました!",
  SUSPEND: "あなたのアカウントは閲覧専用に変更されました。",
  passwordRequired: "パスワードが必要です!",
  usernameRequired: "ユーザー名は必須です!",
  incompletedRequest: "何か問題が発生しました...",
  unauthenticatedUser: "ユーザー認証が無効です。",
  session_not_exists: "セッションが期限切れです。続行するには再度ログインしてください...",
  session_expired: "セッションが期限切れです。続行するには再度ログインしてください...",
  sessionExpired: "セッションが期限切れです。続行するには再度ログインしてください...",
  invalidSession: "セッションが期限切れです。続行するには再度ログインしてください...",
  accountIdRequired: "セッションが期限切れです。続行するには再度ログインしてください...",
  NoUserAccessRight: "権限が拒否されました。続行するにはアップラインに連絡してください...",
  account_not_exists: "パスワードが無効です。",
  invalid_password: "パスワードが無効です。",
  secondary_account_exists: "ニックネームは既に作成されています。",
  systemError: "内部システムエラー。",
  new_login_id_exists: "ニックネームが無効です。別のニックネームを設定してください。",
  loginLimit: "ログイン頻度が高すぎます。1 分後にもう一度お試しください。",
  requestLimit: "要求が頻繁すぎます。",
  insufficient_balance: "残高不足",
  insufficientBalance: "残高不足",
  loginFailed: "ログインに失敗しました。後でもう一度お試しください...",
  requestFailed: "接続が不安定です。接続を確認して、再試行してください。",
  requestPending: "要求保留中...",
  close: "アカウントが閉鎖されました！続行するには、上位ラインに連絡してください...",
  suspend: "アカウントが停止されました！続行するには、上位ラインに連絡してください...",
  freeze: "アカウントが凍結されました！続行するには、上位ラインに連絡してください...",
  alphaNumOnly: "英数字のみ",
  isRequired: "{fname}を入力してください。",
  isMinValue: "{fname}は最低{fvalue}が必要です。",
  isMaxValue: "{fname}は{fvalue}以下である必要があります。",
  isMinLength: "{fname}は少なくとも{fvalue}文字を含む必要があります。",
  isMaxLength: "{fname}は{fvalue}文字以下である必要があります。",
  isAlpha: "{fname}はアルファベットのみ使用できます。",
  isAlphaNum: "{fname}は英数字のみ使用できます。",
  isNumeric: "{fname}は数字のみ使用できます。",
  isEmail: "{fname}は有効なメールアドレスのみ使用できます。",
  isIpAddress: "{fname}は有効なIPv4アドレスのみ使用できます。",
  isSameAs: "{fname}は{fname2}と同じである必要があります。",
  isUrl: "{fname}はURLのみ使用できます。",
  containAlphaNum: "{fname}には、少なくとも1つのアルファベットと1つの数字が必要です。",
  selectOption: "{fname}を選択してください。",
  notSameAs: "{fname}と{fname2}は同じにできません。",
  currPasswordRequired: "現在のパスワードが必要です！",
  newPasswordRequired: "新しいパスワードが必要です！",
  confirmNewPasswordRequired: "新しいパスワードの確認が必要です！",
  passwordsNotMatch: "パスワードが一致しません！",
  nickNameRequired: "ニックネームが必要です！",
  startDateRequired: "開始日が必要です！",
  endDateRequired: "終了日が必要です！",
  pageSizeRequired: "ページサイズが必要です！",
  pageNumberRequired: "ページ番号が必要です！",
  sportsTypeRequired: "スポーツタイプが必要です！",
  workingDateRequired: "作業日が必要です！",
  leagueIdRequired: "リーグIDが必要です！",
  isOutrightRequired: "アウトライトが必要です！",
  duplicate_debit_record: "オッズが更新されるまで少々お待ちください...",
  invalidOddsBall: "オッズが更新されるまで少しお待ちください...",
  oddsDisplayInvalid: "オッズが更新されるまでお待ちください...",
  oddsIsUpdating: "オッズが更新中です...",
  unableToGetBalanceAtTheMoment: "内部サーバーエラー。",
  atLeastTwoMatchToBetParlay: "パーレイにベットするには、少なくとも2つの試合をミックスしてください。",
  atLeastThreeMatchToBetParlay: "パーレイにベットするには、少なくとも3つの試合をミックスしてください。",
  maxParlayTicket: "パーレイごとのチケットの最大数に達しました！",
  matchHide: "この試合は利用できません。他の試合をお試しください。(318)",
  matchNotAllowBet: "この試合は利用できません。他の試合をお試しください。(311)",
  matchNotActive: "この試合は利用できません。他の試合をお試しください。(308)",
  invalidBets: "このマーケットは利用できません。他のマーケットをお試しください。(340)",
  invalidBet: "このマーケットは利用できません。他のマーケットをお試しください。(340)",
  invalidBetType: "このマーケットは利用できません。他のマーケットをお試しください。(314)",
  invalidBetTeam: "このマーケットは利用できません。他のマーケットをお試しください。(328)",
  invalidOdds: "このマーケットは利用できません。他のマーケットをお試しください。(313)",
  betOverMaxPerMatch: "賭け金が最大賭け金を超えています。",
  betOverMax: "賭け金が最大賭け金を超えています。",
  betLowerMin: "賭け金が最小賭け金未満です。",
  betOverMaxPayout: "賭け金が最大払戻金を超えています。",
  memberInactive: "このアカウントの状態はアクティブではありません。上位ラインに確認してください。",
  memberCommissionNotSetup: "コミッションが設定されていません。",
  betOverMaxBet: "ベット金額が最大制限を超えています。ベット制限を確認してください。",
  invalidCurrency: "ベットするのに無効な通貨です。",
  betOverLimit: "賭け金が制限を超えています。",
  selectLeague: "少なくとも1つのリーグを選択してください",
  game_maintenance: "ゲームはメンテナンス中です。",
  betTypeRequired: "ベットタイプが必要です。",
  betAmountRequired: "ベット金額が必要です。",
  invalidBetAmount: "無効なベット金額です。",
  currencyNotSupported: "サポートされていない通貨です。",
  forbiddenAccess: "アクセス禁止",
  connectionFailed: "オペレーターAPIが応答しませんでした。",
  liveCasinoDisabled: "ライブカジノが無効",
  lotteryDisabled: "宝くじが無効",
  MiniGameDisabled: "ミニゲームが無効",
  EsportsDisabled: "Eスポーツゲームが無効",
  login_id_not_exists: "無効なログインです。再度ログインしてください。",
  noMatchSelected: "少なくとも1つの試合を選択してください。",
  invalidRoomRate: "無効なルームレートです。",
  invalidRoomLimit: "無効なルームリミットです。",
  invalidRoomPassword: "無効なPINコードです。PINコードは6桁の数字である必要があります。",
  InvalidRoomPassword: "無効なPINコードです。PINコードは6桁の数字である必要があります。",
  invalidPassword: "無効なパスワードです。",
  InvalidPassword: "無効なパスワードです。",
  roomPasswordRequired: "PINコードが必要です。",
  minMatch: "最低3つの試合を選択してください！",
  maxMatch: "最大{max}試合が選択されました！",
  roomIdRequired: "ルームIDが必要です。",
  roomTypeRequired: "ルームタイプが必要です。",
  sessionTokenRequired: "セッショントークンが必要です。",
  matchDateRequired: "試合日が必要です。",
  matchRequired: "試合が必要です。",
  roomLimitRequired: "ルームリミットが必要です。",
  roomRateRequired: "ルームレートが必要です。",
  minRoomRate: "参加料は10以上でなければなりません。",
  maxRoomRate: "参加料は1000以下でなければなりません。",
  minRoomLimit: "プレイヤーの人数は3人以上でなければなりません。",
  maxRoomLimit: "プレイヤーの人数は30人以下でなければなりません。",
  invalidRoomType: "無効なルームタイプです。",
  invalidMatch: "無効な試合です。",
  dataError: "データエラー",
  MemberExists: "メンバーはすでに存在します。",
  invalidMember: "無効なメンバーです。",
  invalidleague: "無効なリーグです。",
  "createRoomFailed-1": "ルーム作成に失敗しました1。",
  "createRoomFailed-2": "ルーム作成に失敗しました2。",
  "createRoomFailed-3": "ルーム作成に失敗しました3。",
  invalidOperator: "無効なオペレーターです。",
  roomFull: "ルームはすでに満室です。",
  memberExists: "メンバーはすでに存在します。",
  "joinFailed-1": "ルーム参加に失敗しました1。",
  "joinFailed-2": "ルーム参加に失敗しました2。",
  IPJoinedBefore: "IPは以前に参加しました。",
  memberJoinedBefore: "すでに参加していますので、公共の無料ルームには一度しか参加できないことにご注意ください。",
  roomNotExists: "ルームが存在しません。",
  roomIsPublicFree: "ルームは公共の無料ルームです。",
  roomEndedOrCancelled: "ルームが終了またはキャンセルされました。",
  roomEnded: "ルームはすでに終了しました。",
  matchLowerMin: "試合の最小エラーが発生しました。",
  matchOverMax: "試合の最大エラーが発生しました。",
  pointsOverLimit: "ポイントの上限エラーが発生しました。",
  roomOverMax: "ルームの最大エラーが発生しました。",
  invalidRoom: "無効なルームです。",
  roomLimitOverMax: "ルームのリミットが最大を超えました。",
  rateLowerMin: "レートの最小エラーが発生しました。",
  rateOverMax: "レートの最大エラーが発生しました。",
  liveMatchNotAllowed: "ライブ試合は許可されていません。",
  roomLimitLowerMin: "ルームのリミットが最小を下回りました。",
  roomClosed: "ルームは閉鎖されました。",
  tournamentLeagueExists: "トーナメントリーグが存在します。",
  roomExists: "複数のプライベートルームを作成することは許可されていません。",
  RoomExists: "複数のプライベートルームを作成することは許可されていません。",
  betExists: "ベットはすでに存在します。",
  betNotExists: "トーナメントに参加するには有効なベットをしてください。",
  roomClosedNotAllowed: "ルームが閉鎖されたため許可されません。",
  tournamentMatchExists: "トーナメント試合が存在します。",
  account_id_required: "アカウントIDが必要です。",
  agent_comm_rate_required: "エージェントのコミッションレートが必要です。",
  agent_id_required: "エージェントIDが必要です。",
  bet_amount_required: "ベット金額が必要です。",
  bet_id_required: "ベットIDが必要です。",
  credit_amount_required: "クレジット金額が必要です。",
  currency_required: "通貨が必要です。",
  debit_amount_required: "デビット金額が必要です。",
  duplicate_credit_record: "重複するクレジットレコードです。",
  master_comm_rate_required: "マスターのコミッションレートが必要です。",
  master_id_required: "マスターIDが必要です。",
  member_id_required: "メンバーIDが必要です。",
  member_wallet_not_exists: "メンバーウォレットが存在しません。",
  permission_denied: "許可が拒否されました。",
  rate_required: "レートが必要です。",
  result_exists: "結果はすでに存在します。",
  room_id_required: "ルームIDが必要です。",
  s_senior_comm_rate_required: "スーパーシニアのコミッションレートが必要です。",
  s_senior_id_required: "スーパーシニアIDが必要です。",
  senior_comm_rate_required: "シニアのコミッションレートが必要です。",
  senior_id_required: "シニアIDが必要です。",
  shareholder_comm_rate_required: "シェアホルダーのコミッションレートが必要です。",
  shareholder_id_required: "シェアホルダーIDが必要です。",
  transfer_id_required: "トランスファーIDが必要です。",
  minBetAmount: "最小ベット金額エラーが発生しました。",
  tournamentDisabled: "トーナメントは無効化されています。上位ラインに確認してください。",
  marketTypeRequired: "マーケットタイプが必要です。",
  invalidMarketType: "無効なマーケットタイプが提供されました。",
  marketTypeChanged: "マーケットタイプが変更されたため、ベットスリップが削除されました。<br />マーケットから再度選択してください。",
  internalServerError: "内部サーバーエラー！",
  refundTest: "返金テスト！",
  matchChangeTime: "ルームはすでに閉鎖されました。",
  changeStakeMin: "最小制限により入力された賭け金額が変更されました。",
  changeStakeMax: "最大制限により入力された賭け金額が変更されました。",
};
