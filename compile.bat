@echo off

set mode=%1

if "%mode%"=="dev" goto dev_build
if "%mode%"=="prod" goto prod_build
goto all_build

:dev_build
del dist_uat2.7z
del dist_st.7z
del dist_ms.7z
del dist_ms2.7z

call yarn build --mode uat2
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_uat2.7z dist

call yarn build --mode staging
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_st.7z dist

call yarn build --mode middlestaging
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_ms.7z dist

call yarn build --mode middlestaging2
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_ms2.7z dist

goto end

:prod_build
del dist_pr.7z
del dist_cn.7z
del dist_ibc.7z
del dist_ub.7z
del dist_md.7z

call yarn build --mode production
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_pr.7z dist

call yarn build --mode china
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_cn.7z dist

call yarn build --mode ibcbet
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_ibc.7z dist

call yarn build --mode ubett
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_ub.7z dist

call yarn build --mode middleware
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_md.7z dist

goto end

:all_build
del dist_pr.7z
del dist_cn.7z
del dist_ibc.7z
del dist_ub.7z
del dist_md.7z
del dist_joy.7z
del dist_st.7z
del dist_ms2.7z
del dist_ms.7z
del dist_uat2.7z

call yarn build --mode uat2
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_uat2.7z dist

call yarn build --mode staging
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_st.7z dist

call yarn build --mode middlestaging
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_ms.7z dist

call yarn build --mode middlestaging2
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_ms2.7z dist

call yarn build --mode production
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_pr.7z dist

call yarn build --mode china
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_cn.7z dist

call yarn build --mode ibcbet
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_ibc.7z dist

call yarn build --mode ubett
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_ub.7z dist

call yarn build --mode middleware
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_md.7z dist

call yarn build --mode joyful
"C:\Program Files\7-Zip\7z.exe" a -t7z dist_joy.7z dist

:end
