<template lang="pug">
#player-betslip.modal.fade.modal-room
  .modal-dialog.modal-lg.modal-dialog-centered.modal-dialog-scrollable
    .modal-content
      .modal-body(v-if="item")
        .player-betslip-wrapper
          .player-betslip-top
            .player-betslip-icon
              i.fas.fa-user
            .player-betslip-holder {{ item.member_id }}
            .player-betslip-prize
              .player-prize-single
                .player-prize-icon
                  template(v-if="[1,2,3].includes(item.ranking)")
                    img(:src="'img/tn/trophy' + item.ranking + '.svg'")
                  template(v-else)
                    i.fas.fa-trophy
                .player-prize-detail
                  .player-prize-top {{ $t("ui.place") }}
                  .player-prize-bottom(v-if="item.ranking") {{ item.ranking }}
                  .player-prize-bottom(v-else) -
              .player-prize-single
                .player-prize-icon
                  img(src="img/tn/icon-point.svg")
                .player-prize-detail
                  .player-prize-top {{ $t("ui.current_prize") }}
                  .player-prize-bottom(v-if="item.prize") {{ $numeral(item.prize).format("0,0.00") }}
                  .player-prize-bottom(v-else) -
          .player-betslip-room {{ $t("ui.room_id") }}: {{ item.room_id }}
          .player-betslip-content(v-if="item.room_status != 3")
            template(v-if="item.record && item.record.bet_list.length > 0")
              .tournament-mybet-single(v-for="rec in item.record.bet_list")
                .tournament-mybet-date {{ $dayjs(rec.created_on).format("MM/DD/YYYY hh:mm:ss A") }}
                .tournament-mybet-text {{ sports[rec.sports_type] }} - {{ $t("m.BT_" + rec.bet_type) }}
                .tournament-mybet-odds
                  .tournament-mybet-odds-left
                    .mb-0.d-flex
                      template(v-if="['HDP','HDPH'].includes(rec.bet_type)")
                        .tournament-color-green(v-if="rec.home_away === 1") {{ getTeamName("home", rec) }}
                        .tournament-color-green(v-else) {{ rec.away_team_name }}
                        span.ml-2 {{ rec.ball }}
                      template(v-else)
                        .tournament-color-green(v-if="rec.home_away === 1") {{ $t('ui.over') }}
                        .tournament-color-green(v-else) {{ $t('ui.under') }}
                        span.ml-2 {{ Math.abs(rec.ball) }}
                    .tournament-color-red.mx-1 @
                    .mb-0 {{ rec.odds_display }}
                  .tournament-mybet-odds-right
                    .tournament-mybet-text.mb-0 {{ $numeral(rec.bet_member).format("0,0") }}
                .tournament-mybet-small.tournament-mybet-border.tournament-mybet-team.tournament-mybet-border-top
                  | {{ rec.home_team_name }}
                .tournament-mybet-small.tournament-mybet-border.tournament-mybet-team
                  | {{ rec.away_team_name }}
                .tournament-mybet-small.tournament-mybet-border.tournament-mybet-league.tournament-mybet-border-bottom
                  | {{ rec.league_name }}
                .tournament-mybet-details
                  .tournament-mybet-tiny {{ $t("ui.id") }}: {{ rec.bet_id }}
                  //- .tournament-mybet-status {{ $t("ui." + rec.bet_status.toLowerCase()) }}
                  template(v-if="rec.wl_status")
                    .tournament-mybet-status(v-if="rec.wl != null") {{ $t('ui.' + rec.wl_status.toLowerCase().replace(" ", "_")) }} ({{ $numeral(rec.wl).format("0,0.00") }})
                  template(v-else)
                    .tournament-mybet-status(v-if="rec.bet_status") {{ $t('ui.' + rec.bet_status.toLowerCase()) }}
            template(v-else)
              .tournament-mybet-single.d-flex.align-items-center.justify-content-center
                span {{ $t('message.no_information_available') }}
          .player-betslip-content(v-else)
            template(v-if="betResultList == 'undefined' || betResultList.length <= 0")
              .tournament-pool-body-wrapper
                .text-center
                  span {{ $t('message.no_information_available') }}
            template(v-else)
              table.table-info(width='100%')
                tbody
                  tr
                    th.text-center(scope='col', width='4%') {{ $t("ui.no/") }}
                    th.text-left(scope='col', width='18%') {{ $t("ui.trans_time") }}
                    th.text-left(scope='col', width='34%') {{ $t("ui.event") }}
                    th.text-right(scope='col', width='6%') {{ $t("ui.odds") }}
                    th.text-right(scope='col', width='10%') {{ $t("ui.stake") }}
                    th.text-right(scope='col', width='10%') {{ $t("ui.win") }}/{{ $t("ui.lose") }}
                    th.text-right(scope='col', width='10%') {{ $t("ui.points") }}
                    th(scope='col', width='8%') {{ $t("ui.status") }}
                  tr(v-for="(item, index) in betResultList" :class="{ grey: index % 2 === 0 }")
                    td.text-center(valign='top')
                      span {{ index + 1 }}
                    td.text-left(valign='top')
                      div {{ $t("ui.ref_no") }}: {{ item.bet_id }}
                      div {{ $dayjs(item.bet_time).format("MM/DD/YYYY hh:mm:ss A") }}
                    td.text-left(valign='top')
                      .bet-info
                        .bet-type.blue.mb-1(style="font-size: 13px") {{ item.full_half }} - {{ item.goal }}
                        .match-info.d-flex.flex-column.pl-2.mb-1(style="border-left: 4px solid #cc9966cc; border-radius: 4px; overflow: hidden;")
                          .name-league.font-weight-bold {{ item.league_name }}
                          .d-flex
                            .name-home {{ item.home_team_name }}
                            small.text-muted.mx-1 -vs-
                            .name-away {{ item.away_team_name }}
                          .name-league {{ $dayjs(item.match_time).format("MM/DD/YYYY HH:mm A") }}
                    td.text-right(valign='top')
                      div {{ item.odds_display }}
                    td.text-right(valign='top')
                      span {{ $numeral(item.bet_member).format("0,0") }}
                    td.text-right(valign='top')
                      span(:class="{ red: parseFloat(item.winlose) < 0 }") {{ $numeral(item.winlose).format("0,0") }}
                    td.text-right(valign='top')
                      span(:class="{ red: parseFloat(item.payout) < 0 }") {{ $numeral(item.payout).format("0,0") }}
                    td.text-left(valign='top')
                      div {{ parseFloat(item.winlose) != 0 ? (parseFloat(item.winlose) < 0 ? $t("ui.lost") : $t("ui.won")) : $t("ui.draw") }}
                      div {{ item.score }}
</template>

<script>
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";
import service from "@/tournament2/library/_xhr.js";

export default {
  props: {},
  data() {
    return {
      item: null,
      betResultList: [],
      loading: {
        getBetResultList: false,
      },
    };
  },
  computed: {
    sports() {
      return this.$store.state.layout.sports;
    },
    language() {
      return this.$store.getters.language;
    },
  },
  destroyed() {
    EventBus.$off("TN_PLAYER_BETSLIP2", this.showRecord);
  },
  mounted() {
    EventBus.$on("TN_PLAYER_BETSLIP2", this.showRecord);
  },
  methods: {
    showRecord(e) {
      this.item = null;
      this.item = e;
      var e = this.item.room_id;
      if (e) {
        this.getBetResultList();
      }

      $("#player-betslip").modal("show");
    },
    getTeamName(p, e) {
      var name = e[p + "_name_" + this.language];
      if (name == null || name == "" || !name) {
        name = e[p + "_team_name"];
      }
      return name;
    },
    getBetResultList() {
      var e = this.item.room_id;
      if (e == undefined) {
        return;
      }

      var json = {
        account_id: this.$store.getters.accountId,
        session_token: this.$store.getters.sessionToken,
        room_id: e,
        page_number: 1,
        page_size: 100,
        target: this.item.id,
      };

      var feedback = {
        success: false,
        status: errors.session.invalidSession,
      };

      this.loading.getBetResultList = true;
      service.betResult(config.tournamentUrl().betresultlist, json).then(
        (result) => {
          this.loading.getBetResultList = false;
          if (result) {
            feedback.success = result.success;
            feedback.status = result.status;
            if (result.success == true) {
              this.betResultList = result.data.value;
            } else {
              this.$helpers.handleFeedback(feedback.status);
            }
          }
        },
        (err) => {
          this.loading.getBetResultList = false;
          feedback.success = false;
          feedback.status = errors.request.failed;
          this.$helpers.handleFeedback(err.status);
        }
      );
    },
  },
};
</script>
