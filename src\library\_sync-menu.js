import config from "@/config";

export default {
  decode(r) {
    var m = {};

    const liveCountIndex = 0;
    const marketIndex = 1;
    const menuIndex = 2;
    const parlayIndex = 3;
    const sportsIndex = 4;
    const parlayCountIndex = 5;

    m["sports"] = {};
    for (var n in r[sportsIndex]) {
      if (typeof r[sportsIndex][n] != "function") m["sports"][r[sportsIndex][n][0]] = r[sportsIndex][n][1];
    }

    m["live_count"] = r[liveCountIndex];
    m["live_count_index"] = r[liveCountIndex];

    m["markets"] = {};
    for (var n in r[marketIndex]) {
      if (typeof r[marketIndex][n] != "function") m["markets"][r[marketIndex][n]] = config.markets[r[marketIndex][n]];
    }

    m["menu"] = {};
    for (var n in r[menuIndex]) {
      if (typeof r[menuIndex][n] != "function") {
        if (!m["menu"][r[menuIndex][n][1]]) {
          m["menu"][r[menuIndex][n][1]] = {};
        }
        var hdpou = r[menuIndex][n][5];
        if (hdpou < r[menuIndex][n][6]) hdpou = r[menuIndex][n][6];
        if (hdpou < r[menuIndex][n][9]) hdpou = r[menuIndex][n][9];
        var tn = r[menuIndex][n][2];
        if ([20].includes(r[menuIndex][n][0])) {
          tn = hdpou;
        }

        if (config.vg1.includes(r[menuIndex][n][0])) {
          hdpou = tn;
        }

        m["menu"][r[menuIndex][n][1]][r[menuIndex][n][0]] = {
          st: r[menuIndex][n][0],
          mt: r[menuIndex][n][1],
          tn: tn,
          pl: r[menuIndex][n][3],
          lv: r[menuIndex][n][4],
          hdp: r[menuIndex][n][5],
          ou: r[menuIndex][n][6],
          oe: r[menuIndex][n][7],
          oxt: r[menuIndex][n][8],
          ml: r[menuIndex][n][9],
          cs: r[menuIndex][n][10],
          dc: r[menuIndex][n][11],
          fglg: r[menuIndex][n][12],
          htft: r[menuIndex][n][13],
          tg: r[menuIndex][n][14],
          orz: r[menuIndex][n][15],
          hdpou: hdpou
        };
      }
    }

    // add live market to today market
    //
    if (m["menu"]["3"]) {
      if (!m["menu"]["2"]) {
        m["menu"]["2"] = {};
      }

      m["live_count"] = 0;
      for (var n in m["menu"]["3"]) {
        m["live_count"] += m["menu"]["3"][n]["tn"];
        if (m["menu"]["2"][n]) {
          m["menu"]["2"][n]["tn"] += m["menu"]["3"][n]["tn"];
          m["menu"]["2"][n]["pl"] += m["menu"]["3"][n]["pl"];
          m["menu"]["2"][n]["lv"] += m["menu"]["3"][n]["lv"];
          m["menu"]["2"][n]["hdp"] += m["menu"]["3"][n]["hdp"];
          m["menu"]["2"][n]["ou"] += m["menu"]["3"][n]["ou"];
          m["menu"]["2"][n]["oe"] += m["menu"]["3"][n]["oe"];
          m["menu"]["2"][n]["oxt"] += m["menu"]["3"][n]["oxt"];
          m["menu"]["2"][n]["ml"] += m["menu"]["3"][n]["ml"];
          m["menu"]["2"][n]["cs"] += m["menu"]["3"][n]["cs"];
          m["menu"]["2"][n]["dc"] += m["menu"]["3"][n]["dc"];
          m["menu"]["2"][n]["fglg"] += m["menu"]["3"][n]["fglg"];
          m["menu"]["2"][n]["htft"] += m["menu"]["3"][n]["htft"];
          m["menu"]["2"][n]["tg"] += m["menu"]["3"][n]["tg"];
          m["menu"]["2"][n]["orz"] += m["menu"]["3"][n]["orz"];
          m["menu"]["2"][n]["hdpou"] += m["menu"]["3"][n]["hdpou"];
        } else {
          m["menu"]["2"][n] = m["menu"]["3"][n];
        }
      }
    }

    // add outright from early market to today market
    // must run before the above code
    if (m["menu"]["1"]) {
      if (!m["menu"]["2"]) {
        m["menu"]["2"] = {};
      }
      for (var n in m["menu"]["1"]) {
        if (m["menu"]["1"][n]) {
          if (m["menu"]["1"][n]["orz"] > 0) {
            if (!m["menu"]["2"][n]) {
              m["menu"]["2"][n] = {
                st: m["menu"]["1"][n]["st"],
                tn: m["menu"]["1"][n]["orz"],
                orz: m["menu"]["1"][n]["orz"]
              };
            } else {
              m["menu"]["2"][n]["tn"] += m["menu"]["1"][n]["orz"];
              m["menu"]["2"][n]["orz"] += m["menu"]["1"][n]["orz"];
            }
          }
        }
      }
    }

    // copy outright back to early
    if (m["menu"]["2"]) {
      if (!m["menu"]["1"]) {
        m["menu"]["1"] = {};
      }
      for (var n in m["menu"]["2"]) {
        if (m["menu"]["2"][n]) {
          if (m["menu"]["2"][n]["orz"] > 0) {
            if (!m["menu"]["1"][n]) {
              m["menu"]["1"][n] = {
                st: m["menu"]["2"][n]["st"],
                tn: m["menu"]["2"][n]["orz"],
                orz: m["menu"]["2"][n]["orz"]
              };
            }
          }
        }
      }
    }

    // process parlay market
    for (var n in r[parlayIndex]) {
      if (typeof r[parlayIndex][n] != "function") {
        if (!m["menu"][4]) {
          m["menu"][4] = {};
        }
        m["menu"][4][r[parlayIndex][n][0]] = {
          st: r[parlayIndex][n][0],
          mt: 4,
          tn: r[parlayIndex][n][1]
        };
      }
    }

    m["parlay"] = {};
    for (var n in r[parlayIndex]) {
      if (typeof r[parlayIndex][n] != "function") m["parlay"][r[parlayIndex][n][0]] = r[parlayIndex][n][1];
    }

    m["parlay_count"] = r[parlayCountIndex];

    return m;
  }
};
