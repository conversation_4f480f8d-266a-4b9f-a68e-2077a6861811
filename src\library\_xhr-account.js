import config from "@/config";
import errors from "@/errors";
import Vue from "vue";

export default {
  loading: {
    changePassword: false,
    addSecondary: false
  },
  changePassword(args) {
    const url = config.changePasswordUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "changePassword"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("password" in args)) {
        feedback.status = errors.changePassword.currPasswordRequired;
        reject(feedback);
        canRequest = false;
      }
      if (!("new_password" in args)) {
        feedback.status = errors.changePassword.newPasswordRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.password) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.new_password) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading.changePassword == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.changePassword = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.changePassword = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  resolve(feedback);
                } catch (error) {
                  // Failed to change password
                  feedback.success = false;
                  feedback.status = errors.changePassword.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.changePassword = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  },
  addSecondary(args) {
    const url = config.addSecondaryUrl();
    const feedback = {
      success: false,
      status: errors.request.failed,
      source: "addSecondary"
    };
    return new Promise((resolve, reject) => {
      var canRequest = true;

      if (!args) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }

      if (!("account_id" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("session_token" in args)) {
        feedback.status = errors.request.incompleted;
        reject(feedback);
        canRequest = false;
      }
      if (!("new_account_id" in args)) {
        feedback.status = errors.changePassword.nickNameRequired;
        reject(feedback);
        canRequest = false;
      }

      if (!args.account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.session_token) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }
      if (!args.new_account_id) {
        feedback.status = errors.request.failed;
        reject(feedback);
        canRequest = false;
      }

      // if (this.loading.addSecondary == true) {
      //   feedback.status = errors.request.processing;
      //   canRequest = false;
      // }

      if (canRequest == true) {
        this.loading.addSecondary = true;
        Vue.http.post(url, args).then(
          res => {
            this.loading.addSecondary = false;
            if (res) {
              // check status code
              if (typeof res.data.status == "string") {
                feedback.success = res.data.status == "1";
              } else {
                feedback.success = res.data.status == 1;
              }

              feedback.status = res.data.statusdesc;

              if (feedback.success == true) {
                // Successfully response
                try {
                  resolve(feedback);
                } catch (error) {
                  // Failed to add nickname
                  feedback.success = false;
                  feedback.status = errors.changeNickname.failed;
                  reject(feedback);
                }
              } else {
                reject(feedback);
              }
            } else {
              // Response is empty...
              reject(feedback);
            }
          },
          err => {
            this.loading.addSecondary = false;

            feedback.status = errors.request.failed;
            feedback.error = err;
            reject(feedback);
          }
        );
      } else {
        reject(feedback);
      }
    });
  }
};
