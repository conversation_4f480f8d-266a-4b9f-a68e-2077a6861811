<template lang="pug">
.col-12.hx-more-row
  //- small {{ details }}
  .card
    .card-header(
      :id="'heading-cs-' + uid"
      data-toggle="collapse"
      :data-target="'#collapse-cs-' + uid"
      :aria-controls="'collapse-cs-' + uid"
      aria-expanded=true
      :class="layoutIndex == 3 ? 'live': 'non-live'"
    )
      i.fad.fa-chevron-circle-down
      span.header-bettype {{ $t("m.BT_CS") }}
    .collapse.show(
      :aria-labelledby="'heading-cs-' + uid"
      :id="'collapse-cs-' + uid"
    )
      .card-body.p-0(:id="'accordian-cs-' + uid")

        //- small {{ details }}
        .hx-table.hx-match.hx-more-bet(:class="{ 'live': marketType == 3 }")
          .d-flex.flex-column.w-100
            .hx-cell.w-100.h-18
              .hx-row.hx-more-col.header.bl-1.br-1
                .hx-col.w-53
                  .hx 1-0
                .hx-col.w-53
                  .hx 2-0
                .hx-col.w-53
                  .hx 2-1
                .hx-col.w-53
                  .hx 3-0
                .hx-col.w-53
                  .hx 3-1
                .hx-col.w-53
                  .hx 3-2
                .hx-col.w-53
                  .hx 4-0
                .hx-col.w-53
                  .hx 4-1
                .hx-col.w-53
                  .hx 4-2
                .hx-col.w-53
                  .hx 4-3
                .hx-col.w-53
                  .hx 0-0
                .hx-col.w-53
                  .hx 1-1
                .hx-col.w-53
                  .hx 2-2
                .hx-col.w-53
                  .hx 3-3
                .hx-col.w-53
                  .hx 4-4
                .hx-col.w-55
                  .hx AOS
            .d-flex.flex-row.bl-1.br-1.bb-1
              .hx-cell(style="width: 529px;")
                .hx-row.hx-more-col.body
                  .hx-col.w-53(v-for="item in cs1")
                    .hx
                      .hxs(v-if="details != null && details[item] != null && details[item][5] != null && details[item][5] != ''")
                        oddsItem(:odds="details[item]" idx=5 :typ="oddsType" dataType="3" cls="more-value")
                      .hxs.text-center(v-else) -
                .hx-row.hx-more-col.body
                  .hx-col.w-53(v-for="item in cs2")
                    .hx
                      .hxs(v-if="details != null && details[item] != null && details[item][5] != null && details[item][5] != ''")
                        oddsItem(:odds="details[item]" idx=5 :typ="oddsType" dataType="3" cls="more-value")
                      .hxs.text-center(v-else) -
              .hx-cell.flex-fill.bl-1
                .hx-row.hx-more-col.body.h-100
                  .hx-col.w-53.h-100(v-for="item in cs3")
                    .hx.h-100.hx-flex-c
                      .hxs(v-if="details != null && details[item] != null && details[item][5] != null && details[item][5] != ''")
                        oddsItem(:odds="details[item]" idx=5 :typ="oddsType" dataType="3" cls="more-value")
                      .hxs.text-center(v-else) -
</template>

<script>
import oddsItem from "@/components/desktop/main/xtable/oddsItem";
import config from "@/config";

export default {
  components: {
    oddsItem
  },
  props: {
    uid: {
      type: String
    },
    details: {
      type: Object
    },
    matchId: {
      type: Number
    },
    leagueId: {
      type: Number
    },
    marketType: {
      type: Number
    },
    sportsType: {
      type: Number
    },
    betType: {
      type: String
    },
    layoutIndex: {
      type: Number
    }
  },
  data() {
    return {
      cs1: ['1-0', '2-0', '2-1', '3-0', '3-1', '3-2', '4-0', '4-1', '4-2', '4-3'],
      cs2: ['0-1', '0-2', '1-2', '0-3', '1-3', '2-3', '0-4', '1-4', '2-4', '3-4'],
      cs3: ['0-0', '1-1', '2-2', '3-3', '4-4', 'AOS'],
    }
  },
  computed: {
    oddsType() {
      return this.$store.getters.pageDisplay.oddsType;
    }
  },
  methods: {}
};
</script>