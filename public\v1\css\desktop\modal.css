.modal {
  z-index: 10000;
  padding-right: 0 !important;
}
.modal .modal-dialog .modal-content {
  background-color: transparent;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3), 0 0 3px rgba(0, 0, 0, 0.5);
  font-family: "Roboto", "Tahoma", sans-serif;
  padding: 0 !important;
  margin: 0 !important;
  border: 0 !important;
}
.modal .modal-dialog .modal-content .modal-header {
  background: #276FA8;
  padding: 6px 8px;
  display: flex;
  align-items: center;
  font-weight: 500;
  margin: 0 !important;
  border: 0 !important;
}
.modal .modal-dialog .modal-content .modal-header .modal-title {
  text-transform: capitalize;
  color: #fff;
  font-size: 13px !important;
  line-height: 1 !important;
  font-weight: 400 !important;
  height: auto;
  padding: 0 !important;
  margin: 0 !important;
  border: 0 !important;
}
.modal .modal-dialog .modal-content .modal-header .close {
  text-shadow: none;
  color: #fff;
  opacity: 1;
  line-height: 1 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: 0 !important;
  font-size: 15px !important;
  height: auto;
}
.modal .modal-dialog .modal-content .modal-header .close span {
  line-height: 1;
  padding: 0 !important;
  margin: 0 !important;
  border: 0 !important;
  height: auto;
}
.modal .modal-dialog .modal-content .modal-body {
  background: #ffffff;
  padding: 8px;
}
.modal .modal-dialog .modal-content .modal-body .filter-option.selected .input-group .input-group-prepend .input-group-text {
  background: #0f4f8c;
  border: 1px solid #0f4f8c;
  color: #fff;
}
.modal .modal-dialog .modal-content .modal-body .filter-option .input-group {
  height: 27px;
  line-height: 27px;
}
.modal .modal-dialog .modal-content .modal-body .filter-option {
  box-sizing: border-box;
  border: 1px solid #accde7;
}
.modal .modal-dialog .modal-content .modal-body .filter-option .input-group .input-group-prepend {
  height: 27px;
  line-height: 27px;
}
.modal .modal-dialog .modal-content .modal-body .filter-option .input-group .input-group-prepend .input-group-text {
  background: transparent;
  border: 0;
  font-size: 14px;
  border-radius: 3px 3px 3px 3px;
  -webkit-border-radius: 3px 3px 3px 3px;
  text-transform: capitalize;
  padding: 0 8px;
}
.modal .modal-dialog .modal-content .modal-body .filter-option .input-group .input-group-prepend .input-group-text.dark {
  background: #e3f2ff;
}
.modal .modal-dialog .modal-content .modal-body .filter-option .input-group .form-control {
  border: 1px solid #fff;
  font-size: 14px;
  height: 27px;
  line-height: 27px;
  padding: 0 0.75rem;
}
.modal .modal-dialog .modal-content .modal-body #select-league .card {
  background: transparent;
  border-radius: 0;
  border: 0;
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-header {
  text-transform: capitalize;
  background: #c3e5ff;
  padding: 4px 8px;
  font-size: 13px;
  color: #0f4f8c;
  cursor: pointer;
  line-height: 1;
  display: flex;
  align-items: center;
  border-radius: 0 !important;
  font-weight: 600;
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-header .fa-chevron-circle-down {
  font-size: 20px;
  transition: 0.3s transform ease-in-out, 0.3s -webkit-transform ease-in-out;
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-header.collapsed .fa-chevron-circle-down {
  -webkit-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap {
  display: block;
  position: relative;
  margin-bottom: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding-left: 30px;
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap input:checked ~ .checkmark {
  background-color: #1e66aa;
  border: 1px solid #0f4f8c;
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap input:checked ~ .checkmark:after {
  display: block;
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: #c3e5ff;
  border: 1px solid #0f4f8c;
  border-radius: calc(0.25rem - 1px);
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap .checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 7px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap:hover input ~ .checkmark {
  background-color: #276FA8;
  border: 1px solid #5991C1;
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap span {
  position: relative;
  top: -4px;
  font-size: 13px;
  color: #333;
  left: -5px;
  text-transform: uppercase;
}
.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap span.blue {
  color: #0f4f8c;
}
.modal .modal-dialog .modal-content .modal-body .alert-wait {
  color: #333;
  font-size: 15px;
}
.modal .modal-dialog .modal-content .modal-body .btn-refresh {
  padding: 0 0.5rem;
  line-height: 24px;
  font-size: 14px;
  background: #6c757d;
  cursor: pointer;
  border-radius: 0.25rem;
  color: #fff;
}
.modal .modal-dialog .modal-content .modal-body .btn-remove .btn-secondary {
  padding: 0 1rem;
  line-height: 24px;
  font-size: 14px;
}
.modal .modal-dialog .modal-content .modal-footer {
  background: #ffffff;
  border-top: 1px solid #accde7;
  padding: 4px 8px;
}
.modal .modal-dialog .modal-content .modal-footer .btn {
  border: 0;
  border-radius: 3px;
  padding: 6px 8px;
}
.modal .modal-dialog .modal-content .modal-footer .btn-primary {
  background: #276FA8;
}
.fixed-modal-lg {
  width: 800px;
  height: 600px;
}
.fixed-modal-lg .modal-footer button {
  width: 80px;
  font-size: 13px;
}
.modal-footer-sticky {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 8px;
  background: rgba(0, 0, 0, 0.1);
  width: 100%;
  color: #800;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  font-size: 12px;
  font-family: "Oswald", sans-serif;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
}

#modal-score {
  font-size: 14px;
}
#modal-score .league-wrap {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding-left: 20px !important;
}

#modal-score span.text-score {
  text-transform: inherit !important;
  font-size: 12px;
  cursor: pointer;
}
#modal-score span.bet-item {
  color: #7c7c7c !important;
  font-size: 12px;
  cursor: pointer;
}
#modal-score span.accent {
  color: #b53f39 !important;
  font-weight: 700;
  font-size: 12px;
  cursor: pointer;
}
#modal-score span.glyph {
  color: #7c7c7c !important;
  font-size: 12px;
  cursor: pointer;
}
#modal-score span.bet-large {
  color: #01122b !important;
  font-size: 18px !important;
  cursor: pointer;
}
#modal-score span.special-a {
  font-size: 14px !important;
  cursor: pointer;
  padding-right: 2px;
}
#modal-score span.scoreclose {
  cursor: pointer;
  font-size: 16px;
  margin-left: 5px;
  position: relative;
  top: 1px;
}
#modal-score .league-wrap input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
#modal-score .league-wrap input:checked ~ .checkmark {
  background-color: #0f4f8c;
  border: 1px solid #0f4f8c;
}
#modal-score .league-wrap input:checked ~ .checkmark:after {
  display: block;
}
#modal-score .league-wrap .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: #c3e5ff;
  border: 1px solid #0f4f8c;
  border-radius: calc(0.25rem - 1px);
}
#modal-score .league-wrap .checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 7px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
#modal-score .league-wrap:hover input ~ .checkmark {
  background-color: #c3e5ff;
  border: 1px solid #0f4f8c;
}
.morecontent span {
  display: none;
}
.note {
  margin-top: -5px;
  font-size: 12px;
}
#draggable {
  cursor: all-scroll;
}

#modal-score {
  position: relative;
  height: 0% !important;
}
#modal-score .modal-dialog {
  position: fixed;
  width: 100%;
  max-width: 460px;
}

.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap.wrap-forecast {
  display: initial;
}

.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap.wrap-forecast .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 15px;
  width: 15px;
  background-color: #c3e5ff;
  border: 1px solid #0f4f8c;
  border-radius: calc(0.25rem - 1px);
}

.modal .modal-dialog .modal-content .modal-body #select-league .card .card-body .league-wrap.wrap-forecast .checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.sp-body {
  background: #e3f2ff;
  padding: 8px;
}

.sp-header:last-child .sp-body {
  border-radius: 0;
}

#select-league {
  height: 472px;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  will-change: transform;
  border: 1px solid #accde7;
  box-sizing: border-box;
  background: #e3f2ff;
}

.modal.fade .modal-dialog {
  -webkit-transition: -webkit-transform 0.3s ease-out;
  -moz-transition: -moz-transform 0.3s ease-out;
  -o-transition: -o-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
}

.ads-modal {
  width: 100%;
  height: 100%;
  max-width: 852px !important;
}

.ads-modal .modal-content {
  width: 852px;
  height: 480px;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.ads-modal .modal-content .modal-body {
  background: transparent !important;
}

.ads-modal .modal-content .modal-body iframe {
  width: calc(100% + 4px);
  height: calc(100% + 4px);
  margin-top: -2px;
  margin-left: -2px;
  border: 0;
}

.ads-modal .frame {
  width: 100%;
  height: 100%;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3), 0 0 3px rgba(0, 0, 0, 0.5);
  border-radius: 0.3rem;
  overflow: hidden;
}

.ads-modal .scroller {
  width: 100%;
  height: 100%;
  border-radius: 0.3rem;
  overflow: hidden;
  padding-bottom: 24px;
}
.ads-modal .scroller .inner-content {
  width: 100%;
  height: 100%;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3), 0 0 3px rgba(0, 0, 0, 0.5);
  border-radius: 0.3rem;
  overflow: hidden;
}

.ads-modal .scroller .inner-content img {
  width: 100%;
}


.ads-modal .reminder {
  position: absolute;
  left: -22px;
  top: -28px;
  z-index: 1000;
  color: #ffffff88;
  font-family: "Lato";
  font-weight: 600;
  cursor: pointer;
  padding: 2px 0 3px 0;
  border-radius: 3px;
  line-height: 1;
  border: 1px solid transparent;
  box-sizing: border-box;

  font-size: 16px;
  display: flex;
  align-items: center;
}

.ads-modal .reminder:hover {
  text-shadow: 0 0 5px white;
  color: white;
}

.ads-modal .reminder:hover i {
  border: 2px solid #ffffff;
  background: #ff000088;
  color: #ffffff;
}

.ads-modal .reminder i {
  font-size: 18px;
  margin-right: 4px;
  background: #ff000022;
  line-height: 1;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  box-sizing: border-box;
  border: 2px solid #ffffff88;
  color: #ffffff88;
  padding-top: 0;
}