<template lang="pug">
.col-12.setting-right.py-3
  .w-100.mx-auto
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.display_language") }}
      .col-8
        .d-flex.flex-row
          .form-group.mb-0.w-75
            select.form-control(v-model="pageDisplay.language")
              option(v-for="item in languageList" :key="item.id" :value="item.id") {{ item.lang }}
          .text-account.icon-info.ml-2(:title='$t("message.select_display_language")')
            i.fad.fa-question-circle
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.odds_type") }}
      .col-8
        .d-flex.flex-row
          .form-group.mb-0.w-75
            select.form-control(v-model="pageDisplay.oddsType")
              option(value="MY") {{ $t("ui.my_odds") }}
              option(value="HK") {{ $t("ui.hk_odds") }}
              option(value="ID") {{ $t("ui.id_odds") }}
              option(value="DEC") {{ $t("ui.dec_odds") }}
          .text-account.icon-info.ml-2(:title='$t("message.select_odds_type")')
            i.fad.fa-question-circle
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.page_type") }}
      .col-8
        .d-flex.flex-row
          .form-group.mb-0.w-75
            select.form-control(:value="pageType" @input="handlePageType")
              option(value="2") {{ $t("ui.double_line") }} 1
              option(value="5") {{ $t("ui.double_line") }} 2
              option(value="1") {{ $t("ui.single_line") }}
              option(value="3") {{ $t("ui.full_time") }}
              option(value="4") {{ $t("ui.half_time") }}
          .text-account.icon-info.ml-2(:title='$t("message.select_page_type")')
            i.fad.fa-question-circle
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.show_score_map") }}
      .col-8
        .d-flex.flex-row
          .form-group.mb-0.w-75
            select.form-control(v-model="pageDisplay.showScoreMap")
              option(value="true") {{ $t("ui.enable") }}
              option(value="false") {{ $t("ui.disable") }}
          .text-account.icon-info.ml-2(:title='$t("message.select_show_score_map")')
            i.fad.fa-question-circle
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.default_event_sorting") }}
      .col-8
        .d-flex.flex-row
          .form-group.mb-0.w-75
            select.form-control(v-model="pageDisplay.eventSorting")
              option(value="1") {{ $t("ui.normal_sorting") }}
              option(value="2") {{ $t("ui.sort_by_time") }}
          .text-account.icon-info.ml-2(:title='$t("message.select_default_event_sorting")')
            i.fad.fa-question-circle
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.market_type") }}
      .col-8
        .d-flex.flex-row
          .form-group.mb-0.w-75
            select.form-control(v-model="pageDisplay.marketType")
              option(value="1") {{ $t("ui.all_markets") }}
              option(value="2") {{ $t("ui.main_markets") }}
              option(value="3") {{ $t("ui.other_markets") }}
          .text-account.icon-info.ml-2(:title='$t("message.select_market_type")')
            i.fad.fa-question-circle
    .row.mx-0.my-2
      .col-4.px-0.text-right.text-account {{ $t("ui.page_size") }}
      .col-8
        .d-flex.flex-row
          .form-group.mb-0.w-75
            select.form-control(v-model="pageDisplay.pageSize")
              option(value="10") 10
              option(value="20") 20
              option(value="50") 50
              option(value="100") 100
          .text-account.icon-info.ml-2(:title='$t("message.select_page_size")')
            i.fad.fa-question-circle
    .text-center.mb-2.mt-4
      SpinButton(type="button" @click="save" :loading="loading" css="btn-primary btn-result active w-25" :text="$t('ui.save')").mr-1
      SpinButton(type="button" @click="restore" :loading="loading" css="btn-primary btn-result w-25" :text="$t('ui.restore')")
</template>

<script>
import SpinButton from "@/components/ui/SpinButton";
import config from "@/config";
import errors from "@/errors";
import { EventBus } from "@/library/_event-bus.js";

export default {
  components: {
    SpinButton
  },
  data: () => ({
    loading: false,
    languageList: config.languageAvailable,
    defaultPageDisplay: {
      language: "en",
      oddsType: "MY",
      pageType: "2",
      showScoreMap: "false",
      eventSorting: "1",
      marketType: "1",
      pageSize: "10",
      mmoType: "1"
    },
    pageDisplay: {
      language: "en",
      oddsType: "MY",
      pageType: "2",
      showScoreMap: "false",
      eventSorting: "1",
      marketType: "1",
      pageSize: "10",
      mmoType: "1"
    }
  }),
  computed: {
    pageType() {
      switch (this.pageDisplay.pageType) {
      case "2":
        if (this.pageDisplay.mmoType == "1") {
          return "2";
        } else {
          return "5";
        }
        break;
      default:
        return this.pageDisplay.pageType;
        break;
      }
    }
  },
  mounted() {
    this.initial();
  },
  methods: {
    handlePageType(e) {
      switch (e.target.value) {
      case "1":
        this.pageDisplay.pageType = "1";
        this.pageDisplay.mmoType = "1";
        break;
      case "2":
        this.pageDisplay.pageType = "2";
        this.pageDisplay.mmoType = "1";
        break;
      case "3":
        this.pageDisplay.pageType = "3";
        this.pageDisplay.mmoType = "1";
        break;
      case "4":
        this.pageDisplay.pageType = "4";
        this.pageDisplay.mmoType = "1";
        break;
      case "5":
        this.pageDisplay.pageType = "2";
        this.pageDisplay.mmoType = "2";
        break;
      }
    },
    save() {
      this.$store.dispatch("layout/setPageDisplay", this.pageDisplay).then(res => {
        this.$store
          .dispatch("user/setSettings", {
            page_display: JSON.stringify({
              language: this.$store.getters.language,
              oddsType: this.$store.state.layout.pageDisplay.oddsType,
              pageType: this.$store.state.layout.pageDisplay.pageType,
              showScoreMap: this.$store.state.layout.pageDisplay.showScoreMap,
              eventSorting: this.$store.state.layout.pageDisplay.eventSorting,
              marketType: this.$store.state.layout.pageDisplay.marketType,
              pageSize: this.$store.state.layout.pageDisplay.pageSize,
              mmoType: this.$store.state.layout.pageDisplay.mmoType
            }),
            sports_order: JSON.stringify(this.$store.state.layout.order.sports),
            betting: JSON.stringify({
              defaultStake: this.$store.state.layout.betting.defaultStake,
              acceptBetterOdds: this.$store.state.layout.betting.acceptBetterOdds,
              acceptAnyOdds: this.$store.state.layout.betting.acceptAnyOdds,
              autoRefreshOdds: this.$store.state.layout.betting.autoRefreshOdds,
              quickBet: this.$store.state.layout.betting.quickBet,
              defaultStakeAmount: this.$store.state.layout.betting.defaultStakeAmount,
              quickBetAmount: this.$store.state.layout.betting.quickBetAmount
            })
          })
          .then(
            res => {
              this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.succeed"), "success");
            },
            err => {
              this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.failed"), "error");
            }
          );
      });
    },
    restore() {
      this.pageDisplay.language = this.defaultPageDisplay.language;
      this.pageDisplay.oddsType = this.defaultPageDisplay.oddsType;
      this.pageDisplay.pageType = this.defaultPageDisplay.pageType;
      this.pageDisplay.showScoreMap = this.defaultPageDisplay.showScoreMap;
      this.pageDisplay.eventSorting = this.defaultPageDisplay.eventSorting;
      this.pageDisplay.marketType = this.defaultPageDisplay.marketType;
      this.pageDisplay.pageSize = this.defaultPageDisplay.pageSize;
      this.pageDisplay.mmoType = this.defaultPageDisplay.mmoType;
      this.$helpers.showDialog(this.$t("ui.action"), this.$t("message.succeed"), "success");
    },
    initial() {
      this.pageDisplay.language = this.$store.getters.language;
      this.pageDisplay.oddsType = this.$store.state.layout.pageDisplay.oddsType;
      this.pageDisplay.pageType = this.$store.state.layout.pageDisplay.pageType;
      this.pageDisplay.showScoreMap = this.$store.state.layout.pageDisplay.showScoreMap;
      this.pageDisplay.eventSorting = this.$store.state.layout.pageDisplay.eventSorting;
      this.pageDisplay.marketType = this.$store.state.layout.pageDisplay.marketType;
      this.pageDisplay.pageSize = this.$store.state.layout.pageDisplay.pageSize;
      this.pageDisplay.mmoType = this.$store.state.layout.pageDisplay.mmoType;
    }
  }
};
</script>
